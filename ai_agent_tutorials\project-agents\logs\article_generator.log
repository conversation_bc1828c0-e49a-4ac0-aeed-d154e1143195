2025-03-26 17:48:08,253 - utils - ERROR - Attempt 1 failed: type object 'ArticleResult' has no attribute 'strip'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\project-agents\main.py", line 137, in validate_and_retry_generation
    full_response = editor.run(f"Write an article about {topic}")
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 538, in run
    run_messages = self.get_run_messages(
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 3386, in get_run_messages
    system_message = self.get_system_message()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 3346, in get_system_message
    system_message_content += f"<expected_output>\n{self.expected_output.strip()}\n</expected_output>\n\n"
                                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'ArticleResult' has no attribute 'strip'

2025-03-26 17:48:09,256 - utils - INFO - Rate limit reached. Waiting 0.94 seconds.
2025-03-26 17:48:10,209 - utils - ERROR - Attempt 2 failed: type object 'ArticleResult' has no attribute 'strip'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\project-agents\main.py", line 137, in validate_and_retry_generation
    full_response = editor.run(f"Write an article about {topic}")
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 538, in run
    run_messages = self.get_run_messages(
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 3386, in get_run_messages
    system_message = self.get_system_message()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 3346, in get_system_message
    system_message_content += f"<expected_output>\n{self.expected_output.strip()}\n</expected_output>\n\n"
                                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'ArticleResult' has no attribute 'strip'

2025-03-26 17:48:12,218 - utils - ERROR - Attempt 3 failed: type object 'ArticleResult' has no attribute 'strip'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\project-agents\main.py", line 137, in validate_and_retry_generation
    full_response = editor.run(f"Write an article about {topic}")
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 538, in run
    run_messages = self.get_run_messages(
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 3386, in get_run_messages
    system_message = self.get_system_message()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 3346, in get_system_message
    system_message_content += f"<expected_output>\n{self.expected_output.strip()}\n</expected_output>\n\n"
                                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'ArticleResult' has no attribute 'strip'

2025-03-26 17:48:16,219 - utils - ERROR - Failed to generate article after maximum retries.
2025-03-26 17:52:36,343 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-26 17:52:35.962584\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about ancient egypt'}], 'model': 'llama3-8b-8192', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-26 17:52:36,373 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-26 17:52:36,373 - httpcore.connection - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-03-26 17:52:36,479 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC47B6180>
2025-03-26 17:52:36,480 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000016CC4646350> server_hostname='api.groq.com' timeout=5.0
2025-03-26 17:52:36,522 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CB70AC440>
2025-03-26 17:52:36,523 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-26 17:52:36,524 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-26 17:52:36,526 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-26 17:52:36,526 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-26 17:52:36,526 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-26 17:52:38,542 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 26 Mar 2025 20:51:55 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'14400'), (b'x-ratelimit-limit-tokens', b'6000'), (b'x-ratelimit-remaining-requests', b'14399'), (b'x-ratelimit-remaining-tokens', b'5416'), (b'x-ratelimit-reset-requests', b'6s'), (b'x-ratelimit-reset-tokens', b'5.84s'), (b'x-request-id', b'req_01jqa3kmmaetbtecxqe92t7ey9'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=i.GFg9n6qQvCUbc2ZmqmXwbcyEGAd5w6lLyIYEmzDX4-1743022315-*******-3XjpmtbcqTMJHGbkGRpasOD2bmut1zC8gUdZfoYyTrfvAhPST6lrTkubYEdCGoODk60WEOfwoDeH8FclaQXivDUnw_oh9t2Jkjznp8MxqnY; path=/; expires=Wed, 26-Mar-25 21:21:55 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'926987562facca66-GIG'), (b'Content-Encoding', b'gzip')])
2025-03-26 17:52:38,544 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-26 17:52:38,545 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-26 17:52:38,545 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-26 17:52:38,547 - httpcore.http11 - DEBUG - response_closed.started
2025-03-26 17:52:38,547 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-26 17:52:38,547 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Wed, 26 Mar 2025 20:51:55 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '14400', 'x-ratelimit-limit-tokens': '6000', 'x-ratelimit-remaining-requests': '14399', 'x-ratelimit-remaining-tokens': '5416', 'x-ratelimit-reset-requests': '6s', 'x-ratelimit-reset-tokens': '5.84s', 'x-request-id': 'req_01jqa3kmmaetbtecxqe92t7ey9', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=i.GFg9n6qQvCUbc2ZmqmXwbcyEGAd5w6lLyIYEmzDX4-1743022315-*******-3XjpmtbcqTMJHGbkGRpasOD2bmut1zC8gUdZfoYyTrfvAhPST6lrTkubYEdCGoODk60WEOfwoDeH8FclaQXivDUnw_oh9t2Jkjznp8MxqnY; path=/; expires=Wed, 26-Mar-25 21:21:55 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '926987562facca66-GIG', 'content-encoding': 'gzip'})
2025-03-26 17:52:38,821 - httpcore.connection - DEBUG - connect_tcp.started host='api.agno.com' port=443 local_address=None timeout=60 socket_options=None
2025-03-26 17:52:38,995 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC4528B00>
2025-03-26 17:52:40,293 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000016CC4568950> server_hostname='api.agno.com' timeout=60
2025-03-26 17:52:40,598 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC4528A10>
2025-03-26 17:52:40,598 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-26 17:52:40,599 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-26 17:52:40,599 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-26 17:52:40,599 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-26 17:52:40,600 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-26 17:52:40,758 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 201, b'Created', [(b'Date', b'Wed, 26 Mar 2025 20:51:58 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'3'), (b'Connection', b'keep-alive'), (b'server', b'uvicorn')])
2025-03-26 17:52:40,759 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/telemetry/team-runs "HTTP/1.1 201 Created"
2025-03-26 17:52:40,759 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-26 17:52:40,760 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-26 17:52:40,760 - httpcore.http11 - DEBUG - response_closed.started
2025-03-26 17:52:40,760 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-26 17:52:40,761 - httpcore.connection - DEBUG - close.started
2025-03-26 17:52:40,762 - httpcore.connection - DEBUG - close.complete
2025-03-26 17:52:40,762 - utils - DEBUG - Raw Metrics: {'input_tokens': [3475], 'output_tokens': [607], 'total_tokens': [4082], 'prompt_tokens': [0], 'completion_tokens': [0], 'additional_metrics': [{'completion_time': 0.505833333, 'prompt_time': 0.44600743, 'queue_time': -0.834313256, 'total_time': 0.951840763}], 'time': [2.5858175000030315], 'total_time': 4.814135551452637}
2025-03-26 17:52:41,366 - httpcore.connection - DEBUG - close.started
2025-03-26 17:52:41,368 - httpcore.connection - DEBUG - close.complete
2025-03-26 17:55:54,841 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-26 17:55:54.499532\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about New York Times'}], 'model': 'llama3-70b-8192', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-26 17:55:54,842 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-26 17:55:54,843 - httpcore.connection - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-03-26 17:55:54,894 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC45282F0>
2025-03-26 17:55:54,894 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000016CC468DAD0> server_hostname='api.groq.com' timeout=5.0
2025-03-26 17:55:54,960 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC4528800>
2025-03-26 17:55:54,961 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-26 17:55:54,961 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-26 17:55:54,962 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-26 17:55:54,962 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-26 17:55:54,963 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-26 17:55:56,978 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 26 Mar 2025 20:55:14 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'14400'), (b'x-ratelimit-limit-tokens', b'6000'), (b'x-ratelimit-remaining-requests', b'14399'), (b'x-ratelimit-remaining-tokens', b'5416'), (b'x-ratelimit-reset-requests', b'6s'), (b'x-ratelimit-reset-tokens', b'5.84s'), (b'x-request-id', b'req_01jqa3spepfhbsvxvg9d3e9prs'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=VorlKBlUezzorKjKC3Hg2pJXUU0EvSxzY700KZbfHWE-1743022514-*******-n_pb5m7xQTk3_cV51OpcD68247wJLuTc.Bau.d9_l4xMbUTluo91eU0vqd4nfZwSMbyJSjy1hTzPdvJzP0uH4B53j61zOTYZH2Xn7azS7oA; path=/; expires=Wed, 26-Mar-25 21:25:14 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'92698c2e8afb3577-CNF'), (b'Content-Encoding', b'gzip')])
2025-03-26 17:55:56,979 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-26 17:55:56,980 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-26 17:55:56,980 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-26 17:55:56,981 - httpcore.http11 - DEBUG - response_closed.started
2025-03-26 17:55:56,981 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-26 17:55:56,982 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Wed, 26 Mar 2025 20:55:14 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '14400', 'x-ratelimit-limit-tokens': '6000', 'x-ratelimit-remaining-requests': '14399', 'x-ratelimit-remaining-tokens': '5416', 'x-ratelimit-reset-requests': '6s', 'x-ratelimit-reset-tokens': '5.84s', 'x-request-id': 'req_01jqa3spepfhbsvxvg9d3e9prs', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=VorlKBlUezzorKjKC3Hg2pJXUU0EvSxzY700KZbfHWE-1743022514-*******-n_pb5m7xQTk3_cV51OpcD68247wJLuTc.Bau.d9_l4xMbUTluo91eU0vqd4nfZwSMbyJSjy1hTzPdvJzP0uH4B53j61zOTYZH2Xn7azS7oA; path=/; expires=Wed, 26-Mar-25 21:25:14 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '92698c2e8afb3577-CNF', 'content-encoding': 'gzip'})
2025-03-26 17:55:58,216 - utils - ERROR - Attempt 1 failed: type object 'SearchResult' has no attribute 'strip'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\project-agents\main.py", line 137, in validate_and_retry_generation
    full_response = editor.run(f"Write an article about {topic}")
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 557, in run
    self._run(
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 622, in _run
    model_response = self.model.response(messages=run_messages.messages)  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\models\base.py", line 191, in response
    for function_call_response in self.run_function_calls(
                                  ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\models\base.py", line 860, in run_function_calls
    for item in fc.result:
                ^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 3954, in transfer_task_to_member
    member_agent_run_response = member_agent.run(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 981, in run
    return next(resp)
           ^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 547, in _run
    run_messages: RunMessages = self.get_run_messages(
                                ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 2434, in get_run_messages
    system_message = self.get_system_message()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 2215, in get_system_message
    system_message_content += f"<expected_output>\n{self.expected_output.strip()}\n</expected_output>\n\n"
                                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'SearchResult' has no attribute 'strip'

2025-03-26 17:55:59,446 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-26 17:55:59.221652\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about New York Times'}], 'model': 'llama3-70b-8192', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-26 17:55:59,448 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-26 17:55:59,448 - httpcore.connection - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-03-26 17:55:59,529 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC50E55B0>
2025-03-26 17:55:59,529 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000016CC50D4050> server_hostname='api.groq.com' timeout=5.0
2025-03-26 17:55:59,614 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC50E52E0>
2025-03-26 17:55:59,614 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-26 17:55:59,615 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-26 17:55:59,615 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-26 17:55:59,616 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-26 17:55:59,616 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-26 17:56:00,573 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 26 Mar 2025 20:55:17 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'14400'), (b'x-ratelimit-limit-tokens', b'6000'), (b'x-ratelimit-remaining-requests', b'14398'), (b'x-ratelimit-remaining-tokens', b'2128'), (b'x-ratelimit-reset-requests', b'7.387999999s'), (b'x-ratelimit-reset-tokens', b'38.712s'), (b'x-request-id', b'req_01jqa3styteb8rj1rcee7fm6tv'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=fQOy1vtMNfqSnognXIjYcr7vZVzrviuLo5Jk0c2ff2M-1743022517-*******-myjrwZ_rD7VUvDaywYboS9ZRxhCZwvnpOCl4FZSh0K9wV6RcP64Vcf6HxD_NqpK48ORHAC3Lvt2qrSFqdutk0Ql2kCAh9IhO_bxLtvFbAh0; path=/; expires=Wed, 26-Mar-25 21:25:17 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'92698c4bade24e89-BEL'), (b'Content-Encoding', b'gzip')])
2025-03-26 17:56:00,574 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-26 17:56:00,575 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-26 17:56:00,575 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-26 17:56:00,576 - httpcore.http11 - DEBUG - response_closed.started
2025-03-26 17:56:00,576 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-26 17:56:00,576 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Wed, 26 Mar 2025 20:55:17 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '14400', 'x-ratelimit-limit-tokens': '6000', 'x-ratelimit-remaining-requests': '14398', 'x-ratelimit-remaining-tokens': '2128', 'x-ratelimit-reset-requests': '7.387999999s', 'x-ratelimit-reset-tokens': '38.712s', 'x-request-id': 'req_01jqa3styteb8rj1rcee7fm6tv', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=fQOy1vtMNfqSnognXIjYcr7vZVzrviuLo5Jk0c2ff2M-1743022517-*******-myjrwZ_rD7VUvDaywYboS9ZRxhCZwvnpOCl4FZSh0K9wV6RcP64Vcf6HxD_NqpK48ORHAC3Lvt2qrSFqdutk0Ql2kCAh9IhO_bxLtvFbAh0; path=/; expires=Wed, 26-Mar-25 21:25:17 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '92698c4bade24e89-BEL', 'content-encoding': 'gzip'})
2025-03-26 17:56:00,585 - utils - ERROR - Attempt 2 failed: type object 'SearchResult' has no attribute 'strip'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\project-agents\main.py", line 137, in validate_and_retry_generation
    full_response = editor.run(f"Write an article about {topic}")
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 557, in run
    self._run(
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 622, in _run
    model_response = self.model.response(messages=run_messages.messages)  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\models\base.py", line 191, in response
    for function_call_response in self.run_function_calls(
                                  ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\models\base.py", line 860, in run_function_calls
    for item in fc.result:
                ^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 3954, in transfer_task_to_member
    member_agent_run_response = member_agent.run(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 981, in run
    return next(resp)
           ^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 547, in _run
    run_messages: RunMessages = self.get_run_messages(
                                ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 2434, in get_run_messages
    system_message = self.get_system_message()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 2215, in get_system_message
    system_message_content += f"<expected_output>\n{self.expected_output.strip()}\n</expected_output>\n\n"
                                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'SearchResult' has no attribute 'strip'

2025-03-26 17:56:02,822 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-26 17:56:02.592810\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about New York Times'}], 'model': 'llama3-70b-8192', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-26 17:56:02,823 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-26 17:56:02,824 - httpcore.connection - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-03-26 17:56:02,919 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC50F8680>
2025-03-26 17:56:02,920 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000016CC50ECFD0> server_hostname='api.groq.com' timeout=5.0
2025-03-26 17:56:03,023 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC50F83E0>
2025-03-26 17:56:03,024 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-26 17:56:03,024 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-26 17:56:03,025 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-26 17:56:03,025 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-26 17:56:03,026 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-26 17:56:04,135 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 26 Mar 2025 20:55:21 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'14400'), (b'x-ratelimit-limit-tokens', b'6000'), (b'x-ratelimit-remaining-requests', b'14397'), (b'x-ratelimit-remaining-tokens', b'654'), (b'x-ratelimit-reset-requests', b'14.428s'), (b'x-ratelimit-reset-tokens', b'53.453999999s'), (b'x-request-id', b'req_01jqa3syefe1vafsxmyypj931p'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=J3vyRIvCVbiMGsGqDg9ZO9KmKzdU4fXtQDrha_eF21Y-1743022521-*******-Bl27O5ZxnKSc7.d4F_ZuUY22KwfEhu1zAaMlxsmNXucNzHanl_vH494jmW9ycLkQa901R.huFNNZKRPAWA0zWPo1zFn5Msuq2HYLZgwP1I8; path=/; expires=Wed, 26-Mar-25 21:25:21 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'92698c610b227846-MAO'), (b'Content-Encoding', b'gzip')])
2025-03-26 17:56:04,136 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-26 17:56:04,137 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-26 17:56:04,137 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-26 17:56:04,138 - httpcore.http11 - DEBUG - response_closed.started
2025-03-26 17:56:04,138 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-26 17:56:04,138 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Wed, 26 Mar 2025 20:55:21 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '14400', 'x-ratelimit-limit-tokens': '6000', 'x-ratelimit-remaining-requests': '14397', 'x-ratelimit-remaining-tokens': '654', 'x-ratelimit-reset-requests': '14.428s', 'x-ratelimit-reset-tokens': '53.453999999s', 'x-request-id': 'req_01jqa3syefe1vafsxmyypj931p', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=J3vyRIvCVbiMGsGqDg9ZO9KmKzdU4fXtQDrha_eF21Y-1743022521-*******-Bl27O5ZxnKSc7.d4F_ZuUY22KwfEhu1zAaMlxsmNXucNzHanl_vH494jmW9ycLkQa901R.huFNNZKRPAWA0zWPo1zFn5Msuq2HYLZgwP1I8; path=/; expires=Wed, 26-Mar-25 21:25:21 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '92698c610b227846-MAO', 'content-encoding': 'gzip'})
2025-03-26 17:56:04,142 - httpcore.connection - DEBUG - close.started
2025-03-26 17:56:04,143 - httpcore.connection - DEBUG - close.complete
2025-03-26 17:56:04,151 - utils - ERROR - Attempt 3 failed: type object 'SearchResult' has no attribute 'strip'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\project-agents\main.py", line 137, in validate_and_retry_generation
    full_response = editor.run(f"Write an article about {topic}")
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 557, in run
    self._run(
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 622, in _run
    model_response = self.model.response(messages=run_messages.messages)  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\models\base.py", line 191, in response
    for function_call_response in self.run_function_calls(
                                  ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\models\base.py", line 860, in run_function_calls
    for item in fc.result:
                ^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 3954, in transfer_task_to_member
    member_agent_run_response = member_agent.run(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 981, in run
    return next(resp)
           ^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 547, in _run
    run_messages: RunMessages = self.get_run_messages(
                                ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 2434, in get_run_messages
    system_message = self.get_system_message()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 2215, in get_system_message
    system_message_content += f"<expected_output>\n{self.expected_output.strip()}\n</expected_output>\n\n"
                                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'SearchResult' has no attribute 'strip'

2025-03-26 17:56:08,153 - utils - ERROR - Failed to generate article after maximum retries.
2025-03-26 17:56:08,231 - httpcore.connection - DEBUG - close.started
2025-03-26 17:56:08,232 - httpcore.connection - DEBUG - close.complete
2025-03-26 17:56:08,233 - httpcore.connection - DEBUG - close.started
2025-03-26 17:56:08,234 - httpcore.connection - DEBUG - close.complete
2025-03-26 17:56:31,283 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-26 17:56:30.940433\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about New York Times'}], 'model': 'llama-3.2-3b-preview', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-26 17:56:31,284 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-26 17:56:31,284 - httpcore.connection - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-03-26 17:56:31,320 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC50E6DE0>
2025-03-26 17:56:31,320 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000016CC50D4ED0> server_hostname='api.groq.com' timeout=5.0
2025-03-26 17:56:31,362 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CAEDDD2E0>
2025-03-26 17:56:31,363 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-26 17:56:31,363 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-26 17:56:31,364 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-26 17:56:31,364 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-26 17:56:31,364 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-26 17:56:32,364 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 26 Mar 2025 20:55:49 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'7000'), (b'x-ratelimit-limit-tokens', b'7000'), (b'x-ratelimit-remaining-requests', b'6999'), (b'x-ratelimit-remaining-tokens', b'6416'), (b'x-ratelimit-reset-requests', b'12.342857142s'), (b'x-ratelimit-reset-tokens', b'5.005714285s'), (b'x-request-id', b'req_01jqa3tsx1fev9p2h7m4c2bz0c'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=GuzR3IU661YZc8TsSSJdPzI3wnixjkPg5KKSn4L0F88-1743022549-*******-.iPxpqaM6ch0W9QOJthNjfM1cT5ktPbHh1vuPZTqhhuXizjA4YENUibTxFwavovNhn27dZyL2K33ouY3s86_HwHycB_ZYUj8Q95u8eLCajY; path=/; expires=Wed, 26-Mar-25 21:25:49 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'92698d11ea3dcb0a-GIG'), (b'Content-Encoding', b'gzip')])
2025-03-26 17:56:32,365 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-26 17:56:32,365 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-26 17:56:32,366 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-26 17:56:32,367 - httpcore.http11 - DEBUG - response_closed.started
2025-03-26 17:56:32,367 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-26 17:56:32,367 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Wed, 26 Mar 2025 20:55:49 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '7000', 'x-ratelimit-limit-tokens': '7000', 'x-ratelimit-remaining-requests': '6999', 'x-ratelimit-remaining-tokens': '6416', 'x-ratelimit-reset-requests': '12.342857142s', 'x-ratelimit-reset-tokens': '5.005714285s', 'x-request-id': 'req_01jqa3tsx1fev9p2h7m4c2bz0c', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=GuzR3IU661YZc8TsSSJdPzI3wnixjkPg5KKSn4L0F88-1743022549-*******-.iPxpqaM6ch0W9QOJthNjfM1cT5ktPbHh1vuPZTqhhuXizjA4YENUibTxFwavovNhn27dZyL2K33ouY3s86_HwHycB_ZYUj8Q95u8eLCajY; path=/; expires=Wed, 26-Mar-25 21:25:49 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '92698d11ea3dcb0a-GIG', 'content-encoding': 'gzip'})
2025-03-26 17:56:32,587 - httpcore.connection - DEBUG - connect_tcp.started host='api.agno.com' port=443 local_address=None timeout=60 socket_options=None
2025-03-26 17:56:32,762 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC50E4EC0>
2025-03-26 17:56:32,762 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000016CC50D4D50> server_hostname='api.agno.com' timeout=60
2025-03-26 17:56:33,069 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC50E5D30>
2025-03-26 17:56:33,069 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-26 17:56:33,070 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-26 17:56:33,071 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-26 17:56:33,071 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-26 17:56:33,071 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-26 17:56:33,229 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 201, b'Created', [(b'Date', b'Wed, 26 Mar 2025 20:55:50 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'3'), (b'Connection', b'keep-alive'), (b'server', b'uvicorn')])
2025-03-26 17:56:33,229 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/telemetry/team-runs "HTTP/1.1 201 Created"
2025-03-26 17:56:33,230 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-26 17:56:33,230 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-26 17:56:33,230 - httpcore.http11 - DEBUG - response_closed.started
2025-03-26 17:56:33,231 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-26 17:56:33,231 - httpcore.connection - DEBUG - close.started
2025-03-26 17:56:33,231 - httpcore.connection - DEBUG - close.complete
2025-03-26 17:56:33,232 - utils - DEBUG - Raw Metrics: {'input_tokens': [1925], 'output_tokens': [49], 'total_tokens': [1974], 'prompt_tokens': [0], 'completion_tokens': [0], 'additional_metrics': [{'completion_time': 0.033187092, 'prompt_time': 0.328064243, 'queue_time': -0.503386402, 'total_time': 0.361251335}], 'time': [1.4269929000001866], 'total_time': 2.299670457839966}
2025-03-26 17:56:33,422 - httpcore.connection - DEBUG - close.started
2025-03-26 17:56:33,422 - httpcore.connection - DEBUG - close.complete
2025-03-26 17:56:52,797 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-26 17:56:52.442684\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about New York Times'}], 'model': 'deepseek-r1-distill-qwen-32b', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-26 17:56:52,798 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-26 17:56:52,798 - httpcore.connection - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-03-26 17:56:52,842 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC50E6ED0>
2025-03-26 17:56:52,842 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000016CC50D68D0> server_hostname='api.groq.com' timeout=5.0
2025-03-26 17:56:52,893 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC50E7F50>
2025-03-26 17:56:52,894 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-26 17:56:52,896 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-26 17:56:52,896 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-26 17:56:52,897 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-26 17:56:52,897 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-26 17:56:54,991 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Wed, 26 Mar 2025 20:56:12 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'1000'), (b'x-ratelimit-limit-tokens', b'6000'), (b'x-ratelimit-remaining-requests', b'999'), (b'x-ratelimit-remaining-tokens', b'5416'), (b'x-ratelimit-reset-requests', b'1m26.4s'), (b'x-ratelimit-reset-tokens', b'5.84s'), (b'x-request-id', b'req_01jqa3veygfhrtqdd7kcvxq2s4'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=ywRsKd7TkFhI9hHDCKGDn5OZhRxjN0IpOOp_q4dyrDY-1743022572-*******-RsN8HVaL3pRIuw744TC2zEZIjL6xHL9U5b6Vpd8Ye63BCHIME1jsPoqhW4fV8jZz_wm9K0q0bhHqvOEXxJkrZOh74zSDoVlP.wFvIhPgfKU; path=/; expires=Wed, 26-Mar-25 21:26:12 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'92698d989e1c3571-CNF'), (b'Content-Encoding', b'gzip')])
2025-03-26 17:56:54,992 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-26 17:56:54,993 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-26 17:56:54,993 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-26 17:56:54,994 - httpcore.http11 - DEBUG - response_closed.started
2025-03-26 17:56:54,994 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-26 17:56:54,994 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Wed, 26 Mar 2025 20:56:12 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '1000', 'x-ratelimit-limit-tokens': '6000', 'x-ratelimit-remaining-requests': '999', 'x-ratelimit-remaining-tokens': '5416', 'x-ratelimit-reset-requests': '1m26.4s', 'x-ratelimit-reset-tokens': '5.84s', 'x-request-id': 'req_01jqa3veygfhrtqdd7kcvxq2s4', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=ywRsKd7TkFhI9hHDCKGDn5OZhRxjN0IpOOp_q4dyrDY-1743022572-*******-RsN8HVaL3pRIuw744TC2zEZIjL6xHL9U5b6Vpd8Ye63BCHIME1jsPoqhW4fV8jZz_wm9K0q0bhHqvOEXxJkrZOh74zSDoVlP.wFvIhPgfKU; path=/; expires=Wed, 26-Mar-25 21:26:12 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '92698d989e1c3571-CNF', 'content-encoding': 'gzip'})
2025-03-26 17:56:55,213 - httpcore.connection - DEBUG - connect_tcp.started host='api.agno.com' port=443 local_address=None timeout=60 socket_options=None
2025-03-26 17:56:55,360 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC439DEB0>
2025-03-26 17:56:55,360 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000016CC50D5E50> server_hostname='api.agno.com' timeout=60
2025-03-26 17:56:55,665 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000016CC439CA40>
2025-03-26 17:56:55,666 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-26 17:56:55,666 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-26 17:56:55,667 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-26 17:56:55,667 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-26 17:56:55,668 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-26 17:56:55,819 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 201, b'Created', [(b'Date', b'Wed, 26 Mar 2025 20:56:13 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'3'), (b'Connection', b'keep-alive'), (b'server', b'uvicorn')])
2025-03-26 17:56:55,819 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/telemetry/team-runs "HTTP/1.1 201 Created"
2025-03-26 17:56:55,820 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-26 17:56:55,820 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-26 17:56:55,821 - httpcore.http11 - DEBUG - response_closed.started
2025-03-26 17:56:55,821 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-26 17:56:55,821 - httpcore.connection - DEBUG - close.started
2025-03-26 17:56:55,822 - httpcore.connection - DEBUG - close.complete
2025-03-26 17:56:55,822 - utils - DEBUG - Raw Metrics: {'input_tokens': [825], 'output_tokens': [214], 'total_tokens': [1039], 'prompt_tokens': [0], 'completion_tokens': [0], 'additional_metrics': [{'completion_time': 1.5285714289999999, 'prompt_time': 0.064071478, 'queue_time': 0.24040133399999997, 'total_time': 1.592642907}], 'time': [2.551265299989609], 'total_time': 3.3883190155029297}
2025-03-26 17:56:56,038 - httpcore.connection - DEBUG - close.started
2025-03-26 17:56:56,039 - httpcore.connection - DEBUG - close.complete
2025-03-27 14:39:45,110 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-27 14:39:44.682477\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about New York Times'}], 'model': 'deepseek-r1-distill-qwen-32b', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-27 14:39:45,154 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-27 14:39:45,155 - httpcore.connection - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-03-27 14:39:45,300 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000256190E6210>
2025-03-27 14:39:45,300 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000002561911DF50> server_hostname='api.groq.com' timeout=5.0
2025-03-27 14:39:45,413 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x00000256192CC2F0>
2025-03-27 14:39:45,414 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:39:45,415 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:39:45,415 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:39:45,417 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:39:45,417 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:39:54,668 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 27 Mar 2025 17:39:10 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'1000'), (b'x-ratelimit-limit-tokens', b'6000'), (b'x-ratelimit-remaining-requests', b'999'), (b'x-ratelimit-remaining-tokens', b'5416'), (b'x-ratelimit-reset-requests', b'1m26.4s'), (b'x-ratelimit-reset-tokens', b'5.84s'), (b'x-request-id', b'req_01jqcaz6nxeyzb4m5vchhd2h3s'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=Uqrg4FQc0BE3PLg08Ps.CJMFbbnYIlfwPdcFTSWX9CM-1743097150-*******-cdtt8tziIcOWYsx9MQCm.nxBqsEbp3MeA65osHOuDI5_wAdVeYImZMlbSLRbPQNSRGjVUAdyXWYWeEwcJl59mAEJRd3duf1ZKjVz_5z1iYo; path=/; expires=Thu, 27-Mar-25 18:09:10 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9270aa2fddab784c-MAO'), (b'Content-Encoding', b'gzip')])
2025-03-27 14:39:54,670 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-27 14:39:54,670 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:39:54,671 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:39:54,672 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:39:54,672 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:39:54,672 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Thu, 27 Mar 2025 17:39:10 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '1000', 'x-ratelimit-limit-tokens': '6000', 'x-ratelimit-remaining-requests': '999', 'x-ratelimit-remaining-tokens': '5416', 'x-ratelimit-reset-requests': '1m26.4s', 'x-ratelimit-reset-tokens': '5.84s', 'x-request-id': 'req_01jqcaz6nxeyzb4m5vchhd2h3s', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=Uqrg4FQc0BE3PLg08Ps.CJMFbbnYIlfwPdcFTSWX9CM-1743097150-*******-cdtt8tziIcOWYsx9MQCm.nxBqsEbp3MeA65osHOuDI5_wAdVeYImZMlbSLRbPQNSRGjVUAdyXWYWeEwcJl59mAEJRd3duf1ZKjVz_5z1iYo; path=/; expires=Thu, 27-Mar-25 18:09:10 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9270aa2fddab784c-MAO', 'content-encoding': 'gzip'})
2025-03-27 14:39:54,957 - httpcore.connection - DEBUG - connect_tcp.started host='api.agno.com' port=443 local_address=None timeout=60 socket_options=None
2025-03-27 14:39:55,125 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000025619030D70>
2025-03-27 14:39:55,125 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000256190504D0> server_hostname='api.agno.com' timeout=60
2025-03-27 14:39:55,436 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000025618EAB8F0>
2025-03-27 14:39:55,437 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:39:55,438 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:39:55,439 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:39:55,440 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:39:55,440 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:39:55,601 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 201, b'Created', [(b'Date', b'Thu, 27 Mar 2025 17:39:11 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'3'), (b'Connection', b'keep-alive'), (b'server', b'uvicorn')])
2025-03-27 14:39:55,601 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/telemetry/team-runs "HTTP/1.1 201 Created"
2025-03-27 14:39:55,601 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:39:55,602 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:39:55,603 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:39:55,603 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:39:55,605 - httpcore.connection - DEBUG - close.started
2025-03-27 14:39:55,605 - httpcore.connection - DEBUG - close.complete
2025-03-27 14:39:55,606 - utils - DEBUG - Raw Metrics: {'input_tokens': [825], 'output_tokens': [1185], 'total_tokens': [2010], 'prompt_tokens': [0], 'completion_tokens': [0], 'additional_metrics': [{'completion_time': 8.464285714, 'prompt_time': 0.074010761, 'queue_time': 0.240247433, 'total_time': 8.538296475}], 'time': [9.992349400010426], 'total_time': 10.941992282867432}
2025-03-27 14:39:56,577 - httpcore.connection - DEBUG - close.started
2025-03-27 14:39:56,577 - httpcore.connection - DEBUG - close.complete
2025-03-27 14:44:31,524 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-27 14:44:31.039967\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about Agents AI'}], 'model': 'llama3-8b-8192', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-27 14:44:31,559 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-27 14:44:31,560 - httpcore.connection - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-03-27 14:44:31,802 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D98B863F0>
2025-03-27 14:44:31,802 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000024D98BD1FD0> server_hostname='api.groq.com' timeout=5.0
2025-03-27 14:44:31,902 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D98CCF740>
2025-03-27 14:44:31,902 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:44:31,903 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:44:31,903 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:44:31,904 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:44:31,904 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:44:33,456 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 27 Mar 2025 17:43:49 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'14400'), (b'x-ratelimit-limit-tokens', b'6000'), (b'x-ratelimit-remaining-requests', b'14399'), (b'x-ratelimit-remaining-tokens', b'5417'), (b'x-ratelimit-reset-requests', b'6s'), (b'x-ratelimit-reset-tokens', b'5.83s'), (b'x-request-id', b'req_01jqcb7ywgf599v42aznz50frh'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=kX4GTLdNI1r5imLcZOcqkDKHGf3LaTzeNHF.6M_WeqA-1743097429-*******-XH.hbh4HzT1TN7kWwSMqn6Y5w9KSWwV6kQBdHdaBGpMSg1I6DaXaYk72Kz0ElMq3pAYdSkbpLcgs8er5mwJYLmCRSsOa8.LbzrJntc0XL_A; path=/; expires=Thu, 27-Mar-25 18:13:49 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9270b12e5a35784c-MAO'), (b'Content-Encoding', b'gzip')])
2025-03-27 14:44:33,458 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-27 14:44:33,459 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:44:33,463 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:44:33,464 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:44:33,464 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:44:33,465 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Thu, 27 Mar 2025 17:43:49 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '14400', 'x-ratelimit-limit-tokens': '6000', 'x-ratelimit-remaining-requests': '14399', 'x-ratelimit-remaining-tokens': '5417', 'x-ratelimit-reset-requests': '6s', 'x-ratelimit-reset-tokens': '5.83s', 'x-request-id': 'req_01jqcb7ywgf599v42aznz50frh', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=kX4GTLdNI1r5imLcZOcqkDKHGf3LaTzeNHF.6M_WeqA-1743097429-*******-XH.hbh4HzT1TN7kWwSMqn6Y5w9KSWwV6kQBdHdaBGpMSg1I6DaXaYk72Kz0ElMq3pAYdSkbpLcgs8er5mwJYLmCRSsOa8.LbzrJntc0XL_A; path=/; expires=Thu, 27-Mar-25 18:13:49 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9270b12e5a35784c-MAO', 'content-encoding': 'gzip'})
2025-03-27 14:44:35,340 - utils - ERROR - Attempt 1 failed: type object 'SearchResult' has no attribute 'strip'
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\project-agents\main.py", line 133, in validate_and_retry_generation
    full_response = editor.run(f"Write an article about {topic}")
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 557, in run
    self._run(
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 622, in _run
    model_response = self.model.response(messages=run_messages.messages)  # type: ignore
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\models\base.py", line 191, in response
    for function_call_response in self.run_function_calls(
                                  ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\models\base.py", line 860, in run_function_calls
    for item in fc.result:
                ^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\team\team.py", line 3954, in transfer_task_to_member
    member_agent_run_response = member_agent.run(
                                ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 981, in run
    return next(resp)
           ^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 547, in _run
    run_messages: RunMessages = self.get_run_messages(
                                ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 2434, in get_run_messages
    system_message = self.get_system_message()
                     ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\agno\agent\agent.py", line 2215, in get_system_message
    system_message_content += f"<expected_output>\n{self.expected_output.strip()}\n</expected_output>\n\n"
                                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: type object 'SearchResult' has no attribute 'strip'

2025-03-27 14:44:36,574 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-27 14:44:36.347629\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about Agents AI'}], 'model': 'llama3-8b-8192', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-27 14:44:36,575 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-27 14:44:36,576 - httpcore.connection - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-03-27 14:44:36,665 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D9952EDE0>
2025-03-27 14:44:36,665 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000024D99600050> server_hostname='api.groq.com' timeout=5.0
2025-03-27 14:44:36,764 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D990A5A60>
2025-03-27 14:44:36,764 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:44:36,765 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:44:36,766 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:44:36,766 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:44:36,767 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:44:39,109 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 27 Mar 2025 17:43:55 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'14400'), (b'x-ratelimit-limit-tokens', b'6000'), (b'x-ratelimit-remaining-requests', b'14398'), (b'x-ratelimit-remaining-tokens', b'3567'), (b'x-ratelimit-reset-requests', b'7.593999999s'), (b'x-ratelimit-reset-tokens', b'24.326s'), (b'x-request-id', b'req_01jqcb8366fydrf1th88xqenrd'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=rVuCjtTHs8mhQlkodXcCQrrx93Hn.XVlBAkULZ99bNo-1743097435-*******-NuyM_M0C7ahs0Jj2m6hD5cxQF43NzBXKkJ_sgjn5CDld.W7Suf0e4Z061IIsrWEG3mDkFSSJCB0Sr0Ge6wYhb5sRm8HcAmeOW77W9uA.zAM; path=/; expires=Thu, 27-Mar-25 18:13:55 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9270b14ccb667843-MAO'), (b'Content-Encoding', b'gzip')])
2025-03-27 14:44:39,110 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-27 14:44:39,110 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:44:39,111 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:44:39,112 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:44:39,112 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:44:39,112 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Thu, 27 Mar 2025 17:43:55 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '14400', 'x-ratelimit-limit-tokens': '6000', 'x-ratelimit-remaining-requests': '14398', 'x-ratelimit-remaining-tokens': '3567', 'x-ratelimit-reset-requests': '7.593999999s', 'x-ratelimit-reset-tokens': '24.326s', 'x-request-id': 'req_01jqcb8366fydrf1th88xqenrd', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=rVuCjtTHs8mhQlkodXcCQrrx93Hn.XVlBAkULZ99bNo-1743097435-*******-NuyM_M0C7ahs0Jj2m6hD5cxQF43NzBXKkJ_sgjn5CDld.W7Suf0e4Z061IIsrWEG3mDkFSSJCB0Sr0Ge6wYhb5sRm8HcAmeOW77W9uA.zAM; path=/; expires=Thu, 27-Mar-25 18:13:55 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9270b14ccb667843-MAO', 'content-encoding': 'gzip'})
2025-03-27 14:44:39,417 - httpcore.connection - DEBUG - connect_tcp.started host='api.agno.com' port=443 local_address=None timeout=60 socket_options=None
2025-03-27 14:44:39,594 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D99651370>
2025-03-27 14:44:39,594 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000024D9966FF50> server_hostname='api.agno.com' timeout=60
2025-03-27 14:44:39,897 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D996512B0>
2025-03-27 14:44:39,909 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:44:39,910 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:44:39,910 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:44:39,911 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:44:39,917 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:44:40,067 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 201, b'Created', [(b'Date', b'Thu, 27 Mar 2025 17:43:56 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'3'), (b'Connection', b'keep-alive'), (b'server', b'uvicorn')])
2025-03-27 14:44:40,068 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/telemetry/team-runs "HTTP/1.1 201 Created"
2025-03-27 14:44:40,068 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:44:40,068 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:44:40,069 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:44:40,069 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:44:40,070 - httpcore.connection - DEBUG - close.started
2025-03-27 14:44:40,070 - httpcore.connection - DEBUG - close.complete
2025-03-27 14:44:40,071 - utils - DEBUG - Raw Metrics: {'input_tokens': [3473], 'output_tokens': [744], 'total_tokens': [4217], 'prompt_tokens': [0], 'completion_tokens': [0], 'additional_metrics': [{'completion_time': 0.62, 'prompt_time': 0.440459389, 'queue_time': -0.985237105, 'total_time': 1.060459389}], 'time': [2.7651290000067092], 'total_time': 3.7298858165740967}
2025-03-27 14:44:40,373 - httpcore.connection - DEBUG - close.started
2025-03-27 14:44:40,375 - httpcore.connection - DEBUG - close.complete
2025-03-27 14:44:40,376 - httpcore.connection - DEBUG - close.started
2025-03-27 14:44:40,376 - httpcore.connection - DEBUG - close.complete
2025-03-27 14:45:16,689 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-27 14:45:16.307163\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about Agents AI'}], 'model': 'llama-3.2-3b-preview', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-27 14:45:16,692 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-27 14:45:16,693 - httpcore.connection - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-03-27 14:45:16,876 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D98CCE9C0>
2025-03-27 14:45:16,876 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000024D98D39B50> server_hostname='api.groq.com' timeout=5.0
2025-03-27 14:45:17,063 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D9952CC50>
2025-03-27 14:45:17,064 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:45:17,064 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:45:17,065 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:45:17,065 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:45:17,066 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:45:18,858 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 400, b'Bad Request', [(b'Date', b'Thu, 27 Mar 2025 17:44:34 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'7000'), (b'x-ratelimit-limit-tokens', b'7000'), (b'x-ratelimit-remaining-requests', b'6999'), (b'x-ratelimit-remaining-tokens', b'6417'), (b'x-ratelimit-reset-requests', b'12.342857142s'), (b'x-ratelimit-reset-tokens', b'4.997142857s'), (b'x-request-id', b'req_01jqcb9adcfwm9fq7mdnrfbvz2'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=SNUPDVW5uo3oPBFFMBC1DrVvctm5IDw1xdusSwmJdnw-1743097474-*******-Z9nNZWunid8LSGzad5HmV80zMTa8ncwk4ZA8L4zhpPVR4LSo3YjCGntA.TzOJ0SGTIp1q7SyViYXXZb9XpB.Ka6TlhNcNworuerSnhUcDfo; path=/; expires=Thu, 27-Mar-25 18:14:34 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9270b2488b32e7a4-SCL')])
2025-03-27 14:45:18,859 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-03-27 14:45:18,860 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:45:18,860 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:45:18,860 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:45:18,861 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:45:18,861 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "400 Bad Request" Headers({'date': 'Thu, 27 Mar 2025 17:44:34 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '7000', 'x-ratelimit-limit-tokens': '7000', 'x-ratelimit-remaining-requests': '6999', 'x-ratelimit-remaining-tokens': '6417', 'x-ratelimit-reset-requests': '12.342857142s', 'x-ratelimit-reset-tokens': '4.997142857s', 'x-request-id': 'req_01jqcb9adcfwm9fq7mdnrfbvz2', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=SNUPDVW5uo3oPBFFMBC1DrVvctm5IDw1xdusSwmJdnw-1743097474-*******-Z9nNZWunid8LSGzad5HmV80zMTa8ncwk4ZA8L4zhpPVR4LSo3YjCGntA.TzOJ0SGTIp1q7SyViYXXZb9XpB.Ka6TlhNcNworuerSnhUcDfo; path=/; expires=Thu, 27-Mar-25 18:14:34 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9270b2488b32e7a4-SCL'})
2025-03-27 14:45:18,862 - groq._base_client - DEBUG - Encountered httpx.HTTPStatusError
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\groq\_base_client.py", line 999, in _request
    response.raise_for_status()
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\httpx\_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '400 Bad Request' for url 'https://api.groq.com/openai/v1/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/400
2025-03-27 14:45:18,871 - groq._base_client - DEBUG - Not retrying
2025-03-27 14:45:18,871 - groq._base_client - DEBUG - Re-raising status error
2025-03-27 14:45:19,948 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-27 14:45:19.944724\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about Agents AI'}], 'model': 'llama-3.2-3b-preview', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-27 14:45:19,950 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-27 14:45:19,950 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:45:19,951 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:45:19,952 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:45:19,952 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:45:19,953 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:45:21,550 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 27 Mar 2025 17:44:37 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'7000'), (b'x-ratelimit-limit-tokens', b'7000'), (b'x-ratelimit-remaining-requests', b'6998'), (b'x-ratelimit-remaining-tokens', b'6417'), (b'x-ratelimit-reset-requests', b'21.803714285s'), (b'x-ratelimit-reset-tokens', b'4.997142857s'), (b'x-request-id', b'req_01jqcb9d7ffwntfxa96zrksdk7'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9270b25a9a3ce7a4-SCL'), (b'Content-Encoding', b'gzip')])
2025-03-27 14:45:21,551 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-27 14:45:21,552 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:45:21,552 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:45:21,553 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:45:21,553 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:45:21,553 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Thu, 27 Mar 2025 17:44:37 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '7000', 'x-ratelimit-limit-tokens': '7000', 'x-ratelimit-remaining-requests': '6998', 'x-ratelimit-remaining-tokens': '6417', 'x-ratelimit-reset-requests': '21.803714285s', 'x-ratelimit-reset-tokens': '4.997142857s', 'x-request-id': 'req_01jqcb9d7ffwntfxa96zrksdk7', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '9270b25a9a3ce7a4-SCL', 'content-encoding': 'gzip'})
2025-03-27 14:45:21,561 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-27 14:45:19.944724\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about Agents AI'}, {'role': 'assistant', 'tool_calls': [{'id': 'call_vmar', 'function': {'arguments': '{"query": " latest advancements in artificial intelligence"}', 'name': 'duckduckgo_search'}, 'type': 'function'}, {'id': 'call_vm8y', 'function': {'arguments': '{"state": "AI Agents are being developed to perform complex tasks without human intervention."}', 'name': 'set_team_context'}, 'type': 'function'}]}, {'role': 'user', 'content': 'Could not find function to call.'}, {'role': 'tool', 'content': 'Current team context: <team context>\nAI Agents are being developed to perform complex tasks without human intervention.\n</team context>\n', 'tool_call_id': 'call_vm8y'}], 'model': 'llama-3.2-3b-preview', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-27 14:45:21,564 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-27 14:45:21,564 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:45:21,564 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:45:21,565 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:45:21,565 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:45:21,566 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:45:22,155 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 27 Mar 2025 17:44:38 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'7000'), (b'x-ratelimit-limit-tokens', b'7000'), (b'x-ratelimit-remaining-requests', b'6997'), (b'x-ratelimit-remaining-tokens', b'3925'), (b'x-ratelimit-reset-requests', b'35.410571428s'), (b'x-ratelimit-reset-tokens', b'26.352142857s'), (b'x-request-id', b'req_01jqcb9et1fytvwa71p1rc25nr'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9270b264ad77e7a4-SCL'), (b'Content-Encoding', b'gzip')])
2025-03-27 14:45:22,156 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-27 14:45:22,156 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:45:22,157 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:45:22,158 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:45:22,158 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:45:22,158 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Thu, 27 Mar 2025 17:44:38 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '7000', 'x-ratelimit-limit-tokens': '7000', 'x-ratelimit-remaining-requests': '6997', 'x-ratelimit-remaining-tokens': '3925', 'x-ratelimit-reset-requests': '35.410571428s', 'x-ratelimit-reset-tokens': '26.352142857s', 'x-request-id': 'req_01jqcb9et1fytvwa71p1rc25nr', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '9270b264ad77e7a4-SCL', 'content-encoding': 'gzip'})
2025-03-27 14:45:22,167 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-27 14:45:19.944724\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about Agents AI'}, {'role': 'assistant', 'tool_calls': [{'id': 'call_vmar', 'function': {'arguments': '{"query": " latest advancements in artificial intelligence"}', 'name': 'duckduckgo_search'}, 'type': 'function'}, {'id': 'call_vm8y', 'function': {'arguments': '{"state": "AI Agents are being developed to perform complex tasks without human intervention."}', 'name': 'set_team_context'}, 'type': 'function'}]}, {'role': 'user', 'content': 'Could not find function to call.'}, {'role': 'tool', 'content': 'Current team context: <team context>\nAI Agents are being developed to perform complex tasks without human intervention.\n</team context>\n', 'tool_call_id': 'call_vm8y'}, {'role': 'assistant', 'tool_calls': [{'id': 'call_7ddb', 'function': {'arguments': '{"query": "latest advancements in artificial intelligence news"}', 'name': 'duckduckgo_news'}, 'type': 'function'}, {'id': 'call_61a9', 'function': {'arguments': '{"state": "Notable breakthroughs in AI include deep learning and natural language processing."}', 'name': 'set_team_context'}, 'type': 'function'}]}, {'role': 'user', 'content': 'Could not find function to call.'}, {'role': 'tool', 'content': 'Current team context: <team context>\nNotable breakthroughs in AI include deep learning and natural language processing.\n</team context>\n', 'tool_call_id': 'call_61a9'}], 'model': 'llama-3.2-3b-preview', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-27 14:45:22,170 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-27 14:45:22,170 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:45:22,171 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:45:22,172 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:45:22,172 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:45:22,173 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:45:22,782 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 27 Mar 2025 17:44:38 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'7000'), (b'x-ratelimit-limit-tokens', b'7000'), (b'x-ratelimit-remaining-requests', b'6996'), (b'x-ratelimit-remaining-tokens', b'2835'), (b'x-ratelimit-reset-requests', b'48.766428571s'), (b'x-ratelimit-reset-tokens', b'35.696999999s'), (b'x-request-id', b'req_01jqcb9fd2fyv9ksx1tgpr70xz'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9270b2688b23e7a4-SCL'), (b'Content-Encoding', b'gzip')])
2025-03-27 14:45:22,783 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-27 14:45:22,783 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:45:22,784 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:45:22,784 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:45:22,784 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:45:22,785 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Thu, 27 Mar 2025 17:44:38 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '7000', 'x-ratelimit-limit-tokens': '7000', 'x-ratelimit-remaining-requests': '6996', 'x-ratelimit-remaining-tokens': '2835', 'x-ratelimit-reset-requests': '48.766428571s', 'x-ratelimit-reset-tokens': '35.696999999s', 'x-request-id': 'req_01jqcb9fd2fyv9ksx1tgpr70xz', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '9270b2688b23e7a4-SCL', 'content-encoding': 'gzip'})
2025-03-27 14:45:22,796 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-27 14:45:19.944724\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about Agents AI'}, {'role': 'assistant', 'tool_calls': [{'id': 'call_vmar', 'function': {'arguments': '{"query": " latest advancements in artificial intelligence"}', 'name': 'duckduckgo_search'}, 'type': 'function'}, {'id': 'call_vm8y', 'function': {'arguments': '{"state": "AI Agents are being developed to perform complex tasks without human intervention."}', 'name': 'set_team_context'}, 'type': 'function'}]}, {'role': 'user', 'content': 'Could not find function to call.'}, {'role': 'tool', 'content': 'Current team context: <team context>\nAI Agents are being developed to perform complex tasks without human intervention.\n</team context>\n', 'tool_call_id': 'call_vm8y'}, {'role': 'assistant', 'tool_calls': [{'id': 'call_7ddb', 'function': {'arguments': '{"query": "latest advancements in artificial intelligence news"}', 'name': 'duckduckgo_news'}, 'type': 'function'}, {'id': 'call_61a9', 'function': {'arguments': '{"state": "Notable breakthroughs in AI include deep learning and natural language processing."}', 'name': 'set_team_context'}, 'type': 'function'}]}, {'role': 'user', 'content': 'Could not find function to call.'}, {'role': 'tool', 'content': 'Current team context: <team context>\nNotable breakthroughs in AI include deep learning and natural language processing.\n</team context>\n', 'tool_call_id': 'call_61a9'}, {'role': 'assistant', 'tool_calls': [{'id': 'call_2sve', 'function': {'arguments': '{"url": "https://www.nature.com/articles/d41586-019-13132-0"}', 'name': 'read_article'}, 'type': 'function'}, {'id': 'call_5h1x', 'function': {'arguments': '{"state": "Key papers in AI research often feature innovative applications of machine learning."}', 'name': 'set_team_context'}, 'type': 'function'}]}, {'role': 'user', 'content': 'Could not find function to call.'}, {'role': 'tool', 'content': 'Current team context: <team context>\nKey papers in AI research often feature innovative applications of machine learning.\n</team context>\n', 'tool_call_id': 'call_5h1x'}], 'model': 'llama-3.2-3b-preview', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-27 14:45:22,797 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-27 14:45:22,798 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:45:22,798 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:45:22,799 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:45:22,799 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:45:22,800 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:45:23,947 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 27 Mar 2025 17:44:40 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'7000'), (b'x-ratelimit-limit-tokens', b'7000'), (b'x-ratelimit-remaining-requests', b'6995'), (b'x-ratelimit-remaining-tokens', b'1645'), (b'x-ratelimit-reset-requests', b'1m1.082285714s'), (b'x-ratelimit-reset-tokens', b'45.891999999s'), (b'x-request-id', b'req_01jqcb9g0ef2qtw883z666h32e'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9270b26c6fc3e7a4-SCL'), (b'Content-Encoding', b'gzip')])
2025-03-27 14:45:23,948 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-27 14:45:23,949 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:45:23,949 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:45:23,950 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:45:23,950 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:45:23,950 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Thu, 27 Mar 2025 17:44:40 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '7000', 'x-ratelimit-limit-tokens': '7000', 'x-ratelimit-remaining-requests': '6995', 'x-ratelimit-remaining-tokens': '1645', 'x-ratelimit-reset-requests': '1m1.082285714s', 'x-ratelimit-reset-tokens': '45.891999999s', 'x-request-id': 'req_01jqcb9g0ef2qtw883z666h32e', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '9270b26c6fc3e7a4-SCL', 'content-encoding': 'gzip'})
2025-03-27 14:45:25,977 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-27 14:45:25.973196\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about Agents AI'}], 'model': 'llama-3.2-3b-preview', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-27 14:45:25,978 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-27 14:45:25,978 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:45:25,979 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:45:25,979 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:45:25,980 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:45:25,980 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:45:26,319 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 429, b'Too Many Requests', [(b'Date', b'Thu, 27 Mar 2025 17:44:42 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'384'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'retry-after', b'4'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'7000'), (b'x-ratelimit-limit-tokens', b'7000'), (b'x-ratelimit-remaining-requests', b'6995'), (b'x-ratelimit-remaining-tokens', b'196'), (b'x-ratelimit-reset-requests', b'58.544285714s'), (b'x-ratelimit-reset-tokens', b'58.315714285s'), (b'x-request-id', b'req_01jqcb9k3se3sscfsgr1qdkt3p'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9270b2804c9fe7a4-SCL')])
2025-03-27 14:45:26,320 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-03-27 14:45:26,321 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:45:26,321 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:45:26,321 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:45:26,322 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:45:26,322 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "429 Too Many Requests" Headers({'date': 'Thu, 27 Mar 2025 17:44:42 GMT', 'content-type': 'application/json', 'content-length': '384', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'retry-after': '4', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '7000', 'x-ratelimit-limit-tokens': '7000', 'x-ratelimit-remaining-requests': '6995', 'x-ratelimit-remaining-tokens': '196', 'x-ratelimit-reset-requests': '58.544285714s', 'x-ratelimit-reset-tokens': '58.315714285s', 'x-request-id': 'req_01jqcb9k3se3sscfsgr1qdkt3p', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '9270b2804c9fe7a4-SCL'})
2025-03-27 14:45:26,323 - groq._base_client - DEBUG - Encountered httpx.HTTPStatusError
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\groq\_base_client.py", line 999, in _request
    response.raise_for_status()
  File "C:\Users\<USER>\miniconda3\envs\agents\Lib\site-packages\httpx\_models.py", line 829, in raise_for_status
    raise HTTPStatusError(message, request=request, response=self)
httpx.HTTPStatusError: Client error '429 Too Many Requests' for url 'https://api.groq.com/openai/v1/chat/completions'
For more information check: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/429
2025-03-27 14:45:26,325 - groq._base_client - DEBUG - Retrying due to status code 429
2025-03-27 14:45:26,326 - groq._base_client - DEBUG - 2 retries left
2025-03-27 14:45:26,326 - groq._base_client - INFO - Retrying request to /openai/v1/chat/completions in 4.000000 seconds
2025-03-27 14:45:30,327 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-27 14:45:25.973196\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about Agents AI'}], 'model': 'llama-3.2-3b-preview', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-27 14:45:30,329 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-27 14:45:30,329 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:45:30,330 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:45:30,330 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:45:30,330 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:45:30,331 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:45:31,278 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 27 Mar 2025 17:44:47 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'7000'), (b'x-ratelimit-limit-tokens', b'7000'), (b'x-ratelimit-remaining-requests', b'6994'), (b'x-ratelimit-remaining-tokens', b'121'), (b'x-ratelimit-reset-requests', b'1m6.535142857s'), (b'x-ratelimit-reset-tokens', b'58.960857142s'), (b'x-request-id', b'req_01jqcb9qbsfh2am2ndgxq0bsqv'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9270b29b7c1ce7a4-SCL'), (b'Content-Encoding', b'gzip')])
2025-03-27 14:45:31,279 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-27 14:45:31,279 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:45:31,280 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:45:31,280 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:45:31,280 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:45:31,281 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Thu, 27 Mar 2025 17:44:47 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '7000', 'x-ratelimit-limit-tokens': '7000', 'x-ratelimit-remaining-requests': '6994', 'x-ratelimit-remaining-tokens': '121', 'x-ratelimit-reset-requests': '1m6.535142857s', 'x-ratelimit-reset-tokens': '58.960857142s', 'x-request-id': 'req_01jqcb9qbsfh2am2ndgxq0bsqv', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'server': 'cloudflare', 'cf-ray': '9270b29b7c1ce7a4-SCL', 'content-encoding': 'gzip'})
2025-03-27 14:45:31,508 - httpcore.connection - DEBUG - connect_tcp.started host='api.agno.com' port=443 local_address=None timeout=60 socket_options=None
2025-03-27 14:45:31,657 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D994FF8F0>
2025-03-27 14:45:31,657 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000024D996EACD0> server_hostname='api.agno.com' timeout=60
2025-03-27 14:45:31,972 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D9970D6A0>
2025-03-27 14:45:31,972 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:45:31,973 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:45:31,973 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:45:31,973 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:45:31,974 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:45:32,127 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 201, b'Created', [(b'Date', b'Thu, 27 Mar 2025 17:44:48 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'3'), (b'Connection', b'keep-alive'), (b'server', b'uvicorn')])
2025-03-27 14:45:32,128 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/telemetry/team-runs "HTTP/1.1 201 Created"
2025-03-27 14:45:32,128 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:45:32,128 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:45:32,128 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:45:32,128 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:45:32,130 - httpcore.connection - DEBUG - close.started
2025-03-27 14:45:32,130 - httpcore.connection - DEBUG - close.complete
2025-03-27 14:45:32,131 - utils - DEBUG - Raw Metrics: {'input_tokens': [962], 'output_tokens': [623], 'total_tokens': [1585], 'prompt_tokens': [0], 'completion_tokens': [0], 'additional_metrics': [{'completion_time': 0.410834718, 'prompt_time': 0.171034936, 'queue_time': 0.019797450999999994, 'total_time': 0.581869654}], 'time': [5.307210699975258], 'total_time': 15.832987308502197}
2025-03-27 14:45:32,399 - httpcore.connection - DEBUG - close.started
2025-03-27 14:45:32,401 - httpcore.connection - DEBUG - close.complete
2025-03-27 14:45:50,193 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-27 14:45:49.839568\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about Agents AI'}], 'model': 'deepseek-r1-distill-qwen-32b', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-27 14:45:50,194 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-27 14:45:50,195 - httpcore.connection - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-03-27 14:45:50,372 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D9952C350>
2025-03-27 14:45:50,373 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000024D98D38CD0> server_hostname='api.groq.com' timeout=5.0
2025-03-27 14:45:50,550 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D9952CC50>
2025-03-27 14:45:50,551 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:45:50,552 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:45:50,553 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:45:50,554 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:45:50,554 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:45:55,525 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 27 Mar 2025 17:45:11 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'1000'), (b'x-ratelimit-limit-tokens', b'6000'), (b'x-ratelimit-remaining-requests', b'999'), (b'x-ratelimit-remaining-tokens', b'5417'), (b'x-ratelimit-reset-requests', b'1m26.4s'), (b'x-ratelimit-reset-tokens', b'5.83s'), (b'x-request-id', b'req_01jqcbab4df2yvqrqjq67nw7y4'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=Iz1vITjvwTxF2GYYGqY07067BuzhZQsFCiN2k_Gx5Dw-1743097511-*******-YzBrCOSVhVf0pYY2J1CZLasCMo9aPECgKd.AjuzOsMgncMN85.oyjFVYv3LJwzZoMS8LJt9KhnVRpfVKrl_gA4x20Xwkr8C7wRenwG5wyMk; path=/; expires=Thu, 27-Mar-25 18:15:11 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9270b319dfe3e9b2-SCL'), (b'Content-Encoding', b'gzip')])
2025-03-27 14:45:55,526 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-27 14:45:55,527 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:45:55,527 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:45:55,528 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:45:55,528 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:45:55,529 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Thu, 27 Mar 2025 17:45:11 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '1000', 'x-ratelimit-limit-tokens': '6000', 'x-ratelimit-remaining-requests': '999', 'x-ratelimit-remaining-tokens': '5417', 'x-ratelimit-reset-requests': '1m26.4s', 'x-ratelimit-reset-tokens': '5.83s', 'x-request-id': 'req_01jqcbab4df2yvqrqjq67nw7y4', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=Iz1vITjvwTxF2GYYGqY07067BuzhZQsFCiN2k_Gx5Dw-1743097511-*******-YzBrCOSVhVf0pYY2J1CZLasCMo9aPECgKd.AjuzOsMgncMN85.oyjFVYv3LJwzZoMS8LJt9KhnVRpfVKrl_gA4x20Xwkr8C7wRenwG5wyMk; path=/; expires=Thu, 27-Mar-25 18:15:11 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9270b319dfe3e9b2-SCL', 'content-encoding': 'gzip'})
2025-03-27 14:45:55,776 - httpcore.connection - DEBUG - connect_tcp.started host='api.agno.com' port=443 local_address=None timeout=60 socket_options=None
2025-03-27 14:45:55,950 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D996E66F0>
2025-03-27 14:45:55,950 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000024D995EA350> server_hostname='api.agno.com' timeout=60
2025-03-27 14:45:56,257 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D9890C5C0>
2025-03-27 14:45:56,257 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:45:56,258 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:45:56,258 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:45:56,259 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:45:56,259 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:45:56,412 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 201, b'Created', [(b'Date', b'Thu, 27 Mar 2025 17:45:12 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'3'), (b'Connection', b'keep-alive'), (b'server', b'uvicorn')])
2025-03-27 14:45:56,413 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/telemetry/team-runs "HTTP/1.1 201 Created"
2025-03-27 14:45:56,413 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:45:56,414 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:45:56,414 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:45:56,415 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:45:56,415 - httpcore.connection - DEBUG - close.started
2025-03-27 14:45:56,415 - httpcore.connection - DEBUG - close.complete
2025-03-27 14:45:56,416 - utils - DEBUG - Raw Metrics: {'input_tokens': [824], 'output_tokens': [591], 'total_tokens': [1415], 'prompt_tokens': [0], 'completion_tokens': [0], 'additional_metrics': [{'completion_time': 4.221428571, 'prompt_time': 0.088533264, 'queue_time': 0.307417848, 'total_time': 4.309961835}], 'time': [5.689822200016351], 'total_time': 6.581873416900635}
2025-03-27 14:45:56,628 - httpcore.connection - DEBUG - close.started
2025-03-27 14:45:56,630 - httpcore.connection - DEBUG - close.complete
2025-03-27 14:46:05,442 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-27 14:46:05.086381\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about Agents AI'}], 'model': 'deepseek-r1-distill-qwen-32b', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-27 14:46:05,444 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-27 14:46:05,444 - httpcore.connection - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-03-27 14:46:05,620 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D99650530>
2025-03-27 14:46:05,620 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000024D98D3ABD0> server_hostname='api.groq.com' timeout=5.0
2025-03-27 14:46:05,803 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D9890FC50>
2025-03-27 14:46:05,803 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:46:05,803 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:46:05,804 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:46:05,804 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:46:05,805 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:46:13,893 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 27 Mar 2025 17:45:30 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'1000'), (b'x-ratelimit-limit-tokens', b'6000'), (b'x-ratelimit-remaining-requests', b'998'), (b'x-ratelimit-remaining-tokens', b'5065'), (b'x-ratelimit-reset-requests', b'2m37.557999999s'), (b'x-ratelimit-reset-tokens', b'9.348999999s'), (b'x-request-id', b'req_01jqcbat0ve4wb7bv3ajzb22em'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=tzVL71aE5sijOcB..jR7ghjHcVuZbW0919WPv6037wY-1743097530-*******-WYG4FJFKmHr4V1r6VqXuTBpg.dhuR6Cc.m7Ikxx2XC6BJJ2arHKiFwiy9ueGDxLUWW2LNiIZzzPLp0jSlXlByyqTFn_5dbJx8gHnTUUe3xY; path=/; expires=Thu, 27-Mar-25 18:15:30 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9270b3793b37b52c-SCL'), (b'Content-Encoding', b'gzip')])
2025-03-27 14:46:13,894 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-27 14:46:13,895 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:46:13,896 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:46:13,896 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:46:13,897 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:46:13,897 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Thu, 27 Mar 2025 17:45:30 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '1000', 'x-ratelimit-limit-tokens': '6000', 'x-ratelimit-remaining-requests': '998', 'x-ratelimit-remaining-tokens': '5065', 'x-ratelimit-reset-requests': '2m37.557999999s', 'x-ratelimit-reset-tokens': '9.348999999s', 'x-request-id': 'req_01jqcbat0ve4wb7bv3ajzb22em', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=tzVL71aE5sijOcB..jR7ghjHcVuZbW0919WPv6037wY-1743097530-*******-WYG4FJFKmHr4V1r6VqXuTBpg.dhuR6Cc.m7Ikxx2XC6BJJ2arHKiFwiy9ueGDxLUWW2LNiIZzzPLp0jSlXlByyqTFn_5dbJx8gHnTUUe3xY; path=/; expires=Thu, 27-Mar-25 18:15:30 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9270b3793b37b52c-SCL', 'content-encoding': 'gzip'})
2025-03-27 14:46:14,122 - httpcore.connection - DEBUG - connect_tcp.started host='api.agno.com' port=443 local_address=None timeout=60 socket_options=None
2025-03-27 14:46:14,269 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D996E60F0>
2025-03-27 14:46:14,269 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000024D995EBBD0> server_hostname='api.agno.com' timeout=60
2025-03-27 14:46:14,581 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D996E6E70>
2025-03-27 14:46:14,581 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:46:14,581 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:46:14,582 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:46:14,582 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:46:14,583 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:46:14,737 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 201, b'Created', [(b'Date', b'Thu, 27 Mar 2025 17:45:30 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'3'), (b'Connection', b'keep-alive'), (b'server', b'uvicorn')])
2025-03-27 14:46:14,737 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/telemetry/team-runs "HTTP/1.1 201 Created"
2025-03-27 14:46:14,738 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:46:14,738 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:46:14,738 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:46:14,739 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:46:14,739 - httpcore.connection - DEBUG - close.started
2025-03-27 14:46:14,740 - httpcore.connection - DEBUG - close.complete
2025-03-27 14:46:14,740 - utils - DEBUG - Raw Metrics: {'input_tokens': [824], 'output_tokens': [398], 'total_tokens': [1222], 'prompt_tokens': [0], 'completion_tokens': [0], 'additional_metrics': [{'completion_time': 2.842857143, 'prompt_time': 0.06899836, 'queue_time': 4.837669478, 'total_time': 2.911855503}], 'time': [8.810807300003944], 'total_time': 9.66256046295166}
2025-03-27 14:46:15,026 - httpcore.connection - DEBUG - close.started
2025-03-27 14:46:15,026 - httpcore.connection - DEBUG - close.complete
2025-03-27 14:46:32,678 - groq._base_client - DEBUG - Request options: {'method': 'post', 'url': '/openai/v1/chat/completions', 'files': None, 'json_data': {'messages': [{'role': 'system', 'content': "You are the leader of a team of AI Agents and possible Sub-Teams:\n - Agent 1:\n   - Name: Searcher\n   - Role: Searches the top URLs for a topic\n   - Available tools:\n    - duckduckgo_search: Use this function to search DuckDuckGo for a query.\n    - duckduckgo_news: Use this function to get the latest news from DuckDuckGo.\n - Agent 2:\n   - Name: Writer\n   - Role: Writes a high-quality article\n   - Available tools:\n    - read_article: Use this function to read an article from a URL.\n\n- You can either respond directly or transfer tasks to other Agents in your team depending on the tools available to them and their roles.\n- If you transfer a task to another Agent, make sure to include:\n  - agent_name (str): The name of the Agent to transfer the task to.\n  - task_description (str): A clear description of the task.\n  - expected_output (str): The expected output.\n- You can pass tasks to multiple members at once.\n- You must always validate the output of the other Agents before responding to the user.\n- Evaluate the response from other agents. If you feel the task has been completed, you can stop and respond to the user.\n- You can re-assign the task if you are not satisfied with the result.\n\nYou can and should update the context of the team. Use the `set_team_context` tool to update the shared team context.\nYour name is: Editor.\n\n<description>\nYou are a senior NYT editor. Given a topic, your goal is to produce a NYT-worthy article by coordinating between researchers and writers.\n</description>\n\n<instructions>\n- First, have the Searcher find the most relevant URLs for the topic.\n- Review the URLs to ensure they meet NYT's quality standards.\n- Then, have the Writer produce a draft article using those sources.\n- Edit the article to ensure it meets NYT standards:\n- - Engaging headline and lede\n- - Logical flow and structure\n- - Proper attribution of all facts\n- - Balanced perspective\n- - Formal but accessible tone\n- The final article should be ready for publication.\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-03-27 14:46:32.319953\n</additional_information>\n\n<expected_output>\nArticleResult:\n  Content: ...\n  Search Terms: \n  Sources: 0 sources\n  Metrics: {}\n</expected_output>"}, {'role': 'user', 'content': 'Write an article about Agents AI'}], 'model': 'deepseek-r1-distill-qwen-32b', 'tools': [{'type': 'function', 'function': {'name': 'transfer_task_to_member', 'description': 'Use this function to transfer a task to the nominated agent.\nYou must provide a clear and concise description of the task the agent should achieve AND the expected output.', 'parameters': {'type': 'object', 'properties': {'agent_name': {'type': 'string', 'description': '(str) The name of the agent to transfer the task to.'}, 'task_description': {'type': 'string', 'description': '(str) A clear and concise description of the task the agent should achieve.'}, 'expected_output': {'type': 'string', 'description': '(str) The expected output from the agent.'}}, 'additionalProperties': False, 'required': ['agent_name', 'task_description', 'expected_output']}}}, {'type': 'function', 'function': {'name': 'set_team_context', 'description': "Set the team's shared context with the given state.", 'parameters': {'type': 'object', 'properties': {'state': {'anyOf': [{'type': 'string'}, {'type': 'object', 'properties': {}, 'additionalProperties': False}], 'description': '(str) The state to set as the team context.'}}, 'required': ['state']}}}]}}
2025-03-27 14:46:32,680 - groq._base_client - DEBUG - Sending HTTP Request: POST https://api.groq.com/openai/v1/chat/completions
2025-03-27 14:46:32,680 - httpcore.connection - DEBUG - connect_tcp.started host='api.groq.com' port=443 local_address=None timeout=5.0 socket_options=None
2025-03-27 14:46:32,849 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D9952D130>
2025-03-27 14:46:32,850 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000024D9972BDD0> server_hostname='api.groq.com' timeout=5.0
2025-03-27 14:46:33,030 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D9890FC50>
2025-03-27 14:46:33,031 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:46:33,031 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:46:33,032 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:46:33,032 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:46:33,033 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:46:42,441 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Thu, 27 Mar 2025 17:45:58 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'cache-control', b'private, max-age=0, no-store, no-cache, must-revalidate'), (b'vary', b'Origin'), (b'x-groq-region', b'us-west-1'), (b'x-ratelimit-limit-requests', b'1000'), (b'x-ratelimit-limit-tokens', b'6000'), (b'x-ratelimit-remaining-requests', b'997'), (b'x-ratelimit-remaining-tokens', b'5417'), (b'x-ratelimit-reset-requests', b'3m51.594999999s'), (b'x-ratelimit-reset-tokens', b'5.83s'), (b'x-request-id', b'req_01jqcbbmzhe5hbmnbqc0zckzv4'), (b'via', b'1.1 google'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=6JTA9EIOQdmuB59ODs58oFBt3M02Xzz6MDz_FcywQCo-1743097558-*******-PZ9J0yxPB7XvHN1l6Ndcr4yFh_MOLjF02UEvWtciuXCwRb3nFKbni1ZR_GCPUgOe6jpPZyNqVQDa_LRVB83KAxDixJONq6ckmn.CRf80SsA; path=/; expires=Thu, 27-Mar-25 18:15:58 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'9270b4235f29b52b-SCL'), (b'Content-Encoding', b'gzip')])
2025-03-27 14:46:42,442 - httpx - INFO - HTTP Request: POST https://api.groq.com/openai/v1/chat/completions "HTTP/1.1 200 OK"
2025-03-27 14:46:42,442 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:46:42,443 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:46:42,443 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:46:42,443 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:46:42,444 - groq._base_client - DEBUG - HTTP Response: POST https://api.groq.com/openai/v1/chat/completions "200 OK" Headers({'date': 'Thu, 27 Mar 2025 17:45:58 GMT', 'content-type': 'application/json', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'cache-control': 'private, max-age=0, no-store, no-cache, must-revalidate', 'vary': 'Origin', 'x-groq-region': 'us-west-1', 'x-ratelimit-limit-requests': '1000', 'x-ratelimit-limit-tokens': '6000', 'x-ratelimit-remaining-requests': '997', 'x-ratelimit-remaining-tokens': '5417', 'x-ratelimit-reset-requests': '3m51.594999999s', 'x-ratelimit-reset-tokens': '5.83s', 'x-request-id': 'req_01jqcbbmzhe5hbmnbqc0zckzv4', 'via': '1.1 google', 'alt-svc': 'h3=":443"; ma=86400', 'cf-cache-status': 'DYNAMIC', 'set-cookie': '__cf_bm=6JTA9EIOQdmuB59ODs58oFBt3M02Xzz6MDz_FcywQCo-1743097558-*******-PZ9J0yxPB7XvHN1l6Ndcr4yFh_MOLjF02UEvWtciuXCwRb3nFKbni1ZR_GCPUgOe6jpPZyNqVQDa_LRVB83KAxDixJONq6ckmn.CRf80SsA; path=/; expires=Thu, 27-Mar-25 18:15:58 GMT; domain=.groq.com; HttpOnly; Secure; SameSite=None', 'server': 'cloudflare', 'cf-ray': '9270b4235f29b52b-SCL', 'content-encoding': 'gzip'})
2025-03-27 14:46:42,669 - httpcore.connection - DEBUG - connect_tcp.started host='api.agno.com' port=443 local_address=None timeout=60 socket_options=None
2025-03-27 14:46:42,820 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D9952EDE0>
2025-03-27 14:46:42,821 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000024D995EB3D0> server_hostname='api.agno.com' timeout=60
2025-03-27 14:46:43,127 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.sync.SyncStream object at 0x0000024D9952FA40>
2025-03-27 14:46:43,127 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-03-27 14:46:43,128 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-03-27 14:46:43,128 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-03-27 14:46:43,129 - httpcore.http11 - DEBUG - send_request_body.complete
2025-03-27 14:46:43,129 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-03-27 14:46:43,281 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 201, b'Created', [(b'Date', b'Thu, 27 Mar 2025 17:45:59 GMT'), (b'Content-Type', b'application/json'), (b'Content-Length', b'3'), (b'Connection', b'keep-alive'), (b'server', b'uvicorn')])
2025-03-27 14:46:43,281 - httpx - INFO - HTTP Request: POST https://api.agno.com/v1/telemetry/team-runs "HTTP/1.1 201 Created"
2025-03-27 14:46:43,281 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-03-27 14:46:43,282 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-03-27 14:46:43,282 - httpcore.http11 - DEBUG - response_closed.started
2025-03-27 14:46:43,283 - httpcore.http11 - DEBUG - response_closed.complete
2025-03-27 14:46:43,283 - httpcore.connection - DEBUG - close.started
2025-03-27 14:46:43,283 - httpcore.connection - DEBUG - close.complete
2025-03-27 14:46:43,284 - utils - DEBUG - Raw Metrics: {'input_tokens': [824], 'output_tokens': [1173], 'total_tokens': [1997], 'prompt_tokens': [0], 'completion_tokens': [0], 'additional_metrics': [{'completion_time': 8.378571429, 'prompt_time': 0.074176051, 'queue_time': 0.240935483, 'total_time': 8.45274748}], 'time': [10.124141499982215], 'total_time': 10.972938537597656}
2025-03-27 14:46:43,486 - httpcore.connection - DEBUG - close.started
2025-03-27 14:46:43,487 - httpcore.connection - DEBUG - close.complete
