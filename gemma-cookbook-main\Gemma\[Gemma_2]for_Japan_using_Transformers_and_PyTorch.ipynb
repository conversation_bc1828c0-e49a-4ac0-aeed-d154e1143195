{"cells": [{"cell_type": "markdown", "metadata": {"id": "b-FX_tt-s8sU"}, "source": ["##### Copyright 2024 Google LLC."]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "JA485iBMs7ub"}, "outputs": [], "source": ["# @title Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "# https://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "metadata": {"id": "igbYjdwhvioG"}, "source": ["This notebook provides a practical guide to working with Gemma 2 for Japan, the latest variant of Google's open language models.\n", "\n", "The model itself is available on both Kaggle and Hugging Face.\n", "\n", "As the model is currently only available in a 2B size, no special hardware is required.\n", "\n", "<table align=\"left\">\n", "  <td>\n", "    <a target=\"_blank\" href=\"https://colab.research.google.com/github/google-gemini/gemma-cookbook/blob/main/Gemma/[Gemma_2]for_Japan_using_Transformers_and_PyTorch.ipynb\"><img src=\"https://www.tensorflow.org/images/colab_logo_32px.png\" />Run in Google Colab</a>\n", "  </td>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {"id": "y5ZzHdAb9sNW"}, "source": ["### Downloading prerequisites\n", "The **KaggleHub** library will be used to retrieve the model from <PERSON><PERSON>, and the **Transformers** framework from Hugging Face will be used with **PyTorch** for inference.\n", "\n", "Note: the `%%capture` magic keyword suppresses output of a given cell."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "YYPGnh709SJR"}, "outputs": [], "source": ["%%capture\n", "!pip install kagglehub --upgrade\n", "!pip install transformers --upgrade\n", "!pip install torch --upgrade"]}, {"cell_type": "markdown", "metadata": {"id": "mEjYLz9l9eeg"}, "source": ["### Authenticating with <PERSON><PERSON>\n", "\n", "Next, <PERSON><PERSON> will need to be authenticated with.\n", "\n", "Note: the call to `login()` can be skipped if running in a Kaggle notebook."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "8aucYnW5_f1S"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b2f1c08705234b9fad50a04c03229ade", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(HTML(value='<center> <img\\nsrc=https://www.kaggle.com/static/images/site-logo.png\\nalt=\\'Kaggle…"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Kaggle credentials set.\n", "Kaggle credentials successfully validated.\n"]}], "source": ["import kagglehub\n", "\n", "# skip if in a Kaggle notebook\n", "kagglehub.login()"]}, {"cell_type": "markdown", "metadata": {"id": "HQ1PGj_l-uAY"}, "source": ["### Downloading the model\n", "\n", "This next step ensures that you have the necessary model files readily available, ready for loading and inference.\n", "\n", "`kagglehub.model_download()` fetches the model from Ka<PERSON>."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "vws5tt-6_okx"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c778c53186fb4fb5bef9f103d29aeee2", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading 8 files:   0%|          | 0/8 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Downloading from https://www.kaggle.com/api/v1/models/google/gemma-2-2b-jpn-it/transformers/gemma-2-2b-jpn-it/1/download/model.safetensors.index.json...\n", "Downloading from https://www.kaggle.com/api/v1/models/google/gemma-2-2b-jpn-it/transformers/gemma-2-2b-jpn-it/1/download/tokenizer_config.json...\n", "Downloading from https://www.kaggle.com/api/v1/models/google/gemma-2-2b-jpn-it/transformers/gemma-2-2b-jpn-it/1/download/tokenizer.model...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "\n", "  0%|          | 0.00/1.06k [00:00<?, ?B/s]\u001b[A\u001b[A\n", "100%|██████████| 1.06k/1.06k [00:00<00:00, 166kB/s]\n", "\n", "\n", "100%|██████████| 23.7k/23.7k [00:00<00:00, 3.01MB/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloading from https://www.kaggle.com/api/v1/models/google/gemma-2-2b-jpn-it/transformers/gemma-2-2b-jpn-it/1/download/model-00001-of-00002.safetensors...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "\n", "  0%|          | 0.00/4.65G [00:00<?, ?B/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloading from https://www.kaggle.com/api/v1/models/google/gemma-2-2b-jpn-it/transformers/gemma-2-2b-jpn-it/1/download/model-00002-of-00002.safetensors...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[A\n", "\n", "\n", "  0%|          | 0.00/230M [00:00<?, ?B/s]\u001b[A\u001b[A\u001b[A"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloading from https://www.kaggle.com/api/v1/models/google/gemma-2-2b-jpn-it/transformers/gemma-2-2b-jpn-it/1/download/special_tokens_map.json...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "\n", "\n", "\n", "100%|██████████| 555/555 [00:00<00:00, 39.6kB/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloading from https://www.kaggle.com/api/v1/models/google/gemma-2-2b-jpn-it/transformers/gemma-2-2b-jpn-it/1/download/generation_config.json...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloading from https://www.kaggle.com/api/v1/models/google/gemma-2-2b-jpn-it/transformers/gemma-2-2b-jpn-it/1/download/config.json...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "\n", "\n", "\n", "100%|██████████| 168/168 [00:00<00:00, 103kB/s]\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 805/805 [00:00<00:00, 496kB/s]\n", "\n", "  0%|          | 1.00M/4.65G [00:01<1:46:34, 780kB/s]\u001b[A\n", "\n", " 25%|██▍       | 1.00M/4.04M [00:01<00:04, 771kB/s]\u001b[A\u001b[A\n", "\n", "\n", "  0%|          | 1.00M/230M [00:01<05:13, 765kB/s]\u001b[A\u001b[A\u001b[A\n", "\n", "\n", "  1%|          | 2.00M/230M [00:01<02:56, 1.35MB/s]\u001b[A\u001b[A\u001b[A\n", "  0%|          | 2.00M/4.65G [00:01<1:02:06, 1.34MB/s]\u001b[A\n", "\n", "100%|██████████| 4.04M/4.04M [00:01<00:00, 2.31MB/s]\n", "\n", "\n", "\n", "  2%|▏         | 4.00M/230M [00:02<01:12, 3.24MB/s]\u001b[A\u001b[A\u001b[A\n", "  0%|          | 5.00M/4.65G [00:02<20:28, 4.06MB/s]  \u001b[A\n", "\n", "\n", "  2%|▏         | 5.00M/230M [00:02<01:02, 3.77MB/s]\u001b[A\u001b[A\u001b[A\n", "  0%|          | 7.00M/4.65G [00:02<14:29, 5.73MB/s]\u001b[A\n", "\n", "\n", "  4%|▍         | 9.00M/230M [00:02<00:26, 8.59MB/s]\u001b[A\u001b[A\u001b[A\n", "  0%|          | 10.0M/4.65G [00:02<09:29, 8.74MB/s]\u001b[A\n", "  0%|          | 12.0M/4.65G [00:02<08:03, 10.3MB/s]\u001b[A\n", "  0%|          | 14.0M/4.65G [00:02<06:59, 11.9MB/s]\u001b[A\n", "\n", "\n", "  5%|▍         | 11.0M/230M [00:02<00:28, 8.03MB/s]\u001b[A\u001b[A\u001b[A\n", "  0%|          | 16.0M/4.65G [00:02<06:25, 12.9MB/s]\u001b[A\n", "  0%|          | 18.0M/4.65G [00:02<05:45, 14.4MB/s]\u001b[A\n", "\n", "\n", "  6%|▌         | 13.0M/230M [00:02<00:26, 8.60MB/s]\u001b[A\u001b[A\u001b[A\n", "  0%|          | 20.0M/4.65G [00:02<05:37, 14.7MB/s]\u001b[A\n", "\n", "\n", "  7%|▋         | 15.0M/230M [00:02<00:26, 8.45MB/s]\u001b[A\u001b[A\u001b[A\n", "  0%|          | 22.0M/4.65G [00:02<06:12, 13.3MB/s]\u001b[A\n", "\n", "\n", "  7%|▋         | 17.0M/230M [00:03<00:21, 10.3MB/s]\u001b[A\u001b[A\u001b[A\n", "  1%|          | 26.0M/4.65G [00:03<04:48, 17.2MB/s]\u001b[A\n", "\n", "\n", "  8%|▊         | 19.0M/230M [00:03<00:18, 11.7MB/s]\u001b[A\u001b[A\u001b[A\n", "  1%|          | 28.0M/4.65G [00:03<10:04, 8.20MB/s]\u001b[A\n", "\n", "\n", "  9%|▉         | 21.0M/230M [00:03<00:33, 6.51MB/s]\u001b[A\u001b[A\u001b[A\n", "  1%|          | 37.0M/4.65G [00:04<05:03, 16.3MB/s]\u001b[A\n", "\n", "\n", " 13%|█▎        | 30.0M/230M [00:04<00:14, 14.7MB/s]\u001b[A\u001b[A\u001b[A\n", "  1%|          | 41.0M/4.65G [00:04<04:53, 16.9MB/s]\u001b[A\n", "\n", "\n", " 15%|█▍        | 34.0M/230M [00:04<00:13, 15.5MB/s]\u001b[A\u001b[A\u001b[A\n", "\n", "\n", " 16%|█▌        | 37.0M/230M [00:04<00:13, 15.4MB/s]\u001b[A\u001b[A\u001b[A\n", "  1%|          | 45.0M/4.65G [00:04<04:56, 16.7MB/s]\u001b[A\n", "  1%|          | 47.0M/4.65G [00:04<05:32, 14.8MB/s]\u001b[A\n", "  1%|          | 50.0M/4.65G [00:05<05:30, 14.9MB/s]\u001b[A\n", "\n", "\n", " 18%|█▊        | 41.0M/230M [00:05<00:16, 12.3MB/s]\u001b[A\u001b[A\u001b[A\n", "\n", "\n", " 19%|█▊        | 43.0M/230M [00:05<00:26, 7.44MB/s]\u001b[A\u001b[A\u001b[A\n", "  1%|          | 52.0M/4.65G [00:05<10:50, 7.59MB/s]\u001b[A\n", "  1%|▏         | 61.0M/4.65G [00:06<05:45, 14.2MB/s]\u001b[A\n", "\n", "\n", " 23%|██▎       | 52.0M/230M [00:06<00:13, 13.4MB/s]\u001b[A\u001b[A\u001b[A\n", "\n", "\n", " 24%|██▎       | 54.0M/230M [00:06<00:13, 13.5MB/s]\u001b[A\u001b[A\u001b[A\n", "  1%|▏         | 64.0M/4.65G [00:06<05:27, 15.0MB/s]\u001b[A\n", "\n", "\n", " 25%|██▍       | 57.0M/230M [00:06<00:11, 15.5MB/s]\u001b[A\u001b[A\u001b[A\n", "  1%|▏         | 67.0M/4.65G [00:06<04:50, 16.9MB/s]\u001b[A\n", "\n", "\n", " 26%|██▌       | 60.0M/230M [00:06<00:11, 15.3MB/s]\u001b[A\u001b[A\u001b[A\n", "  1%|▏         | 70.0M/4.65G [00:06<04:59, 16.4MB/s]\u001b[A\n", "  2%|▏         | 72.0M/4.65G [00:06<05:25, 15.1MB/s]\u001b[A\n", "\n", "\n", " 27%|██▋       | 63.0M/230M [00:06<00:11, 15.2MB/s]\u001b[A\u001b[A\u001b[A\n", "  2%|▏         | 76.0M/4.65G [00:07<05:03, 16.2MB/s]\u001b[A\n", "\n", "\n", " 29%|██▉       | 67.0M/230M [00:07<00:10, 16.1MB/s]\u001b[A\u001b[A\u001b[A\n", "\n", "\n", " 30%|███       | 69.0M/230M [00:07<00:11, 14.7MB/s]\u001b[A\u001b[A\u001b[A\n", "  2%|▏         | 78.0M/4.65G [00:07<05:44, 14.2MB/s]\u001b[A\n", "  2%|▏         | 83.0M/4.65G [00:07<04:46, 17.1MB/s]\u001b[A\n", "\n", "\n", " 31%|███▏      | 72.0M/230M [00:07<00:11, 14.1MB/s]\u001b[A\u001b[A\u001b[A\n", "  2%|▏         | 87.0M/4.65G [00:07<04:38, 17.6MB/s]\u001b[A\n", "\n", "\n", " 33%|███▎      | 76.0M/230M [00:07<00:10, 15.3MB/s]\u001b[A\u001b[A\u001b[A\n", "  2%|▏         | 90.0M/4.65G [00:07<04:49, 16.9MB/s]\u001b[A\n", "\n", "\n", " 35%|███▍      | 80.0M/230M [00:07<00:09, 16.2MB/s]\u001b[A\u001b[A\u001b[A\n", "  2%|▏         | 94.0M/4.65G [00:08<04:40, 17.4MB/s]\u001b[A\n", "\n", "\n", " 36%|███▌      | 83.0M/230M [00:08<00:09, 15.8MB/s]\u001b[A\u001b[A\u001b[A\n", "\n", "\n", " 37%|███▋      | 85.0M/230M [00:08<00:19, 7.79MB/s]\u001b[A\u001b[A\u001b[A\n", "  2%|▏         | 96.0M/4.65G [00:08<10:04, 8.09MB/s]\u001b[A\n", "\n", "\n", " 41%|████      | 94.0M/230M [00:09<00:09, 14.5MB/s]\u001b[A\u001b[A\u001b[A\n", "  2%|▏         | 105M/4.65G [00:09<05:33, 14.6MB/s] \u001b[A\n", "\n", "\n", " 42%|████▏     | 97.0M/230M [00:09<00:14, 9.73MB/s]\u001b[A\u001b[A\u001b[A\n", "  2%|▏         | 108M/4.65G [00:09<08:15, 9.83MB/s]\u001b[A\n", "\n", "\n", " 46%|████▌     | 105M/230M [00:10<00:09, 14.5MB/s] \u001b[A\u001b[A\u001b[A\n", "  2%|▏         | 117M/4.65G [00:10<05:17, 15.3MB/s]\u001b[A\n", "\n", "\n", " 47%|████▋     | 108M/230M [00:10<00:08, 14.5MB/s]\u001b[A\u001b[A\u001b[A\n", "  3%|▎         | 120M/4.65G [00:10<05:26, 14.9MB/s]\u001b[A\n", "  3%|▎         | 122M/4.65G [00:10<05:41, 14.2MB/s]\u001b[A\n", "\n", "\n", " 48%|████▊     | 110M/230M [00:10<00:09, 13.4MB/s]\u001b[A\u001b[A\u001b[A\n", "  3%|▎         | 125M/4.65G [00:10<04:57, 16.3MB/s]\u001b[A\n", "\n", "\n", " 50%|████▉     | 114M/230M [00:10<00:08, 14.5MB/s]\u001b[A\u001b[A\u001b[A\n", "  3%|▎         | 128M/4.65G [00:10<05:02, 16.0MB/s]\u001b[A\n", "\n", "\n", " 51%|█████     | 116M/230M [00:10<00:08, 14.0MB/s]\u001b[A\u001b[A\u001b[A\n", "  3%|▎         | 130M/4.65G [00:10<05:08, 15.7MB/s]\u001b[A\n", "\n", "\n", " 51%|█████▏    | 118M/230M [00:11<00:08, 13.2MB/s]\u001b[A\u001b[A\u001b[A\n", "  3%|▎         | 133M/4.65G [00:11<05:11, 15.6MB/s]\u001b[A\n", "\n", "\n", " 52%|█████▏    | 120M/230M [00:11<00:17, 6.75MB/s]\u001b[A\u001b[A\u001b[A\n", "  3%|▎         | 135M/4.65G [00:11<10:42, 7.54MB/s]\u001b[A\n", "  3%|▎         | 145M/4.65G [00:12<05:14, 15.4MB/s]\u001b[A\n", "\n", "\n", " 56%|█████▌    | 129M/230M [00:12<00:07, 13.3MB/s]\u001b[A\u001b[A\u001b[A\n", "\n", "\n", " 57%|█████▋    | 131M/230M [00:12<00:11, 8.64MB/s]\u001b[A\u001b[A\u001b[A\n", "  3%|▎         | 148M/4.65G [00:12<07:54, 10.2MB/s]\u001b[A\n", "\n", "\n", " 61%|██████    | 140M/230M [00:13<00:06, 14.5MB/s]\u001b[A\u001b[A\u001b[A\n", "  3%|▎         | 157M/4.65G [00:13<05:03, 15.9MB/s]\u001b[A\n", "\n", "\n", " 62%|██████▏   | 143M/230M [00:13<00:05, 15.2MB/s]\u001b[A\u001b[A\u001b[A\n", "  3%|▎         | 160M/4.65G [00:13<05:05, 15.8MB/s]\u001b[A\n", "\n", "\n", " 64%|██████▎   | 146M/230M [00:13<00:05, 16.1MB/s]\u001b[A\u001b[A\u001b[A\n", "  3%|▎         | 163M/4.65G [00:13<04:45, 16.9MB/s]\u001b[A\n", "\n", "\n", " 65%|██████▍   | 149M/230M [00:13<00:05, 16.7MB/s]\u001b[A\u001b[A\u001b[A\n", "  3%|▎         | 166M/4.65G [00:13<04:52, 16.5MB/s]\u001b[A\n", "  4%|▎         | 168M/4.65G [00:13<04:44, 16.9MB/s]\u001b[A\n", "\n", "\n", " 66%|██████▌   | 151M/230M [00:13<00:05, 15.2MB/s]\u001b[A\u001b[A\u001b[A\n", "\n", "\n", " 67%|██████▋   | 154M/230M [00:13<00:04, 17.6MB/s]\u001b[A\u001b[A\u001b[A\n", "  4%|▎         | 170M/4.65G [00:13<04:47, 16.7MB/s]\u001b[A\n", "  4%|▎         | 173M/4.65G [00:13<04:15, 18.8MB/s]\u001b[A\n", "\n", "\n", " 68%|██████▊   | 157M/230M [00:14<00:04, 16.7MB/s]\u001b[A\u001b[A\u001b[A\n", "  4%|▎         | 176M/4.65G [00:14<04:34, 17.5MB/s]\u001b[A\n", "\n", "\n", " 69%|██████▉   | 159M/230M [00:14<00:09, 7.44MB/s]\u001b[A\u001b[A\u001b[A\n", "  4%|▎         | 178M/4.65G [00:14<09:44, 8.22MB/s]\u001b[A\n", "  4%|▍         | 187M/4.65G [00:15<05:02, 15.8MB/s]\u001b[A\n", "\n", "\n", " 73%|███████▎  | 168M/230M [00:15<00:04, 14.2MB/s]\u001b[A\u001b[A\u001b[A\n", "\n", "\n", " 74%|███████▍  | 171M/230M [00:15<00:06, 9.65MB/s]\u001b[A\u001b[A\u001b[A\n", "  4%|▍         | 190M/4.65G [00:15<07:56, 10.1MB/s]\u001b[A\n", "  4%|▍         | 198M/4.65G [00:16<05:14, 15.2MB/s]\u001b[A\n", "\n", "\n", " 78%|███████▊  | 179M/230M [00:16<00:03, 14.5MB/s]\u001b[A\u001b[A\u001b[A\n", "\n", "\n", " 79%|███████▉  | 182M/230M [00:16<00:03, 14.6MB/s]\u001b[A\u001b[A\u001b[A\n", "  4%|▍         | 202M/4.65G [00:16<05:01, 15.9MB/s]\u001b[A\n", "  4%|▍         | 205M/4.65G [00:16<05:02, 15.8MB/s]\u001b[A\n", "\n", "\n", " 81%|████████  | 185M/230M [00:16<00:03, 14.1MB/s]\u001b[A\u001b[A\u001b[A\n", "  4%|▍         | 209M/4.65G [00:16<04:49, 16.5MB/s]\u001b[A\n", "\n", "\n", " 81%|████████▏ | 187M/230M [00:16<00:03, 13.0MB/s]\u001b[A\u001b[A\u001b[A\n", "  4%|▍         | 213M/4.65G [00:16<04:39, 17.0MB/s]\u001b[A\n", "\n", "\n", " 83%|████████▎ | 191M/230M [00:17<00:02, 14.3MB/s]\u001b[A\u001b[A\u001b[A\n", "  5%|▍         | 216M/4.65G [00:17<04:46, 16.6MB/s]\u001b[A\n", "\n", "\n", " 84%|████████▍ | 193M/230M [00:17<00:05, 7.24MB/s]\u001b[A\u001b[A\u001b[A\n", "  5%|▍         | 218M/4.65G [00:17<09:00, 8.81MB/s]\u001b[A\n", "  5%|▍         | 227M/4.65G [00:18<05:06, 15.5MB/s]\u001b[A\n", "\n", "\n", " 88%|████████▊ | 203M/230M [00:18<00:02, 13.8MB/s]\u001b[A\u001b[A\u001b[A\n", "  5%|▍         | 230M/4.65G [00:18<07:45, 10.2MB/s]\u001b[A\n", "\n", "\n", " 90%|████████▉ | 206M/230M [00:18<00:02, 9.87MB/s]\u001b[A\u001b[A\u001b[A\n", "\n", "\n", " 92%|█████████▏| 211M/230M [00:19<00:01, 12.4MB/s]\u001b[A\u001b[A\u001b[A\n", "  5%|▌         | 238M/4.65G [00:19<05:16, 15.0MB/s]\u001b[A\n", "\n", "\n", " 93%|█████████▎| 213M/230M [00:19<00:01, 12.2MB/s]\u001b[A\u001b[A\u001b[A\n", "  5%|▌         | 241M/4.65G [00:19<05:14, 15.0MB/s]\u001b[A\n", "\n", "\n", " 94%|█████████▍| 216M/230M [00:19<00:01, 13.6MB/s]\u001b[A\u001b[A\u001b[A\n", "  5%|▌         | 244M/4.65G [00:19<04:45, 16.6MB/s]\u001b[A\n", "\n", "\n", " 95%|█████████▍| 218M/230M [00:19<00:00, 14.1MB/s]\u001b[A\u001b[A\u001b[A\n", "\n", "\n", " 96%|█████████▌| 220M/230M [00:19<00:00, 14.7MB/s]\u001b[A\u001b[A\u001b[A\n", "  5%|▌         | 247M/4.65G [00:19<05:36, 14.0MB/s]\u001b[A\n", "\n", "\n", " 97%|█████████▋| 222M/230M [00:19<00:00, 14.9MB/s]\u001b[A\u001b[A\u001b[A\n", "  5%|▌         | 249M/4.65G [00:19<05:27, 14.5MB/s]\u001b[A\n", "  5%|▌         | 251M/4.65G [00:19<05:06, 15.4MB/s]\u001b[A\n", "\n", "\n", " 98%|█████████▊| 225M/230M [00:19<00:00, 14.1MB/s]\u001b[A\u001b[A\u001b[A\n", "  5%|▌         | 253M/4.65G [00:20<05:01, 15.7MB/s]\u001b[A\n", "\n", "\n", " 99%|█████████▉| 228M/230M [00:20<00:00, 14.2MB/s]\u001b[A\u001b[A\u001b[A\n", "\n", "\n", "100%|██████████| 230M/230M [00:20<00:00, 11.6MB/s]\n", "\n", "  5%|▌         | 255M/4.65G [00:20<11:31, 6.82MB/s]\u001b[A\n", "  6%|▌         | 265M/4.65G [00:21<05:17, 14.8MB/s]\u001b[A\n", "  6%|▌         | 268M/4.65G [00:21<07:53, 9.95MB/s]\u001b[A\n", "  6%|▌         | 276M/4.65G [00:22<05:13, 15.0MB/s]\u001b[A\n", "  6%|▌         | 279M/4.65G [00:22<05:02, 15.5MB/s]\u001b[A\n", "  6%|▌         | 283M/4.65G [00:22<04:44, 16.5MB/s]\u001b[A\n", "  6%|▌         | 286M/4.65G [00:22<04:17, 18.2MB/s]\u001b[A\n", "  6%|▌         | 289M/4.65G [00:22<04:29, 17.4MB/s]\u001b[A\n", "  6%|▌         | 292M/4.65G [00:22<04:38, 16.8MB/s]\u001b[A\n", "  6%|▌         | 294M/4.65G [00:23<04:43, 16.5MB/s]\u001b[A\n", "  6%|▌         | 296M/4.65G [00:23<10:37, 7.34MB/s]\u001b[A\n", "  6%|▋         | 305M/4.65G [00:24<05:27, 14.2MB/s]\u001b[A\n", "  6%|▋         | 308M/4.65G [00:24<08:04, 9.62MB/s]\u001b[A\n", "  7%|▋         | 315M/4.65G [00:25<05:11, 15.0MB/s]\u001b[A\n", "  7%|▋         | 318M/4.65G [00:25<08:15, 9.39MB/s]\u001b[A\n", "  7%|▋         | 326M/4.65G [00:26<05:30, 14.1MB/s]\u001b[A\n", "  7%|▋         | 329M/4.65G [00:26<05:34, 13.9MB/s]\u001b[A\n", "  7%|▋         | 332M/4.65G [00:26<05:27, 14.2MB/s]\u001b[A\n", "  7%|▋         | 336M/4.65G [00:26<05:05, 15.2MB/s]\u001b[A\n", "  7%|▋         | 339M/4.65G [00:26<05:04, 15.2MB/s]\u001b[A\n", "  7%|▋         | 341M/4.65G [00:27<05:28, 14.1MB/s]\u001b[A\n", "  7%|▋         | 346M/4.65G [00:27<04:40, 16.5MB/s]\u001b[A\n", "  7%|▋         | 350M/4.65G [00:27<04:30, 17.1MB/s]\u001b[A\n", "  7%|▋         | 353M/4.65G [00:27<04:37, 16.7MB/s]\u001b[A\n", "  7%|▋         | 356M/4.65G [00:27<04:43, 16.3MB/s]\u001b[A\n", "  8%|▊         | 360M/4.65G [00:28<04:31, 17.0MB/s]\u001b[A\n", "  8%|▊         | 363M/4.65G [00:28<04:39, 16.5MB/s]\u001b[A\n", "  8%|▊         | 367M/4.65G [00:28<04:28, 17.1MB/s]\u001b[A\n", "  8%|▊         | 371M/4.65G [00:28<04:22, 17.5MB/s]\u001b[A\n", "  8%|▊         | 374M/4.65G [00:29<04:31, 16.9MB/s]\u001b[A\n", "  8%|▊         | 376M/4.65G [00:29<04:57, 15.5MB/s]\u001b[A\n", "  8%|▊         | 381M/4.65G [00:29<04:15, 18.0MB/s]\u001b[A\n", "  8%|▊         | 385M/4.65G [00:29<04:21, 17.6MB/s]\u001b[A\n", "  8%|▊         | 388M/4.65G [00:29<04:21, 17.5MB/s]\u001b[A\n", "  8%|▊         | 392M/4.65G [00:30<04:16, 17.8MB/s]\u001b[A\n", "  8%|▊         | 394M/4.65G [00:30<04:10, 18.3MB/s]\u001b[A\n", "  8%|▊         | 396M/4.65G [00:30<04:25, 17.2MB/s]\u001b[A\n", "  8%|▊         | 399M/4.65G [00:30<04:24, 17.2MB/s]\u001b[A\n", "  8%|▊         | 403M/4.65G [00:30<04:17, 17.7MB/s]\u001b[A\n", "  9%|▊         | 406M/4.65G [00:31<04:29, 16.9MB/s]\u001b[A\n", "  9%|▊         | 408M/4.65G [00:31<05:15, 14.5MB/s]\u001b[A\n", "  9%|▊         | 413M/4.65G [00:31<04:23, 17.3MB/s]\u001b[A\n", "  9%|▊         | 416M/4.65G [00:31<04:32, 16.7MB/s]\u001b[A\n", "  9%|▉         | 420M/4.65G [00:31<04:23, 17.3MB/s]\u001b[A\n", "  9%|▉         | 424M/4.65G [00:32<04:16, 17.7MB/s]\u001b[A\n", "  9%|▉         | 426M/4.65G [00:32<09:01, 8.39MB/s]\u001b[A\n", "  9%|▉         | 435M/4.65G [00:33<05:02, 15.0MB/s]\u001b[A\n", "  9%|▉         | 438M/4.65G [00:33<07:33, 9.98MB/s]\u001b[A\n", "  9%|▉         | 446M/4.65G [00:34<05:05, 14.8MB/s]\u001b[A\n", "  9%|▉         | 449M/4.65G [00:34<04:51, 15.5MB/s]\u001b[A\n", " 10%|▉         | 452M/4.65G [00:34<04:35, 16.4MB/s]\u001b[A\n", " 10%|▉         | 455M/4.65G [00:34<04:23, 17.1MB/s]\u001b[A\n", " 10%|▉         | 458M/4.65G [00:34<04:31, 16.6MB/s]\u001b[A\n", " 10%|▉         | 461M/4.65G [00:34<04:37, 16.2MB/s]\u001b[A\n", " 10%|▉         | 464M/4.65G [00:35<04:41, 16.0MB/s]\u001b[A\n", " 10%|▉         | 466M/4.65G [00:35<08:28, 8.86MB/s]\u001b[A\n", " 10%|▉         | 475M/4.65G [00:35<04:37, 16.2MB/s]\u001b[A\n", " 10%|█         | 479M/4.65G [00:36<04:27, 16.8MB/s]\u001b[A\n", " 10%|█         | 482M/4.65G [00:36<07:18, 10.2MB/s]\u001b[A\n", " 10%|█         | 490M/4.65G [00:37<04:51, 15.4MB/s]\u001b[A\n", " 10%|█         | 494M/4.65G [00:37<04:38, 16.0MB/s]\u001b[A\n", " 10%|█         | 497M/4.65G [00:37<04:41, 15.9MB/s]\u001b[A\n", " 11%|█         | 500M/4.65G [00:37<04:18, 17.3MB/s]\u001b[A\n", " 11%|█         | 503M/4.65G [00:37<04:10, 17.8MB/s]\u001b[A\n", " 11%|█         | 505M/4.65G [00:38<04:29, 16.5MB/s]\u001b[A\n", " 11%|█         | 507M/4.65G [00:38<09:39, 7.69MB/s]\u001b[A\n", " 11%|█         | 517M/4.65G [00:39<04:48, 15.4MB/s]\u001b[A\n", " 11%|█         | 520M/4.65G [00:39<07:13, 10.2MB/s]\u001b[A\n", " 11%|█         | 527M/4.65G [00:40<04:46, 15.5MB/s]\u001b[A\n", " 11%|█         | 530M/4.65G [00:40<05:02, 14.7MB/s]\u001b[A\n", " 11%|█         | 533M/4.65G [00:40<05:10, 14.3MB/s]\u001b[A\n", " 11%|█▏        | 536M/4.65G [00:40<05:40, 13.0MB/s]\u001b[A\n", " 11%|█▏        | 538M/4.65G [00:40<05:50, 12.6MB/s]\u001b[A\n", " 11%|█▏        | 541M/4.65G [00:40<04:56, 14.9MB/s]\u001b[A\n", " 11%|█▏        | 543M/4.65G [00:41<10:07, 7.27MB/s]\u001b[A\n", " 12%|█▏        | 552M/4.65G [00:42<05:15, 14.0MB/s]\u001b[A\n", " 12%|█▏        | 555M/4.65G [00:42<07:45, 9.47MB/s]\u001b[A\n", " 12%|█▏        | 563M/4.65G [00:43<05:04, 14.4MB/s]\u001b[A\n", " 12%|█▏        | 566M/4.65G [00:43<04:51, 15.1MB/s]\u001b[A\n", " 12%|█▏        | 570M/4.65G [00:43<04:32, 16.1MB/s]\u001b[A\n", " 12%|█▏        | 573M/4.65G [00:43<04:35, 15.9MB/s]\u001b[A\n", " 12%|█▏        | 576M/4.65G [00:43<04:03, 18.0MB/s]\u001b[A\n", " 12%|█▏        | 579M/4.65G [00:43<04:14, 17.2MB/s]\u001b[A\n", " 12%|█▏        | 581M/4.65G [00:44<04:21, 16.7MB/s]\u001b[A\n", " 12%|█▏        | 583M/4.65G [00:44<09:34, 7.62MB/s]\u001b[A\n", " 12%|█▏        | 592M/4.65G [00:45<04:57, 14.7MB/s]\u001b[A\n", " 13%|█▎        | 595M/4.65G [00:45<07:30, 9.69MB/s]\u001b[A\n", " 13%|█▎        | 603M/4.65G [00:46<04:56, 14.7MB/s]\u001b[A\n", " 13%|█▎        | 606M/4.65G [00:46<04:43, 15.3MB/s]\u001b[A\n", " 13%|█▎        | 610M/4.65G [00:46<04:40, 15.5MB/s]\u001b[A\n", " 13%|█▎        | 614M/4.65G [00:46<04:26, 16.3MB/s]\u001b[A\n", " 13%|█▎        | 617M/4.65G [00:46<04:15, 17.0MB/s]\u001b[A\n", " 13%|█▎        | 620M/4.65G [00:46<03:48, 19.0MB/s]\u001b[A\n", " 13%|█▎        | 623M/4.65G [00:47<04:02, 17.8MB/s]\u001b[A\n", " 13%|█▎        | 625M/4.65G [00:47<08:08, 8.88MB/s]\u001b[A\n", " 13%|█▎        | 634M/4.65G [00:48<04:28, 16.1MB/s]\u001b[A\n", " 13%|█▎        | 637M/4.65G [00:48<07:02, 10.2MB/s]\u001b[A\n", " 13%|█▎        | 642M/4.65G [00:49<05:23, 13.4MB/s]\u001b[A\n", " 14%|█▎        | 645M/4.65G [00:49<05:21, 13.4MB/s]\u001b[A\n", " 14%|█▎        | 649M/4.65G [00:49<04:41, 15.3MB/s]\u001b[A\n", " 14%|█▎        | 653M/4.65G [00:49<04:32, 15.8MB/s]\u001b[A\n", " 14%|█▍        | 656M/4.65G [00:49<04:27, 16.1MB/s]\u001b[A\n", " 14%|█▍        | 660M/4.65G [00:49<04:15, 16.8MB/s]\u001b[A\n", " 14%|█▍        | 664M/4.65G [00:50<04:13, 16.9MB/s]\u001b[A\n", " 14%|█▍        | 666M/4.65G [00:50<06:48, 10.5MB/s]\u001b[A\n", " 14%|█▍        | 673M/4.65G [00:51<04:07, 17.3MB/s]\u001b[A\n", " 14%|█▍        | 676M/4.65G [00:51<04:35, 15.6MB/s]\u001b[A\n", " 14%|█▍        | 680M/4.65G [00:51<04:00, 17.8MB/s]\u001b[A\n", " 14%|█▍        | 683M/4.65G [00:51<03:47, 18.8MB/s]\u001b[A\n", " 14%|█▍        | 686M/4.65G [00:51<03:54, 18.2MB/s]\u001b[A\n", " 14%|█▍        | 689M/4.65G [00:52<03:57, 17.9MB/s]\u001b[A\n", " 15%|█▍        | 691M/4.65G [00:52<04:47, 14.8MB/s]\u001b[A\n", " 15%|█▍        | 695M/4.65G [00:52<03:50, 18.4MB/s]\u001b[A\n", " 15%|█▍        | 698M/4.65G [00:52<04:04, 17.4MB/s]\u001b[A\n", " 15%|█▍        | 701M/4.65G [00:52<03:47, 18.7MB/s]\u001b[A\n", " 15%|█▍        | 703M/4.65G [00:52<04:01, 17.6MB/s]\u001b[A\n", " 15%|█▍        | 706M/4.65G [00:52<03:56, 18.0MB/s]\u001b[A\n", " 15%|█▍        | 708M/4.65G [00:52<03:51, 18.4MB/s]\u001b[A\n", " 15%|█▍        | 710M/4.65G [00:53<04:12, 16.8MB/s]\u001b[A\n", " 15%|█▍        | 712M/4.65G [00:53<09:08, 7.73MB/s]\u001b[A\n", " 15%|█▌        | 721M/4.65G [00:54<04:27, 15.8MB/s]\u001b[A\n", " 15%|█▌        | 724M/4.65G [00:54<07:05, 9.93MB/s]\u001b[A\n", " 15%|█▌        | 732M/4.65G [00:55<04:37, 15.2MB/s]\u001b[A\n", " 15%|█▌        | 735M/4.65G [00:55<04:15, 16.5MB/s]\u001b[A\n", " 16%|█▌        | 738M/4.65G [00:55<03:59, 17.6MB/s]\u001b[A\n", " 16%|█▌        | 741M/4.65G [00:55<04:07, 17.0MB/s]\u001b[A\n", " 16%|█▌        | 744M/4.65G [00:55<04:14, 16.5MB/s]\u001b[A\n", " 16%|█▌        | 747M/4.65G [00:55<04:19, 16.2MB/s]\u001b[A\n", " 16%|█▌        | 749M/4.65G [00:56<04:44, 14.8MB/s]\u001b[A\n", " 16%|█▌        | 751M/4.65G [00:56<09:36, 7.29MB/s]\u001b[A\n", " 16%|█▌        | 761M/4.65G [00:57<04:37, 15.1MB/s]\u001b[A\n", " 16%|█▌        | 764M/4.65G [00:57<06:56, 10.1MB/s]\u001b[A\n", " 16%|█▌        | 772M/4.65G [00:58<04:38, 15.0MB/s]\u001b[A\n", " 16%|█▋        | 775M/4.65G [00:58<04:28, 15.6MB/s]\u001b[A\n", " 16%|█▋        | 778M/4.65G [00:58<04:08, 16.8MB/s]\u001b[A\n", " 16%|█▋        | 781M/4.65G [00:58<04:02, 17.2MB/s]\u001b[A\n", " 16%|█▋        | 784M/4.65G [00:58<04:09, 16.7MB/s]\u001b[A\n", " 17%|█▋        | 786M/4.65G [00:58<04:03, 17.1MB/s]\u001b[A\n", " 17%|█▋        | 788M/4.65G [00:58<04:30, 15.4MB/s]\u001b[A\n", " 17%|█▋        | 791M/4.65G [00:59<04:00, 17.3MB/s]\u001b[A\n", " 17%|█▋        | 793M/4.65G [00:59<08:43, 7.95MB/s]\u001b[A\n", " 17%|█▋        | 802M/4.65G [01:00<04:28, 15.5MB/s]\u001b[A\n", " 17%|█▋        | 805M/4.65G [01:00<06:57, 9.92MB/s]\u001b[A\n", " 17%|█▋        | 813M/4.65G [01:01<04:34, 15.1MB/s]\u001b[A\n", " 17%|█▋        | 816M/4.65G [01:01<04:13, 16.3MB/s]\u001b[A\n", " 17%|█▋        | 819M/4.65G [01:01<03:56, 17.5MB/s]\u001b[A\n", " 17%|█▋        | 822M/4.65G [01:01<04:04, 16.9MB/s]\u001b[A\n", " 17%|█▋        | 825M/4.65G [01:01<04:10, 16.5MB/s]\u001b[A\n", " 17%|█▋        | 827M/4.65G [01:01<03:59, 17.2MB/s]\u001b[A\n", " 17%|█▋        | 829M/4.65G [01:01<04:00, 17.1MB/s]\u001b[A\n", " 17%|█▋        | 831M/4.65G [01:02<04:29, 15.3MB/s]\u001b[A\n", " 18%|█▊        | 833M/4.65G [01:02<09:49, 6.99MB/s]\u001b[A\n", " 18%|█▊        | 841M/4.65G [01:03<04:33, 15.0MB/s]\u001b[A\n", " 18%|█▊        | 844M/4.65G [01:03<07:45, 8.81MB/s]\u001b[A\n", " 18%|█▊        | 852M/4.65G [01:04<04:54, 13.9MB/s]\u001b[A\n", " 18%|█▊        | 855M/4.65G [01:04<04:37, 14.8MB/s]\u001b[A\n", " 18%|█▊        | 858M/4.65G [01:04<04:04, 16.7MB/s]\u001b[A\n", " 18%|█▊        | 861M/4.65G [01:04<04:09, 16.4MB/s]\u001b[A\n", " 18%|█▊        | 864M/4.65G [01:04<04:13, 16.1MB/s]\u001b[A\n", " 18%|█▊        | 866M/4.65G [01:04<04:17, 15.8MB/s]\u001b[A\n", " 18%|█▊        | 869M/4.65G [01:04<03:41, 18.4MB/s]\u001b[A\n", " 18%|█▊        | 872M/4.65G [01:05<03:53, 17.4MB/s]\u001b[A\n", " 18%|█▊        | 874M/4.65G [01:05<08:14, 8.24MB/s]\u001b[A\n", " 19%|█▊        | 883M/4.65G [01:06<04:21, 15.5MB/s]\u001b[A\n", " 19%|█▊        | 886M/4.65G [01:06<06:44, 10.0MB/s]\u001b[A\n", " 19%|█▉        | 894M/4.65G [01:07<04:26, 15.2MB/s]\u001b[A\n", " 19%|█▉        | 897M/4.65G [01:07<04:14, 15.9MB/s]\u001b[A\n", " 19%|█▉        | 900M/4.65G [01:07<03:48, 17.7MB/s]\u001b[A\n", " 19%|█▉        | 903M/4.65G [01:07<03:57, 17.0MB/s]\u001b[A\n", " 19%|█▉        | 906M/4.65G [01:07<04:03, 16.6MB/s]\u001b[A\n", " 19%|█▉        | 908M/4.65G [01:07<04:05, 16.4MB/s]\u001b[A\n", " 19%|█▉        | 911M/4.65G [01:07<03:35, 18.7MB/s]\u001b[A\n", " 19%|█▉        | 914M/4.65G [01:08<03:49, 17.6MB/s]\u001b[A\n", " 19%|█▉        | 916M/4.65G [01:08<08:02, 8.34MB/s]\u001b[A\n", " 19%|█▉        | 926M/4.65G [01:09<04:02, 16.6MB/s]\u001b[A\n", " 20%|█▉        | 929M/4.65G [01:09<06:18, 10.6MB/s]\u001b[A\n", " 20%|█▉        | 937M/4.65G [01:10<04:18, 15.5MB/s]\u001b[A\n", " 20%|█▉        | 940M/4.65G [01:10<04:18, 15.5MB/s]\u001b[A\n", " 20%|█▉        | 944M/4.65G [01:10<04:06, 16.2MB/s]\u001b[A\n", " 20%|█▉        | 948M/4.65G [01:10<03:57, 16.8MB/s]\u001b[A\n", " 20%|█▉        | 951M/4.65G [01:10<04:02, 16.5MB/s]\u001b[A\n", " 20%|██        | 955M/4.65G [01:11<03:54, 17.0MB/s]\u001b[A\n", " 20%|██        | 957M/4.65G [01:11<07:26, 8.93MB/s]\u001b[A\n", " 20%|██        | 966M/4.65G [01:12<04:16, 15.5MB/s]\u001b[A\n", " 20%|██        | 969M/4.65G [01:12<06:29, 10.2MB/s]\u001b[A\n", " 21%|██        | 977M/4.65G [01:13<04:25, 15.0MB/s]\u001b[A\n", " 21%|██        | 980M/4.65G [01:13<04:30, 14.6MB/s]\u001b[A\n", " 21%|██        | 982M/4.65G [01:13<04:42, 14.0MB/s]\u001b[A\n", " 21%|██        | 986M/4.65G [01:13<04:21, 15.1MB/s]\u001b[A\n", " 21%|██        | 990M/4.65G [01:13<04:05, 16.1MB/s]\u001b[A\n", " 21%|██        | 993M/4.65G [01:14<04:08, 15.9MB/s]\u001b[A\n", " 21%|██        | 995M/4.65G [01:14<07:52, 8.34MB/s]\u001b[A\n", " 21%|██        | 0.98G/4.65G [01:15<04:22, 15.0MB/s]\u001b[A\n", " 21%|██        | 0.98G/4.65G [01:15<06:32, 10.0MB/s]\u001b[A\n", " 21%|██▏       | 0.99G/4.65G [01:16<04:24, 14.8MB/s]\u001b[A\n", " 21%|██▏       | 0.99G/4.65G [01:16<04:12, 15.5MB/s]\u001b[A\n", " 21%|██▏       | 1.00G/4.65G [01:16<03:45, 17.4MB/s]\u001b[A\n", " 22%|██▏       | 1.00G/4.65G [01:16<03:51, 16.9MB/s]\u001b[A\n", " 22%|██▏       | 1.00G/4.65G [01:16<03:57, 16.5MB/s]\u001b[A\n", " 22%|██▏       | 1.01G/4.65G [01:16<04:01, 16.2MB/s]\u001b[A\n", " 22%|██▏       | 1.01G/4.65G [01:17<04:14, 15.4MB/s]\u001b[A\n", " 22%|██▏       | 1.01G/4.65G [01:17<04:34, 14.2MB/s]\u001b[A\n", " 22%|██▏       | 1.01G/4.65G [01:17<08:11, 7.94MB/s]\u001b[A\n", " 22%|██▏       | 1.02G/4.65G [01:18<04:06, 15.8MB/s]\u001b[A\n", " 22%|██▏       | 1.02G/4.65G [01:18<06:31, 9.94MB/s]\u001b[A\n", " 22%|██▏       | 1.03G/4.65G [01:19<04:30, 14.4MB/s]\u001b[A\n", " 22%|██▏       | 1.03G/4.65G [01:19<04:19, 15.0MB/s]\u001b[A\n", " 22%|██▏       | 1.04G/4.65G [01:19<03:48, 17.0MB/s]\u001b[A\n", " 22%|██▏       | 1.04G/4.65G [01:19<03:53, 16.6MB/s]\u001b[A\n", " 22%|██▏       | 1.04G/4.65G [01:19<03:54, 16.5MB/s]\u001b[A\n", " 22%|██▏       | 1.04G/4.65G [01:19<03:57, 16.3MB/s]\u001b[A\n", " 23%|██▎       | 1.05G/4.65G [01:19<03:31, 18.3MB/s]\u001b[A\n", " 23%|██▎       | 1.05G/4.65G [01:20<03:54, 16.5MB/s]\u001b[A\n", " 23%|██▎       | 1.05G/4.65G [01:20<08:33, 7.51MB/s]\u001b[A\n", " 23%|██▎       | 1.06G/4.65G [01:21<04:16, 15.0MB/s]\u001b[A\n", " 23%|██▎       | 1.06G/4.65G [01:21<06:35, 9.73MB/s]\u001b[A\n", " 23%|██▎       | 1.07G/4.65G [01:22<04:17, 14.9MB/s]\u001b[A\n", " 23%|██▎       | 1.07G/4.65G [01:22<04:08, 15.5MB/s]\u001b[A\n", " 23%|██▎       | 1.08G/4.65G [01:22<03:57, 16.2MB/s]\u001b[A\n", " 23%|██▎       | 1.08G/4.65G [01:22<03:52, 16.5MB/s]\u001b[A\n", " 23%|██▎       | 1.08G/4.65G [01:22<04:01, 15.9MB/s]\u001b[A\n", " 23%|██▎       | 1.08G/4.65G [01:22<03:32, 18.0MB/s]\u001b[A\n", " 23%|██▎       | 1.09G/4.65G [01:22<03:48, 16.7MB/s]\u001b[A\n", " 23%|██▎       | 1.09G/4.65G [01:23<04:00, 15.9MB/s]\u001b[A\n", " 23%|██▎       | 1.09G/4.65G [01:23<08:36, 7.40MB/s]\u001b[A\n", " 24%|██▎       | 1.10G/4.65G [01:24<03:59, 15.9MB/s]\u001b[A\n", " 24%|██▎       | 1.10G/4.65G [01:24<06:08, 10.3MB/s]\u001b[A\n", " 24%|██▍       | 1.11G/4.65G [01:25<04:07, 15.3MB/s]\u001b[A\n", " 24%|██▍       | 1.11G/4.65G [01:25<04:15, 14.9MB/s]\u001b[A\n", " 24%|██▍       | 1.12G/4.65G [01:25<04:36, 13.7MB/s]\u001b[A\n", " 24%|██▍       | 1.12G/4.65G [01:25<04:13, 14.9MB/s]\u001b[A\n", " 24%|██▍       | 1.12G/4.65G [01:25<03:57, 15.9MB/s]\u001b[A\n", " 24%|██▍       | 1.12G/4.65G [01:25<03:54, 16.1MB/s]\u001b[A\n", " 24%|██▍       | 1.13G/4.65G [01:26<03:44, 16.8MB/s]\u001b[A\n", " 24%|██▍       | 1.13G/4.65G [01:26<03:50, 16.4MB/s]\u001b[A\n", " 24%|██▍       | 1.13G/4.65G [01:26<06:31, 9.65MB/s]\u001b[A\n", " 25%|██▍       | 1.14G/4.65G [01:27<03:31, 17.8MB/s]\u001b[A\n", " 25%|██▍       | 1.14G/4.65G [01:27<03:14, 19.3MB/s]\u001b[A\n", " 25%|██▍       | 1.15G/4.65G [01:27<03:10, 19.7MB/s]\u001b[A\n", " 25%|██▍       | 1.15G/4.65G [01:27<03:24, 18.4MB/s]\u001b[A\n", " 25%|██▍       | 1.15G/4.65G [01:27<03:48, 16.4MB/s]\u001b[A\n", " 25%|██▍       | 1.15G/4.65G [01:27<03:21, 18.6MB/s]\u001b[A\n", " 25%|██▍       | 1.16G/4.65G [01:28<03:50, 16.2MB/s]\u001b[A\n", " 25%|██▍       | 1.16G/4.65G [01:28<03:50, 16.3MB/s]\u001b[A\n", " 25%|██▍       | 1.16G/4.65G [01:28<08:21, 7.46MB/s]\u001b[A\n", " 25%|██▌       | 1.17G/4.65G [01:29<04:02, 15.4MB/s]\u001b[A\n", " 25%|██▌       | 1.17G/4.65G [01:29<03:50, 16.2MB/s]\u001b[A\n", " 25%|██▌       | 1.18G/4.65G [01:29<05:12, 11.9MB/s]\u001b[A\n", " 25%|██▌       | 1.18G/4.65G [01:30<03:31, 17.5MB/s]\u001b[A\n", " 26%|██▌       | 1.19G/4.65G [01:30<03:38, 17.0MB/s]\u001b[A\n", " 26%|██▌       | 1.19G/4.65G [01:30<05:44, 10.8MB/s]\u001b[A\n", " 26%|██▌       | 1.20G/4.65G [01:30<03:13, 19.1MB/s]\u001b[A\n", " 26%|██▌       | 1.20G/4.65G [01:31<03:33, 17.4MB/s]\u001b[A\n", " 26%|██▌       | 1.20G/4.65G [01:31<05:17, 11.6MB/s]\u001b[A\n", " 26%|██▌       | 1.21G/4.65G [01:32<03:29, 17.6MB/s]\u001b[A\n", " 26%|██▌       | 1.22G/4.65G [01:32<03:35, 17.1MB/s]\u001b[A\n", " 26%|██▌       | 1.22G/4.65G [01:32<04:57, 12.4MB/s]\u001b[A\n", " 26%|██▋       | 1.23G/4.65G [01:33<03:27, 17.7MB/s]\u001b[A\n", " 26%|██▋       | 1.23G/4.65G [01:33<03:11, 19.2MB/s]\u001b[A\n", " 27%|██▋       | 1.23G/4.65G [01:33<03:20, 18.2MB/s]\u001b[A\n", " 27%|██▋       | 1.24G/4.65G [01:33<03:17, 18.6MB/s]\u001b[A\n", " 27%|██▋       | 1.24G/4.65G [01:33<03:27, 17.7MB/s]\u001b[A\n", " 27%|██▋       | 1.24G/4.65G [01:34<03:35, 17.0MB/s]\u001b[A\n", " 27%|██▋       | 1.24G/4.65G [01:34<07:47, 7.82MB/s]\u001b[A\n", " 27%|██▋       | 1.25G/4.65G [01:35<04:11, 14.5MB/s]\u001b[A\n", " 27%|██▋       | 1.25G/4.65G [01:35<03:58, 15.2MB/s]\u001b[A\n", " 27%|██▋       | 1.26G/4.65G [01:35<04:23, 13.8MB/s]\u001b[A\n", " 27%|██▋       | 1.26G/4.65G [01:35<04:10, 14.5MB/s]\u001b[A\n", " 27%|██▋       | 1.26G/4.65G [01:35<04:01, 15.0MB/s]\u001b[A\n", " 27%|██▋       | 1.26G/4.65G [01:35<03:48, 15.9MB/s]\u001b[A\n", " 27%|██▋       | 1.27G/4.65G [01:35<03:43, 16.3MB/s]\u001b[A\n", " 27%|██▋       | 1.27G/4.65G [01:36<03:32, 17.0MB/s]\u001b[A\n", " 27%|██▋       | 1.27G/4.65G [01:36<08:41, 6.95MB/s]\u001b[A\n", " 28%|██▊       | 1.28G/4.65G [01:37<04:07, 14.6MB/s]\u001b[A\n", " 28%|██▊       | 1.28G/4.65G [01:37<06:20, 9.50MB/s]\u001b[A\n", " 28%|██▊       | 1.29G/4.65G [01:38<04:06, 14.6MB/s]\u001b[A\n", " 28%|██▊       | 1.29G/4.65G [01:38<06:00, 9.99MB/s]\u001b[A\n", " 28%|██▊       | 1.30G/4.65G [01:39<04:03, 14.8MB/s]\u001b[A\n", " 28%|██▊       | 1.30G/4.65G [01:39<03:50, 15.5MB/s]\u001b[A\n", " 28%|██▊       | 1.31G/4.65G [01:39<03:40, 16.2MB/s]\u001b[A\n", " 28%|██▊       | 1.31G/4.65G [01:39<03:51, 15.5MB/s]\u001b[A\n", " 28%|██▊       | 1.31G/4.65G [01:40<04:14, 14.1MB/s]\u001b[A\n", " 28%|██▊       | 1.32G/4.65G [01:40<08:28, 7.03MB/s]\u001b[A\n", " 29%|██▊       | 1.32G/4.65G [01:41<04:33, 13.0MB/s]\u001b[A\n", " 29%|██▊       | 1.33G/4.65G [01:41<04:32, 13.1MB/s]\u001b[A\n", " 29%|██▊       | 1.33G/4.65G [01:41<04:39, 12.7MB/s]\u001b[A\n", " 29%|██▊       | 1.33G/4.65G [01:41<04:11, 14.2MB/s]\u001b[A\n", " 29%|██▉       | 1.34G/4.65G [01:41<03:52, 15.3MB/s]\u001b[A\n", " 29%|██▉       | 1.34G/4.65G [01:42<03:42, 16.0MB/s]\u001b[A\n", " 29%|██▉       | 1.34G/4.65G [01:42<08:00, 7.38MB/s]\u001b[A\n", " 29%|██▉       | 1.35G/4.65G [01:43<04:13, 14.0MB/s]\u001b[A\n", " 29%|██▉       | 1.35G/4.65G [01:43<06:10, 9.53MB/s]\u001b[A\n", " 29%|██▉       | 1.36G/4.65G [01:44<04:05, 14.4MB/s]\u001b[A\n", " 29%|██▉       | 1.36G/4.65G [01:44<05:53, 9.98MB/s]\u001b[A\n", " 30%|██▉       | 1.37G/4.65G [01:45<04:00, 14.6MB/s]\u001b[A\n", " 30%|██▉       | 1.37G/4.65G [01:45<03:42, 15.8MB/s]\u001b[A\n", " 30%|██▉       | 1.38G/4.65G [01:45<03:27, 17.0MB/s]\u001b[A\n", " 30%|██▉       | 1.38G/4.65G [01:45<03:31, 16.6MB/s]\u001b[A\n", " 30%|██▉       | 1.38G/4.65G [01:45<03:35, 16.2MB/s]\u001b[A\n", " 30%|██▉       | 1.39G/4.65G [01:45<03:38, 16.0MB/s]\u001b[A\n", " 30%|██▉       | 1.39G/4.65G [01:46<03:41, 15.8MB/s]\u001b[A\n", " 30%|██▉       | 1.39G/4.65G [01:46<03:29, 16.7MB/s]\u001b[A\n", " 30%|███       | 1.40G/4.65G [01:46<03:21, 17.3MB/s]\u001b[A\n", " 30%|███       | 1.40G/4.65G [01:46<03:28, 16.7MB/s]\u001b[A\n", " 30%|███       | 1.40G/4.65G [01:46<03:20, 17.3MB/s]\u001b[A\n", " 30%|███       | 1.41G/4.65G [01:47<03:15, 17.7MB/s]\u001b[A\n", " 30%|███       | 1.41G/4.65G [01:47<06:20, 9.13MB/s]\u001b[A\n", " 31%|███       | 1.42G/4.65G [01:48<03:36, 16.0MB/s]\u001b[A\n", " 31%|███       | 1.42G/4.65G [01:48<03:28, 16.6MB/s]\u001b[A\n", " 31%|███       | 1.42G/4.65G [01:48<04:47, 12.0MB/s]\u001b[A\n", " 31%|███       | 1.43G/4.65G [01:48<03:12, 17.9MB/s]\u001b[A\n", " 31%|███       | 1.43G/4.65G [01:49<03:07, 18.3MB/s]\u001b[A\n", " 31%|███       | 1.44G/4.65G [01:49<03:16, 17.6MB/s]\u001b[A\n", " 31%|███       | 1.44G/4.65G [01:49<03:12, 17.9MB/s]\u001b[A\n", " 31%|███       | 1.44G/4.65G [01:49<03:20, 17.1MB/s]\u001b[A\n", " 31%|███       | 1.45G/4.65G [01:49<03:50, 14.9MB/s]\u001b[A\n", " 31%|███       | 1.45G/4.65G [01:50<03:35, 15.9MB/s]\u001b[A\n", " 31%|███▏      | 1.45G/4.65G [01:50<03:37, 15.7MB/s]\u001b[A\n", " 31%|███▏      | 1.46G/4.65G [01:50<03:25, 16.6MB/s]\u001b[A\n", " 31%|███▏      | 1.46G/4.65G [01:50<03:17, 17.3MB/s]\u001b[A\n", " 31%|███▏      | 1.46G/4.65G [01:50<03:15, 17.5MB/s]\u001b[A\n", " 32%|███▏      | 1.46G/4.65G [01:50<03:12, 17.8MB/s]\u001b[A\n", " 32%|███▏      | 1.47G/4.65G [01:51<03:21, 16.9MB/s]\u001b[A\n", " 32%|███▏      | 1.47G/4.65G [01:51<03:14, 17.6MB/s]\u001b[A\n", " 32%|███▏      | 1.47G/4.65G [01:51<03:21, 16.9MB/s]\u001b[A\n", " 32%|███▏      | 1.48G/4.65G [01:52<03:14, 17.5MB/s]\u001b[A\n", " 32%|███▏      | 1.48G/4.65G [01:52<03:36, 15.7MB/s]\u001b[A\n", " 32%|███▏      | 1.48G/4.65G [01:52<03:58, 14.2MB/s]\u001b[A\n", " 32%|███▏      | 1.49G/4.65G [01:52<03:37, 15.6MB/s]\u001b[A\n", " 32%|███▏      | 1.49G/4.65G [01:52<03:25, 16.5MB/s]\u001b[A\n", " 32%|███▏      | 1.49G/4.65G [01:52<03:56, 14.3MB/s]\u001b[A\n", " 32%|███▏      | 1.50G/4.65G [01:53<03:12, 17.5MB/s]\u001b[A\n", " 32%|███▏      | 1.50G/4.65G [01:53<03:08, 17.9MB/s]\u001b[A\n", " 32%|███▏      | 1.50G/4.65G [01:53<03:16, 17.2MB/s]\u001b[A\n", " 32%|███▏      | 1.51G/4.65G [01:53<03:11, 17.6MB/s]\u001b[A\n", " 33%|███▎      | 1.51G/4.65G [01:54<03:07, 17.9MB/s]\u001b[A\n", " 33%|███▎      | 1.51G/4.65G [01:54<05:52, 9.55MB/s]\u001b[A\n", " 33%|███▎      | 1.52G/4.65G [01:55<03:24, 16.4MB/s]\u001b[A\n", " 33%|███▎      | 1.53G/4.65G [01:55<03:27, 16.2MB/s]\u001b[A\n", " 33%|███▎      | 1.53G/4.65G [01:55<03:19, 16.8MB/s]\u001b[A\n", " 33%|███▎      | 1.53G/4.65G [01:55<03:13, 17.3MB/s]\u001b[A\n", " 33%|███▎      | 1.54G/4.65G [01:56<03:18, 16.8MB/s]\u001b[A\n", " 33%|███▎      | 1.54G/4.65G [01:56<03:38, 15.2MB/s]\u001b[A\n", " 33%|███▎      | 1.54G/4.65G [01:56<03:08, 17.6MB/s]\u001b[A\n", " 33%|███▎      | 1.55G/4.65G [01:56<03:15, 17.0MB/s]\u001b[A\n", " 33%|███▎      | 1.55G/4.65G [01:56<03:09, 17.5MB/s]\u001b[A\n", " 33%|███▎      | 1.55G/4.65G [01:56<03:08, 17.6MB/s]\u001b[A\n", " 34%|███▎      | 1.56G/4.65G [01:57<03:12, 17.2MB/s]\u001b[A\n", " 34%|███▎      | 1.56G/4.65G [01:57<05:53, 9.38MB/s]\u001b[A\n", " 34%|███▍      | 1.57G/4.65G [01:58<03:10, 17.4MB/s]\u001b[A\n", " 34%|███▍      | 1.57G/4.65G [01:58<03:14, 17.0MB/s]\u001b[A\n", " 34%|███▍      | 1.58G/4.65G [01:58<03:09, 17.4MB/s]\u001b[A\n", " 34%|███▍      | 1.58G/4.65G [01:58<03:05, 17.7MB/s]\u001b[A\n", " 34%|███▍      | 1.58G/4.65G [01:59<03:12, 17.1MB/s]\u001b[A\n", " 34%|███▍      | 1.58G/4.65G [01:59<03:35, 15.3MB/s]\u001b[A\n", " 34%|███▍      | 1.59G/4.65G [01:59<03:05, 17.7MB/s]\u001b[A\n", " 34%|███▍      | 1.59G/4.65G [01:59<03:12, 17.0MB/s]\u001b[A\n", " 34%|███▍      | 1.60G/4.65G [01:59<03:06, 17.5MB/s]\u001b[A\n", " 34%|███▍      | 1.60G/4.65G [01:59<03:03, 17.9MB/s]\u001b[A\n", " 34%|███▍      | 1.60G/4.65G [02:00<03:10, 17.2MB/s]\u001b[A\n", " 35%|███▍      | 1.60G/4.65G [02:00<05:49, 9.33MB/s]\u001b[A\n", " 35%|███▍      | 1.61G/4.65G [02:01<03:17, 16.4MB/s]\u001b[A\n", " 35%|███▍      | 1.62G/4.65G [02:01<05:11, 10.4MB/s]\u001b[A\n", " 35%|███▍      | 1.62G/4.65G [02:02<03:20, 16.2MB/s]\u001b[A\n", " 35%|███▌      | 1.63G/4.65G [02:02<03:22, 16.0MB/s]\u001b[A\n", " 35%|███▌      | 1.63G/4.65G [02:02<03:14, 16.6MB/s]\u001b[A\n", " 35%|███▌      | 1.64G/4.65G [02:02<03:08, 17.1MB/s]\u001b[A\n", " 35%|███▌      | 1.64G/4.65G [02:02<03:13, 16.7MB/s]\u001b[A\n", " 35%|███▌      | 1.64G/4.65G [02:03<03:07, 17.2MB/s]\u001b[A\n", " 35%|███▌      | 1.64G/4.65G [02:03<06:02, 8.89MB/s]\u001b[A\n", " 36%|███▌      | 1.65G/4.65G [02:04<03:30, 15.3MB/s]\u001b[A\n", " 36%|███▌      | 1.66G/4.65G [02:04<05:14, 10.2MB/s]\u001b[A\n", " 36%|███▌      | 1.66G/4.65G [02:05<03:34, 14.9MB/s]\u001b[A\n", " 36%|███▌      | 1.67G/4.65G [02:05<03:33, 15.0MB/s]\u001b[A\n", " 36%|███▌      | 1.67G/4.65G [02:05<03:21, 15.8MB/s]\u001b[A\n", " 36%|███▌      | 1.67G/4.65G [02:05<03:13, 16.5MB/s]\u001b[A\n", " 36%|███▌      | 1.68G/4.65G [02:05<03:16, 16.2MB/s]\u001b[A\n", " 36%|███▌      | 1.68G/4.65G [02:06<03:17, 16.2MB/s]\u001b[A\n", " 36%|███▌      | 1.68G/4.65G [02:06<06:20, 8.37MB/s]\u001b[A\n", " 36%|███▋      | 1.69G/4.65G [02:07<04:28, 11.8MB/s]\u001b[A\n", " 36%|███▋      | 1.69G/4.65G [02:07<06:40, 7.93MB/s]\u001b[A\n", " 37%|███▋      | 1.70G/4.65G [02:07<03:36, 14.6MB/s]\u001b[A\n", " 37%|███▋      | 1.70G/4.65G [02:08<03:33, 14.8MB/s]\u001b[A\n", " 37%|███▋      | 1.71G/4.65G [02:08<05:45, 9.14MB/s]\u001b[A\n", " 37%|███▋      | 1.71G/4.65G [02:09<03:28, 15.1MB/s]\u001b[A\n", " 37%|███▋      | 1.72G/4.65G [02:09<03:18, 15.8MB/s]\u001b[A\n", " 37%|███▋      | 1.72G/4.65G [02:09<03:10, 16.5MB/s]\u001b[A\n", " 37%|███▋      | 1.72G/4.65G [02:09<03:13, 16.2MB/s]\u001b[A\n", " 37%|███▋      | 1.73G/4.65G [02:10<03:06, 16.8MB/s]\u001b[A\n", " 37%|███▋      | 1.73G/4.65G [02:10<06:10, 8.45MB/s]\u001b[A\n", " 37%|███▋      | 1.74G/4.65G [02:11<03:34, 14.5MB/s]\u001b[A\n", " 38%|███▊      | 1.74G/4.65G [02:11<05:13, 9.95MB/s]\u001b[A\n", " 38%|███▊      | 1.75G/4.65G [02:12<03:22, 15.3MB/s]\u001b[A\n", " 38%|███▊      | 1.75G/4.65G [02:12<03:22, 15.3MB/s]\u001b[A\n", " 38%|███▊      | 1.76G/4.65G [02:12<03:23, 15.2MB/s]\u001b[A\n", " 38%|███▊      | 1.76G/4.65G [02:12<03:31, 14.6MB/s]\u001b[A\n", " 38%|███▊      | 1.76G/4.65G [02:12<03:28, 14.8MB/s]\u001b[A\n", " 38%|███▊      | 1.77G/4.65G [02:13<03:15, 15.8MB/s]\u001b[A\n", " 38%|███▊      | 1.77G/4.65G [02:13<05:11, 9.92MB/s]\u001b[A\n", " 38%|███▊      | 1.78G/4.65G [02:13<03:01, 17.0MB/s]\u001b[A\n", " 38%|███▊      | 1.78G/4.65G [02:14<03:04, 16.7MB/s]\u001b[A\n", " 38%|███▊      | 1.78G/4.65G [02:14<05:02, 10.1MB/s]\u001b[A\n", " 39%|███▊      | 1.79G/4.65G [02:15<03:07, 16.4MB/s]\u001b[A\n", " 39%|███▊      | 1.80G/4.65G [02:15<03:01, 16.9MB/s]\u001b[A\n", " 39%|███▉      | 1.80G/4.65G [02:15<02:56, 17.3MB/s]\u001b[A\n", " 39%|███▉      | 1.80G/4.65G [02:15<03:01, 16.8MB/s]\u001b[A\n", " 39%|███▉      | 1.81G/4.65G [02:15<02:44, 18.5MB/s]\u001b[A\n", " 39%|███▉      | 1.81G/4.65G [02:16<02:46, 18.2MB/s]\u001b[A\n", " 39%|███▉      | 1.81G/4.65G [02:16<05:49, 8.72MB/s]\u001b[A\n", " 39%|███▉      | 1.82G/4.65G [02:17<03:16, 15.4MB/s]\u001b[A\n", " 39%|███▉      | 1.82G/4.65G [02:17<03:16, 15.4MB/s]\u001b[A\n", " 39%|███▉      | 1.83G/4.65G [02:17<03:07, 16.2MB/s]\u001b[A\n", " 39%|███▉      | 1.83G/4.65G [02:17<02:59, 16.8MB/s]\u001b[A\n", " 39%|███▉      | 1.83G/4.65G [02:17<03:02, 16.5MB/s]\u001b[A\n", " 40%|███▉      | 1.84G/4.65G [02:18<03:24, 14.7MB/s]\u001b[A\n", " 40%|███▉      | 1.84G/4.65G [02:18<05:50, 8.59MB/s]\u001b[A\n", " 40%|███▉      | 1.85G/4.65G [02:19<02:58, 16.8MB/s]\u001b[A\n", " 40%|███▉      | 1.85G/4.65G [02:19<05:02, 9.91MB/s]\u001b[A\n", " 40%|████      | 1.86G/4.65G [02:20<03:22, 14.8MB/s]\u001b[A\n", " 40%|████      | 1.86G/4.65G [02:20<03:13, 15.4MB/s]\u001b[A\n", " 40%|████      | 1.87G/4.65G [02:20<03:12, 15.5MB/s]\u001b[A\n", " 40%|████      | 1.87G/4.65G [02:20<02:50, 17.5MB/s]\u001b[A\n", " 40%|████      | 1.87G/4.65G [02:20<02:56, 16.9MB/s]\u001b[A\n", " 40%|████      | 1.87G/4.65G [02:20<02:50, 17.5MB/s]\u001b[A\n", " 40%|████      | 1.88G/4.65G [02:21<02:56, 16.9MB/s]\u001b[A\n", " 40%|████      | 1.88G/4.65G [02:21<05:52, 8.44MB/s]\u001b[A\n", " 41%|████      | 1.89G/4.65G [02:22<03:11, 15.4MB/s]\u001b[A\n", " 41%|████      | 1.89G/4.65G [02:22<04:54, 10.1MB/s]\u001b[A\n", " 41%|████      | 1.90G/4.65G [02:23<03:26, 14.3MB/s]\u001b[A\n", " 41%|████      | 1.90G/4.65G [02:23<03:15, 15.1MB/s]\u001b[A\n", " 41%|████      | 1.90G/4.65G [02:23<03:02, 16.1MB/s]\u001b[A\n", " 41%|████      | 1.91G/4.65G [02:23<03:06, 15.8MB/s]\u001b[A\n", " 41%|████      | 1.91G/4.65G [02:23<03:02, 16.1MB/s]\u001b[A\n", " 41%|████      | 1.91G/4.65G [02:23<02:55, 16.7MB/s]\u001b[A\n", " 41%|████      | 1.91G/4.65G [02:23<03:28, 14.1MB/s]\u001b[A\n", " 41%|████      | 1.92G/4.65G [02:24<03:22, 14.4MB/s]\u001b[A\n", " 41%|████▏     | 1.92G/4.65G [02:24<05:53, 8.29MB/s]\u001b[A\n", " 41%|████▏     | 1.93G/4.65G [02:25<03:00, 16.2MB/s]\u001b[A\n", " 42%|████▏     | 1.93G/4.65G [02:25<04:48, 10.1MB/s]\u001b[A\n", " 42%|████▏     | 1.94G/4.65G [02:26<03:10, 15.3MB/s]\u001b[A\n", " 42%|████▏     | 1.94G/4.65G [02:26<03:03, 15.9MB/s]\u001b[A\n", " 42%|████▏     | 1.94G/4.65G [02:26<03:02, 15.9MB/s]\u001b[A\n", " 42%|████▏     | 1.95G/4.65G [02:26<02:45, 17.5MB/s]\u001b[A\n", " 42%|████▏     | 1.95G/4.65G [02:26<02:37, 18.4MB/s]\u001b[A\n", " 42%|████▏     | 1.95G/4.65G [02:26<02:45, 17.5MB/s]\u001b[A\n", " 42%|████▏     | 1.96G/4.65G [02:27<03:03, 15.8MB/s]\u001b[A\n", " 42%|████▏     | 1.96G/4.65G [02:27<06:15, 7.70MB/s]\u001b[A\n", " 42%|████▏     | 1.97G/4.65G [02:28<03:03, 15.7MB/s]\u001b[A\n", " 42%|████▏     | 1.97G/4.65G [02:28<04:40, 10.3MB/s]\u001b[A\n", " 43%|████▎     | 1.98G/4.65G [02:29<03:08, 15.2MB/s]\u001b[A\n", " 43%|████▎     | 1.98G/4.65G [02:29<02:59, 15.9MB/s]\u001b[A\n", " 43%|████▎     | 1.98G/4.65G [02:29<02:41, 17.7MB/s]\u001b[A\n", " 43%|████▎     | 1.99G/4.65G [02:29<02:46, 17.1MB/s]\u001b[A\n", " 43%|████▎     | 1.99G/4.65G [02:29<02:51, 16.6MB/s]\u001b[A\n", " 43%|████▎     | 1.99G/4.65G [02:29<03:14, 14.6MB/s]\u001b[A\n", " 43%|████▎     | 1.99G/4.65G [02:30<03:14, 14.7MB/s]\u001b[A\n", " 43%|████▎     | 2.00G/4.65G [02:30<06:15, 7.58MB/s]\u001b[A\n", " 43%|████▎     | 2.00G/4.65G [02:31<03:13, 14.7MB/s]\u001b[A\n", " 43%|████▎     | 2.01G/4.65G [02:31<04:53, 9.66MB/s]\u001b[A\n", " 43%|████▎     | 2.02G/4.65G [02:32<03:13, 14.6MB/s]\u001b[A\n", " 43%|████▎     | 2.02G/4.65G [02:32<03:04, 15.3MB/s]\u001b[A\n", " 44%|████▎     | 2.02G/4.65G [02:32<02:58, 15.8MB/s]\u001b[A\n", " 44%|████▎     | 2.02G/4.65G [02:32<02:58, 15.8MB/s]\u001b[A\n", " 44%|████▎     | 2.03G/4.65G [02:32<02:53, 16.3MB/s]\u001b[A\n", " 44%|████▎     | 2.03G/4.65G [02:32<02:46, 16.9MB/s]\u001b[A\n", " 44%|████▎     | 2.03G/4.65G [02:32<02:53, 16.2MB/s]\u001b[A\n", " 44%|████▍     | 2.03G/4.65G [02:33<02:48, 16.7MB/s]\u001b[A\n", " 44%|████▍     | 2.04G/4.65G [02:33<06:09, 7.58MB/s]\u001b[A\n", " 44%|████▍     | 2.04G/4.65G [02:34<02:55, 15.9MB/s]\u001b[A\n", " 44%|████▍     | 2.05G/4.65G [02:34<04:30, 10.3MB/s]\u001b[A\n", " 44%|████▍     | 2.06G/4.65G [02:35<03:01, 15.4MB/s]\u001b[A\n", " 44%|████▍     | 2.06G/4.65G [02:35<02:55, 15.9MB/s]\u001b[A\n", " 44%|████▍     | 2.06G/4.65G [02:35<03:09, 14.7MB/s]\u001b[A\n", " 44%|████▍     | 2.07G/4.65G [02:35<02:56, 15.7MB/s]\u001b[A\n", " 45%|████▍     | 2.07G/4.65G [02:35<02:57, 15.6MB/s]\u001b[A\n", " 45%|████▍     | 2.07G/4.65G [02:36<02:52, 16.0MB/s]\u001b[A\n", " 45%|████▍     | 2.07G/4.65G [02:36<02:54, 15.8MB/s]\u001b[A\n", " 45%|████▍     | 2.08G/4.65G [02:36<04:54, 9.36MB/s]\u001b[A\n", " 45%|████▍     | 2.08G/4.65G [02:37<02:54, 15.8MB/s]\u001b[A\n", " 45%|████▍     | 2.09G/4.65G [02:37<04:55, 9.29MB/s]\u001b[A\n", " 45%|████▌     | 2.09G/4.65G [02:38<03:08, 14.6MB/s]\u001b[A\n", " 45%|████▌     | 2.10G/4.65G [02:38<03:00, 15.2MB/s]\u001b[A\n", " 45%|████▌     | 2.10G/4.65G [02:38<02:47, 16.4MB/s]\u001b[A\n", " 45%|████▌     | 2.10G/4.65G [02:38<02:30, 18.1MB/s]\u001b[A\n", " 45%|████▌     | 2.11G/4.65G [02:38<02:37, 17.3MB/s]\u001b[A\n", " 45%|████▌     | 2.11G/4.65G [02:38<02:42, 16.7MB/s]\u001b[A\n", " 45%|████▌     | 2.11G/4.65G [02:39<02:44, 16.6MB/s]\u001b[A\n", " 46%|████▌     | 2.11G/4.65G [02:39<02:23, 18.9MB/s]\u001b[A\n", " 46%|████▌     | 2.12G/4.65G [02:39<04:44, 9.56MB/s]\u001b[A\n", " 46%|████▌     | 2.12G/4.65G [02:40<02:40, 16.9MB/s]\u001b[A\n", " 46%|████▌     | 2.13G/4.65G [02:40<04:22, 10.3MB/s]\u001b[A\n", " 46%|████▌     | 2.13G/4.65G [02:40<03:03, 14.7MB/s]\u001b[A\n", " 46%|████▌     | 2.14G/4.65G [02:41<02:53, 15.5MB/s]\u001b[A\n", " 46%|████▌     | 2.14G/4.65G [02:41<04:26, 10.1MB/s]\u001b[A\n", " 46%|████▋     | 2.15G/4.65G [02:42<02:57, 15.1MB/s]\u001b[A\n", " 46%|████▋     | 2.15G/4.65G [02:42<04:25, 10.1MB/s]\u001b[A\n", " 47%|████▋     | 2.16G/4.65G [02:43<03:03, 14.6MB/s]\u001b[A\n", " 47%|████▋     | 2.16G/4.65G [02:43<03:01, 14.7MB/s]\u001b[A\n", " 47%|████▋     | 2.17G/4.65G [02:43<02:50, 15.6MB/s]\u001b[A\n", " 47%|████▋     | 2.17G/4.65G [02:43<02:51, 15.5MB/s]\u001b[A\n", " 47%|████▋     | 2.17G/4.65G [02:43<02:53, 15.3MB/s]\u001b[A\n", " 47%|████▋     | 2.17G/4.65G [02:44<03:01, 14.6MB/s]\u001b[A\n", " 47%|████▋     | 2.18G/4.65G [02:44<02:53, 15.2MB/s]\u001b[A\n", " 47%|████▋     | 2.18G/4.65G [02:44<05:19, 8.28MB/s]\u001b[A\n", " 47%|████▋     | 2.19G/4.65G [02:45<02:44, 16.0MB/s]\u001b[A\n", " 47%|████▋     | 2.19G/4.65G [02:45<02:37, 16.7MB/s]\u001b[A\n", " 47%|████▋     | 2.20G/4.65G [02:45<02:32, 17.2MB/s]\u001b[A\n", " 47%|████▋     | 2.20G/4.65G [02:45<02:37, 16.7MB/s]\u001b[A\n", " 47%|████▋     | 2.20G/4.65G [02:45<02:22, 18.4MB/s]\u001b[A\n", " 47%|████▋     | 2.20G/4.65G [02:46<02:19, 18.8MB/s]\u001b[A\n", " 47%|████▋     | 2.21G/4.65G [02:46<05:08, 8.49MB/s]\u001b[A\n", " 48%|████▊     | 2.21G/4.65G [02:47<02:58, 14.6MB/s]\u001b[A\n", " 48%|████▊     | 2.22G/4.65G [02:47<02:51, 15.2MB/s]\u001b[A\n", " 48%|████▊     | 2.22G/4.65G [02:47<02:36, 16.6MB/s]\u001b[A\n", " 48%|████▊     | 2.22G/4.65G [02:47<02:21, 18.4MB/s]\u001b[A\n", " 48%|████▊     | 2.23G/4.65G [02:47<02:28, 17.5MB/s]\u001b[A\n", " 48%|████▊     | 2.23G/4.65G [02:47<02:44, 15.8MB/s]\u001b[A\n", " 48%|████▊     | 2.23G/4.65G [02:48<02:49, 15.3MB/s]\u001b[A\n", " 48%|████▊     | 2.23G/4.65G [02:48<03:03, 14.1MB/s]\u001b[A\n", " 48%|████▊     | 2.23G/4.65G [02:48<05:23, 8.00MB/s]\u001b[A\n", " 48%|████▊     | 2.24G/4.65G [02:49<02:50, 15.1MB/s]\u001b[A\n", " 48%|████▊     | 2.24G/4.65G [02:49<04:52, 8.82MB/s]\u001b[A\n", " 48%|████▊     | 2.25G/4.65G [02:50<02:59, 14.3MB/s]\u001b[A\n", " 49%|████▊     | 2.25G/4.65G [02:50<02:42, 15.8MB/s]\u001b[A\n", " 49%|████▊     | 2.26G/4.65G [02:50<02:32, 16.8MB/s]\u001b[A\n", " 49%|████▊     | 2.26G/4.65G [02:50<02:34, 16.5MB/s]\u001b[A\n", " 49%|████▊     | 2.26G/4.65G [02:50<02:37, 16.2MB/s]\u001b[A\n", " 49%|████▉     | 2.27G/4.65G [02:50<02:39, 16.0MB/s]\u001b[A\n", " 49%|████▉     | 2.27G/4.65G [02:50<02:23, 17.8MB/s]\u001b[A\n", " 49%|████▉     | 2.27G/4.65G [02:51<02:23, 17.7MB/s]\u001b[A\n", " 49%|████▉     | 2.27G/4.65G [02:51<05:16, 8.04MB/s]\u001b[A\n", " 49%|████▉     | 2.28G/4.65G [02:52<02:43, 15.5MB/s]\u001b[A\n", " 49%|████▉     | 2.29G/4.65G [02:52<04:14, 9.97MB/s]\u001b[A\n", " 49%|████▉     | 2.29G/4.65G [02:53<02:56, 14.3MB/s]\u001b[A\n", " 49%|████▉     | 2.29G/4.65G [02:53<02:49, 14.9MB/s]\u001b[A\n", " 49%|████▉     | 2.30G/4.65G [02:53<02:59, 14.1MB/s]\u001b[A\n", " 50%|████▉     | 2.30G/4.65G [02:53<02:45, 15.2MB/s]\u001b[A\n", " 50%|████▉     | 2.30G/4.65G [02:54<02:44, 15.2MB/s]\u001b[A\n", " 50%|████▉     | 2.31G/4.65G [02:54<02:59, 14.0MB/s]\u001b[A\n", " 50%|████▉     | 2.31G/4.65G [02:54<02:51, 14.7MB/s]\u001b[A\n", " 50%|████▉     | 2.31G/4.65G [02:54<03:02, 13.7MB/s]\u001b[A\n", " 50%|████▉     | 2.31G/4.65G [02:54<02:33, 16.4MB/s]\u001b[A\n", " 50%|████▉     | 2.32G/4.65G [02:54<02:37, 15.9MB/s]\u001b[A\n", " 50%|████▉     | 2.32G/4.65G [02:54<02:39, 15.7MB/s]\u001b[A\n", " 50%|████▉     | 2.32G/4.65G [02:55<02:35, 16.1MB/s]\u001b[A\n", " 50%|█████     | 2.32G/4.65G [02:55<05:35, 7.43MB/s]\u001b[A\n", " 50%|█████     | 2.33G/4.65G [02:56<02:46, 14.9MB/s]\u001b[A\n", " 50%|█████     | 2.33G/4.65G [02:56<02:39, 15.5MB/s]\u001b[A\n", " 50%|█████     | 2.34G/4.65G [02:56<02:30, 16.5MB/s]\u001b[A\n", " 50%|█████     | 2.34G/4.65G [02:56<02:32, 16.2MB/s]\u001b[A\n", " 50%|█████     | 2.35G/4.65G [02:57<02:26, 16.9MB/s]\u001b[A\n", " 51%|█████     | 2.35G/4.65G [02:57<02:53, 14.2MB/s]\u001b[A\n", " 51%|█████     | 2.35G/4.65G [02:57<02:25, 17.0MB/s]\u001b[A\n", " 51%|█████     | 2.36G/4.65G [02:57<02:26, 16.7MB/s]\u001b[A\n", " 51%|█████     | 2.36G/4.65G [02:57<02:46, 14.8MB/s]\u001b[A\n", " 51%|█████     | 2.36G/4.65G [02:58<02:43, 15.0MB/s]\u001b[A\n", " 51%|█████     | 2.36G/4.65G [02:58<05:35, 7.30MB/s]\u001b[A\n", " 51%|█████     | 2.37G/4.65G [02:59<02:54, 14.0MB/s]\u001b[A\n", " 51%|█████     | 2.38G/4.65G [02:59<04:16, 9.50MB/s]\u001b[A\n", " 51%|█████▏    | 2.38G/4.65G [03:00<02:40, 15.1MB/s]\u001b[A\n", " 51%|█████▏    | 2.39G/4.65G [03:00<02:40, 15.1MB/s]\u001b[A\n", " 51%|█████▏    | 2.39G/4.65G [03:00<02:46, 14.5MB/s]\u001b[A\n", " 51%|█████▏    | 2.39G/4.65G [03:00<02:49, 14.3MB/s]\u001b[A\n", " 52%|█████▏    | 2.39G/4.65G [03:00<02:46, 14.6MB/s]\u001b[A\n", " 52%|█████▏    | 2.40G/4.65G [03:01<02:33, 15.7MB/s]\u001b[A\n", " 52%|█████▏    | 2.40G/4.65G [03:01<05:05, 7.90MB/s]\u001b[A\n", " 52%|█████▏    | 2.41G/4.65G [03:02<02:46, 14.4MB/s]\u001b[A\n", " 52%|█████▏    | 2.41G/4.65G [03:02<04:06, 9.73MB/s]\u001b[A\n", " 52%|█████▏    | 2.42G/4.65G [03:03<02:43, 14.6MB/s]\u001b[A\n", " 52%|█████▏    | 2.42G/4.65G [03:03<02:37, 15.1MB/s]\u001b[A\n", " 52%|█████▏    | 2.43G/4.65G [03:03<02:23, 16.6MB/s]\u001b[A\n", " 52%|█████▏    | 2.43G/4.65G [03:03<02:19, 17.0MB/s]\u001b[A\n", " 52%|█████▏    | 2.43G/4.65G [03:03<02:23, 16.6MB/s]\u001b[A\n", " 52%|█████▏    | 2.43G/4.65G [03:03<02:26, 16.2MB/s]\u001b[A\n", " 52%|█████▏    | 2.44G/4.65G [03:04<02:19, 17.0MB/s]\u001b[A\n", " 53%|█████▎    | 2.44G/4.65G [03:04<05:11, 7.60MB/s]\u001b[A\n", " 53%|█████▎    | 2.45G/4.65G [03:05<02:36, 15.1MB/s]\u001b[A\n", " 53%|█████▎    | 2.45G/4.65G [03:05<03:51, 10.2MB/s]\u001b[A\n", " 53%|█████▎    | 2.46G/4.65G [03:06<02:36, 15.0MB/s]\u001b[A\n", " 53%|█████▎    | 2.46G/4.65G [03:06<03:48, 10.3MB/s]\u001b[A\n", " 53%|█████▎    | 2.47G/4.65G [03:07<02:37, 14.8MB/s]\u001b[A\n", " 53%|█████▎    | 2.47G/4.65G [03:07<02:25, 16.0MB/s]\u001b[A\n", " 53%|█████▎    | 2.48G/4.65G [03:07<02:23, 16.2MB/s]\u001b[A\n", " 53%|█████▎    | 2.48G/4.65G [03:07<02:18, 16.8MB/s]\u001b[A\n", " 53%|█████▎    | 2.48G/4.65G [03:07<02:21, 16.5MB/s]\u001b[A\n", " 53%|█████▎    | 2.48G/4.65G [03:07<02:32, 15.2MB/s]\u001b[A\n", " 54%|█████▎    | 2.49G/4.65G [03:08<02:10, 17.7MB/s]\u001b[A\n", " 54%|█████▎    | 2.49G/4.65G [03:08<04:45, 8.11MB/s]\u001b[A\n", " 54%|█████▍    | 2.50G/4.65G [03:09<02:46, 13.9MB/s]\u001b[A\n", " 54%|█████▍    | 2.50G/4.65G [03:09<04:05, 9.40MB/s]\u001b[A\n", " 54%|█████▍    | 2.51G/4.65G [03:10<02:35, 14.7MB/s]\u001b[A\n", " 54%|█████▍    | 2.51G/4.65G [03:10<02:30, 15.2MB/s]\u001b[A\n", " 54%|█████▍    | 2.52G/4.65G [03:10<02:22, 16.0MB/s]\u001b[A\n", " 54%|█████▍    | 2.52G/4.65G [03:10<02:21, 16.2MB/s]\u001b[A\n", " 54%|█████▍    | 2.52G/4.65G [03:10<02:17, 16.5MB/s]\u001b[A\n", " 54%|█████▍    | 2.53G/4.65G [03:11<02:02, 18.6MB/s]\u001b[A\n", " 54%|█████▍    | 2.53G/4.65G [03:11<04:17, 8.81MB/s]\u001b[A\n", " 55%|█████▍    | 2.54G/4.65G [03:12<02:39, 14.2MB/s]\u001b[A\n", " 55%|█████▍    | 2.54G/4.65G [03:12<03:51, 9.77MB/s]\u001b[A\n", " 55%|█████▍    | 2.55G/4.65G [03:13<02:34, 14.6MB/s]\u001b[A\n", " 55%|█████▍    | 2.55G/4.65G [03:13<02:27, 15.3MB/s]\u001b[A\n", " 55%|█████▍    | 2.55G/4.65G [03:13<02:18, 16.3MB/s]\u001b[A\n", " 55%|█████▌    | 2.56G/4.65G [03:13<02:36, 14.3MB/s]\u001b[A\n", " 55%|█████▌    | 2.56G/4.65G [03:13<02:33, 14.6MB/s]\u001b[A\n", " 55%|█████▌    | 2.56G/4.65G [03:14<02:22, 15.7MB/s]\u001b[A\n", " 55%|█████▌    | 2.57G/4.65G [03:14<03:57, 9.40MB/s]\u001b[A\n", " 55%|█████▌    | 2.57G/4.65G [03:14<02:29, 14.9MB/s]\u001b[A\n", " 55%|█████▌    | 2.58G/4.65G [03:15<02:48, 13.2MB/s]\u001b[A\n", " 55%|█████▌    | 2.58G/4.65G [03:15<04:19, 8.57MB/s]\u001b[A\n", " 56%|█████▌    | 2.59G/4.65G [03:16<02:46, 13.3MB/s]\u001b[A\n", " 56%|█████▌    | 2.59G/4.65G [03:16<02:37, 14.0MB/s]\u001b[A\n", " 56%|█████▌    | 2.59G/4.65G [03:16<02:43, 13.5MB/s]\u001b[A\n", " 56%|█████▌    | 2.59G/4.65G [03:16<02:22, 15.4MB/s]\u001b[A\n", " 56%|█████▌    | 2.59G/4.65G [03:16<02:19, 15.8MB/s]\u001b[A\n", " 56%|█████▌    | 2.60G/4.65G [03:16<02:17, 16.0MB/s]\u001b[A\n", " 56%|█████▌    | 2.60G/4.65G [03:17<02:10, 16.9MB/s]\u001b[A\n", " 56%|█████▌    | 2.60G/4.65G [03:17<05:37, 6.50MB/s]\u001b[A\n", " 56%|█████▌    | 2.61G/4.65G [03:18<02:37, 13.9MB/s]\u001b[A\n", " 56%|█████▌    | 2.61G/4.65G [03:18<03:56, 9.23MB/s]\u001b[A\n", " 56%|█████▋    | 2.62G/4.65G [03:19<02:32, 14.3MB/s]\u001b[A\n", " 56%|█████▋    | 2.62G/4.65G [03:19<02:30, 14.5MB/s]\u001b[A\n", " 57%|█████▋    | 2.63G/4.65G [03:19<02:17, 15.7MB/s]\u001b[A\n", " 57%|█████▋    | 2.63G/4.65G [03:19<02:18, 15.7MB/s]\u001b[A\n", " 57%|█████▋    | 2.63G/4.65G [03:19<02:12, 16.3MB/s]\u001b[A\n", " 57%|█████▋    | 2.63G/4.65G [03:19<02:10, 16.6MB/s]\u001b[A\n", " 57%|█████▋    | 2.63G/4.65G [03:19<02:05, 17.2MB/s]\u001b[A\n", " 57%|█████▋    | 2.64G/4.65G [03:20<02:04, 17.3MB/s]\u001b[A\n", " 57%|█████▋    | 2.64G/4.65G [03:20<05:22, 6.67MB/s]\u001b[A\n", " 57%|█████▋    | 2.65G/4.65G [03:21<02:23, 15.0MB/s]\u001b[A\n", " 57%|█████▋    | 2.65G/4.65G [03:21<03:36, 9.88MB/s]\u001b[A\n", " 57%|█████▋    | 2.66G/4.65G [03:22<02:22, 15.0MB/s]\u001b[A\n", " 57%|█████▋    | 2.66G/4.65G [03:22<02:17, 15.5MB/s]\u001b[A\n", " 57%|█████▋    | 2.67G/4.65G [03:22<02:07, 16.6MB/s]\u001b[A\n", " 57%|█████▋    | 2.67G/4.65G [03:22<01:55, 18.4MB/s]\u001b[A\n", " 58%|█████▊    | 2.67G/4.65G [03:22<02:00, 17.5MB/s]\u001b[A\n", " 58%|█████▊    | 2.67G/4.65G [03:22<02:05, 16.9MB/s]\u001b[A\n", " 58%|█████▊    | 2.68G/4.65G [03:23<02:06, 16.7MB/s]\u001b[A\n", " 58%|█████▊    | 2.68G/4.65G [03:23<04:51, 7.25MB/s]\u001b[A\n", " 58%|█████▊    | 2.69G/4.65G [03:24<02:21, 14.9MB/s]\u001b[A\n", " 58%|█████▊    | 2.69G/4.65G [03:24<03:17, 10.6MB/s]\u001b[A\n", " 58%|█████▊    | 2.70G/4.65G [03:25<02:13, 15.7MB/s]\u001b[A\n", " 58%|█████▊    | 2.70G/4.65G [03:25<02:03, 16.8MB/s]\u001b[A\n", " 58%|█████▊    | 2.71G/4.65G [03:25<01:56, 17.9MB/s]\u001b[A\n", " 58%|█████▊    | 2.71G/4.65G [03:25<03:05, 11.2MB/s]\u001b[A\n", " 58%|█████▊    | 2.71G/4.65G [03:26<03:10, 10.9MB/s]\u001b[A\n", " 58%|█████▊    | 2.71G/4.65G [03:26<05:02, 6.85MB/s]\u001b[A\n", " 59%|█████▊    | 2.72G/4.65G [03:27<02:36, 13.2MB/s]\u001b[A\n", " 59%|█████▊    | 2.72G/4.65G [03:27<03:43, 9.23MB/s]\u001b[A\n", " 59%|█████▉    | 2.73G/4.65G [03:28<02:27, 14.0MB/s]\u001b[A\n", " 59%|█████▉    | 2.73G/4.65G [03:28<02:24, 14.2MB/s]\u001b[A\n", " 59%|█████▉    | 2.74G/4.65G [03:28<02:13, 15.3MB/s]\u001b[A\n", " 59%|█████▉    | 2.74G/4.65G [03:28<02:13, 15.3MB/s]\u001b[A\n", " 59%|█████▉    | 2.74G/4.65G [03:28<02:17, 14.9MB/s]\u001b[A\n", " 59%|█████▉    | 2.75G/4.65G [03:29<02:16, 15.0MB/s]\u001b[A\n", " 59%|█████▉    | 2.75G/4.65G [03:29<04:37, 7.35MB/s]\u001b[A\n", " 59%|█████▉    | 2.76G/4.65G [03:30<02:26, 13.8MB/s]\u001b[A\n", " 59%|█████▉    | 2.76G/4.65G [03:30<03:33, 9.47MB/s]\u001b[A\n", " 60%|█████▉    | 2.77G/4.65G [03:31<02:20, 14.3MB/s]\u001b[A\n", " 60%|█████▉    | 2.77G/4.65G [03:31<02:09, 15.6MB/s]\u001b[A\n", " 60%|█████▉    | 2.77G/4.65G [03:31<02:24, 13.9MB/s]\u001b[A\n", " 60%|█████▉    | 2.78G/4.65G [03:31<02:13, 15.0MB/s]\u001b[A\n", " 60%|█████▉    | 2.78G/4.65G [03:31<02:12, 15.1MB/s]\u001b[A\n", " 60%|█████▉    | 2.78G/4.65G [03:32<02:04, 16.1MB/s]\u001b[A\n", " 60%|█████▉    | 2.79G/4.65G [03:32<03:54, 8.51MB/s]\u001b[A\n", " 60%|██████    | 2.79G/4.65G [03:33<02:11, 15.1MB/s]\u001b[A\n", " 60%|██████    | 2.80G/4.65G [03:33<03:18, 9.99MB/s]\u001b[A\n", " 60%|██████    | 2.81G/4.65G [03:34<02:13, 14.8MB/s]\u001b[A\n", " 60%|██████    | 2.81G/4.65G [03:34<02:07, 15.5MB/s]\u001b[A\n", " 61%|██████    | 2.81G/4.65G [03:34<02:06, 15.6MB/s]\u001b[A\n", " 61%|██████    | 2.82G/4.65G [03:34<02:00, 16.3MB/s]\u001b[A\n", " 61%|██████    | 2.82G/4.65G [03:34<01:55, 17.0MB/s]\u001b[A\n", " 61%|██████    | 2.82G/4.65G [03:34<01:44, 18.7MB/s]\u001b[A\n", " 61%|██████    | 2.83G/4.65G [03:35<01:48, 18.0MB/s]\u001b[A\n", " 61%|██████    | 2.83G/4.65G [03:35<03:42, 8.78MB/s]\u001b[A\n", " 61%|██████    | 2.84G/4.65G [03:36<02:03, 15.8MB/s]\u001b[A\n", " 61%|██████    | 2.84G/4.65G [03:36<03:10, 10.2MB/s]\u001b[A\n", " 61%|██████▏   | 2.85G/4.65G [03:37<02:08, 15.0MB/s]\u001b[A\n", " 61%|██████▏   | 2.85G/4.65G [03:37<02:07, 15.1MB/s]\u001b[A\n", " 61%|██████▏   | 2.85G/4.65G [03:37<02:09, 14.9MB/s]\u001b[A\n", " 61%|██████▏   | 2.85G/4.65G [03:37<02:32, 12.6MB/s]\u001b[A\n", " 61%|██████▏   | 2.86G/4.65G [03:37<02:24, 13.3MB/s]\u001b[A\n", " 62%|██████▏   | 2.86G/4.65G [03:37<02:13, 14.4MB/s]\u001b[A\n", " 62%|██████▏   | 2.86G/4.65G [03:38<02:30, 12.8MB/s]\u001b[A\n", " 62%|██████▏   | 2.86G/4.65G [03:38<04:39, 6.84MB/s]\u001b[A\n", " 62%|██████▏   | 2.87G/4.65G [03:39<02:13, 14.3MB/s]\u001b[A\n", " 62%|██████▏   | 2.87G/4.65G [03:39<03:22, 9.38MB/s]\u001b[A\n", " 62%|██████▏   | 2.88G/4.65G [03:40<02:01, 15.6MB/s]\u001b[A\n", " 62%|██████▏   | 2.88G/4.65G [03:40<01:51, 16.9MB/s]\u001b[A\n", " 62%|██████▏   | 2.89G/4.65G [03:40<01:48, 17.3MB/s]\u001b[A\n", " 62%|██████▏   | 2.89G/4.65G [03:40<01:48, 17.4MB/s]\u001b[A\n", " 62%|██████▏   | 2.89G/4.65G [03:40<01:52, 16.8MB/s]\u001b[A\n", " 62%|██████▏   | 2.90G/4.65G [03:40<01:52, 16.7MB/s]\u001b[A\n", " 62%|██████▏   | 2.90G/4.65G [03:41<01:55, 16.3MB/s]\u001b[A\n", " 62%|██████▏   | 2.90G/4.65G [03:41<04:13, 7.38MB/s]\u001b[A\n", " 63%|██████▎   | 2.91G/4.65G [03:42<02:09, 14.4MB/s]\u001b[A\n", " 63%|██████▎   | 2.91G/4.65G [03:42<03:14, 9.56MB/s]\u001b[A\n", " 63%|██████▎   | 2.92G/4.65G [03:43<02:07, 14.6MB/s]\u001b[A\n", " 63%|██████▎   | 2.92G/4.65G [03:43<01:56, 15.9MB/s]\u001b[A\n", " 63%|██████▎   | 2.93G/4.65G [03:43<01:48, 16.9MB/s]\u001b[A\n", " 63%|██████▎   | 2.93G/4.65G [03:43<01:51, 16.5MB/s]\u001b[A\n", " 63%|██████▎   | 2.93G/4.65G [03:43<01:53, 16.2MB/s]\u001b[A\n", " 63%|██████▎   | 2.93G/4.65G [03:43<01:54, 16.0MB/s]\u001b[A\n", " 63%|██████▎   | 2.94G/4.65G [03:44<01:40, 18.2MB/s]\u001b[A\n", " 63%|██████▎   | 2.94G/4.65G [03:44<03:36, 8.47MB/s]\u001b[A\n", " 63%|██████▎   | 2.95G/4.65G [03:45<02:07, 14.2MB/s]\u001b[A\n", " 64%|██████▎   | 2.95G/4.65G [03:45<03:09, 9.62MB/s]\u001b[A\n", " 64%|██████▎   | 2.96G/4.65G [03:46<02:04, 14.6MB/s]\u001b[A\n", " 64%|██████▍   | 2.96G/4.65G [03:46<01:53, 15.9MB/s]\u001b[A\n", " 64%|██████▍   | 2.96G/4.65G [03:46<01:50, 16.4MB/s]\u001b[A\n", " 64%|██████▍   | 2.97G/4.65G [03:46<01:57, 15.3MB/s]\u001b[A\n", " 64%|██████▍   | 2.97G/4.65G [03:46<01:53, 15.9MB/s]\u001b[A\n", " 64%|██████▍   | 2.97G/4.65G [03:46<02:09, 13.9MB/s]\u001b[A\n", " 64%|██████▍   | 2.97G/4.65G [03:47<02:05, 14.3MB/s]\u001b[A\n", " 64%|██████▍   | 2.98G/4.65G [03:47<04:07, 7.24MB/s]\u001b[A\n", " 64%|██████▍   | 2.99G/4.65G [03:48<02:04, 14.3MB/s]\u001b[A\n", " 64%|██████▍   | 2.99G/4.65G [03:48<03:05, 9.59MB/s]\u001b[A\n", " 65%|██████▍   | 3.00G/4.65G [03:49<01:55, 15.3MB/s]\u001b[A\n", " 65%|██████▍   | 3.00G/4.65G [03:49<01:55, 15.3MB/s]\u001b[A\n", " 65%|██████▍   | 3.00G/4.65G [03:49<01:53, 15.6MB/s]\u001b[A\n", " 65%|██████▍   | 3.01G/4.65G [03:49<01:53, 15.5MB/s]\u001b[A\n", " 65%|██████▍   | 3.01G/4.65G [03:49<01:40, 17.4MB/s]\u001b[A\n", " 65%|██████▍   | 3.01G/4.65G [03:49<01:44, 16.8MB/s]\u001b[A\n", " 65%|██████▍   | 3.01G/4.65G [03:50<01:45, 16.7MB/s]\u001b[A\n", " 65%|██████▍   | 3.02G/4.65G [03:50<03:51, 7.56MB/s]\u001b[A\n", " 65%|██████▌   | 3.02G/4.65G [03:51<01:58, 14.7MB/s]\u001b[A\n", " 65%|██████▌   | 3.03G/4.65G [03:51<02:59, 9.68MB/s]\u001b[A\n", " 65%|██████▌   | 3.04G/4.65G [03:52<01:58, 14.6MB/s]\u001b[A\n", " 65%|██████▌   | 3.04G/4.65G [03:52<01:52, 15.4MB/s]\u001b[A\n", " 65%|██████▌   | 3.04G/4.65G [03:52<01:39, 17.2MB/s]\u001b[A\n", " 66%|██████▌   | 3.04G/4.65G [03:52<01:42, 16.7MB/s]\u001b[A\n", " 66%|██████▌   | 3.05G/4.65G [03:52<01:44, 16.4MB/s]\u001b[A\n", " 66%|██████▌   | 3.05G/4.65G [03:52<01:53, 15.0MB/s]\u001b[A\n", " 66%|██████▌   | 3.05G/4.65G [03:53<01:45, 16.2MB/s]\u001b[A\n", " 66%|██████▌   | 3.05G/4.65G [03:53<03:31, 8.09MB/s]\u001b[A\n", " 66%|██████▌   | 3.06G/4.65G [03:54<01:52, 15.1MB/s]\u001b[A\n", " 66%|██████▌   | 3.07G/4.65G [03:54<02:52, 9.83MB/s]\u001b[A\n", " 66%|██████▌   | 3.07G/4.65G [03:55<01:54, 14.7MB/s]\u001b[A\n", " 66%|██████▌   | 3.08G/4.65G [03:55<01:57, 14.3MB/s]\u001b[A\n", " 66%|██████▋   | 3.08G/4.65G [03:55<02:06, 13.3MB/s]\u001b[A\n", " 66%|██████▋   | 3.08G/4.65G [03:55<02:02, 13.7MB/s]\u001b[A\n", " 66%|██████▋   | 3.08G/4.65G [03:55<01:58, 14.1MB/s]\u001b[A\n", " 66%|██████▋   | 3.09G/4.65G [03:55<01:41, 16.5MB/s]\u001b[A\n", " 66%|██████▋   | 3.09G/4.65G [03:56<01:51, 15.0MB/s]\u001b[A\n", " 67%|██████▋   | 3.09G/4.65G [03:56<03:46, 7.38MB/s]\u001b[A\n", " 67%|██████▋   | 3.10G/4.65G [03:57<01:53, 14.7MB/s]\u001b[A\n", " 67%|██████▋   | 3.10G/4.65G [03:57<02:50, 9.70MB/s]\u001b[A\n", " 67%|██████▋   | 3.11G/4.65G [03:58<01:52, 14.7MB/s]\u001b[A\n", " 67%|██████▋   | 3.11G/4.65G [03:58<01:51, 14.8MB/s]\u001b[A\n", " 67%|██████▋   | 3.12G/4.65G [03:58<01:44, 15.7MB/s]\u001b[A\n", " 67%|██████▋   | 3.12G/4.65G [03:58<01:39, 16.4MB/s]\u001b[A\n", " 67%|██████▋   | 3.12G/4.65G [03:58<01:41, 16.2MB/s]\u001b[A\n", " 67%|██████▋   | 3.13G/4.65G [03:59<01:36, 16.9MB/s]\u001b[A\n", " 67%|██████▋   | 3.13G/4.65G [03:59<02:57, 9.19MB/s]\u001b[A\n", " 68%|██████▊   | 3.14G/4.65G [04:00<01:42, 15.8MB/s]\u001b[A\n", " 68%|██████▊   | 3.14G/4.65G [04:00<02:35, 10.4MB/s]\u001b[A\n", " 68%|██████▊   | 3.15G/4.65G [04:01<01:38, 16.3MB/s]\u001b[A\n", " 68%|██████▊   | 3.15G/4.65G [04:01<01:49, 14.6MB/s]\u001b[A\n", " 68%|██████▊   | 3.16G/4.65G [04:01<01:51, 14.3MB/s]\u001b[A\n", " 68%|██████▊   | 3.16G/4.65G [04:01<01:49, 14.5MB/s]\u001b[A\n", " 68%|██████▊   | 3.16G/4.65G [04:01<01:55, 13.8MB/s]\u001b[A\n", " 68%|██████▊   | 3.16G/4.65G [04:02<01:44, 15.2MB/s]\u001b[A\n", " 68%|██████▊   | 3.17G/4.65G [04:02<03:16, 8.08MB/s]\u001b[A\n", " 68%|██████▊   | 3.17G/4.65G [04:03<01:47, 14.7MB/s]\u001b[A\n", " 68%|██████▊   | 3.18G/4.65G [04:03<02:39, 9.86MB/s]\u001b[A\n", " 69%|██████▊   | 3.19G/4.65G [04:04<01:45, 14.8MB/s]\u001b[A\n", " 69%|██████▊   | 3.19G/4.65G [04:04<01:36, 16.1MB/s]\u001b[A\n", " 69%|██████▊   | 3.19G/4.65G [04:04<01:31, 17.1MB/s]\u001b[A\n", " 69%|██████▉   | 3.19G/4.65G [04:04<01:48, 14.4MB/s]\u001b[A\n", " 69%|██████▉   | 3.20G/4.65G [04:04<01:49, 14.2MB/s]\u001b[A\n", " 69%|██████▉   | 3.20G/4.65G [04:04<01:46, 14.5MB/s]\u001b[A\n", " 69%|██████▉   | 3.20G/4.65G [04:05<01:31, 16.9MB/s]\u001b[A\n", " 69%|██████▉   | 3.20G/4.65G [04:05<03:16, 7.86MB/s]\u001b[A\n", " 69%|██████▉   | 3.21G/4.65G [04:06<01:43, 14.9MB/s]\u001b[A\n", " 69%|██████▉   | 3.22G/4.65G [04:06<02:36, 9.83MB/s]\u001b[A\n", " 69%|██████▉   | 3.22G/4.65G [04:07<01:42, 14.9MB/s]\u001b[A\n", " 69%|██████▉   | 3.23G/4.65G [04:07<01:34, 16.1MB/s]\u001b[A\n", " 70%|██████▉   | 3.23G/4.65G [04:07<01:27, 17.3MB/s]\u001b[A\n", " 70%|██████▉   | 3.23G/4.65G [04:07<01:30, 16.8MB/s]\u001b[A\n", " 70%|██████▉   | 3.24G/4.65G [04:07<01:32, 16.4MB/s]\u001b[A\n", " 70%|██████▉   | 3.24G/4.65G [04:07<01:33, 16.1MB/s]\u001b[A\n", " 70%|██████▉   | 3.24G/4.65G [04:07<01:23, 18.0MB/s]\u001b[A\n", " 70%|██████▉   | 3.24G/4.65G [04:08<01:37, 15.5MB/s]\u001b[A\n", " 70%|██████▉   | 3.25G/4.65G [04:08<03:02, 8.22MB/s]\u001b[A\n", " 70%|███████   | 3.25G/4.65G [04:09<01:33, 15.9MB/s]\u001b[A\n", " 70%|███████   | 3.26G/4.65G [04:09<02:27, 10.1MB/s]\u001b[A\n", " 70%|███████   | 3.26G/4.65G [04:10<01:37, 15.2MB/s]\u001b[A\n", " 70%|███████   | 3.27G/4.65G [04:10<01:37, 15.2MB/s]\u001b[A\n", " 70%|███████   | 3.27G/4.65G [04:10<01:32, 16.0MB/s]\u001b[A\n", " 71%|███████   | 3.28G/4.65G [04:10<01:28, 16.7MB/s]\u001b[A\n", " 71%|███████   | 3.28G/4.65G [04:10<01:29, 16.3MB/s]\u001b[A\n", " 71%|███████   | 3.28G/4.65G [04:11<01:26, 17.0MB/s]\u001b[A\n", " 71%|███████   | 3.28G/4.65G [04:11<02:46, 8.79MB/s]\u001b[A\n", " 71%|███████   | 3.29G/4.65G [04:12<01:34, 15.3MB/s]\u001b[A\n", " 71%|███████   | 3.30G/4.65G [04:12<02:22, 10.2MB/s]\u001b[A\n", " 71%|███████   | 3.30G/4.65G [04:13<01:35, 15.0MB/s]\u001b[A\n", " 71%|███████   | 3.31G/4.65G [04:13<01:31, 15.7MB/s]\u001b[A\n", " 71%|███████   | 3.31G/4.65G [04:13<01:21, 17.6MB/s]\u001b[A\n", " 71%|███████▏  | 3.31G/4.65G [04:13<01:27, 16.4MB/s]\u001b[A\n", " 71%|███████▏  | 3.32G/4.65G [04:13<01:29, 16.0MB/s]\u001b[A\n", " 71%|███████▏  | 3.32G/4.65G [04:13<01:28, 16.0MB/s]\u001b[A\n", " 71%|███████▏  | 3.32G/4.65G [04:14<01:47, 13.3MB/s]\u001b[A\n", " 71%|███████▏  | 3.32G/4.65G [04:14<03:24, 6.96MB/s]\u001b[A\n", " 72%|███████▏  | 3.33G/4.65G [04:15<01:40, 14.1MB/s]\u001b[A\n", " 72%|███████▏  | 3.33G/4.65G [04:15<02:29, 9.41MB/s]\u001b[A\n", " 72%|███████▏  | 3.34G/4.65G [04:16<01:32, 15.1MB/s]\u001b[A\n", " 72%|███████▏  | 3.34G/4.65G [04:16<01:32, 15.2MB/s]\u001b[A\n", " 72%|███████▏  | 3.35G/4.65G [04:16<01:27, 16.0MB/s]\u001b[A\n", " 72%|███████▏  | 3.35G/4.65G [04:16<01:27, 15.8MB/s]\u001b[A\n", " 72%|███████▏  | 3.36G/4.65G [04:16<01:23, 16.5MB/s]\u001b[A\n", " 72%|███████▏  | 3.36G/4.65G [04:17<01:22, 16.7MB/s]\u001b[A\n", " 72%|███████▏  | 3.36G/4.65G [04:17<02:31, 9.10MB/s]\u001b[A\n", " 73%|███████▎  | 3.37G/4.65G [04:18<01:27, 15.6MB/s]\u001b[A\n", " 73%|███████▎  | 3.37G/4.65G [04:18<02:12, 10.3MB/s]\u001b[A\n", " 73%|███████▎  | 3.38G/4.65G [04:19<01:30, 15.0MB/s]\u001b[A\n", " 73%|███████▎  | 3.38G/4.65G [04:19<01:29, 15.1MB/s]\u001b[A\n", " 73%|███████▎  | 3.39G/4.65G [04:19<01:32, 14.7MB/s]\u001b[A\n", " 73%|███████▎  | 3.39G/4.65G [04:19<01:31, 14.7MB/s]\u001b[A\n", " 73%|███████▎  | 3.39G/4.65G [04:19<01:28, 15.3MB/s]\u001b[A\n", " 73%|███████▎  | 3.39G/4.65G [04:19<01:34, 14.2MB/s]\u001b[A\n", " 73%|███████▎  | 3.40G/4.65G [04:20<01:48, 12.4MB/s]\u001b[A\n", " 73%|███████▎  | 3.40G/4.65G [04:20<02:52, 7.76MB/s]\u001b[A\n", " 73%|███████▎  | 3.41G/4.65G [04:21<01:24, 15.7MB/s]\u001b[A\n", " 73%|███████▎  | 3.41G/4.65G [04:21<01:28, 15.0MB/s]\u001b[A\n", " 73%|███████▎  | 3.41G/4.65G [04:21<01:11, 18.4MB/s]\u001b[A\n", " 74%|███████▎  | 3.42G/4.65G [04:21<01:09, 19.1MB/s]\u001b[A\n", " 74%|███████▎  | 3.42G/4.65G [04:21<01:13, 18.0MB/s]\u001b[A\n", " 74%|███████▎  | 3.42G/4.65G [04:22<01:21, 16.1MB/s]\u001b[A\n", " 74%|███████▎  | 3.42G/4.65G [04:22<01:30, 14.5MB/s]\u001b[A\n", " 74%|███████▍  | 3.43G/4.65G [04:22<01:16, 17.2MB/s]\u001b[A\n", " 74%|███████▍  | 3.43G/4.65G [04:22<01:13, 17.7MB/s]\u001b[A\n", " 74%|███████▍  | 3.43G/4.65G [04:22<01:15, 17.2MB/s]\u001b[A\n", " 74%|███████▍  | 3.44G/4.65G [04:22<01:06, 19.6MB/s]\u001b[A\n", " 74%|███████▍  | 3.44G/4.65G [04:22<01:08, 19.0MB/s]\u001b[A\n", " 74%|███████▍  | 3.44G/4.65G [04:23<01:16, 16.8MB/s]\u001b[A\n", " 74%|███████▍  | 3.44G/4.65G [04:23<02:26, 8.78MB/s]\u001b[A\n", " 74%|███████▍  | 3.45G/4.65G [04:24<01:16, 16.8MB/s]\u001b[A\n", " 74%|███████▍  | 3.46G/4.65G [04:24<01:14, 17.1MB/s]\u001b[A\n", " 74%|███████▍  | 3.46G/4.65G [04:24<01:05, 19.4MB/s]\u001b[A\n", " 75%|███████▍  | 3.46G/4.65G [04:24<01:09, 18.2MB/s]\u001b[A\n", " 75%|███████▍  | 3.47G/4.65G [04:24<01:09, 18.4MB/s]\u001b[A\n", " 75%|███████▍  | 3.47G/4.65G [04:25<01:17, 16.4MB/s]\u001b[A\n", " 75%|███████▍  | 3.47G/4.65G [04:25<01:26, 14.6MB/s]\u001b[A\n", " 75%|███████▍  | 3.47G/4.65G [04:25<01:21, 15.5MB/s]\u001b[A\n", " 75%|███████▍  | 3.48G/4.65G [04:25<01:31, 13.7MB/s]\u001b[A\n", " 75%|███████▍  | 3.48G/4.65G [04:25<01:22, 15.3MB/s]\u001b[A\n", " 75%|███████▍  | 3.48G/4.65G [04:25<01:16, 16.3MB/s]\u001b[A\n", " 75%|███████▌  | 3.49G/4.65G [04:26<01:17, 16.0MB/s]\u001b[A\n", " 75%|███████▌  | 3.49G/4.65G [04:26<02:16, 9.09MB/s]\u001b[A\n", " 75%|███████▌  | 3.50G/4.65G [04:27<01:14, 16.6MB/s]\u001b[A\n", " 75%|███████▌  | 3.50G/4.65G [04:27<01:58, 10.4MB/s]\u001b[A\n", " 76%|███████▌  | 3.51G/4.65G [04:27<01:19, 15.4MB/s]\u001b[A\n", " 76%|███████▌  | 3.51G/4.65G [04:28<01:19, 15.4MB/s]\u001b[A\n", " 76%|███████▌  | 3.51G/4.65G [04:28<02:11, 9.25MB/s]\u001b[A\n", " 76%|███████▌  | 3.52G/4.65G [04:29<01:19, 15.1MB/s]\u001b[A\n", " 76%|███████▌  | 3.52G/4.65G [04:29<01:59, 10.1MB/s]\u001b[A\n", " 76%|███████▌  | 3.53G/4.65G [04:30<01:21, 14.8MB/s]\u001b[A\n", " 76%|███████▌  | 3.54G/4.65G [04:30<01:16, 15.5MB/s]\u001b[A\n", " 76%|███████▌  | 3.54G/4.65G [04:30<01:14, 15.9MB/s]\u001b[A\n", " 76%|███████▌  | 3.54G/4.65G [04:30<01:25, 13.9MB/s]\u001b[A\n", " 76%|███████▋  | 3.54G/4.65G [04:30<01:29, 13.2MB/s]\u001b[A\n", " 76%|███████▋  | 3.55G/4.65G [04:31<01:24, 14.0MB/s]\u001b[A\n", " 76%|███████▋  | 3.55G/4.65G [04:31<02:37, 7.49MB/s]\u001b[A\n", " 77%|███████▋  | 3.56G/4.65G [04:32<01:16, 15.3MB/s]\u001b[A\n", " 77%|███████▋  | 3.56G/4.65G [04:32<01:15, 15.3MB/s]\u001b[A\n", " 77%|███████▋  | 3.56G/4.65G [04:32<01:11, 16.2MB/s]\u001b[A\n", " 77%|███████▋  | 3.57G/4.65G [04:33<01:42, 11.3MB/s]\u001b[A\n", " 77%|███████▋  | 3.57G/4.65G [04:33<02:49, 6.83MB/s]\u001b[A\n", " 77%|███████▋  | 3.58G/4.65G [04:34<01:24, 13.5MB/s]\u001b[A\n", " 77%|███████▋  | 3.58G/4.65G [04:34<01:22, 13.8MB/s]\u001b[A\n", " 77%|███████▋  | 3.58G/4.65G [04:34<01:16, 14.9MB/s]\u001b[A\n", " 77%|███████▋  | 3.59G/4.65G [04:34<01:11, 15.8MB/s]\u001b[A\n", " 77%|███████▋  | 3.59G/4.65G [04:34<01:12, 15.7MB/s]\u001b[A\n", " 77%|███████▋  | 3.59G/4.65G [04:34<01:03, 17.7MB/s]\u001b[A\n", " 77%|███████▋  | 3.60G/4.65G [04:35<01:06, 17.0MB/s]\u001b[A\n", " 77%|███████▋  | 3.60G/4.65G [04:35<02:01, 9.23MB/s]\u001b[A\n", " 78%|███████▊  | 3.61G/4.65G [04:36<01:07, 16.4MB/s]\u001b[A\n", " 78%|███████▊  | 3.61G/4.65G [04:36<01:46, 10.4MB/s]\u001b[A\n", " 78%|███████▊  | 3.62G/4.65G [04:37<01:11, 15.5MB/s]\u001b[A\n", " 78%|███████▊  | 3.62G/4.65G [04:37<01:08, 16.0MB/s]\u001b[A\n", " 78%|███████▊  | 3.63G/4.65G [04:37<01:05, 16.7MB/s]\u001b[A\n", " 78%|███████▊  | 3.63G/4.65G [04:37<01:06, 16.3MB/s]\u001b[A\n", " 78%|███████▊  | 3.63G/4.65G [04:37<01:04, 16.9MB/s]\u001b[A\n", " 78%|███████▊  | 3.63G/4.65G [04:37<01:15, 14.4MB/s]\u001b[A\n", " 78%|███████▊  | 3.63G/4.65G [04:38<01:20, 13.5MB/s]\u001b[A\n", " 78%|███████▊  | 3.64G/4.65G [04:38<02:31, 7.16MB/s]\u001b[A\n", " 78%|███████▊  | 3.65G/4.65G [04:39<01:14, 14.5MB/s]\u001b[A\n", " 79%|███████▊  | 3.65G/4.65G [04:39<02:01, 8.85MB/s]\u001b[A\n", " 79%|███████▊  | 3.66G/4.65G [04:40<01:11, 14.8MB/s]\u001b[A\n", " 79%|███████▉  | 3.66G/4.65G [04:40<01:12, 14.5MB/s]\u001b[A\n", " 79%|███████▉  | 3.66G/4.65G [04:40<01:11, 14.7MB/s]\u001b[A\n", " 79%|███████▉  | 3.67G/4.65G [04:40<01:11, 14.7MB/s]\u001b[A\n", " 79%|███████▉  | 3.67G/4.65G [04:40<01:10, 14.8MB/s]\u001b[A\n", " 79%|███████▉  | 3.67G/4.65G [04:41<01:05, 15.9MB/s]\u001b[A\n", " 79%|███████▉  | 3.67G/4.65G [04:41<02:04, 8.36MB/s]\u001b[A\n", " 79%|███████▉  | 3.68G/4.65G [04:42<01:09, 14.9MB/s]\u001b[A\n", " 79%|███████▉  | 3.69G/4.65G [04:42<01:43, 9.97MB/s]\u001b[A\n", " 80%|███████▉  | 3.69G/4.65G [04:43<01:08, 14.9MB/s]\u001b[A\n", " 80%|███████▉  | 3.70G/4.65G [04:43<01:06, 15.4MB/s]\u001b[A\n", " 80%|███████▉  | 3.70G/4.65G [04:43<01:01, 16.4MB/s]\u001b[A\n", " 80%|███████▉  | 3.70G/4.65G [04:43<01:06, 15.2MB/s]\u001b[A\n", " 80%|███████▉  | 3.71G/4.65G [04:43<01:06, 15.3MB/s]\u001b[A\n", " 80%|███████▉  | 3.71G/4.65G [04:43<01:04, 15.6MB/s]\u001b[A\n", " 80%|███████▉  | 3.71G/4.65G [04:44<01:09, 14.4MB/s]\u001b[A\n", " 80%|███████▉  | 3.71G/4.65G [04:44<02:19, 7.18MB/s]\u001b[A\n", " 80%|████████  | 3.72G/4.65G [04:45<01:08, 14.4MB/s]\u001b[A\n", " 80%|████████  | 3.72G/4.65G [04:45<01:43, 9.54MB/s]\u001b[A\n", " 80%|████████  | 3.73G/4.65G [04:46<01:08, 14.4MB/s]\u001b[A\n", " 80%|████████  | 3.73G/4.65G [04:46<01:07, 14.5MB/s]\u001b[A\n", " 80%|████████  | 3.74G/4.65G [04:46<01:04, 15.0MB/s]\u001b[A\n", " 81%|████████  | 3.74G/4.65G [04:46<01:04, 15.0MB/s]\u001b[A\n", " 81%|████████  | 3.74G/4.65G [04:46<01:00, 15.9MB/s]\u001b[A\n", " 81%|████████  | 3.75G/4.65G [04:47<00:54, 17.5MB/s]\u001b[A\n", " 81%|████████  | 3.75G/4.65G [04:47<00:55, 17.5MB/s]\u001b[A\n", " 81%|████████  | 3.75G/4.65G [04:47<01:49, 8.75MB/s]\u001b[A\n", " 81%|████████  | 3.76G/4.65G [04:48<00:58, 16.2MB/s]\u001b[A\n", " 81%|████████  | 3.76G/4.65G [04:48<01:32, 10.2MB/s]\u001b[A\n", " 81%|████████  | 3.77G/4.65G [04:49<01:01, 15.3MB/s]\u001b[A\n", " 81%|████████  | 3.77G/4.65G [04:49<00:58, 15.9MB/s]\u001b[A\n", " 81%|████████▏ | 3.78G/4.65G [04:49<00:58, 15.9MB/s]\u001b[A\n", " 81%|████████▏ | 3.78G/4.65G [04:49<00:55, 16.9MB/s]\u001b[A\n", " 81%|████████▏ | 3.78G/4.65G [04:49<00:55, 16.7MB/s]\u001b[A\n", " 81%|████████▏ | 3.78G/4.65G [04:49<00:53, 17.1MB/s]\u001b[A\n", " 82%|████████▏ | 3.79G/4.65G [04:49<00:55, 16.6MB/s]\u001b[A\n", " 82%|████████▏ | 3.79G/4.65G [04:50<00:55, 16.7MB/s]\u001b[A\n", " 82%|████████▏ | 3.79G/4.65G [04:50<02:07, 7.21MB/s]\u001b[A\n", " 82%|████████▏ | 3.80G/4.65G [04:51<01:01, 14.8MB/s]\u001b[A\n", " 82%|████████▏ | 3.80G/4.65G [04:51<01:34, 9.59MB/s]\u001b[A\n", " 82%|████████▏ | 3.81G/4.65G [04:52<01:01, 14.5MB/s]\u001b[A\n", " 82%|████████▏ | 3.81G/4.65G [04:52<01:01, 14.6MB/s]\u001b[A\n", " 82%|████████▏ | 3.81G/4.65G [04:52<01:18, 11.4MB/s]\u001b[A\n", " 82%|████████▏ | 3.82G/4.65G [04:52<01:23, 10.7MB/s]\u001b[A\n", " 82%|████████▏ | 3.82G/4.65G [04:53<01:22, 10.8MB/s]\u001b[A\n", " 82%|████████▏ | 3.82G/4.65G [04:53<01:09, 12.8MB/s]\u001b[A\n", " 82%|████████▏ | 3.82G/4.65G [04:53<01:39, 8.90MB/s]\u001b[A\n", " 82%|████████▏ | 3.83G/4.65G [04:54<00:53, 16.3MB/s]\u001b[A\n", " 83%|████████▎ | 3.83G/4.65G [04:54<01:30, 9.66MB/s]\u001b[A\n", " 83%|████████▎ | 3.84G/4.65G [04:55<00:55, 15.6MB/s]\u001b[A\n", " 83%|████████▎ | 3.85G/4.65G [04:55<00:55, 15.5MB/s]\u001b[A\n", " 83%|████████▎ | 3.85G/4.65G [04:55<00:52, 16.2MB/s]\u001b[A\n", " 83%|████████▎ | 3.85G/4.65G [04:55<00:50, 16.9MB/s]\u001b[A\n", " 83%|████████▎ | 3.86G/4.65G [04:55<00:51, 16.5MB/s]\u001b[A\n", " 83%|████████▎ | 3.86G/4.65G [04:55<00:52, 15.9MB/s]\u001b[A\n", " 83%|████████▎ | 3.86G/4.65G [04:56<00:42, 19.8MB/s]\u001b[A\n", " 83%|████████▎ | 3.87G/4.65G [04:56<00:45, 18.4MB/s]\u001b[A\n", " 83%|████████▎ | 3.87G/4.65G [04:56<00:47, 17.4MB/s]\u001b[A\n", " 83%|████████▎ | 3.87G/4.65G [04:56<00:49, 16.8MB/s]\u001b[A\n", " 83%|████████▎ | 3.88G/4.65G [04:57<00:47, 17.4MB/s]\u001b[A\n", " 83%|████████▎ | 3.88G/4.65G [04:57<01:40, 8.24MB/s]\u001b[A\n", " 84%|████████▎ | 3.89G/4.65G [04:58<00:53, 15.1MB/s]\u001b[A\n", " 84%|████████▎ | 3.89G/4.65G [04:58<00:49, 16.5MB/s]\u001b[A\n", " 84%|████████▍ | 3.89G/4.65G [04:58<00:45, 17.6MB/s]\u001b[A\n", " 84%|████████▍ | 3.90G/4.65G [04:58<00:47, 17.0MB/s]\u001b[A\n", " 84%|████████▍ | 3.90G/4.65G [04:58<00:51, 15.5MB/s]\u001b[A\n", " 84%|████████▍ | 3.90G/4.65G [04:58<00:45, 17.7MB/s]\u001b[A\n", " 84%|████████▍ | 3.90G/4.65G [04:58<00:45, 17.4MB/s]\u001b[A\n", " 84%|████████▍ | 3.90G/4.65G [04:58<00:49, 16.0MB/s]\u001b[A\n", " 84%|████████▍ | 3.91G/4.65G [04:59<00:48, 16.4MB/s]\u001b[A\n", " 84%|████████▍ | 3.91G/4.65G [04:59<01:27, 9.01MB/s]\u001b[A\n", " 84%|████████▍ | 3.92G/4.65G [05:00<00:46, 16.7MB/s]\u001b[A\n", " 84%|████████▍ | 3.92G/4.65G [05:00<00:47, 16.4MB/s]\u001b[A\n", " 85%|████████▍ | 3.93G/4.65G [05:00<00:45, 17.0MB/s]\u001b[A\n", " 85%|████████▍ | 3.93G/4.65G [05:00<00:43, 17.5MB/s]\u001b[A\n", " 85%|████████▍ | 3.93G/4.65G [05:00<00:45, 16.9MB/s]\u001b[A\n", " 85%|████████▍ | 3.94G/4.65G [05:01<00:43, 17.4MB/s]\u001b[A\n", " 85%|████████▍ | 3.94G/4.65G [05:01<00:42, 17.8MB/s]\u001b[A\n", " 85%|████████▍ | 3.94G/4.65G [05:01<00:44, 17.1MB/s]\u001b[A\n", " 85%|████████▍ | 3.95G/4.65G [05:01<00:42, 17.6MB/s]\u001b[A\n", " 85%|████████▌ | 3.95G/4.65G [05:01<00:44, 16.7MB/s]\u001b[A\n", " 85%|████████▌ | 3.95G/4.65G [05:02<00:45, 16.6MB/s]\u001b[A\n", " 85%|████████▌ | 3.95G/4.65G [05:02<00:49, 14.9MB/s]\u001b[A\n", " 85%|████████▌ | 3.96G/4.65G [05:02<00:49, 15.0MB/s]\u001b[A\n", " 85%|████████▌ | 3.96G/4.65G [05:02<00:45, 16.2MB/s]\u001b[A\n", " 85%|████████▌ | 3.96G/4.65G [05:02<00:45, 16.0MB/s]\u001b[A\n", " 85%|████████▌ | 3.97G/4.65G [05:03<00:43, 16.8MB/s]\u001b[A\n", " 85%|████████▌ | 3.97G/4.65G [05:03<01:14, 9.73MB/s]\u001b[A\n", " 86%|████████▌ | 3.98G/4.65G [05:04<00:41, 17.3MB/s]\u001b[A\n", " 86%|████████▌ | 3.98G/4.65G [05:04<00:38, 18.5MB/s]\u001b[A\n", " 86%|████████▌ | 3.98G/4.65G [05:04<00:36, 19.3MB/s]\u001b[A\n", " 86%|████████▌ | 3.99G/4.65G [05:04<00:38, 18.1MB/s]\u001b[A\n", " 86%|████████▌ | 3.99G/4.65G [05:04<00:43, 16.4MB/s]\u001b[A\n", " 86%|████████▌ | 3.99G/4.65G [05:04<00:38, 18.3MB/s]\u001b[A\n", " 86%|████████▌ | 3.99G/4.65G [05:04<00:38, 18.0MB/s]\u001b[A\n", " 86%|████████▌ | 4.00G/4.65G [05:05<00:44, 15.7MB/s]\u001b[A\n", " 86%|████████▌ | 4.00G/4.65G [05:05<00:41, 16.8MB/s]\u001b[A\n", " 86%|████████▌ | 4.00G/4.65G [05:05<01:15, 9.11MB/s]\u001b[A\n", " 86%|████████▋ | 4.01G/4.65G [05:06<00:39, 17.1MB/s]\u001b[A\n", " 86%|████████▋ | 4.01G/4.65G [05:06<00:38, 17.5MB/s]\u001b[A\n", " 86%|████████▋ | 4.02G/4.65G [05:06<00:37, 17.8MB/s]\u001b[A\n", " 87%|████████▋ | 4.02G/4.65G [05:06<00:39, 17.2MB/s]\u001b[A\n", " 87%|████████▋ | 4.02G/4.65G [05:07<00:37, 17.6MB/s]\u001b[A\n", " 87%|████████▋ | 4.03G/4.65G [05:07<01:19, 8.33MB/s]\u001b[A\n", " 87%|████████▋ | 4.04G/4.65G [05:08<00:42, 15.4MB/s]\u001b[A\n", " 87%|████████▋ | 4.04G/4.65G [05:08<01:02, 10.4MB/s]\u001b[A\n", " 87%|████████▋ | 4.05G/4.65G [05:09<00:42, 15.2MB/s]\u001b[A\n", " 87%|████████▋ | 4.05G/4.65G [05:09<00:42, 15.2MB/s]\u001b[A\n", " 87%|████████▋ | 4.05G/4.65G [05:09<00:40, 15.8MB/s]\u001b[A\n", " 87%|████████▋ | 4.05G/4.65G [05:09<00:40, 15.5MB/s]\u001b[A\n", " 87%|████████▋ | 4.06G/4.65G [05:09<00:39, 15.9MB/s]\u001b[A\n", " 87%|████████▋ | 4.06G/4.65G [05:10<00:49, 12.7MB/s]\u001b[A\n", " 87%|████████▋ | 4.06G/4.65G [05:10<01:38, 6.38MB/s]\u001b[A\n", " 88%|████████▊ | 4.07G/4.65G [05:11<00:48, 12.9MB/s]\u001b[A\n", " 88%|████████▊ | 4.07G/4.65G [05:11<00:47, 12.9MB/s]\u001b[A\n", " 88%|████████▊ | 4.08G/4.65G [05:11<00:39, 15.4MB/s]\u001b[A\n", " 88%|████████▊ | 4.08G/4.65G [05:11<00:40, 15.1MB/s]\u001b[A\n", " 88%|████████▊ | 4.08G/4.65G [05:11<00:41, 14.8MB/s]\u001b[A\n", " 88%|████████▊ | 4.08G/4.65G [05:11<00:47, 12.7MB/s]\u001b[A\n", " 88%|████████▊ | 4.08G/4.65G [05:12<00:44, 13.5MB/s]\u001b[A\n", " 88%|████████▊ | 4.09G/4.65G [05:12<01:26, 6.92MB/s]\u001b[A\n", " 88%|████████▊ | 4.10G/4.65G [05:13<00:39, 14.8MB/s]\u001b[A\n", " 88%|████████▊ | 4.10G/4.65G [05:13<00:58, 9.97MB/s]\u001b[A\n", " 88%|████████▊ | 4.11G/4.65G [05:14<00:38, 15.0MB/s]\u001b[A\n", " 88%|████████▊ | 4.11G/4.65G [05:14<00:37, 15.5MB/s]\u001b[A\n", " 89%|████████▊ | 4.11G/4.65G [05:14<00:35, 16.3MB/s]\u001b[A\n", " 89%|████████▊ | 4.12G/4.65G [05:14<00:35, 16.1MB/s]\u001b[A\n", " 89%|████████▊ | 4.12G/4.65G [05:14<00:34, 16.3MB/s]\u001b[A\n", " 89%|████████▊ | 4.12G/4.65G [05:14<00:33, 16.6MB/s]\u001b[A\n", " 89%|████████▉ | 4.12G/4.65G [05:14<00:34, 16.3MB/s]\u001b[A\n", " 89%|████████▉ | 4.13G/4.65G [05:15<00:30, 18.5MB/s]\u001b[A\n", " 89%|████████▉ | 4.13G/4.65G [05:15<01:04, 8.62MB/s]\u001b[A\n", " 89%|████████▉ | 4.14G/4.65G [05:16<00:33, 16.4MB/s]\u001b[A\n", " 89%|████████▉ | 4.14G/4.65G [05:16<00:53, 10.2MB/s]\u001b[A\n", " 89%|████████▉ | 4.15G/4.65G [05:17<00:34, 15.3MB/s]\u001b[A\n", " 89%|████████▉ | 4.15G/4.65G [05:17<00:34, 15.3MB/s]\u001b[A\n", " 89%|████████▉ | 4.15G/4.65G [05:17<00:32, 16.1MB/s]\u001b[A\n", " 89%|████████▉ | 4.16G/4.65G [05:17<00:29, 17.5MB/s]\u001b[A\n", " 90%|████████▉ | 4.16G/4.65G [05:17<00:30, 16.9MB/s]\u001b[A\n", " 90%|████████▉ | 4.16G/4.65G [05:17<00:30, 17.1MB/s]\u001b[A\n", " 90%|████████▉ | 4.16G/4.65G [05:17<00:29, 17.6MB/s]\u001b[A\n", " 90%|████████▉ | 4.17G/4.65G [05:18<00:29, 17.6MB/s]\u001b[A\n", " 90%|████████▉ | 4.17G/4.65G [05:18<01:04, 7.91MB/s]\u001b[A\n", " 90%|████████▉ | 4.18G/4.65G [05:19<00:29, 16.8MB/s]\u001b[A\n", " 90%|████████▉ | 4.18G/4.65G [05:19<00:28, 17.4MB/s]\u001b[A\n", " 90%|█████████ | 4.18G/4.65G [05:19<00:25, 19.3MB/s]\u001b[A\n", " 90%|█████████ | 4.19G/4.65G [05:19<00:27, 18.1MB/s]\u001b[A\n", " 90%|█████████ | 4.19G/4.65G [05:19<00:28, 17.3MB/s]\u001b[A\n", " 90%|█████████ | 4.19G/4.65G [05:20<00:31, 15.6MB/s]\u001b[A\n", " 90%|█████████ | 4.19G/4.65G [05:20<00:34, 13.9MB/s]\u001b[A\n", " 90%|█████████ | 4.20G/4.65G [05:20<00:28, 17.1MB/s]\u001b[A\n", " 90%|█████████ | 4.20G/4.65G [05:20<00:27, 17.6MB/s]\u001b[A\n", " 91%|█████████ | 4.21G/4.65G [05:20<00:29, 16.2MB/s]\u001b[A\n", " 91%|█████████ | 4.21G/4.65G [05:20<00:29, 15.8MB/s]\u001b[A\n", " 91%|█████████ | 4.21G/4.65G [05:21<00:27, 16.7MB/s]\u001b[A\n", " 91%|█████████ | 4.21G/4.65G [05:21<00:49, 9.45MB/s]\u001b[A\n", " 91%|█████████ | 4.22G/4.65G [05:22<00:26, 16.9MB/s]\u001b[A\n", " 91%|█████████ | 4.23G/4.65G [05:22<00:42, 10.5MB/s]\u001b[A\n", " 91%|█████████ | 4.23G/4.65G [05:23<00:28, 15.6MB/s]\u001b[A\n", " 91%|█████████ | 4.24G/4.65G [05:23<00:26, 16.9MB/s]\u001b[A\n", " 91%|█████████▏| 4.24G/4.65G [05:23<00:24, 17.8MB/s]\u001b[A\n", " 91%|█████████▏| 4.24G/4.65G [05:23<00:25, 17.2MB/s]\u001b[A\n", " 91%|█████████▏| 4.25G/4.65G [05:23<00:25, 16.7MB/s]\u001b[A\n", " 91%|█████████▏| 4.25G/4.65G [05:23<00:26, 16.3MB/s]\u001b[A\n", " 92%|█████████▏| 4.25G/4.65G [05:23<00:23, 18.3MB/s]\u001b[A\n", " 92%|█████████▏| 4.25G/4.65G [05:24<00:23, 17.9MB/s]\u001b[A\n", " 92%|█████████▏| 4.25G/4.65G [05:24<00:51, 8.19MB/s]\u001b[A\n", " 92%|█████████▏| 4.26G/4.65G [05:25<00:26, 15.7MB/s]\u001b[A\n", " 92%|█████████▏| 4.27G/4.65G [05:25<00:40, 10.0MB/s]\u001b[A\n", " 92%|█████████▏| 4.27G/4.65G [05:25<00:26, 15.2MB/s]\u001b[A\n", " 92%|█████████▏| 4.28G/4.65G [05:26<00:24, 15.9MB/s]\u001b[A\n", " 92%|█████████▏| 4.28G/4.65G [05:26<00:37, 10.3MB/s]\u001b[A\n", " 92%|█████████▏| 4.29G/4.65G [05:27<00:26, 14.5MB/s]\u001b[A\n", " 92%|█████████▏| 4.29G/4.65G [05:27<00:24, 15.4MB/s]\u001b[A\n", " 92%|█████████▏| 4.30G/4.65G [05:27<00:23, 16.1MB/s]\u001b[A\n", " 93%|█████████▎| 4.30G/4.65G [05:27<00:23, 16.0MB/s]\u001b[A\n", " 93%|█████████▎| 4.30G/4.65G [05:28<00:22, 16.7MB/s]\u001b[A\n", " 93%|█████████▎| 4.30G/4.65G [05:28<00:43, 8.36MB/s]\u001b[A\n", " 93%|█████████▎| 4.31G/4.65G [05:29<00:24, 14.5MB/s]\u001b[A\n", " 93%|█████████▎| 4.32G/4.65G [05:29<00:35, 9.97MB/s]\u001b[A\n", " 93%|█████████▎| 4.32G/4.65G [05:30<00:23, 14.6MB/s]\u001b[A\n", " 93%|█████████▎| 4.33G/4.65G [05:30<00:23, 14.7MB/s]\u001b[A\n", " 93%|█████████▎| 4.33G/4.65G [05:30<00:20, 16.5MB/s]\u001b[A\n", " 93%|█████████▎| 4.33G/4.65G [05:30<00:20, 16.5MB/s]\u001b[A\n", " 93%|█████████▎| 4.34G/4.65G [05:30<00:24, 13.4MB/s]\u001b[A\n", " 93%|█████████▎| 4.34G/4.65G [05:31<00:26, 12.6MB/s]\u001b[A\n", " 93%|█████████▎| 4.34G/4.65G [05:31<00:48, 6.83MB/s]\u001b[A\n", " 94%|█████████▎| 4.35G/4.65G [05:32<00:22, 14.0MB/s]\u001b[A\n", " 94%|█████████▎| 4.35G/4.65G [05:32<00:30, 10.3MB/s]\u001b[A\n", " 94%|█████████▍| 4.36G/4.65G [05:33<00:19, 15.8MB/s]\u001b[A\n", " 94%|█████████▍| 4.36G/4.65G [05:33<00:19, 15.7MB/s]\u001b[A\n", " 94%|█████████▍| 4.37G/4.65G [05:33<00:18, 16.4MB/s]\u001b[A\n", " 94%|█████████▍| 4.37G/4.65G [05:33<00:18, 16.2MB/s]\u001b[A\n", " 94%|█████████▍| 4.37G/4.65G [05:33<00:17, 16.9MB/s]\u001b[A\n", " 94%|█████████▍| 4.38G/4.65G [05:34<00:16, 17.4MB/s]\u001b[A\n", " 94%|█████████▍| 4.38G/4.65G [05:34<00:33, 8.57MB/s]\u001b[A\n", " 94%|█████████▍| 4.39G/4.65G [05:35<00:18, 14.9MB/s]\u001b[A\n", " 95%|█████████▍| 4.39G/4.65G [05:35<00:27, 10.0MB/s]\u001b[A\n", " 95%|█████████▍| 4.40G/4.65G [05:36<00:16, 15.8MB/s]\u001b[A\n", " 95%|█████████▍| 4.40G/4.65G [05:36<00:17, 15.2MB/s]\u001b[A\n", " 95%|█████████▍| 4.41G/4.65G [05:36<00:17, 15.0MB/s]\u001b[A\n", " 95%|█████████▍| 4.41G/4.65G [05:36<00:16, 14.9MB/s]\u001b[A\n", " 95%|█████████▍| 4.41G/4.65G [05:36<00:15, 15.9MB/s]\u001b[A\n", " 95%|█████████▌| 4.42G/4.65G [05:37<00:15, 16.0MB/s]\u001b[A\n", " 95%|█████████▌| 4.42G/4.65G [05:37<00:27, 8.72MB/s]\u001b[A\n", " 95%|█████████▌| 4.43G/4.65G [05:38<00:15, 15.1MB/s]\u001b[A\n", " 95%|█████████▌| 4.43G/4.65G [05:38<00:22, 10.1MB/s]\u001b[A\n", " 96%|█████████▌| 4.44G/4.65G [05:39<00:15, 14.7MB/s]\u001b[A\n", " 96%|█████████▌| 4.44G/4.65G [05:39<00:21, 10.3MB/s]\u001b[A\n", " 96%|█████████▌| 4.45G/4.65G [05:40<00:14, 14.9MB/s]\u001b[A\n", " 96%|█████████▌| 4.45G/4.65G [05:40<00:12, 16.0MB/s]\u001b[A\n", " 96%|█████████▌| 4.46G/4.65G [05:40<00:13, 15.7MB/s]\u001b[A\n", " 96%|█████████▌| 4.46G/4.65G [05:40<00:13, 14.9MB/s]\u001b[A\n", " 96%|█████████▌| 4.46G/4.65G [05:40<00:13, 15.0MB/s]\u001b[A\n", " 96%|█████████▌| 4.46G/4.65G [05:40<00:12, 16.0MB/s]\u001b[A\n", " 96%|█████████▌| 4.47G/4.65G [05:41<00:11, 16.7MB/s]\u001b[A\n", " 96%|█████████▌| 4.47G/4.65G [05:41<00:10, 18.5MB/s]\u001b[A\n", " 96%|█████████▋| 4.47G/4.65G [05:41<00:18, 9.95MB/s]\u001b[A\n", " 96%|█████████▋| 4.48G/4.65G [05:42<00:10, 17.4MB/s]\u001b[A\n", " 97%|█████████▋| 4.48G/4.65G [05:42<00:10, 17.0MB/s]\u001b[A\n", " 97%|█████████▋| 4.49G/4.65G [05:42<00:09, 17.4MB/s]\u001b[A\n", " 97%|█████████▋| 4.49G/4.65G [05:42<00:09, 17.8MB/s]\u001b[A\n", " 97%|█████████▋| 4.50G/4.65G [05:43<00:09, 17.1MB/s]\u001b[A\n", " 97%|█████████▋| 4.50G/4.65G [05:43<00:19, 8.25MB/s]\u001b[A\n", " 97%|█████████▋| 4.51G/4.65G [05:44<00:10, 14.7MB/s]\u001b[A\n", " 97%|█████████▋| 4.51G/4.65G [05:44<00:09, 15.4MB/s]\u001b[A\n", " 97%|█████████▋| 4.51G/4.65G [05:44<00:10, 14.3MB/s]\u001b[A\n", " 97%|█████████▋| 4.52G/4.65G [05:44<00:09, 15.4MB/s]\u001b[A\n", " 97%|█████████▋| 4.52G/4.65G [05:44<00:09, 13.8MB/s]\u001b[A\n", " 97%|█████████▋| 4.52G/4.65G [05:45<00:09, 13.6MB/s]\u001b[A\n", " 97%|█████████▋| 4.52G/4.65G [05:45<00:17, 7.72MB/s]\u001b[A\n", " 98%|█████████▊| 4.53G/4.65G [05:46<00:07, 15.3MB/s]\u001b[A\n", " 98%|█████████▊| 4.54G/4.65G [05:46<00:11, 10.2MB/s]\u001b[A\n", " 98%|█████████▊| 4.54G/4.65G [05:47<00:07, 15.1MB/s]\u001b[A\n", " 98%|█████████▊| 4.55G/4.65G [05:47<00:06, 15.7MB/s]\u001b[A\n", " 98%|█████████▊| 4.55G/4.65G [05:47<00:06, 16.5MB/s]\u001b[A\n", " 98%|█████████▊| 4.55G/4.65G [05:47<00:06, 16.7MB/s]\u001b[A\n", " 98%|█████████▊| 4.55G/4.65G [05:47<00:06, 16.2MB/s]\u001b[A\n", " 98%|█████████▊| 4.56G/4.65G [05:47<00:05, 18.2MB/s]\u001b[A\n", " 98%|█████████▊| 4.56G/4.65G [05:47<00:05, 16.8MB/s]\u001b[A\n", " 98%|█████████▊| 4.56G/4.65G [05:48<00:05, 17.4MB/s]\u001b[A\n", " 98%|█████████▊| 4.56G/4.65G [05:48<00:05, 17.3MB/s]\u001b[A\n", " 98%|█████████▊| 4.56G/4.65G [05:48<00:11, 7.76MB/s]\u001b[A\n", " 98%|█████████▊| 4.57G/4.65G [05:49<00:04, 16.0MB/s]\u001b[A\n", " 99%|█████████▊| 4.58G/4.65G [05:49<00:07, 9.91MB/s]\u001b[A\n", " 99%|█████████▊| 4.58G/4.65G [05:50<00:04, 15.1MB/s]\u001b[A\n", " 99%|█████████▊| 4.59G/4.65G [05:50<00:04, 15.0MB/s]\u001b[A\n", " 99%|█████████▉| 4.59G/4.65G [05:50<00:04, 14.7MB/s]\u001b[A\n", " 99%|█████████▉| 4.59G/4.65G [05:50<00:03, 14.9MB/s]\u001b[A\n", " 99%|█████████▉| 4.60G/4.65G [05:50<00:03, 15.9MB/s]\u001b[A\n", " 99%|█████████▉| 4.60G/4.65G [05:51<00:02, 16.7MB/s]\u001b[A\n", " 99%|█████████▉| 4.60G/4.65G [05:51<00:05, 8.82MB/s]\u001b[A\n", " 99%|█████████▉| 4.61G/4.65G [05:52<00:02, 15.4MB/s]\u001b[A\n", " 99%|█████████▉| 4.61G/4.65G [05:52<00:03, 10.2MB/s]\u001b[A\n", " 99%|█████████▉| 4.62G/4.65G [05:53<00:01, 14.9MB/s]\u001b[A\n", "100%|█████████▉| 4.62G/4.65G [05:53<00:01, 15.0MB/s]\u001b[A\n", "100%|█████████▉| 4.63G/4.65G [05:53<00:01, 14.9MB/s]\u001b[A\n", "100%|█████████▉| 4.63G/4.65G [05:53<00:01, 14.1MB/s]\u001b[A\n", "100%|█████████▉| 4.63G/4.65G [05:53<00:00, 15.3MB/s]\u001b[A\n", "100%|█████████▉| 4.64G/4.65G [05:54<00:00, 15.3MB/s]\u001b[A\n", "100%|██████████| 4.65G/4.65G [05:54<00:00, 14.1MB/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Path to model files: /root/.cache/kagglehub/models/google/gemma-2-2b-jpn-it/transformers/gemma-2-2b-jpn-it/1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# Download latest version\n", "path = kagglehub.model_download(\"google/gemma-2-2b-jpn-it/transformers/gemma-2-2b-jpn-it\")\n", "\n", "print(\"Path to model files:\", path)"]}, {"cell_type": "markdown", "metadata": {"id": "tEYSy35XBjD7"}, "source": ["### Importing PyTorch\n", "\n", "For faster inference, it's good to use an accelerator such as a TPU or GPU - and also very important to make sure that PyTorch is set up to use it.\n", "\n", "The next cell imports PyTorch, then checks whether CUDA is available for use with a GPU."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "OlRXzOeIDS7s"}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "torch.cuda.is_available()"]}, {"cell_type": "markdown", "metadata": {"id": "khI26WE3twLx"}, "source": ["Now you can use Transformers. Here you set up the tokenizer and model, and make sure that the model is on the correct device."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "vYymKy3p_XE2"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6b97fc35010e44c3ae3ac2fa39a976dd", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from transformers import AutoTokenizer, AutoModelForCausalLM\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(path, local_files_only=True)\n", "model = AutoModelForCausalLM.from_pretrained(\n", "    path,\n", "    device_map=\"auto\",\n", "    torch_dtype=torch.bfloat16,\n", "    local_files_only=True\n", ").to(device)"]}, {"cell_type": "markdown", "metadata": {"id": "TppHj4wPu5cF"}, "source": ["Here are some helper functions - `format_gemma_instruction()` is very simple (and maybe doesn't even need to be a function!), wrapping the user's instruction in Gemma format.\n", "\n", "`unwrap_gemma_response()` takes care of stripping the response you get from the model, removing tokens as well as the prompt.\n", "\n", "For more information on Gemma formatting, see https://ai.google.dev/gemma/docs/formatting"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "E21r9AL6FRX1"}, "outputs": [], "source": ["def format_gemma_instruction(instruction: str) -> str:\n", "  return f\"<start_of_turn>user {instruction}<end_of_turn><start_of_turn>model\"\n", "\n", "def unwrap_gemma_response(query: str, response: str) -> str:\n", "  end_sequence = '<end_of_turn>'\n", "\n", "  start_idx = 0\n", "  query_idx = response.find(query)\n", "\n", "  if query_idx >= 0:\n", "    start_idx = query_idx + len(query)\n", "\n", "  trim = response[start_idx:]\n", "\n", "  end_idx = len(trim) - 1\n", "  endseq_idx = trim.find(end_sequence)\n", "\n", "  if endseq_idx >= 0:\n", "    end_idx = endseq_idx\n", "\n", "  return trim[:end_idx].strip()"]}, {"cell_type": "markdown", "metadata": {"id": "a7rqMiZ4vlnm"}, "source": ["First, you will send a prompt in Japanese to the model, asking it to write us a poem in Japanese."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "s2CFT7fOFaMy"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The 'max_batch_size' argument of HybridCache is deprecated and will be removed in v4.46. Use the more precisely named 'batch_size' argument instead.\n", "Starting from v4.46, the `logits` model output will have the same type as the model (except at train time, where it will always be FP32)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["機械学習の波、広がる未来の光、\n", "データの海、複雑な知識を導く。\n", "複雑なパターン、隠された法則を見つける、\n", "予測と改善、未来を形作る。\n", "\n", "ニューラルネットワーク、複雑な脳の像、\n", "学習と進化、無限の可能性を秘める。\n", "教師あり学習、教師なし学習、\n", "データの力、人間の知恵を融合する。\n", "\n", "機械学習の力、無限の可能性を秘める、\n", "複雑な問題を解き明かす、未来を拓く。\n", "AIの進化、人類の未来を左右する、\n", "新たな時代を迎えよう。\n"]}], "source": ["# Our prompt will be, \"Write me a poem about machine learning.\" in Japanese.\n", "input_text = \"マシーンラーニングについての詩を書いてください。\"\n", "\n", "input_formatted = format_gemma_instruction(input_text)\n", "\n", "input_ids = tokenizer(input_formatted, return_tensors=\"pt\").to(device)\n", "\n", "outputs = model.generate(**input_ids, max_new_tokens=1024)\n", "formatted = unwrap_gemma_response(input_formatted, tokenizer.decode(outputs[0]))\n", "\n", "print(formatted)"]}, {"cell_type": "markdown", "metadata": {"id": "8H1FS_A7vuDI"}, "source": ["Next, you can feed that poem back into the model, asking it to translate it into English."]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "DDK4sYMaBSb8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here's the translation of the Japanese poem:\n", "\n", "**The wave of machine learning, spreading the light of the future,\n", "A vast ocean of data, guiding complex knowledge.\n", "Finding complex patterns, uncovering hidden laws,\n", "Prediction and improvement, shaping the future.\n", "\n", "A neural network, a complex image of the brain,\n", "Learning and evolution, holding infinite possibilities.\n", "Supervised and unsupervised learning,\n", "The power of data, fusing human wisdom.\n", "\n", "The power of machine learning, holding infinite possibilities,\n", "Unraveling complex problems, opening up the future.\n", "The evolution of AI, shaping the future of humanity,\n", "We are ushering in a new era.**\n", "\n", "\n", "\n", "**Explanation of the Poem's Themes:**\n", "\n", "* **Machine Learning's Impact:** The poem highlights the growing influence of machine learning, emphasizing its ability to shape the future.\n", "* **Data as the Foundation:**  The poem emphasizes the role of data as the foundation for machine learning, suggesting that it is the key to unlocking its potential.\n", "* **Complexity and Pattern Recognition:** The poem focuses on the ability of machine learning to identify complex patterns and uncover hidden laws within data.\n", "* **Prediction and Improvement:** The poem highlights the role of prediction and improvement in shaping the future, suggesting that machine learning can be used to make better decisions and create a better world.\n", "* **AI's Role in the Future:** The poem suggests that AI, powered by machine learning, will play a crucial role in shaping the future of humanity.\n", "\n", "\n", "\n", "Let me know if you'd like any further clarification on the poem's meaning or symbolism.\n"]}], "source": ["translation_input_text = \"Translate the following poem from Japanese to English. \\n\\n\" + formatted\n", "translation_input_formatted = format_gemma_instruction(translation_input_text)\n", "\n", "translation_input_ids = tokenizer(translation_input_formatted, return_tensors=\"pt\").to(device)\n", "\n", "translation_output = model.generate(**translation_input_ids, max_new_tokens=2048)\n", "translation_formatted = unwrap_gemma_response(translation_input_formatted, tokenizer.decode(translation_output[0]))\n", "\n", "print(translation_formatted)"]}, {"cell_type": "markdown", "metadata": {"id": "r2uMH5c4v5e3"}, "source": ["And you're all done!"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "[Gemma_2]for_Japan_using_Transformers_and_PyTorch.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}