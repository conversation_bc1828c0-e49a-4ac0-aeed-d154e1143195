"""
Módulo de agentes usando a biblioteca Agno para o aplicativo de artigos acadêmicos ABNT.
"""

import os
import logging
from typing import List, Dict, Any, Optional

# Importar Agno
from agno.agent import Agent
from agno.models.groq import Groq
from agno.models.openai import OpenAIChat
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.knowledge.text import TextKnowledgeBase
from agno.vectordb.lancedb import LanceDb, SearchType
from agno.embedder.openai import OpenAIEmbedder

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ResearchAgent:
    """Agente responsável pela pesquisa de artigos científicos."""

    def __init__(self, api_key: str = None, model_id: str = "llama3-70b-8192"):
        """
        Inicializa o agente de pesquisa.

        Args:
            api_key (str, optional): Chave de API do Groq. Se não fornecida, usa a variável de ambiente GROQ_API_KEY.
            model_id (str, optional): ID do modelo a ser usado. Padrão é "llama3-70b-8192".
        """
        # Configurar a chave de API
        self.api_key = api_key or os.environ.get("GROQ_API_KEY")
        if not self.api_key:
            raise ValueError("API key não fornecida e GROQ_API_KEY não encontrada nas variáveis de ambiente.")

        # Inicializar o agente
        self.agent = Agent(
            model=Groq(id=model_id),
            description="Você é um assistente de pesquisa acadêmica especializado em encontrar informações relevantes para artigos científicos.",
            instructions=[
                "Sua tarefa é pesquisar informações relevantes sobre um tópico específico para um artigo acadêmico.",
                "Use a ferramenta de busca para encontrar artigos científicos e informações acadêmicas relevantes.",
                "Priorize fontes acadêmicas confiáveis como artigos científicos, livros acadêmicos e publicações de universidades.",
                "Forneça informações detalhadas e precisas, incluindo autores, datas e fontes.",
                "Organize as informações de forma clara e estruturada para facilitar a escrita do artigo."
            ],
            tools=[DuckDuckGoTools()],
            show_tool_calls=True,
            markdown=True
        )

    def research(self, topic: str, num_results: int = 10) -> List[Dict[str, Any]]:
        """
        Realiza pesquisa sobre um tópico específico.

        Args:
            topic (str): Tópico a ser pesquisado.
            num_results (int, optional): Número de resultados a serem retornados. Padrão é 10.

        Returns:
            List[Dict[str, Any]]: Lista de resultados da pesquisa.
        """
        prompt = f"""
        Realize uma pesquisa acadêmica completa sobre o tópico: "{topic}".

        Encontre {num_results} fontes acadêmicas relevantes e confiáveis.
        Para cada fonte, forneça:
        1. Título completo
        2. Autores (lista completa)
        3. Data de publicação
        4. URL ou DOI
        5. Resumo ou descrição breve (2-3 frases)

        Organize os resultados em formato estruturado para facilitar a citação no formato ABNT.
        """

        response = self.agent.run(prompt)

        # Processar a resposta para extrair os resultados estruturados
        # Nota: Em uma implementação real, seria necessário um parser mais robusto
        results = []
        try:
            # Implementação simplificada - em produção, use um parser mais robusto
            lines = response.content.split("\n")
            current_result = {}

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                if line.startswith("##") or line.startswith("**") or line.startswith("1."):
                    # Novo resultado
                    if current_result and "title" in current_result:
                        results.append(current_result)
                    current_result = {"title": line.replace("##", "").replace("**", "").strip()}
                elif ":" in line:
                    key, value = line.split(":", 1)
                    key = key.lower().strip()
                    value = value.strip()

                    if "autor" in key or "author" in key:
                        current_result["authors"] = [a.strip() for a in value.split(",")]
                    elif "data" in key or "date" in key or "publicado" in key or "published" in key:
                        current_result["published"] = value
                    elif "url" in key or "doi" in key:
                        current_result["url"] = value
                    elif "resumo" in key or "descrição" in key or "summary" in key or "description" in key:
                        current_result["description"] = value

            # Adicionar o último resultado
            if current_result and "title" in current_result:
                results.append(current_result)

            # Garantir que todos os resultados tenham os campos necessários
            for result in results:
                # Garantir que tenha URL
                if "url" not in result:
                    result["url"] = f"source_{len(results)}"
                # Garantir que tenha autores
                if "authors" not in result:
                    result["authors"] = ["Autor desconhecido"]
                # Garantir que tenha data de publicação
                if "published" not in result:
                    result["published"] = "Data desconhecida"
                # Garantir que tenha descrição
                if "description" not in result:
                    result["description"] = "Sem descrição disponível"

        except Exception as e:
            logger.error(f"Erro ao processar resultados da pesquisa: {e}")
            # Em caso de erro, retornar pelo menos um resultado válido
            if not results:
                results = [{
                    "title": "Fonte de informação genérica",
                    "authors": ["Autor desconhecido"],
                    "published": "Data desconhecida",
                    "url": "source_1",
                    "description": "Informação obtida durante a pesquisa."
                }]

        return results[:num_results]


class OutlineAgent:
    """Agente responsável pela geração de esboços de artigos."""

    def __init__(self, api_key: str = None, model_id: str = "llama3-70b-8192"):
        """
        Inicializa o agente de esboço.

        Args:
            api_key (str, optional): Chave de API do Groq. Se não fornecida, usa a variável de ambiente GROQ_API_KEY.
            model_id (str, optional): ID do modelo a ser usado. Padrão é "llama3-70b-8192".
        """
        # Configurar a chave de API
        self.api_key = api_key or os.environ.get("GROQ_API_KEY")
        if not self.api_key:
            raise ValueError("API key não fornecida e GROQ_API_KEY não encontrada nas variáveis de ambiente.")

        # Inicializar o agente
        self.agent = Agent(
            model=Groq(id=model_id),
            description="Você é um especialista em estruturação de artigos acadêmicos, com profundo conhecimento das normas ABNT.",
            instructions=[
                "Sua tarefa é criar um esboço detalhado para um artigo acadêmico com base em um tópico e resultados de pesquisa.",
                "Siga as normas ABNT para estruturação de artigos acadêmicos.",
                "Crie uma estrutura lógica e coerente que facilite o desenvolvimento do artigo.",
                "Inclua seções e subseções relevantes para o tópico.",
                "Sugira pontos-chave a serem abordados em cada seção."
            ],
            markdown=True
        )

    def generate_outline(self, topic: str, research_results: List[Dict[str, Any]], format_type: str = "standard") -> str:
        """
        Gera um esboço para um artigo com base no tópico e nos resultados da pesquisa.

        Args:
            topic (str): Tópico do artigo.
            research_results (List[Dict[str, Any]]): Resultados da pesquisa.
            format_type (str, optional): Tipo de formato do artigo ("standard" ou "academic"). Padrão é "standard".

        Returns:
            str: Esboço do artigo.
        """
        # Preparar o contexto com os resultados da pesquisa
        research_context = "Resultados da pesquisa:\n\n"
        for i, result in enumerate(research_results, 1):
            research_context += f"{i}. {result.get('title', 'Sem título')}\n"
            research_context += f"   Autores: {', '.join(result.get('authors', ['Desconhecido']))}\n"
            research_context += f"   Publicado: {result.get('published', 'Data desconhecida')}\n"
            research_context += f"   URL/DOI: {result.get('url', 'Não disponível')}\n"
            research_context += f"   Descrição: {result.get('description', 'Sem descrição')}\n\n"

        # Definir o formato do artigo
        format_instructions = ""
        if format_type == "academic":
            format_instructions = """
            O esboço deve seguir o formato acadêmico estruturado com as seguintes seções:
            1. Título
            2. Resumo
            3. Palavras-chave
            4. Abstract (em inglês)
            5. Keywords (em inglês)
            6. Introdução
            7. Metodologia, Material e Métodos
            8. Desenvolvimento
            9. Resultados e Discussão
            10. Conclusões
            11. Referências Bibliográficas
            """
        else:
            format_instructions = """
            O esboço deve seguir um formato padrão de artigo, com introdução, desenvolvimento (dividido em seções temáticas relevantes) e conclusão.
            """

        prompt = f"""
        Crie um esboço detalhado para um artigo acadêmico sobre o tópico: "{topic}".

        {format_instructions}

        Use os resultados da pesquisa fornecidos para informar o esboço:

        {research_context}

        Para cada seção principal, sugira 3-5 pontos-chave ou subseções a serem abordados.
        Indique onde e como as fontes da pesquisa podem ser utilizadas.

        O esboço deve ser detalhado o suficiente para guiar a escrita do artigo completo, mas conciso e bem organizado.
        """

        response = self.agent.run(prompt)
        return response.content


class ArticleAgent:
    """Agente responsável pela geração de artigos completos."""

    def __init__(self, api_key: str = None, model_id: str = "llama3-70b-8192"):
        """
        Inicializa o agente de artigos.

        Args:
            api_key (str, optional): Chave de API do Groq. Se não fornecida, usa a variável de ambiente GROQ_API_KEY.
            model_id (str, optional): ID do modelo a ser usado. Padrão é "llama3-70b-8192".
        """
        # Configurar a chave de API
        self.api_key = api_key or os.environ.get("GROQ_API_KEY")
        if not self.api_key:
            raise ValueError("API key não fornecida e GROQ_API_KEY não encontrada nas variáveis de ambiente.")

        # Inicializar o agente
        self.agent = Agent(
            model=Groq(id=model_id),
            description="Você é um escritor acadêmico especializado em criar artigos completos seguindo as normas ABNT.",
            instructions=[
                "Sua tarefa é criar um artigo acadêmico completo com base em um tópico, esboço e resultados de pesquisa.",
                "Siga as normas ABNT para formatação e citações.",
                "Desenvolva cada seção do esboço de forma completa e coerente.",
                "Utilize as fontes de pesquisa fornecidas para embasar o artigo.",
                "Inclua citações no formato ABNT ao longo do texto.",
                "Crie uma lista de referências bibliográficas no final do artigo."
            ],
            markdown=True
        )

    def generate_article(self, topic: str, outline: str, research_results: List[Dict[str, Any]],
                         format_type: str = "standard", word_counts: Optional[Dict[str, int]] = None) -> str:
        """
        Gera um artigo completo com base no tópico, esboço e resultados da pesquisa.

        Args:
            topic (str): Tópico do artigo.
            outline (str): Esboço do artigo.
            research_results (List[Dict[str, Any]]): Resultados da pesquisa.
            format_type (str, optional): Tipo de formato do artigo ("standard" ou "academic"). Padrão é "standard".
            word_counts (Dict[str, int], optional): Contagem de palavras para cada seção (apenas para formato acadêmico).

        Returns:
            str: Artigo completo.
        """
        # Preparar o contexto com os resultados da pesquisa
        research_context = "Resultados da pesquisa:\n\n"
        for i, result in enumerate(research_results, 1):
            research_context += f"{i}. {result.get('title', 'Sem título')}\n"
            research_context += f"   Autores: {', '.join(result.get('authors', ['Desconhecido']))}\n"
            research_context += f"   Publicado: {result.get('published', 'Data desconhecida')}\n"
            research_context += f"   URL/DOI: {result.get('url', 'Não disponível')}\n"
            research_context += f"   Descrição: {result.get('description', 'Sem descrição')}\n\n"

        # Definir instruções de formato
        format_instructions = ""
        if format_type == "academic":
            if word_counts:
                total_words = sum(word_counts.values()) + word_counts.get("resumo", 200)  # Adicionar o abstract (mesmo tamanho do resumo)
                format_instructions = f"""
                O artigo deve seguir o formato acadêmico estruturado com as seguintes seções e contagens de palavras:

                1. Título
                2. Resumo (aproximadamente {word_counts.get('resumo', 200)} palavras)
                3. Palavras-chave (3-5 palavras-chave separadas por ponto e vírgula)
                4. Abstract (em inglês, aproximadamente {word_counts.get('resumo', 200)} palavras)
                5. Keywords (em inglês, 3-5 palavras-chave)
                6. Introdução (aproximadamente {word_counts.get('intro', 400)} palavras)
                7. Metodologia, Material e Métodos (aproximadamente {word_counts.get('metodo', 400)} palavras)
                8. Desenvolvimento (aproximadamente {word_counts.get('desenv', 600)} palavras)
                9. Resultados e Discussão (aproximadamente {word_counts.get('result', 600)} palavras)
                10. Conclusões (aproximadamente {word_counts.get('concl', 200)} palavras)
                11. Referências Bibliográficas

                Total aproximado: {total_words} palavras (excluindo título e referências)
                """
            else:
                format_instructions = """
                O artigo deve seguir o formato acadêmico estruturado com as seguintes seções:
                1. Título
                2. Resumo (150-250 palavras)
                3. Palavras-chave (3-5 palavras-chave separadas por ponto e vírgula)
                4. Abstract (em inglês, 150-250 palavras)
                5. Keywords (em inglês, 3-5 palavras-chave)
                6. Introdução
                7. Metodologia, Material e Métodos
                8. Desenvolvimento
                9. Resultados e Discussão
                10. Conclusões
                11. Referências Bibliográficas
                """
        else:
            format_instructions = """
            O artigo deve seguir um formato padrão estilo Wikipedia, com introdução, desenvolvimento (dividido em seções temáticas) e conclusão.
            O artigo deve ter aproximadamente 1500-2000 palavras no total.
            """

        prompt = f"""
        Crie um artigo acadêmico completo sobre o tópico: "{topic}".

        Utilize o seguinte esboço como estrutura:

        {outline}

        {format_instructions}

        Utilize os resultados da pesquisa fornecidos como fontes:

        {research_context}

        Instruções para citações no formato ABNT:
        - Citação direta curta (até 3 linhas): use aspas e indique o autor, ano e página entre parênteses. Ex: "texto citado" (SOBRENOME, ano, p. XX).
        - Citação indireta: apenas indique o autor e ano entre parênteses. Ex: (SOBRENOME, ano).
        - Use o URL/DOI fornecido para identificar cada fonte.

        Formate o artigo usando Markdown para os títulos e subtítulos.
        Ao final do artigo, inclua uma seção de "Referências Bibliográficas" no formato ABNT.
        """

        response = self.agent.run(prompt)
        return response.content


class PolishingAgent:
    """Agente responsável pelo polimento e revisão de artigos."""

    def __init__(self, api_key: str = None, model_id: str = "llama3-70b-8192"):
        """
        Inicializa o agente de polimento.

        Args:
            api_key (str, optional): Chave de API do Groq. Se não fornecida, usa a variável de ambiente GROQ_API_KEY.
            model_id (str, optional): ID do modelo a ser usado. Padrão é "llama3-70b-8192".
        """
        # Configurar a chave de API
        self.api_key = api_key or os.environ.get("GROQ_API_KEY")
        if not self.api_key:
            raise ValueError("API key não fornecida e GROQ_API_KEY não encontrada nas variáveis de ambiente.")

        # Inicializar o agente
        self.agent = Agent(
            model=Groq(id=model_id),
            description="Você é um editor acadêmico especializado em revisar e polir artigos científicos.",
            instructions=[
                "Sua tarefa é revisar e polir um artigo acadêmico para melhorar sua qualidade.",
                "Corrija erros gramaticais, ortográficos e de pontuação.",
                "Melhore a clareza, coesão e coerência do texto.",
                "Verifique a consistência das citações e referências no formato ABNT.",
                "Mantenha o estilo acadêmico e formal do texto.",
                "Preserve o conteúdo e a estrutura original do artigo."
            ],
            markdown=True
        )

    def polish_article(self, article: str, format_type: str = "standard") -> str:
        """
        Revisa e poli um artigo para melhorar sua qualidade.

        Args:
            article (str): Artigo a ser polido.
            format_type (str, optional): Tipo de formato do artigo ("standard" ou "academic"). Padrão é "standard".

        Returns:
            str: Artigo polido.
        """
        format_instructions = ""
        if format_type == "academic":
            format_instructions = """
            Certifique-se de que o artigo mantém a estrutura acadêmica com todas as seções necessárias:
            Título, Resumo, Palavras-chave, Abstract, Keywords, Introdução, Metodologia, Desenvolvimento,
            Resultados e Discussão, Conclusões, e Referências Bibliográficas.
            """
        else:
            format_instructions = """
            Certifique-se de que o artigo mantém a estrutura padrão estilo Wikipedia, com introdução,
            desenvolvimento (dividido em seções temáticas) e conclusão.
            """

        prompt = f"""
        Revise e poli o seguinte artigo acadêmico para melhorar sua qualidade:

        {article}

        {format_instructions}

        Instruções específicas:
        1. Corrija erros gramaticais, ortográficos e de pontuação.
        2. Melhore a clareza, coesão e coerência do texto.
        3. Verifique a consistência das citações e referências no formato ABNT.
        4. Mantenha o estilo acadêmico e formal do texto.
        5. Preserve o conteúdo e a estrutura original do artigo.
        6. Não adicione novas informações ou altere significativamente o conteúdo.

        Retorne o artigo completo revisado e polido, mantendo a formatação Markdown.
        """

        response = self.agent.run(prompt)
        return response.content


class AgentTeam:
    """Equipe de agentes para geração de artigos acadêmicos."""

    def __init__(self, api_key: str = None, model_id: str = "llama3-70b-8192"):
        """
        Inicializa a equipe de agentes.

        Args:
            api_key (str, optional): Chave de API do Groq. Se não fornecida, usa a variável de ambiente GROQ_API_KEY.
            model_id (str, optional): ID do modelo a ser usado. Padrão é "llama3-70b-8192".
        """
        self.research_agent = ResearchAgent(api_key, model_id)
        self.outline_agent = OutlineAgent(api_key, model_id)
        self.article_agent = ArticleAgent(api_key, model_id)
        self.polishing_agent = PolishingAgent(api_key, model_id)

    def generate_complete_article(self, topic: str, num_results: int = 10,
                                  format_type: str = "standard",
                                  word_counts: Optional[Dict[str, int]] = None,
                                  do_research: bool = True,
                                  do_generate_outline: bool = True,
                                  do_generate_article: bool = True,
                                  do_polish_article: bool = True) -> Dict[str, Any]:
        """
        Gera um artigo acadêmico completo usando a equipe de agentes.

        Args:
            topic (str): Tópico do artigo.
            num_results (int, optional): Número de resultados de pesquisa. Padrão é 10.
            format_type (str, optional): Tipo de formato do artigo ("standard" ou "academic"). Padrão é "standard".
            word_counts (Dict[str, int], optional): Contagem de palavras para cada seção (apenas para formato acadêmico).
            do_research (bool, optional): Se deve realizar a pesquisa. Padrão é True.
            do_generate_outline (bool, optional): Se deve gerar o esboço. Padrão é True.
            do_generate_article (bool, optional): Se deve gerar o artigo. Padrão é True.
            do_polish_article (bool, optional): Se deve polir o artigo. Padrão é True.

        Returns:
            Dict[str, Any]: Dicionário com os resultados de cada etapa.
        """
        results = {
            "topic": topic,
            "format_type": format_type,
            "word_counts": word_counts
        }

        # Etapa 1: Pesquisa
        if do_research:
            logger.info(f"Realizando pesquisa sobre: {topic}")
            research_results = self.research_agent.research(topic, num_results)
            results["research_results"] = research_results
        else:
            logger.info("Etapa de pesquisa ignorada.")
            results["research_results"] = []

        # Etapa 2: Geração de esboço
        if do_generate_outline and "research_results" in results:
            logger.info("Gerando esboço do artigo")
            outline = self.outline_agent.generate_outline(topic, results["research_results"], format_type)
            results["outline"] = outline
        else:
            logger.info("Etapa de geração de esboço ignorada.")
            results["outline"] = f"# {topic}\n\nEsboço não gerado."

        # Etapa 3: Geração do artigo
        if do_generate_article and "outline" in results and "research_results" in results:
            logger.info("Gerando artigo completo")
            article = self.article_agent.generate_article(
                topic,
                results["outline"],
                results["research_results"],
                format_type,
                word_counts
            )
            results["article"] = article
        else:
            logger.info("Etapa de geração de artigo ignorada.")
            results["article"] = f"# {topic}\n\nArtigo não gerado."

        # Etapa 4: Polimento do artigo
        if do_polish_article and "article" in results:
            logger.info("Polindo artigo")
            polished_article = self.polishing_agent.polish_article(results["article"], format_type)
            results["polished_article"] = polished_article
        else:
            logger.info("Etapa de polimento de artigo ignorada.")
            results["polished_article"] = None

        return results
