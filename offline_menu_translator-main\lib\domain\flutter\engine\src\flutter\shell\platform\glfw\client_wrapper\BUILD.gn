# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/shell/platform/common/client_wrapper/publish.gni")
import("//flutter/testing/testing.gni")

_wrapper_includes = [
  "include/flutter/flutter_engine.h",
  "include/flutter/flutter_window.h",
  "include/flutter/flutter_window_controller.h",
  "include/flutter/plugin_registrar_glfw.h",
]

_wrapper_sources = [
  "flutter_engine.cc",
  "flutter_window_controller.cc",
]

# This code will be merged into .../common/cpp/client_wrapper for client use,
# so uses header paths that assume the merged state. Include the header
# directory of the core wrapper files so these includes will work.
config("relative_core_wrapper_headers") {
  include_dirs =
      [ "//flutter/shell/platform/common/client_wrapper/include/flutter" ]
}

# GLFW client wrapper build for internal use by the shell implementation.
source_set("client_wrapper_glfw") {
  sources = _wrapper_sources
  public = _wrapper_includes

  deps = [
    "//flutter/shell/platform/common:common_cpp_library_headers",
    "//flutter/shell/platform/common/client_wrapper:client_wrapper",
    "//flutter/shell/platform/glfw:flutter_glfw_headers",
  ]

  configs +=
      [ "//flutter/shell/platform/common:desktop_library_implementation" ]

  public_configs = [
    ":relative_core_wrapper_headers",
    "//flutter/shell/platform/common:relative_flutter_library_headers",
    "//flutter/shell/platform/glfw:relative_flutter_glfw_headers",
  ]
}

# Copies the GLFW client wrapper code to the output directory with a _glfw
# suffix.
publish_client_wrapper_extension("publish_wrapper_glfw") {
  public = _wrapper_includes
  sources = _wrapper_sources
  directory_suffix = "glfw"
}

source_set("client_wrapper_library_stubs_glfw") {
  sources = [
    "testing/stub_flutter_glfw_api.cc",
    "testing/stub_flutter_glfw_api.h",
  ]

  defines = [ "FLUTTER_DESKTOP_LIBRARY" ]

  public_deps = [ "//flutter/shell/platform/glfw:flutter_glfw_headers" ]
}

test_fixtures("client_wrapper_glfw_fixtures") {
  fixtures = []
}

executable("client_wrapper_glfw_unittests") {
  testonly = true

  sources = [
    "flutter_engine_unittests.cc",
    "flutter_window_controller_unittests.cc",
    "flutter_window_unittests.cc",
    "plugin_registrar_glfw_unittests.cc",
  ]

  defines = [ "FLUTTER_DESKTOP_LIBRARY" ]

  deps = [
    ":client_wrapper_glfw",
    ":client_wrapper_glfw_fixtures",
    ":client_wrapper_library_stubs_glfw",
    "//flutter/shell/platform/common/client_wrapper:client_wrapper_library_stubs",
    "//flutter/testing",

    # TODO(chunhtai): Consider refactoring flutter_root/testing so that there's a testing
    # target that doesn't require a Dart runtime to be linked in.
    # https://github.com/flutter/flutter/issues/41414.
    "$dart_src/runtime:libdart_jit",
  ]
}
