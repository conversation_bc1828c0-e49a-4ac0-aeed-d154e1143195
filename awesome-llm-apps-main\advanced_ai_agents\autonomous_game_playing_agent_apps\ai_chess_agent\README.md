# ♜ <PERSON> White vs <PERSON> Black: Chess Game

An advanced Chess game system where two AI agents play chess against each other using Autogen in a streamlit app. It is built with robust move validation and game state management.

## Features

### Multi-Agent Architecture
- Player White: OpenAI-powered strategic decision maker
- Player Black: OpenAI-powered tactical opponent
- Board Proxy: Validation agent for move legality and game state

### Safety & Validation
- Robust move verification system
- Illegal move prevention
- Real-time board state monitoring
- Secure game progression control

### Strategic Gameplay
- AI-powered position evaluation
- Deep tactical analysis
- Dynamic strategy adaptation
- Complete chess ruleset implementation


### How to get Started?

1. Clone the GitHub repository

```bash
git clone https://github.com/Shubhamsaboo/awesome-llm-apps.git
cd ai_agent_tutorials/ai_chess_game
```
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```
3. Get your OpenAI API Key

- Sign up for an [OpenAI account](https://platform.openai.com/) (or the LLM provider of your choice) and obtain your API key.

4. Run the Streamlit App
```bash
streamlit run ai_chess_agent.py
```

