"""
Módulo para busca mista (Arxiv + Internet).
"""

import logging
from typing import List, Dict, Any, Optional

from modules.arxiv_search import ArxivSearch
from modules.web_search import WebSearch

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MixedSearch:
    """Classe para busca mista (Arxiv + Internet)."""
    
    def __init__(self, max_results: int = 10):
        """
        Inicializa o objeto de busca mista.
        
        Args:
            max_results (int, optional): Número máximo de resultados a serem retornados. Padrão é 10.
        """
        self.max_results = max_results
        self.arxiv_search = ArxivSearch(max_results=max_results // 2)
        self.web_search = WebSearch(max_results=max_results // 2)
    
    def search(self, query: str) -> List[Dict[str, Any]]:
        """
        Realiza uma busca mista (Arxiv + Internet).
        
        Args:
            query (str): Consulta de busca.
            
        Returns:
            List[Dict[str, Any]]: Lista de resultados da busca.
        """
        # Realizar busca no Arxiv
        arxiv_results = self.arxiv_search.search(query)
        
        # Realizar busca na internet
        web_results = self.web_search.search(query)
        
        # Adicionar fonte aos resultados do Arxiv
        for result in arxiv_results:
            result['source'] = 'arxiv'
        
        # Combinar resultados
        mixed_results = []
        
        # Intercalar resultados (1 do Arxiv, 1 da internet, etc.)
        for i in range(max(len(arxiv_results), len(web_results))):
            if i < len(arxiv_results):
                mixed_results.append(arxiv_results[i])
            if i < len(web_results):
                mixed_results.append(web_results[i])
        
        # Limitar o número de resultados
        mixed_results = mixed_results[:self.max_results]
        
        return mixed_results
