"""
Módulo de utilitários para o aplicativo de artigos acadêmicos ABNT.
Implementa funções auxiliares usadas em todo o aplicativo.
"""

import os
import re
import json
import logging

logger = logging.getLogger(__name__)

def sanitize_topic(topic):
    """
    Sanitiza o nome do tópico para uso em nomes de arquivos.

    Args:
        topic (str): Tópico a ser sanitizado.

    Returns:
        str: Tópico sanitizado.
    """
    # Remover caracteres especiais e substituir espaços por underscores
    sanitized = re.sub(r'[^\w\s-]', '', topic).strip().lower()
    sanitized = re.sub(r'[-\s]+', '_', sanitized)
    return sanitized

def save_article(output_dir, topic, research_results, outline, article, polished_article=None, history_manager=None, article_format="standard", word_counts=None):
    """
    Salva o artigo e metadados.

    Args:
        output_dir (str): Diretório de saída.
        topic (str): Tópico do artigo.
        research_results (list): Resultados da pesquisa.
        outline (str): Esboço do artigo.
        article (str): Artigo gerado.
        polished_article (str, optional): Artigo polido.
        history_manager (HistoryManager, optional): Gerenciador de histórico.
        article_format (str, optional): Formato do artigo ("standard" ou "academic").
        word_counts (dict, optional): Contagem de palavras para cada seção do artigo acadêmico.

    Returns:
        str: Caminho do diretório do artigo.
    """
    import uuid
    from datetime import datetime

    # Gerar ID único para o artigo
    article_id = str(uuid.uuid4())

    # Criar diretório para o tópico
    sanitized_topic = sanitize_topic(topic)
    topic_dir = os.path.join(output_dir, sanitized_topic)
    os.makedirs(topic_dir, exist_ok=True)

    # Salvar resultados da pesquisa
    with open(os.path.join(topic_dir, "research_results.json"), 'w', encoding='utf-8') as f:
        json.dump(research_results, f, ensure_ascii=False, indent=2)

    # Salvar esboço
    with open(os.path.join(topic_dir, "outline.txt"), 'w', encoding='utf-8') as f:
        f.write(outline)

    # Salvar artigo
    with open(os.path.join(topic_dir, "article.txt"), 'w', encoding='utf-8') as f:
        f.write(article)

    # Salvar artigo polido, se disponível
    final_article_path = os.path.join(topic_dir, "article.txt")
    if polished_article:
        with open(os.path.join(topic_dir, "polished_article.txt"), 'w', encoding='utf-8') as f:
            f.write(polished_article)
        final_article_path = os.path.join(topic_dir, "polished_article.txt")

    # Criar arquivo de fontes
    url_to_info = {}
    for i, result in enumerate(research_results):
        # Verificar se o resultado tem a chave 'url'
        if "url" in result:
            url = result["url"]
        else:
            # Gerar uma URL fictícia para resultados sem URL
            url = f"source_{i+1}"
            # Adicionar a URL ao resultado para manter a consistência
            result["url"] = url

        # Adicionar ao dicionário
        url_to_info[url] = result

    with open(os.path.join(topic_dir, "url_to_info.json"), 'w', encoding='utf-8') as f:
        json.dump(url_to_info, f, ensure_ascii=False, indent=2)

    # Adicionar ao histórico se o gerenciador estiver disponível
    if history_manager:
        # Extrair o primeiro parágrafo para resumo
        summary = ""
        if polished_article or article:
            content = polished_article if polished_article else article
            # Tentar extrair o primeiro parágrafo após o título
            paragraphs = [p.strip() for p in content.split('\n\n') if p.strip() and not p.startswith('#')]
            if paragraphs:
                summary = paragraphs[0][:200] + "..." if len(paragraphs[0]) > 200 else paragraphs[0]

        # Criar informações do artigo para o histórico
        article_info = {
            "id": article_id,
            "title": topic,
            "sanitized_title": sanitized_topic,
            "path": topic_dir,
            "summary": summary,
            "sources_count": len(research_results),
            "has_polished": polished_article is not None,
            "created_at": datetime.now().isoformat(),
            "final_article_path": final_article_path,
            "article_format": article_format
        }

        # Adicionar contagens de palavras se disponíveis
        if word_counts and article_format == "academic":
            article_info["word_counts"] = word_counts

        # Adicionar ao histórico
        history_manager.add_to_history(article_info)

    return topic_dir

def display_article(article_path):
    """
    Exibe o conteúdo de um artigo.

    Args:
        article_path (str): Caminho para o arquivo do artigo.

    Returns:
        str: Conteúdo do artigo.
    """
    try:
        with open(article_path, 'r', encoding='utf-8') as f:
            article_content = f.read()
        return article_content
    except Exception as e:
        logger.error(f"Erro ao ler o artigo: {e}")
        return f"Erro ao ler o artigo: {str(e)}"

def display_sources(sources_path):
    """
    Exibe as fontes de um artigo.

    Args:
        sources_path (str): Caminho para o arquivo de fontes.

    Returns:
        str: Conteúdo formatado das fontes.
    """
    try:
        with open(sources_path, 'r', encoding='utf-8') as f:
            sources = json.load(f)

        if not sources:
            return "Nenhuma fonte disponível."

        content = "## Fontes de Pesquisa\n\n"

        for i, (url, info) in enumerate(sources.items(), 1):
            title = info.get('title', 'Sem título')
            authors = info.get('authors', [])
            published = info.get('published', 'Data desconhecida')

            content += f"### {i}. {title}\n\n"

            if authors:
                content += f"**Autores:** {', '.join(authors)}\n\n"

            content += f"**Publicado:** {published}\n\n"

            if 'pdf_url' in info:
                content += f"**PDF:** [Link]({info['pdf_url']})\n\n"

            content += f"**URL:** [Link]({url})\n\n"

            if 'description' in info:
                description = info['description']
                if len(description) > 300:
                    description = description[:297] + '...'
                content += f"**Resumo:** {description}\n\n"

            content += "---\n\n"

        return content
    except Exception as e:
        logger.error(f"Erro ao exibir fontes: {e}")
        return f"Erro ao exibir fontes: {str(e)}"

def format_abnt_reference(info):
    """
    Formata uma referência bibliográfica no estilo ABNT.

    Args:
        info (dict): Informações da fonte.

    Returns:
        str: Referência formatada.
    """
    # Obter os dados necessários
    authors = info.get('authors', ['s.n.'])  # s.n. = sine nomine (sem nome)
    title = info.get('title', 'Sem título')
    year = info.get('published', 'n.d.')  # n.d. = no date (sem data)
    if len(year) >= 4:  # Extrair apenas o ano se a data estiver no formato completo
        year = year[:4]
    url = info.get('url', '')
    categories = info.get('categories', [])

    # Formatar os autores (sobrenome em maiúsculas, seguido de vírgula e iniciais do nome)
    formatted_authors = []
    for author in authors:
        parts = author.split()
        if len(parts) > 1:
            # Último nome como sobrenome, seguido por vírgula e iniciais
            last_name = parts[-1].upper()
            initials = ' '.join([p[0].upper() + '.' for p in parts[:-1]])
            formatted_authors.append(f"{last_name}, {initials}")
        else:
            # Se houver apenas uma parte no nome
            formatted_authors.append(parts[0].upper())

    # Juntar os autores com ponto e vírgula
    authors_str = "; ".join(formatted_authors)

    # Construir a referência no formato ABNT
    reference = f"{authors_str}. **{title}**. {year}. "

    # Adicionar categorias como palavras-chave
    if categories:
        reference += f"Palavras-chave: {', '.join(categories)}. "

    # Adicionar URL
    reference += f"Disponível em: {url}. "

    # Adicionar data de acesso
    from datetime import datetime
    access_date = datetime.now().strftime("%d %b. %Y")
    reference += f"Acesso em: {access_date}."

    return reference
