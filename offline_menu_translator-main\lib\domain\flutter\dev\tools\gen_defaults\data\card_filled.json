{"version": "6_1_0", "md.comp.filled-card.container.color": "surfaceContainerHighest", "md.comp.filled-card.container.elevation": "md.sys.elevation.level0", "md.comp.filled-card.container.shadow-color": "shadow", "md.comp.filled-card.container.shape": "md.sys.shape.corner.medium", "md.comp.filled-card.disabled.container.color": "surfaceVariant", "md.comp.filled-card.disabled.container.elevation": "md.sys.elevation.level0", "md.comp.filled-card.disabled.container.opacity": 0.38, "md.comp.filled-card.dragged.container.elevation": "md.sys.elevation.level3", "md.comp.filled-card.dragged.state-layer.color": "onSurface", "md.comp.filled-card.dragged.state-layer.opacity": "md.sys.state.dragged.state-layer-opacity", "md.comp.filled-card.focus.container.elevation": "md.sys.elevation.level0", "md.comp.filled-card.focus.indicator.color": "secondary", "md.comp.filled-card.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.filled-card.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.filled-card.focus.state-layer.color": "onSurface", "md.comp.filled-card.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.filled-card.hover.container.elevation": "md.sys.elevation.level1", "md.comp.filled-card.hover.state-layer.color": "onSurface", "md.comp.filled-card.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.filled-card.icon.color": "primary", "md.comp.filled-card.icon.size": 24.0, "md.comp.filled-card.pressed.container.elevation": "md.sys.elevation.level0", "md.comp.filled-card.pressed.state-layer.color": "onSurface", "md.comp.filled-card.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity"}