import streamlit as st
from transformers import pipeline, AutoTokenizer
import PyPDF2
import docx
import pdfkit
import tempfile
import os
import torch
from typing import List
import nltk
from nltk.tokenize import sent_tokenize
import re

# Configuração inicial do NLTK
@st.cache_resource
def setup_nltk():
    """Configura e baixa os recursos necessários do NLTK"""
    try:
        # Tenta criar diretório para dados do NLTK se não existir
        nltk_data_dir = os.path.expanduser('~/nltk_data')
        if not os.path.exists(nltk_data_dir):
            os.makedirs(nltk_data_dir)
        
        # Baixa o punkt tokenizer
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            with st.spinner('Baixando recursos necessários...'):
                nltk.download('punkt')
                st.success('Recursos baixados com sucesso!')
    except Exception as e:
        st.error(f"Erro ao configurar NLTK: {str(e)}")
        raise e

# Chama a função de setup
setup_nltk()

def clean_text(text: str) -> str:
    """Limpa e prepara o texto para processamento"""
    # Remove caracteres especiais e múltiplos espaços
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'[^\w\s.,!?;:-]', '', text)
    return text.strip()

def validate_text_length(text: str, tokenizer) -> bool:
    """Verifica se o texto está dentro dos limites aceitáveis"""
    tokens = tokenizer.encode(text)
    return 10 <= len(tokens) <= tokenizer.model_max_length

# Função para dividir texto em chunks usando regex como fallback
def split_text_into_chunks(text: str, tokenizer, max_chunk_size: int = 1000) -> List[str]:
    """Divide o texto em chunks processáveis"""
    # Limpa o texto primeiro
    text = clean_text(text)
    
    try:
        # Tenta usar o NLTK para dividir em sentenças
        sentences = sent_tokenize(text)
    except Exception as e:
        st.warning("Usando método alternativo de divisão de texto...")
        # Fallback: divide por pontuação comum
        sentences = [s.strip() for s in re.split('[.!?]+', text) if s.strip()]
    
    chunks = []
    current_chunk = []
    current_length = 0
    
    for sentence in sentences:
        # Verifica o tamanho em tokens
        sentence_tokens = tokenizer.encode(sentence)
        sentence_length = len(sentence_tokens)
        
        # Se a sentença for muito grande, divide ela
        if sentence_length > max_chunk_size:
            # Divide em partes menores
            words = sentence.split()
            temp_chunk = []
            temp_length = 0
            
            for word in words:
                word_tokens = tokenizer.encode(word + " ")
                if temp_length + len(word_tokens) > max_chunk_size:
                    if temp_chunk:
                        chunks.append(" ".join(temp_chunk))
                    temp_chunk = [word]
                    temp_length = len(word_tokens)
                else:
                    temp_chunk.append(word)
                    temp_length += len(word_tokens)
            
            if temp_chunk:
                chunks.append(" ".join(temp_chunk))
            continue
        
        # Verifica se adicionar a sentença excederia o limite
        if current_length + sentence_length > max_chunk_size:
            if current_chunk:
                chunks.append(" ".join(current_chunk))
            current_chunk = [sentence]
            current_length = sentence_length
        else:
            current_chunk.append(sentence)
            current_length += sentence_length
    
    # Adiciona o último chunk se existir
    if current_chunk:
        chunks.append(" ".join(current_chunk))
    
    return chunks if chunks else [text]  # retorna o texto original se não conseguir dividir

# Função para extrair texto de PDF
def extract_text_from_pdf(file):
    reader = PyPDF2.PdfReader(file)
    text = ""
    for page in reader.pages:
        text += page.extract_text() + "\n"
    return text

# Função para extrair texto de DOCX
def extract_text_from_docx(file):
    doc = docx.Document(file)
    return "\n".join([p.text for p in doc.paragraphs])

def try_summarize_chunk(summarizer, chunk, max_length, min_length, retry_count=0):
    """Tenta sumarizar um chunk com diferentes configurações"""
    try:
        # Configurações para cada tentativa
        configs = [
            # Primeira tentativa: configuração padrão
            {"max_length": max_length, "min_length": min_length, "do_sample": False},
            # Segunda tentativa: mais conservadora
            {"max_length": min(max_length, 150), "min_length": 30, "do_sample": True, "num_beams": 2},
            # Terceira tentativa: muito conservadora
            {"max_length": 100, "min_length": 20, "do_sample": True, "num_beams": 1, "temperature": 0.7},
        ]
        
        if retry_count >= len(configs):
            return None
            
        return summarizer(chunk, **configs[retry_count])[0]['summary_text']
    except Exception as e:
        if retry_count < len(configs) - 1:
            return try_summarize_chunk(summarizer, chunk, max_length, min_length, retry_count + 1)
        raise e

# Função para sumarização
def summarize_text(text: str, model_name: str, max_length: int, min_length: int) -> str:
    try:
        if not text.strip():
            raise ValueError("O texto está vazio")
        
        # Usa modelo mais estável como fallback se necessário
        try:
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            summarizer = pipeline("summarization", model=model_name, tokenizer=tokenizer)
        except Exception as e:
            st.warning(f"Erro ao carregar modelo {model_name}, usando modelo alternativo...")
            model_name = "facebook/bart-large-cnn"  # Modelo mais estável como fallback
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            summarizer = pipeline("summarization", model=model_name, tokenizer=tokenizer)
        
        # Valida o texto
        if not validate_text_length(text, tokenizer):
            st.warning("O texto pode ser muito curto ou muito longo. Tentando processar em partes...")
        
        # Divide o texto em chunks menores para processamento mais seguro
        chunk_size = min(tokenizer.model_max_length - 100, 500)  # Tamanho mais conservador
        chunks = split_text_into_chunks(text, tokenizer, max_chunk_size=chunk_size)
        
        if not chunks:
            raise ValueError("Não foi possível dividir o texto em partes processáveis")
        
        summaries = []
        total_chunks = len(chunks)
        
        for i, chunk in enumerate(chunks, 1):
            try:
                # Atualiza o progresso
                progress = (i / total_chunks) * 50
                st.session_state.progress_bar.progress(int(progress))
                
                # Remove espaços em excesso e caracteres especiais
                chunk = clean_text(chunk)
                if not chunk:
                    continue
                
                # Verifica o tamanho do chunk
                chunk_tokens = tokenizer.encode(chunk)
                if len(chunk_tokens) < 10:  # Muito curto para sumarizar
                    continue
                
                # Tenta sumarizar com sistema de retry
                summary = try_summarize_chunk(
                    summarizer,
                    chunk,
                    max_length=max_length,
                    min_length=min_length
                )
                
                if summary:
                    summaries.append(summary)
                
            except Exception as e:
                st.warning(f"Erro ao processar parte {i} do texto. Tentando próxima parte...")
                continue
        
        if not summaries:
            raise ValueError("Não foi possível gerar resumo para nenhuma parte do texto")
        
        final_summary = " ".join(summaries)
        
        # Se houver múltiplos chunks, tenta fazer um resumo final
        if len(summaries) > 1:
            try:
                final_summary = try_summarize_chunk(
                    summarizer,
                    final_summary,
                    max_length=max_length,
                    min_length=min_length
                ) or final_summary
            except Exception as e:
                st.warning("Não foi possível combinar os resumos, mostrando resultados separados")
        
        return final_summary
    
    except Exception as e:
        st.error(f"Erro durante a sumarização: {str(e)}")
        return ""

# Função para realçar termos-chave
def highlight_keywords(text, keywords):
    for kw in keywords:
        text = text.replace(kw, f"**{kw}**")
    return text

# Função para sugerir palavras/frases importantes (simples)
def suggest_keywords(text, n=5):
    words = text.split()
    freq = {}
    for w in words:
        freq[w] = freq.get(w, 0) + 1
    sorted_words = sorted(freq.items(), key=lambda x: x[1], reverse=True)
    return [w[0] for w in sorted_words[:n]]

# Streamlit UI
st.title("Sumarizador Inteligente de Artigos e Documentos")

# Sidebar com configurações avançadas
with st.sidebar:
    st.header("Configurações Avançadas")
    st.write("Ajuste os parâmetros de sumarização para melhorar os resultados.")
    
    advanced_options = st.checkbox("Mostrar opções avançadas")
    
    if advanced_options:
        max_chunk_size = st.slider(
            "Tamanho máximo de cada chunk:",
            min_value=100,
            max_value=2000,
            value=1000,
            help="Controla o tamanho máximo de cada parte do texto processada"
        )
        
        do_sample = st.checkbox(
            "Usar amostragem",
            value=False,
            help="Permite maior variabilidade no resumo, mas pode ser menos preciso"
        )
        
        num_beams = st.slider(
            "Número de beams:",
            min_value=1,
            max_value=4,
            value=2,
            help="Aumenta a qualidade mas reduz a velocidade"
        )
    else:
        max_chunk_size = 1000
        do_sample = False
        num_beams = 2

st.write("Faça upload de um artigo científico, trabalho acadêmico, documento técnico ou insira o texto abaixo para obter um resumo automático e preciso.")

input_method = st.radio("Como deseja inserir o documento?", ("Upload de arquivo", "Inserir texto"))

text = ""
if input_method == "Upload de arquivo":
    uploaded_file = st.file_uploader("Escolha um arquivo (PDF ou DOCX)", type=["pdf", "docx"])
    if uploaded_file:
        try:
            with st.spinner("Extraindo texto do documento..."):
                if uploaded_file.type == "application/pdf":
                    text = extract_text_from_pdf(uploaded_file)
                elif uploaded_file.type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                    text = extract_text_from_docx(uploaded_file)
                
                if text.strip():
                    st.success("Texto extraído com sucesso!")
                    with st.expander("Visualizar texto extraído"):
                        st.text(text[:1000] + "..." if len(text) > 1000 else text)
                else:
                    st.error("Não foi possível extrair texto do documento")
        except Exception as e:
            st.error(f"Erro ao processar o arquivo: {str(e)}")
else:
    text = st.text_area("Cole o texto aqui:", height=300)

col1, col2 = st.columns([2, 1])
with col1:
    model_name = st.selectbox(
        "Escolha o modelo de sumarização:",
        [
            "facebook/bart-large-cnn",  # Modelo mais estável como padrão
            "sshleifer/distilbart-cnn-12-6",  # Versão mais leve do BART
            "google/pegasus-xsum",  # Bom para textos curtos
            "t5-base",  # Opção alternativa
        ],
        index=0,  # Seleciona o primeiro modelo como padrão
        help="O modelo BART-CNN é recomendado para melhor estabilidade"
    )

with col2:
    level = st.slider(
        "Nível de detalhe do resumo:",
        min_value=50,
        max_value=500,
        value=150,
        help="Valores menores geram resumos mais curtos e concisos"
    )

if st.button("Gerar Resumo") and text:
    # Inicializa a barra de progresso no estado da sessão
    st.session_state.progress_bar = st.progress(0)
    
    with st.spinner("Gerando resumo..."):
        try:
            st.info("Processando o texto...")
            
            # Gera o resumo com as configurações avançadas
            summary = summarize_text(text, model_name, max_length=level, min_length=int(level/2))
            
            if summary:
                st.session_state.progress_bar.progress(75)
                st.info("Gerando palavras-chave...")
                
                # Gera keywords e highlight
                keywords = suggest_keywords(summary, n=7)
                summary_highlighted = highlight_keywords(summary, keywords)
                
                st.session_state.progress_bar.progress(100)
                
                # Mostra resultados
                st.markdown("### Resumo:")
                st.markdown(summary_highlighted)
                st.markdown("---")
                st.markdown("**Termos-chave sugeridos:** " + ", ".join(keywords))
                
                # Exportação
                col1, col2 = st.columns(2)
                with col1:
                    if st.download_button("📄 Baixar Resumo em TXT", summary, file_name="resumo.txt"):
                        st.success("Resumo baixado com sucesso!")
                
                with col2:
                    if st.button("📑 Gerar PDF"):
                        with st.spinner("Gerando PDF..."):
                            try:
                                with tempfile.NamedTemporaryFile(delete=False, suffix=".html") as tmp_html:
                                    html_content = f"""
                                    <html>
                                        <head>
                                            <style>
                                                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                                                h1 {{ color: #2e6c80; }}
                                                .keywords {{ color: #666; font-style: italic; }}
                                            </style>
                                        </head>
                                        <body>
                                            <h1>Resumo</h1>
                                            <p>{summary}</p>
                                            <p class="keywords"><b>Palavras-chave:</b> {", ".join(keywords)}</p>
                                        </body>
                                    </html>
                                    """
                                    tmp_html.write(html_content.encode("utf-8"))
                                    tmp_html.flush()
                                    
                                    pdf_path = tmp_html.name.replace(".html", ".pdf")
                                    pdfkit.from_file(tmp_html.name, pdf_path)
                                    
                                    with open(pdf_path, "rb") as f:
                                        if st.download_button("⬇️ Baixar PDF", f, file_name="resumo.pdf"):
                                            st.success("PDF baixado com sucesso!")
                                    
                                    # Limpa arquivos temporários
                                    os.remove(pdf_path)
                                    os.remove(tmp_html.name)
                            except Exception as e:
                                st.error(f"Erro ao gerar PDF: {str(e)}")
            else:
                st.error("Não foi possível gerar o resumo. Por favor, tente com um texto diferente ou outro modelo.")
        
        except Exception as e:
            st.error(f"Ocorreu um erro inesperado: {str(e)}")
        finally:
            if 'progress_bar' in st.session_state:
                st.session_state.progress_bar.empty()
