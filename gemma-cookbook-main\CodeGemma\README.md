# CodeGemma

This folder is organized into several categories, each focusing on a specific aspect of working with CodeGemma models:

* [Inference](#inference): How to load and run CodeGemma for inference
* [Finetuning](#finetuning): Dive into finetuning CodeGemma models for specific tasks and domains

## Inference
| Notebook Name | Description |
| :--------------------------------------------------------------------------------------------- | ------------------------------------------------ |
| [[CodeGemma_1]Common_use_cases.ipynb]([CodeGemma_1]Common_use_cases.ipynb)   | Illustrate some common use cases for CodeGemma . |

## Finetuning
| Notebook Name | Description |
| :--------------------------------------------------------------------------------------------- | ------------------------------------------------ |
| [[CodeGemma_1]Finetune_with_SQL.ipynb]([CodeGemma_1]Finetune_with_SQL.ipynb) | Fine-Tuning CodeGemma on the SQL Spider Dataset. |
