{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Placeholder"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Placeholder"]}, {"cell_type": "markdown", "metadata": {"id": "9xLDGk41C7IF"}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 513, "referenced_widgets": ["73fc3ac47de840cda7a07297153404d5", "8c8b54f4d34a4b59a5a2513bbc082297", "7bd2abffca3f4193afffd3f1a7c8ff8a", "9ecdc7e0e9c247c59e9eab93455153b8", "84f4e9f7ff6f433bbf13078e43ebbe2a", "7475f8a404624b1b844d8148fabb493b", "9d4aae9ebe594bd6875a5963eb6d77ed", "62b6d78abada429393c499e0479eb56e", "2aecce21a3a04971b4f5c9fd0b059738", "2af66136423c41c6908c5a61c6fa2e5d", "8bf92078d48448848402ede49b1b5182", "78ae8aea29e04420bcfe616061934d98", "e22b1706b6be49af8e31d2c512ccaaf6", "71ac48ba667b4af291df3a33e7643c6c", "7d16f0e5aaec4429a5b8d3c4878ea74a", "a21de38381c241309bc798be604c3bf5", "f26f1de0f3f34010ac0d37d6f3ed74b6", "0af61827920e415daf64cfcb1685ee63", "77a82cf2255b43e088812087af4d8ead", "fad668efab014d28877087f38f3c034e", "e764888263e54f0ba2f603992d678096", "2dccbbbdfbf645b8b5896953f6e8a59b", "45385885a26e42d0b1f52eda382fd6f4", "ccb21e1b7fc14a6ab10689154ab2b94a", "f5c7ab8d65c4418a943a51a401720f67", "f6aaa74bce474abcbc9daf0515b6ffbd", "76bd63e567e94650a82272ca6fd85011", "d3cac12f194a40a187bbba68da36173c", "30439a7bf22f416cbc01cfe87e433e1f", "3265b3490b11426a8aa44d541981a296", "78a31b91439c4c99a07f8f8d8dfb548f", "d2a3a751ac2f491c9e7cb7a4092ab0b6", "b652bb727c1148c4aac9ed751920e3ae", "14e519d5e0834fd9a70a111140f8429a", "41f3770e6d864c1794a1fa93feaf8605", "6aa1670b808b42db894756b904243947", "5757f1d68b8f4571a05486d846127b14", "14cbf1b61170470bbb9d63e31d0ca310", "3324ce12dc3e45fab9984eb83a92eb39", "0ccabea3b39d4b148d420e26b53d3990", "2d65a3b83b524eeaafde8e406e90198f", "d179ef65b2b842d69548302c2f7a31be", "2362fd437a134c30b147df947ee97128", "7f22e31fc7174ff2bfa8bc2624d33dc7", "643db314d8e94feeb9cd8747909eaabe", "b80bb8f2cc92400aa34934978f9bc865", "03f0c63c572249aabb7fb61272af1f1b", "4113f045f9bc440b8dfaed5dfab14c1b", "86a231a75d4a4586a53e4c7e74c63550", "573653d0298246868e5d93069ca0fef5", "c20a42f12c4545dfafdba22267e44d4b", "4b74060c0bb44a35b847e6752b93e463", "7e211233455342d48bd60f6d499b91f5", "520ca077c6854e6fa1e019c0b209eae7", "8f1fce549e494998b13f523bd793d300", "bbb5e6a0a8b64d10802fbf3e89778306", "0cef8128eeba4638bd268f9a1a364427", "399cc918cec24db7b99af9e5f3c5adb4", "3a611a2e00be4da6833172d928802fb9", "0f2f303ae91349e29132055105fcbdec", "17c08a96e04244a995e658eeb5a69699", "9269dee0b0104b22b4043d3661f79350", "a405cbc6d42d4f8f8be7c05f4e34128c", "e702e948bb34481d995c75be223c4e16", "c9949e2b99d14fa0bc4cfc88f42c2afb", "48bbf9b662af47c5b99ebe24a6b70549", "c1af10a57c0a40cf818d0974facdcc36", "e4c1ff933c6e4ef49afbedffcdd99d5c", "3caf3a6d48da403099bcdee89bda6949", "7e0bbe2417e0420db1502390ba7c6c2d", "5522658c84dc47a491f5deb08353b631", "aecb331a01a44c9e92c74840faaf2616", "caadc7751ff847b4a831dd6b2b53e60b", "f0dd55c8f71a4f47a91a7018ab89d092", "7a2d1ba8441e4a368a505c38d2c811ad", "6657bbba902e43429224ed9c426389fe", "a25ba73fb99448a3b46ac3571e2db0be", "c2f518986b724620bbc67f7b6ede6d96", "ed214eea73f54928af276883ddd53b5e", "b241468a1d684fbb820baff6b9f9e362", "0f7da532813445f1be01977cdec364ab", "de7ac823a59d488995aa37bfbcec2e5b", "fc5321766b8742ca97c4a839ba30d421", "c4256f50e38e481083f3d366bfd91d3f", "f7e4e90fb3fe4bbb85adb039404dd8e2", "e0f8b5f130064bd69f4f3298e9536b5a", "20956dab8f3e4f22830b4d9e56a5232a", "4efe313f923c4ae585fb8a4353cecd97", "3af59a3e9ac2430da201262fc54b5998", "ad746cc40cdb461bba8cf04033d4afc5", "3d09770f22854ca38213d4a64ace6df3", "910df16ec7494eb68fb1f8ce95046414", "080c7eed9cc0475980e85a530c91e616", "e4fe961d149b461fb1d168d226c8aa1d", "2a588c12d3814de495fa865a63629ba8", "607a235508e54fdda0aef75311c7a213", "383e14f14b0d4328a80e6b0283c421fa", "913324d3476a4d04887d200f91afc553", "4c0c6892c4f347deb4a85544664ff1e1", "a863bb17b5984981bf9bd4ff93e438f4", "b28286deba7c4152a8d2c21ea0d19402", "fb9e17975b2349de81115173af6ac17a", "f3b068de4fa6466e88970b73c868de0a", "2127bee2928e43578b8b51e3c1fdfabf", "a97f3d8c7bdf4292ae8ed133f059f635", "67053bf18cd04347a0f2d2ff6134ae88", "80e56300aee14b4386832aa1618c5d43", "d096e33830754ea5b2e8f102548b805a", "a913ea3370404f5f972754715889aced", "35db2f7869f44aeab422409d27d5f7ec"]}, "id": "QmUBVEnvCDJv", "outputId": "f0d7f72f-8166-439b-96da-ed7e30f8bdb3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "🦥 Unsloth Zoo will now patch everything to make training faster!\n", "==((====))==  Unsloth 2025.3.19: Fast Qwen2_5_Vl patching. Transformers: 4.50.0.\n", "   \\\\   /|    Tesla T4. Num GPUs = 1. Max memory: 14.741 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.6.0+cu124. CUDA: 7.5. CUDA Toolkit: 12.4. Triton: 3.2.0\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.29.post3. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "73fc3ac47de840cda7a07297153404d5", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/5.97G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "78ae8aea29e04420bcfe616061934d98", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/267 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "45385885a26e42d0b1f52eda382fd6f4", "version_major": 2, "version_minor": 0}, "text/plain": ["preprocessor_config.json:   0%|          | 0.00/575 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.50, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "14e519d5e0834fd9a70a111140f8429a", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/7.33k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "643db314d8e94feeb9cd8747909eaabe", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.json:   0%|          | 0.00/2.78M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bbb5e6a0a8b64d10802fbf3e89778306", "version_major": 2, "version_minor": 0}, "text/plain": ["merges.txt:   0%|          | 0.00/1.67M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c1af10a57c0a40cf818d0974facdcc36", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/11.4M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c2f518986b724620bbc67f7b6ede6d96", "version_major": 2, "version_minor": 0}, "text/plain": ["added_tokens.json:   0%|          | 0.00/605 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3af59a3e9ac2430da201262fc54b5998", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/614 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a863bb17b5984981bf9bd4ff93e438f4", "version_major": 2, "version_minor": 0}, "text/plain": ["chat_template.json:   0%|          | 0.00/1.05k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastVisionModel # FastLanguageModel for LLMs\n", "import torch\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/Llama-3.2-11B-Vision-Instruct-bnb-4bit\", # Llama 3.2 vision support\n", "    \"unsloth/Llama-3.2-11B-Vision-bnb-4bit\",\n", "    \"unsloth/Llama-3.2-90B-Vision-Instruct-bnb-4bit\", # Can fit in a 80GB card!\n", "    \"unsloth/Llama-3.2-90B-Vision-bnb-4bit\",\n", "\n", "    \"unsloth/Pixtral-12B-2409-bnb-4bit\",              # Pixtral fits in 16GB!\n", "    \"unsloth/Pixtral-12B-Base-2409-bnb-4bit\",         # Pixtral base model\n", "\n", "    \"unsloth/Qwen2-VL-2B-Instruct-bnb-4bit\",          # Qwen2 VL support\n", "    \"unsloth/Qwen2-VL-7B-Instruct-bnb-4bit\",\n", "    \"unsloth/Qwen2-VL-72B-Instruct-bnb-4bit\",\n", "\n", "    \"unsloth/llava-v1.6-mistral-7b-hf-bnb-4bit\",      # Any Llava variant works!\n", "    \"unsloth/llava-1.5-7b-hf-bnb-4bit\",\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastVisionModel.from_pretrained(\n", "    \"unsloth/Qwen2.5-VL-7B-Instruct-bnb-4bit\",\n", "    load_in_4bit = True, # Use 4bit to reduce memory use. False for 16bit LoRA.\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for long context\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "SXd9bTZd1aaL"}, "source": ["We now add LoRA adapters for parameter efficient finetuning - this allows us to only efficiently train 1% of all parameters.\n", "\n", "**[NEW]** We also support finetuning ONLY the vision part of the model, or ONLY the language part. Or you can select both! You can also select to finetune the attention or the MLP layers!"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "6bZsfBuZDeCL"}, "outputs": [], "source": ["model = FastVisionModel.get_peft_model(\n", "    model,\n", "    finetune_vision_layers     = True, # False if not finetuning vision layers\n", "    finetune_language_layers   = True, # False if not finetuning language layers\n", "    finetune_attention_modules = True, # False if not finetuning attention layers\n", "    finetune_mlp_modules       = True, # False if not finetuning MLP layers\n", "\n", "    r = 16,           # The larger, the higher the accuracy, but might overfit\n", "    lora_alpha = 16,  # Recommended alpha == r at least\n", "    lora_dropout = 0,\n", "    bias = \"none\",\n", "    random_state = 3407,\n", "    use_rslora = False,  # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", "    # target_modules = \"all-linear\", # Optional now! Can specify a list if needed\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "vITh0KVJ10qX"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We'll be using a sampled dataset of handwritten maths formulas. The goal is to convert these images into a computer readable form - ie in LaTeX form, so we can render it. This can be very useful for complex formulas.\n", "\n", "You can access the dataset [here](https://huggingface.co/datasets/unsloth/LaTeX_OCR). The full dataset is [here](https://huggingface.co/datasets/linxy/LaTeX_OCR)."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 177, "referenced_widgets": ["5e4f3f4910d1477b924362c095a0cedd", "b5a01de096fa4e2daf66e235bd5686bb", "e9195f3a3e29475f941c302fe2a88a83", "0d6f6fb66522436786728d8602e2f466", "94b72e08764c486d8eed43a4557f1d98", "8e8281a56a3d4879b6b376077890c920", "630894e18b264f7dae8eddb17304a1cd", "697fc92abb824cf4a9bc78709640ba54", "eb6d552d6c1d4e82a521b29b36ae3db0", "7572f2d06114442ba94fc22761a55346", "c6dcd15e155949cbb9698e43e55b7a90", "7365368b63d9425db5d4e5514e6e0586", "5997909fc34645ce9a03d82ab0932766", "ea4ebb90b708493c85fa81fecf4d60c0", "227724312eb045a58616f63c3a5b7407", "4f58e8a2ddbb4a73a2421ea37c68fea9", "7e89e5546e394162b7b0f793478527ff", "db9ea237050045d8861d76bb3b995753", "3a484309bbe148abb2ef05452850728a", "5e601d00c0094d999a5b37b9555c111c", "e7bd6c674af541d08c0acb5e47579156", "927147d620b0426cb46b49b6d8dc2ffc", "4ebb36e479884847bcef5736e276dfb9", "7b5a059a8ef84817851d109fd7e3a7b5", "0e949ae97d544d65af09cebe63041b8e", "c817a779a9ff4a848391a7c7dff1e4ca", "abae462b98c84006b71d4a62c8a47d5a", "dc1a755aae6c44849d818fd96c046649", "2650d2ce196e404a98cbfac96d0a9bef", "36d579fdc2fb4c59b827f5bf7a92abd4", "fccec1c2a389401eb614f5f1cfb15f0c", "7b4d303966274c71b50312df12e7b87a", "ce4bb2dffcf84ae9bf1f8ac86b2d768e", "639b886f72e24951909e4ffb6cfa247d", "7b13cf1727a04f5ca6de1f7eb536d800", "631ce764f4684225a3c1e4455717a555", "48af93dada914d8b88d5ffffd24ac666", "4bd339c9d3ee43f39678d2c18baeef53", "b1b12b8def994e748b08b27abcef4b87", "bc38022049ce47628d8c215959c38b91", "331d5d2bc67f44f283fc82481cb60411", "0a5ad3bd338b4e6ea94a11a199d2f5ba", "5c41f695158641f485349c20a1405cfd", "89bc17f1911a43cca7196da232500fd6", "f5629e8c82c848218f69810e1b21b0e4", "5244452af0734f3183a7384435f9e1fe", "69f645decfb64baca11cfbdb9773a458", "d1788fabe3e34f6eb60fecab3c7490ba", "8f423066ba8f421495f934a269053cc1", "569d1435402e4533b0427a4b191b6754", "b834a7ab12544f9b8305f5728677e921", "e72ac7c9b7824c81a6e77e6fadad9f9c", "828ad6e5fcfb44b3b4559ec5682d095d", "dac611678f4949ae98051bdc975e083f", "0a21bae13a3c46509db70dbe8e9de5eb"]}, "id": "LjY75GoYUCB8", "outputId": "ece64855-55d4-4011-c5b8-33a9bb622734"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5e4f3f4910d1477b924362c095a0cedd", "version_major": 2, "version_minor": 0}, "text/plain": ["README.md:   0%|          | 0.00/519 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7365368b63d9425db5d4e5514e6e0586", "version_major": 2, "version_minor": 0}, "text/plain": ["train-00000-of-00001.parquet:   0%|          | 0.00/344M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4ebb36e479884847bcef5736e276dfb9", "version_major": 2, "version_minor": 0}, "text/plain": ["test-00000-of-00001.parquet:   0%|          | 0.00/38.2M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "639b886f72e24951909e4ffb6cfa247d", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split:   0%|          | 0/68686 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f5629e8c82c848218f69810e1b21b0e4", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating test split:   0%|          | 0/7632 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from datasets import load_dataset\n", "dataset = load_dataset(\"unsloth/LaTeX_OCR\", split = \"train\")"]}, {"cell_type": "markdown", "metadata": {"id": "W1W2Qhsz6rUT"}, "source": ["Let's take an overview look at the dataset. We shall see what the 3rd image is, and what caption it had."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "bfcSGwIb6p_R", "outputId": "f354ceda-b2d2-4045-9f83-202fef2b7d5a"}, "outputs": [{"data": {"text/plain": ["Dataset({\n", "    features: ['image', 'text'],\n", "    num_rows: 68686\n", "})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 67}, "id": "uOLWY2936t1n", "outputId": "c982fa75-e70f-4851-a5e2-1b1a9c8409d5"}, "outputs": [{"data": {"image/jpeg": "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", "image/png": "iVBORw0KGgoAAAANSUhEUgAAAUAAAAAyCAIAAACib5WDAAAYrUlEQVR4Ae3cebSuUx0HcCSFEBHKlNZCQi2zureMyTwrY2UeQlSaXNdQxjSSMltFSJF5bjCzDMuilUUUIjSKotLtc+83+z73eYd7znve877n3vXuP56zn/389m/av2FP75l10qRJswzKQAMDDcyYGphtxmS7P1wPgl1/9N4nqoZ77I/4TOvA3dU+bP/5z39mnXXWPtnSgGwfNGC4lf/+9799oD1kkjOnA1N6tD9kPUwHELbZZ5/9hRde+Oc//zkd0Ol9FgteffXVsR/apyfHTP6dCT3zzDP/+Mc/ZpttTPvImGauYxuh9BdffPHvf//7v/71r46RpCNPM5Zc7uijj37729/+ne98J9m4Y7Riwete9zpPKb1jJIOOo6qBjPinP/3pt7zlLddcc82///1vBjCqFDtHzhxnpsIraP/nP//5IossMvfcc3/5y18mHTfuWMZky0022YSKf/rTnwrJSHSMTd8//vGP55133qabbvr888/Dg9uOsQ06jpIG4sCvvPLKxhtvbNzFboS48SiRGwnamTADS25i5+abbz7PPPMYg85j2yyz8F7J/IYbbrjyyiu/8pWvwPmGN7xB/uwAp0HSy7xggw02uO2222688cbXv/71HeAZdOmBBpiQYoAuv/zy973vfccee+xf//pXa6gMYg8YGAaJkXj/WOub3HjVVVfxsb/85S+m0JasI2EyqVs4gNACmD8rI0Go+9/+9jcYllpqKalYZZCBR6LPUe2blHv77bdzp2OOOQatkUzlRonV2Yfh62MelDNImBxYsJxzzjlly66wbCrO8cRjyA3DSHCK6/POO6/JM+NQHwmqbvUl0RjhpFsSTRdPBnG6UovaIFdffXWQpk7TRdsXgJlnCk3XfEyMPPPMMw8++OA55pgj8XLkahUXIMmojxwbPCxj5Hi6hWG6dtwtQmMHD5GHKDWwTOLG1JBVNdmJAzNoCaSKZezUX375ZRqXMKeM0VDHqZf8D9F0esOSaCK3ZBrfG4pjgQojUYYYkU278DxE4N5L14kDE6mLGzBU0xXtWABT33e/+13PDTfc0DOq771O21MkbFJ6F91migqHp8Z0sTez8sorW94LK1qqnAeg2tL3urgc1TXlxCcATT+Vxixrv/SlL33xi18kcmymfJ0RK00cmFTkVFQoRUmLp0kpIR3STJw4EYB6bdSHpQJ94ZycJae971IolvEoLWGpFRUILS/NdtZbbz0w7R0YqqqNtsfciuJw2yPs/PPPb33umdfhIqnBFx3W1FgDa3wFjw1jqlLdrqdtwIW3kQxxI9HGFvxPMbfJ9la+ptEzzGhnDEa2OqbVrwB8AlBspqAqFYKAIReYPAvyAjPDVZo4sB0gCVZRIbCSFk8LSzJffPHFRx11VFXdnYlNiXBKAgoqBUmhWBYepSUsFchSMTYYlknOPvtswwNh+dSqAlWxUTCtMLfq3lk7S3388cfPPffcxx57zFM9cbAzbHoRHOe23J9++umXXnqpqsamOA2f2SN31fGCCy5Ya621jOl73vOeww47jDYwEyvXF8I//elPKto1NsWWRthEgTb2oHsbDPifYm6T7a1QSaNnJIr3/vjHP77++uvBBFv5Gj/0CUBTHw6HCRNO4J3nX3311Ysttli2poItmgHTJgQU9sZOZarKwpOROO200/785z+TiiRbb7219lxGIZvbEXvsscc555zzqU99yjavFqrvQBjIFR0///nP/+hHP1I/5ZRTHJozAght3N9///2sxwbghz70IWBarrvuOpaEpQ9/+MNrrrmmLo3G6uDXIY30izfjAb4Vb4z41FNP/dnPfrbffvtB+PGPf3y++ebbZpttxo8fr2MJHK26d9wOuSNl04TDDz/8d7/7nfqee+7ZMTYdyXjkkUf+5Cc/eec73/mLX/zC0Gy22Wa01CjCZI1PmvTkk0/+8pe/3GWXXfR985vfTN7vf//7H/vYx3JjIZzcddddAjTH/tWvfrXoooti8o1vfGNoBaA8jRcvEol23nnnueaaq7TXKm0Ggr1973vf+/3vf6+7Kclee+1lUsDfTj75ZPEI/o022shwE8ftl69//euXXnopKfQycG9605vKkGl817veteWWW7LJj370o0UDNQ7dw1lppZWeeuopA7HPPvuwE6xO1su0mqnxP6Zfw32epCI55wnHZ511ltDOJahVi+QmYwhd6txJF9qpdh96HRXAa6+9ttj/3HPPPfLIIwxF1MCAAeC6biyiwm4SSjzTgiVf0QVZJRdOEp4du/tkjKsApa6jXS5fP/GJTyDhvqtPv/nNb9T333//Wscg4WyChbH3tUa3oO19JSJLJjjHP1ZlVG7geBmTjXymhaplXZJedtll4XnxxRdPxaAo9L/ccsvtuOOOvOjZZ59997vf7RgcQKM+IQTv0yGHHLLDDjt89rOfbaUiCldCpfZE7uGHH+ZUpHjwwQchRBfMgQceqEVoMNxePR2/uf2irgswU5jqkFUNoGigkUNXA2DA6qGHHhpUnjXNuLyhsdgYoQQga2aNjUrQ2N8ydeJKHQpL5VTS4Gc+8xkmLgyLcwsvvLBP0pTrB4L9Cius4DoRJVbDvFeabVpIOAX3/x8gUfnhD38oOggWCy20EO8VbumRpnxdYIEFjAE7A3bSSSdpEZv5ueSPJXV0a0EdDOx33HGHp15VctV6SIgLBGSmKi67alx66aWdPIlQ5t4+1RhmUhnjKqqO6yxDoahUOsaDJX3vvPNOHoh/bH/kIx/BqrmMdvirmOmHxi655JI11ljD1SIC0hIM4pcn3QabRnMTWdr4Gvq3vvWtPPMb3/gG95Npq2pRhxM5OfzXv/61QSRObVAKG+bnSnmtMgbPMsssI4HDb0QwYHDNbO+77z5Ts1133dXMCLzuku26667LhUAi/Y53vKM6ZDpqXH/99QlIAzihgUYOkdPOjRWVSNGomSqH6sB4QVRU+9T/V8yVYgzUr732WiKZjJV2NxmoJucNJld8mGoCXGCGXkkYk9ZM3qTWP/zhD8zFPFY7nDBTrtF69NFHGZBZMeuha3nAJMrX6L1Kjma1szzzbXacpK2xClPqIoU1gtD+gQ98wDQMGPyeN910k8FgrCDDYWiZgIj9jJ6xYq+KVr1fBZNIe2KSoR900EHGxcycHbP18lVFAaPQKiu86KKL0pinzOYXGslykUW7GabwLUdZYpxwwgnU0qjzqAiMcN/UEoItsc9kVdGS1yoD8Gi3LYxKYUCG5IfA8pXhielm8qQIrQxEbcgCTA+rrrqqugJDjcMI8slPfjKzLTbTSjP64seTwZhpM9Hw4zmmyjRrYMKIf7feeisWb7nllrvvvptapSkyyIfcmPzmEtXEG717mr+54suAiO21FIGcEi2e4RQXPBNiWQls1sDg5QHZmO9hAAZgeLCoM49ijg888MAqq6xy/vnnW+zla0FeKtphM4VOdi3tpRLRzBsF+wMOOAB+S0H7GWhFHPN5TFpiGVrYwq1gbwpgb+zEE08kCCTgC85qvTT2poI9xexup512MuWjRiZuZWsBabBkVMo0VSnMkMhFbqJtt912SadayjhGENKR1xIJTsq3bhJhH3roIWAGSBQzP4cWTpAq3Nuc5YknngDAWyDUvVAMTkrTkqeWVApMKto5pDqHNAomQcKrqT4qcPpK/1xI+vVKapAoqtSGDKRimiYKQ2JSAGGNw3RncpEdQqWmGZ80ogIbo0L6c5/73BFHHCGC+E0LrtI3zPf9OVXjZDMAYpKJqFBN11rIwCtwPG7cOLwSTJ38kTDcgzF+VjKkVY+O8gkYczEYxYGDRCSWJ0V3eVVfc/JvfvObjqYMJPwyfHZZ9t133+OOO+4LX/jChRdeKCTrizQSQV57winEEKHKQGC04MSg2uSQMfwswTrZls/uu+8OwCcABuZtb3ubEzJgCy64oBZ2Y+FtN4Wfy8Bl1AtOkMBqIte46u5raHkaIC701a9+1RqYYWULyg6F7UZGRmNOCriW+SeVxi35m0ZSiHHBE+YJbuDwSfMkIqyT4cRuK2GE7Ir99re/pTrxNMeHNAatOAgYNiNY80yYo5xQF2jgN+J55RJKNAOVinP7448/3tTPBuS2227LA8ULjEXnrtbZEtc3XTy1e9aGLI34YQZkwUMjh5GUQoQ5GOhQwKppplBRiTmZj5jS2xtjhzbV0IW/CtbPOjlTyK8SLeD4teZJzB1/5Wtp76xiGHQ0JJnpBcl73/te29rqJkue9G5OmE/qlMWksovAS9NefTIgr1ydXYqUWE1Lgcmr++gE4beClAl8xgYMlnQxR0qMkGyNkPavfe1r4KMKXaIB7amYkVqzmX86jfAcYhEjUoYIX8CqvZZddlkugSVukKmdVzzzWxaJQ5+owrxUI0vVYrFgL5ekitcUX8kiZsW1NEZF9vzV2ToZaSD3+IVazgYy4ltZyPb2Mr1WceZrTTk8SiHLkksu6WlSE/yegVdBSLt77CuuuKLX4MzAkctXMgYe2+G8NmSxDbsndCX4gm/kMORMKEwwQ6KpZhAqpXCYfbUsuEpjwPCDVc/Sq2eVqRkYT9R08803cxgHDMYvhQOvs846+E7kBlMtGGUrLMYsDkz1kzpUpBLChWoVr6h4mj8vv/zyZGZwBttWlrgL3iskNj8TNbTo++1vf/vee+9Ni741EuU1DokKfkpjtWJEfeKlxlWwNx7yjB0UacoK3EQDP5b6ZtGcVsfddtuNXOjapDFNxXkw58kiLRmIDzJKqNJKXTt+1PGWFqR1Zzp0GzwFUqXWUn2tdTQW7BV7VvLYphbSiTgmeygCFu+8WhCChJlicxgD2GvUmOz3wQ9+UAt+ggRR4RKYpCqW6bj33nvDKeB+61vfOuOMM6IcIth6hBM8zUAVbvMsygFGdlwhIcfmNem36MQngRt152omYqeffnpMUXuKqTu00SSVouUpE9aGLEJ5hge0zPtqHOaTdiUiN9XMa5Qnj6xCt5YV9sBNdug2NlxgVLCkVFt6Vw+LnuTxFJDQLo3xyYTMptmPMICBidCWWLXCSbQQGwyFAjYMjMPqhQuFSkbXjCuoRFAbV/kUljJ711FjYPI1z+DEm2mYwTCo2tGqwqDoFYcRLSdhVtRSqB3XnHVfccUVYEgKxpxKPRRzTiNva6mh1TLcIvbbtAs/w+rb2DGTQPOO4IkaZZ6wLSzyIoJEY056yJXNqhrdjEtNRWC06CKNq8PmmWSlAieX81WYYNxa2hfTKKUpTNgz+riFsPYPGPLV/FlSZWMw4FZFPgDcOGQAHClzTnoI/005hDZ2CL6NZnyFBLBpXSFXs4G8msUIcJ7h0LNnZaqv8gEx2wzHdohAaMxsx2+//fb2QqRB2YbMykg4y3gYe0tKOPm2nQmBPCdv3BUV7oQBnOAntsXfDJhX1IOhykNa7IFRsdAen6+BFTzCpIMo2xumGPZpDJ4NNh1zrA1/8Ni/QUJ+CzbrMXt4hZ9CPV+9CjppDIeoR1EcxlpA0VcjkWF2NGJhKdBoiX0Ac6qZRWMwaLGCZYWezHerrbZq7Ii6+YJzWr7hq6M+sSZswJzb4KZOaREuiUxSLYTldeEwXz2LimQw+0BwCrLmqMJEbNT+XzKnWAY/DMSR0GT4iRMnYlhjwabiVYngk933gAPKaw0Mfqq2N2HyzOqik8DoosJgnEeYXefVXK/VkAGQ5+V27CHdnsPI1UYzIUcDZamCtyrz6mmRDLDkWVpqYKP3OtWBjahdEBZseaPCPjzFM2Yk/Fv8YCIiNeWGJK1KsZWozGUMokLoaMGpBvwFoUYUw0DpVb42pZ5GmHkFG9W3KZ/BZrQcIaDC5YRVkKw5dk/ewDgstRXkE5wk0p7kZiCrmANsciHiWNqZiEKlUYmYtuJMzgUpIcMJLcaMsW0kSCwfZDMVyQT/whaFOJFmcCGhxam7mYgAauamY45/SkcWDxIhgR8JkcgZkhbc8gHLBI4qLOpos127wsd4mumoRstadEmXT3niXIUUOWUJq0jAKdfpZXExfvx4yIFFRrGVk/skfCRL18YoJNz0UvSqUdSiBBUxlfI65cvkR7rYTlPyFTO5hVIbspAWak211Dkw+OlyCKaVZoJQLIjG6Kcp/zDgBJineo/LVAfuAeHIb0/CJlaVXEyn2lKt02NUWW0s9TL8soHYDLKVlttQafUpqOx4Sd1GCNFwgqgKV3//+99vxc558rNv4U+7osWc0MF1+JTzJVIhI6sJXpTwYchDwjqflSiZvevldBfyfJ0wYUKtY1MZccWLINExdB2/ueSESqxZI2O1xKUo9UYkNT0kf5qFwpn56k1TTsvjabFXocQNZLEmgZjsIZ1nRgfz4T+vVYBavdbd13Thh5JwkUJ7jdXgMa/BqlHwOnkYpjDTnsOCp1Ez6W7cmRYlwNmosdDt43MaB54i8v8fRQV57wqLlCXnuP0bQ6cOwxM1FfyFh9LSvhKdZlrlWAVwGy0j52tIxP60KFUS1ZagkuWYRdWBY0kylXZTO93t96iXFakkoFfQOiHnzElQWri616RQnGBDwT8f1m7TBSHtkMfNQrexoxZgOEyJIVKvE7JI5GlV5sYbsLiiPG9qPW7cONEk3T1rRa8gLHqQyeVeYFqgsrrRXUUxkREsTByEGAwQpIZt6K+FXGMX/Gi02LH9Llx6rRHySkDbGQIWeQGHVZXpcqgvQZpqRjsM9G9kM5rhRGNjCXBj+2i3TOPAo00MfsNsDGJtXSEXnfIcWi6htyuYIQnyRgdmH77KKg5gpSavll7FgUnHyhVrBFu49i3j5NpNOE2n9XXY7hk8Zte2HtAy740U/NC61w1nZqGX4pJZtaMW3duUpvYkavA9qaZNx1afwmojWh5i1dPGsiHUN91bIW/fHiVwJDe0nJIAromfV5P/Mu2vIhwKh001E2GH6MBVir2s99qBuysbFbMM2rcZZg2cZNVoZB0TbeXAjQjtJ/G97BSwmNVWW+0HP/iB1antJROwmJ0rfmBYoQWhe7yQJJPbwdpiiy28OpwEkHNXW6lZz3Nmy85axzYOk5QSDqc4zjTzi7QP61lDUsU/LDy9AR5JpKhxOEM48Owso8eFmth0t4jaNWVS1mYcw46ousOkbiFvj4cgyDkvtafiX89Ks7m96CajzOzkUIHB7w1sQY0bN841I7f2uLcVI4/1CQZPe/5LLLGEug0Y57pSNzxuiQlJcgsAedv9qmrHNqeO1fPVHI3CUEooDkv/NSRV/NB2gLAwM6wKQpwTM61kj7/VuB06hz0TZFhSTxe4Dw48LOuZrgAAZGBWZcu365jbU2dPvNcUmjfa3zbLlYGdgbl/ixO+J8GKJtZmiSmZ51dxsjZIfHWCoovixqJ9Y3dI/TNxlipAiErq1V7qjWZaA2j1ikSrT521dx1hKzYQah+aWzn2EDkcIlgr9vrV3qfrI90Ql3HzEJj8NJ8b5ApKLT90g05zHPyTPdn2lFT90IL3urWS3+5Y9DoE4mN2m++55x5enVNZgQbPKUkXuAVmi9VPLJCRBFwCF4l05MNaIs5rnSb/1bE5Q4PW0dFAFD523ZvRzOjFhq01Jx9WuitLlpqOkdiGtSjkcTyEVJz0xmbKVX6nRz65+MH9+K09FT9b5b06ZqOlsKc7SDD+HwUk7soDCLnsqPN2MErpMqj0WAPRv3HhvWP2GKkPU+gRBkqjSKFykc0eZxiuFvABN5alMo0dzy3bcOUKR5DHUROMPWVaVyMkSb5t/mau6zKGnzG7deTQ0qzYRWsZdcKECUwhvQoVUujilrVr4QKEnze6pBH8bmXI5+qBKV0GlR5rgOsaXFsYBsJ+RI+pD5Uc5maskrjo2gAJXZDI/5SSytLeXVmS0nM9QIyAvJZIG8nJn25uaXda1vh10DKjaCDmZLhd+7Wi6foJZbf0MOOtgaUyyhUR7RtJuTJYrrPSSC3LDTWGtYaTeNFyRGQT2LV+u81W3QY1PXzCQCkmvT4BkHV5viQMzNfW6Cf/SBMkGM8CRpBCojQOKr3UQLzLuLij4lqru4Nu6RqjrhtYF4TqViToC56clCLNE0aJAZi5k8Hz0zxRw2Wv+G1TcubS/gFAdbXcFGzQOMY1YMTtR/h/YJZFthjVGcDY5HlWbHUhDPQDBS1nxVsqo82Febud5/woZ7RpDfD3VwPCtEVQTgT6y0l76jOwAxNM9OnNrAYhYSKHOu0VKlQPBaw9ksHXsaMBntzqhHksMDljO3CPNdizeNFjuQbkmmogk9PeZIimDAylceDAQ9HSAGaggTGqgRlvF3qMKnLA1kAD/dDAwIH7ofUBzYEGuqSBgQN3SZEDNAMN9EMDAwfuh9YHNAca6JIGBg7cJUUO0Aw00A8NDBy4H1of0BxooEsaGDhwlxQ5QDPQQD80MHDgfmh9QHOggS5pYODAXVLkAM1AA/3QwP8AGMg7qICuIqsAAAAASUVORK5CYII=", "text/plain": ["<PIL.PngImagePlugin.PngImageFile image mode=RGB size=320x50>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[2][\"image\"]"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 53}, "id": "VTzhtzNRAEL1", "outputId": "25f65a56-3511-4cbe-a92a-45e9f5800875"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'H ^ { \\\\prime } = \\\\beta N \\\\int d \\\\lambda \\\\biggl \\\\{ \\\\frac { 1 } { 2 \\\\beta ^ { 2 } N ^ { 2 } } \\\\partial _ { \\\\lambda } \\\\zeta ^ { \\\\dagger } \\\\partial _ { \\\\lambda } \\\\zeta + V ( \\\\lambda ) \\\\zeta ^ { \\\\dagger } \\\\zeta \\\\biggr \\\\} \\\\ .'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[2][\"text\"]"]}, {"cell_type": "markdown", "metadata": {"id": "NAeQ9LXCAEkW"}, "source": ["We can also render the LaTeX in the browser directly!"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 58}, "id": "lXjfJr4W6z8P", "outputId": "0f3309fa-1cda-4902-c1da-906242949df4"}, "outputs": [{"data": {"text/latex": ["$\\displaystyle H ^ { \\prime } = \\beta N \\int d \\lambda \\biggl \\{ \\frac { 1 } { 2 \\beta ^ { 2 } N ^ { 2 } } \\partial _ { \\lambda } \\zeta ^ { \\dagger } \\partial _ { \\lambda } \\zeta + V ( \\lambda ) \\zeta ^ { \\dagger } \\zeta \\biggr \\} \\ .$"], "text/plain": ["<IPython.core.display.Math object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import display, Math, Latex\n", "\n", "latex = dataset[2][\"text\"]\n", "display(Math(latex))"]}, {"cell_type": "markdown", "metadata": {"id": "K9CBpiISFa6C"}, "source": ["To format the dataset, all vision finetuning tasks should be formatted as follows:\n", "\n", "```python\n", "[\n", "{ \"role\": \"user\",\n", "  \"content\": [{\"type\": \"text\",  \"text\": Q}, {\"type\": \"image\", \"image\": image} ]\n", "},\n", "{ \"role\": \"assistant\",\n", "  \"content\": [{\"type\": \"text\",  \"text\": A} ]\n", "},\n", "]\n", "```"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "oPXzJZzHEgXe"}, "outputs": [], "source": ["instruction = \"Write the LaTeX representation for this image.\"\n", "\n", "def convert_to_conversation(sample):\n", "    conversation = [\n", "        { \"role\": \"user\",\n", "          \"content\" : [\n", "            {\"type\" : \"text\",  \"text\"  : instruction},\n", "            {\"type\" : \"image\", \"image\" : sample[\"image\"]} ]\n", "        },\n", "        { \"role\" : \"assistant\",\n", "          \"content\" : [\n", "            {\"type\" : \"text\",  \"text\"  : sample[\"text\"]} ]\n", "        },\n", "    ]\n", "    return { \"messages\" : conversation }\n", "pass"]}, {"cell_type": "markdown", "metadata": {"id": "FY-9u-OD6_gE"}, "source": ["Let's convert the dataset into the \"correct\" format for finetuning:"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "gFW2qXIr7Ezy"}, "outputs": [], "source": ["converted_dataset = [convert_to_conversation(sample) for sample in dataset]"]}, {"cell_type": "markdown", "metadata": {"id": "ndDUB23CGAC5"}, "source": ["We look at how the conversations are structured for the first example:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gGFzmplrEy9I", "outputId": "4837b837-edca-4e90-9c57-f73e262b3b04"}, "outputs": [{"data": {"text/plain": ["{'messages': [{'role': 'user',\n", "   'content': [{'type': 'text',\n", "     'text': 'Write the LaTeX representation for this image.'},\n", "    {'type': 'image',\n", "     'image': <PIL.PngImagePlugin.PngImageFile image mode=RGB size=160x40>}]},\n", "  {'role': 'assistant',\n", "   'content': [{'type': 'text',\n", "     'text': '{ \\\\frac { N } { M } } \\\\in { \\\\bf Z } , { \\\\frac { M } { P } } \\\\in { \\\\bf Z } , { \\\\frac { P } { Q } } \\\\in { \\\\bf Z }'}]}]}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["converted_dataset[0]"]}, {"cell_type": "markdown", "metadata": {"id": "FecKS-dA82f5"}, "source": ["Let's first see before we do any finetuning what the model outputs for the first example!"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vcat4UxA81vr", "outputId": "da7df71a-0c96-4dee-c995-1958c66b6498"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```latex\n", "H' = \\beta N \\int d\\lambda \\left\\{ \\frac{1}{2\\beta^2 N^2} \\partial_\\lambda \\zeta^\\dagger \\partial_\\lambda \\zeta + V(\\lambda) \\zeta^\\dagger \\zeta \\right\\}.\n", "```<|im_end|>\n"]}], "source": ["FastVisionModel.for_inference(model) # Enable for inference!\n", "\n", "image = dataset[2][\"image\"]\n", "instruction = \"Write the LaTeX representation for this image.\"\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": [\n", "        {\"type\": \"image\"},\n", "        {\"type\": \"text\", \"text\": instruction}\n", "    ]}\n", "]\n", "input_text = tokenizer.apply_chat_template(messages, add_generation_prompt = True)\n", "inputs = tokenizer(\n", "    image,\n", "    input_text,\n", "    add_special_tokens = False,\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128,\n", "                   use_cache = True, temperature = 1.5, min_p = 0.1)"]}, {"cell_type": "markdown", "metadata": {"id": "idAEIeSQ3xdS"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `SFTTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 60 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`. We also support TRL's `DPOTrainer`!\n", "\n", "We use our new `UnslothVisionDataCollator` which will help in our vision finetuning setup."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "95_Nn-89DhsL", "outputId": "e810bb24-fc3a-47d1-b942-0932f037b066"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unsloth: Model does not have a default image size - using 512\n"]}], "source": ["from unsloth.trainer import UnslothVisionDataCollator\n", "from trl import SFTTrainer, SFTConfig\n", "\n", "FastVisionModel.for_training(model) # Enable for training!\n", "\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    data_collator = UnslothVisionDataCollator(model, tokenizer), # Must use!\n", "    train_dataset = converted_dataset,\n", "    args = SFTConfig(\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4,\n", "        warmup_steps = 5,\n", "        max_steps = 30,\n", "        # num_train_epochs = 1, # Set this instead of max_steps for full training runs\n", "        learning_rate = 2e-4,\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\",     # For Weights and Biases\n", "\n", "        # You MUST put the below items for vision finetuning:\n", "        remove_unused_columns = False,\n", "        dataset_text_field = \"\",\n", "        dataset_kwargs = {\"skip_prepare_dataset\": True},\n", "        dataset_num_proc = 4,\n", "        max_seq_length = 2048,\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "2ejIt2xSNKKp", "outputId": "bc91e205-4012-4395-c174-0960d725b84c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU = Tesla T4. Max memory = 14.741 GB.\n", "6.068 GB of memory reserved.\n"]}], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "yqxqAZ7KJ4oL", "outputId": "6afd0011-e8a3-478c-aa56-39ed056f586e"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 68,686 | Num Epochs = 1 | Total steps = 30\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient accumulation steps = 4\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (2 x 4 x 1) = 8\n", " \"-____-\"     Trainable parameters = 51,521,536/7,000,000,000 (0.74% trained)\n", "`use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Will smartly offload gradients to save VRAM!\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='30' max='30' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [30/30 02:59, Epoch 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>2.787200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>3.326000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>3.431700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>2.313500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>2.053400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>2.038500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>1.522700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>1.004700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.716800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.744900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.611900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.499200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.369000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.361600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.353800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.252400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.183800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.196700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.126000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.190200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.183100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.164300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.130300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.098900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.195100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.140000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.081400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.235800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.085400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.131800</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "pCqnaKmlO1U9", "outputId": "63756a82-c628-490c-a005-abb4e7d41b4c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["219.9988 seconds used for training.\n", "3.67 minutes used for training.\n", "Peak reserved memory = 6.484 GB.\n", "Peak reserved memory for training = 0.416 GB.\n", "Peak reserved memory % of max memory = 43.986 %.\n", "Peak reserved memory for training % of max memory = 2.822 %.\n"]}], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {"id": "ekOmTR1hSNcr"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model! You can change the instruction and input - leave the output blank!\n", "\n", "We use `min_p = 0.1` and `temperature = 1.5`. Read this [Tweet](https://x.com/menhguin/status/1826132708508213629) for more information on why."]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kR3gIAX-SM2q", "outputId": "cd538aab-c139-4c64-aedf-4b95781d6a3f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["H ^ { \\prime } = \\beta N \\int d \\lambda \\left\\{ \\frac { 1 } { 2 \\beta ^ { 2 } \\bar { N } ^ { 2 } } \\partial _ { \\lambda } \\zeta ^ { \\dagger } \\partial _ { \\lambda } \\zeta + V ( \\lambda ) \\zeta ^ { \\dagger } \\zeta \\right\\} .<|im_end|>\n"]}], "source": ["FastVisionModel.for_inference(model) # Enable for inference!\n", "\n", "image = dataset[2][\"image\"]\n", "instruction = \"Write the LaTeX representation for this image.\"\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": [\n", "        {\"type\": \"image\"},\n", "        {\"type\": \"text\", \"text\": instruction}\n", "    ]}\n", "]\n", "input_text = tokenizer.apply_chat_template(messages, add_generation_prompt = True)\n", "inputs = tokenizer(\n", "    image,\n", "    input_text,\n", "    add_special_tokens = False,\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128,\n", "                   use_cache = True, temperature = 1.5, min_p = 0.1)"]}, {"cell_type": "markdown", "metadata": {"id": "uMuVrWbjAzhc"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "upcOlWe7A1vc", "outputId": "beb1593d-d312-4410-d3ca-040a51017b47"}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "AEEcJ4qfC7Lp"}, "source": ["Now if you want to load the LoRA adapters we just saved for inference, set `False` to `True`:"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MKX_XKs_BNZR", "outputId": "2b32c167-b6a0-4e80-8a97-9f40806c088f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\frac { N } { M } \\in \\mathbf { Z } , \\frac { P } { Q } \\in \\mathbf { Z } , P \\in \\mathbf { Z }<|im_end|>\n"]}], "source": ["if False:\n", "    from unsloth import FastVisionModel\n", "    model, tokenizer = FastVisionModel.from_pretrained(\n", "        model_name = \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        load_in_4bit = True, # Set to False for 16bit LoRA\n", "    )\n", "    FastVisionModel.for_inference(model) # Enable for inference!\n", "\n", "image = dataset[0][\"image\"]\n", "instruction = \"Write the LaTeX representation for this image.\"\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": [\n", "        {\"type\": \"image\"},\n", "        {\"type\": \"text\", \"text\": instruction}\n", "    ]}\n", "]\n", "input_text = tokenizer.apply_chat_template(messages, add_generation_prompt = True)\n", "inputs = tokenizer(\n", "    image,\n", "    input_text,\n", "    add_special_tokens = False,\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128,\n", "                   use_cache = True, temperature = 1.5, min_p = 0.1)"]}, {"cell_type": "markdown", "metadata": {"id": "f422JgM9sdVT"}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": 20, "metadata": {"id": "iHjt_SMYsd3P"}, "outputs": [], "source": ["# Select ONLY 1 to save! (Both not needed!)\n", "\n", "# Save locally to 16bit\n", "if False: model.save_pretrained_merged(\"unsloth_finetune\", tokenizer,)\n", "\n", "# To export and save to your Hugging Face account\n", "if False: model.push_to_hub_merged(\"YOUR_USERNAME/unsloth_finetune\", tokenizer, token = \"PUT_HERE\")"]}, {"cell_type": "markdown", "metadata": {"id": "i3KclRdVC7IQ"}, "source": ["And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/u54VK8m8tk) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "unsloth_env", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.11.11"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"03f0c63c572249aabb7fb61272af1f1b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4b74060c0bb44a35b847e6752b93e463", "max": 2776833, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7e211233455342d48bd60f6d499b91f5", "value": 2776833}}, "080c7eed9cc0475980e85a530c91e616": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0a21bae13a3c46509db70dbe8e9de5eb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0a5ad3bd338b4e6ea94a11a199d2f5ba": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0af61827920e415daf64cfcb1685ee63": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0ccabea3b39d4b148d420e26b53d3990": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0cef8128eeba4638bd268f9a1a364427": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_17c08a96e04244a995e658eeb5a69699", "placeholder": "​", "style": "IPY_MODEL_9269dee0b0104b22b4043d3661f79350", "value": "merges.txt: 100%"}}, "0d6f6fb66522436786728d8602e2f466": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7572f2d06114442ba94fc22761a55346", "placeholder": "​", "style": "IPY_MODEL_c6dcd15e155949cbb9698e43e55b7a90", "value": " 519/519 [00:00&lt;00:00, 10.6kB/s]"}}, "0e949ae97d544d65af09cebe63041b8e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_36d579fdc2fb4c59b827f5bf7a92abd4", "max": 38205016, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fccec1c2a389401eb614f5f1cfb15f0c", "value": 38205016}}, "0f2f303ae91349e29132055105fcbdec": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0f7da532813445f1be01977cdec364ab": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_20956dab8f3e4f22830b4d9e56a5232a", "placeholder": "​", "style": "IPY_MODEL_4efe313f923c4ae585fb8a4353cecd97", "value": " 605/605 [00:00&lt;00:00, 41.2kB/s]"}}, "14cbf1b61170470bbb9d63e31d0ca310": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "14e519d5e0834fd9a70a111140f8429a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_41f3770e6d864c1794a1fa93feaf8605", "IPY_MODEL_6aa1670b808b42db894756b904243947", "IPY_MODEL_5757f1d68b8f4571a05486d846127b14"], "layout": "IPY_MODEL_14cbf1b61170470bbb9d63e31d0ca310"}}, "17c08a96e04244a995e658eeb5a69699": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "20956dab8f3e4f22830b4d9e56a5232a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2127bee2928e43578b8b51e3c1fdfabf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "227724312eb045a58616f63c3a5b7407": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e7bd6c674af541d08c0acb5e47579156", "placeholder": "​", "style": "IPY_MODEL_927147d620b0426cb46b49b6d8dc2ffc", "value": " 344M/344M [00:02&lt;00:00, 273MB/s]"}}, "2362fd437a134c30b147df947ee97128": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2650d2ce196e404a98cbfac96d0a9bef": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2a588c12d3814de495fa865a63629ba8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2aecce21a3a04971b4f5c9fd0b059738": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2af66136423c41c6908c5a61c6fa2e5d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2d65a3b83b524eeaafde8e406e90198f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2dccbbbdfbf645b8b5896953f6e8a59b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "30439a7bf22f416cbc01cfe87e433e1f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3265b3490b11426a8aa44d541981a296": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "331d5d2bc67f44f283fc82481cb60411": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3324ce12dc3e45fab9984eb83a92eb39": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "35db2f7869f44aeab422409d27d5f7ec": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "36d579fdc2fb4c59b827f5bf7a92abd4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "383e14f14b0d4328a80e6b0283c421fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "399cc918cec24db7b99af9e5f3c5adb4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a405cbc6d42d4f8f8be7c05f4e34128c", "max": 1671853, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e702e948bb34481d995c75be223c4e16", "value": 1671853}}, "3a484309bbe148abb2ef05452850728a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3a611a2e00be4da6833172d928802fb9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c9949e2b99d14fa0bc4cfc88f42c2afb", "placeholder": "​", "style": "IPY_MODEL_48bbf9b662af47c5b99ebe24a6b70549", "value": " 1.67M/1.67M [00:00&lt;00:00, 8.69MB/s]"}}, "3af59a3e9ac2430da201262fc54b5998": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ad746cc40cdb461bba8cf04033d4afc5", "IPY_MODEL_3d09770f22854ca38213d4a64ace6df3", "IPY_MODEL_910df16ec7494eb68fb1f8ce95046414"], "layout": "IPY_MODEL_080c7eed9cc0475980e85a530c91e616"}}, "3caf3a6d48da403099bcdee89bda6949": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f0dd55c8f71a4f47a91a7018ab89d092", "max": 11421896, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7a2d1ba8441e4a368a505c38d2c811ad", "value": 11421896}}, "3d09770f22854ca38213d4a64ace6df3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_607a235508e54fdda0aef75311c7a213", "max": 614, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_383e14f14b0d4328a80e6b0283c421fa", "value": 614}}, "4113f045f9bc440b8dfaed5dfab14c1b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_520ca077c6854e6fa1e019c0b209eae7", "placeholder": "​", "style": "IPY_MODEL_8f1fce549e494998b13f523bd793d300", "value": " 2.78M/2.78M [00:00&lt;00:00, 8.66MB/s]"}}, "41f3770e6d864c1794a1fa93feaf8605": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3324ce12dc3e45fab9984eb83a92eb39", "placeholder": "​", "style": "IPY_MODEL_0ccabea3b39d4b148d420e26b53d3990", "value": "tokenizer_config.json: 100%"}}, "45385885a26e42d0b1f52eda382fd6f4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ccb21e1b7fc14a6ab10689154ab2b94a", "IPY_MODEL_f5c7ab8d65c4418a943a51a401720f67", "IPY_MODEL_f6aaa74bce474abcbc9daf0515b6ffbd"], "layout": "IPY_MODEL_76bd63e567e94650a82272ca6fd85011"}}, "48af93dada914d8b88d5ffffd24ac666": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5c41f695158641f485349c20a1405cfd", "placeholder": "​", "style": "IPY_MODEL_89bc17f1911a43cca7196da232500fd6", "value": " 68686/68686 [00:02&lt;00:00, 39045.96 examples/s]"}}, "48bbf9b662af47c5b99ebe24a6b70549": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4b74060c0bb44a35b847e6752b93e463": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4bd339c9d3ee43f39678d2c18baeef53": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4c0c6892c4f347deb4a85544664ff1e1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4ebb36e479884847bcef5736e276dfb9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7b5a059a8ef84817851d109fd7e3a7b5", "IPY_MODEL_0e949ae97d544d65af09cebe63041b8e", "IPY_MODEL_c817a779a9ff4a848391a7c7dff1e4ca"], "layout": "IPY_MODEL_abae462b98c84006b71d4a62c8a47d5a"}}, "4efe313f923c4ae585fb8a4353cecd97": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4f58e8a2ddbb4a73a2421ea37c68fea9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "520ca077c6854e6fa1e019c0b209eae7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5244452af0734f3183a7384435f9e1fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_569d1435402e4533b0427a4b191b6754", "placeholder": "​", "style": "IPY_MODEL_b834a7ab12544f9b8305f5728677e921", "value": "Generating test split: 100%"}}, "5522658c84dc47a491f5deb08353b631": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "569d1435402e4533b0427a4b191b6754": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "573653d0298246868e5d93069ca0fef5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5757f1d68b8f4571a05486d846127b14": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2362fd437a134c30b147df947ee97128", "placeholder": "​", "style": "IPY_MODEL_7f22e31fc7174ff2bfa8bc2624d33dc7", "value": " 7.33k/7.33k [00:00&lt;00:00, 749kB/s]"}}, "5997909fc34645ce9a03d82ab0932766": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7e89e5546e394162b7b0f793478527ff", "placeholder": "​", "style": "IPY_MODEL_db9ea237050045d8861d76bb3b995753", "value": "train-00000-of-00001.parquet: 100%"}}, "5c41f695158641f485349c20a1405cfd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5e4f3f4910d1477b924362c095a0cedd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b5a01de096fa4e2daf66e235bd5686bb", "IPY_MODEL_e9195f3a3e29475f941c302fe2a88a83", "IPY_MODEL_0d6f6fb66522436786728d8602e2f466"], "layout": "IPY_MODEL_94b72e08764c486d8eed43a4557f1d98"}}, "5e601d00c0094d999a5b37b9555c111c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "607a235508e54fdda0aef75311c7a213": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "62b6d78abada429393c499e0479eb56e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "630894e18b264f7dae8eddb17304a1cd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "631ce764f4684225a3c1e4455717a555": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_331d5d2bc67f44f283fc82481cb60411", "max": 68686, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_0a5ad3bd338b4e6ea94a11a199d2f5ba", "value": 68686}}, "639b886f72e24951909e4ffb6cfa247d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7b13cf1727a04f5ca6de1f7eb536d800", "IPY_MODEL_631ce764f4684225a3c1e4455717a555", "IPY_MODEL_48af93dada914d8b88d5ffffd24ac666"], "layout": "IPY_MODEL_4bd339c9d3ee43f39678d2c18baeef53"}}, "643db314d8e94feeb9cd8747909eaabe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b80bb8f2cc92400aa34934978f9bc865", "IPY_MODEL_03f0c63c572249aabb7fb61272af1f1b", "IPY_MODEL_4113f045f9bc440b8dfaed5dfab14c1b"], "layout": "IPY_MODEL_86a231a75d4a4586a53e4c7e74c63550"}}, "6657bbba902e43429224ed9c426389fe": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "67053bf18cd04347a0f2d2ff6134ae88": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "697fc92abb824cf4a9bc78709640ba54": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "69f645decfb64baca11cfbdb9773a458": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e72ac7c9b7824c81a6e77e6fadad9f9c", "max": 7632, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_828ad6e5fcfb44b3b4559ec5682d095d", "value": 7632}}, "6aa1670b808b42db894756b904243947": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2d65a3b83b524eeaafde8e406e90198f", "max": 7326, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d179ef65b2b842d69548302c2f7a31be", "value": 7326}}, "71ac48ba667b4af291df3a33e7643c6c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_77a82cf2255b43e088812087af4d8ead", "max": 267, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fad668efab014d28877087f38f3c034e", "value": 267}}, "7365368b63d9425db5d4e5514e6e0586": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5997909fc34645ce9a03d82ab0932766", "IPY_MODEL_ea4ebb90b708493c85fa81fecf4d60c0", "IPY_MODEL_227724312eb045a58616f63c3a5b7407"], "layout": "IPY_MODEL_4f58e8a2ddbb4a73a2421ea37c68fea9"}}, "73fc3ac47de840cda7a07297153404d5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_8c8b54f4d34a4b59a5a2513bbc082297", "IPY_MODEL_7bd2abffca3f4193afffd3f1a7c8ff8a", "IPY_MODEL_9ecdc7e0e9c247c59e9eab93455153b8"], "layout": "IPY_MODEL_84f4e9f7ff6f433bbf13078e43ebbe2a"}}, "7475f8a404624b1b844d8148fabb493b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7572f2d06114442ba94fc22761a55346": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "76bd63e567e94650a82272ca6fd85011": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "77a82cf2255b43e088812087af4d8ead": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "78a31b91439c4c99a07f8f8d8dfb548f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "78ae8aea29e04420bcfe616061934d98": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e22b1706b6be49af8e31d2c512ccaaf6", "IPY_MODEL_71ac48ba667b4af291df3a33e7643c6c", "IPY_MODEL_7d16f0e5aaec4429a5b8d3c4878ea74a"], "layout": "IPY_MODEL_a21de38381c241309bc798be604c3bf5"}}, "7a2d1ba8441e4a368a505c38d2c811ad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7b13cf1727a04f5ca6de1f7eb536d800": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b1b12b8def994e748b08b27abcef4b87", "placeholder": "​", "style": "IPY_MODEL_bc38022049ce47628d8c215959c38b91", "value": "Generating train split: 100%"}}, "7b4d303966274c71b50312df12e7b87a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7b5a059a8ef84817851d109fd7e3a7b5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dc1a755aae6c44849d818fd96c046649", "placeholder": "​", "style": "IPY_MODEL_2650d2ce196e404a98cbfac96d0a9bef", "value": "test-00000-of-00001.parquet: 100%"}}, "7bd2abffca3f4193afffd3f1a7c8ff8a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_62b6d78abada429393c499e0479eb56e", "max": 5965743348, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2aecce21a3a04971b4f5c9fd0b059738", "value": 5965742780}}, "7d16f0e5aaec4429a5b8d3c4878ea74a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e764888263e54f0ba2f603992d678096", "placeholder": "​", "style": "IPY_MODEL_2dccbbbdfbf645b8b5896953f6e8a59b", "value": " 267/267 [00:00&lt;00:00, 21.4kB/s]"}}, "7e0bbe2417e0420db1502390ba7c6c2d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6657bbba902e43429224ed9c426389fe", "placeholder": "​", "style": "IPY_MODEL_a25ba73fb99448a3b46ac3571e2db0be", "value": " 11.4M/11.4M [00:00&lt;00:00, 39.2MB/s]"}}, "7e211233455342d48bd60f6d499b91f5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7e89e5546e394162b7b0f793478527ff": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7f22e31fc7174ff2bfa8bc2624d33dc7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "80e56300aee14b4386832aa1618c5d43": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "828ad6e5fcfb44b3b4559ec5682d095d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "84f4e9f7ff6f433bbf13078e43ebbe2a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "86a231a75d4a4586a53e4c7e74c63550": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "89bc17f1911a43cca7196da232500fd6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8bf92078d48448848402ede49b1b5182": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8c8b54f4d34a4b59a5a2513bbc082297": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7475f8a404624b1b844d8148fabb493b", "placeholder": "​", "style": "IPY_MODEL_9d4aae9ebe594bd6875a5963eb6d77ed", "value": "model.safetensors: 100%"}}, "8e8281a56a3d4879b6b376077890c920": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8f1fce549e494998b13f523bd793d300": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8f423066ba8f421495f934a269053cc1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "910df16ec7494eb68fb1f8ce95046414": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_913324d3476a4d04887d200f91afc553", "placeholder": "​", "style": "IPY_MODEL_4c0c6892c4f347deb4a85544664ff1e1", "value": " 614/614 [00:00&lt;00:00, 36.0kB/s]"}}, "913324d3476a4d04887d200f91afc553": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9269dee0b0104b22b4043d3661f79350": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "927147d620b0426cb46b49b6d8dc2ffc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "94b72e08764c486d8eed43a4557f1d98": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9d4aae9ebe594bd6875a5963eb6d77ed": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9ecdc7e0e9c247c59e9eab93455153b8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2af66136423c41c6908c5a61c6fa2e5d", "placeholder": "​", "style": "IPY_MODEL_8bf92078d48448848402ede49b1b5182", "value": " 5.97G/5.97G [01:00&lt;00:00, 615MB/s]"}}, "a21de38381c241309bc798be604c3bf5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a25ba73fb99448a3b46ac3571e2db0be": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a405cbc6d42d4f8f8be7c05f4e34128c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a863bb17b5984981bf9bd4ff93e438f4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b28286deba7c4152a8d2c21ea0d19402", "IPY_MODEL_fb9e17975b2349de81115173af6ac17a", "IPY_MODEL_f3b068de4fa6466e88970b73c868de0a"], "layout": "IPY_MODEL_2127bee2928e43578b8b51e3c1fdfabf"}}, "a913ea3370404f5f972754715889aced": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a97f3d8c7bdf4292ae8ed133f059f635": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "abae462b98c84006b71d4a62c8a47d5a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ad746cc40cdb461bba8cf04033d4afc5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e4fe961d149b461fb1d168d226c8aa1d", "placeholder": "​", "style": "IPY_MODEL_2a588c12d3814de495fa865a63629ba8", "value": "special_tokens_map.json: 100%"}}, "aecb331a01a44c9e92c74840faaf2616": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b1b12b8def994e748b08b27abcef4b87": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b241468a1d684fbb820baff6b9f9e362": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f7e4e90fb3fe4bbb85adb039404dd8e2", "max": 605, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e0f8b5f130064bd69f4f3298e9536b5a", "value": 605}}, "b28286deba7c4152a8d2c21ea0d19402": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a97f3d8c7bdf4292ae8ed133f059f635", "placeholder": "​", "style": "IPY_MODEL_67053bf18cd04347a0f2d2ff6134ae88", "value": "chat_template.json: 100%"}}, "b5a01de096fa4e2daf66e235bd5686bb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8e8281a56a3d4879b6b376077890c920", "placeholder": "​", "style": "IPY_MODEL_630894e18b264f7dae8eddb17304a1cd", "value": "README.md: 100%"}}, "b652bb727c1148c4aac9ed751920e3ae": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b80bb8f2cc92400aa34934978f9bc865": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_573653d0298246868e5d93069ca0fef5", "placeholder": "​", "style": "IPY_MODEL_c20a42f12c4545dfafdba22267e44d4b", "value": "vocab.json: 100%"}}, "b834a7ab12544f9b8305f5728677e921": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bbb5e6a0a8b64d10802fbf3e89778306": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0cef8128eeba4638bd268f9a1a364427", "IPY_MODEL_399cc918cec24db7b99af9e5f3c5adb4", "IPY_MODEL_3a611a2e00be4da6833172d928802fb9"], "layout": "IPY_MODEL_0f2f303ae91349e29132055105fcbdec"}}, "bc38022049ce47628d8c215959c38b91": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c1af10a57c0a40cf818d0974facdcc36": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e4c1ff933c6e4ef49afbedffcdd99d5c", "IPY_MODEL_3caf3a6d48da403099bcdee89bda6949", "IPY_MODEL_7e0bbe2417e0420db1502390ba7c6c2d"], "layout": "IPY_MODEL_5522658c84dc47a491f5deb08353b631"}}, "c20a42f12c4545dfafdba22267e44d4b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c2f518986b724620bbc67f7b6ede6d96": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ed214eea73f54928af276883ddd53b5e", "IPY_MODEL_b241468a1d684fbb820baff6b9f9e362", "IPY_MODEL_0f7da532813445f1be01977cdec364ab"], "layout": "IPY_MODEL_de7ac823a59d488995aa37bfbcec2e5b"}}, "c4256f50e38e481083f3d366bfd91d3f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c6dcd15e155949cbb9698e43e55b7a90": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c817a779a9ff4a848391a7c7dff1e4ca": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7b4d303966274c71b50312df12e7b87a", "placeholder": "​", "style": "IPY_MODEL_ce4bb2dffcf84ae9bf1f8ac86b2d768e", "value": " 38.2M/38.2M [00:00&lt;00:00, 71.5MB/s]"}}, "c9949e2b99d14fa0bc4cfc88f42c2afb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "caadc7751ff847b4a831dd6b2b53e60b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ccb21e1b7fc14a6ab10689154ab2b94a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d3cac12f194a40a187bbba68da36173c", "placeholder": "​", "style": "IPY_MODEL_30439a7bf22f416cbc01cfe87e433e1f", "value": "preprocessor_config.json: 100%"}}, "ce4bb2dffcf84ae9bf1f8ac86b2d768e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d096e33830754ea5b2e8f102548b805a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d1788fabe3e34f6eb60fecab3c7490ba": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dac611678f4949ae98051bdc975e083f", "placeholder": "​", "style": "IPY_MODEL_0a21bae13a3c46509db70dbe8e9de5eb", "value": " 7632/7632 [00:00&lt;00:00, 15768.15 examples/s]"}}, "d179ef65b2b842d69548302c2f7a31be": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d2a3a751ac2f491c9e7cb7a4092ab0b6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d3cac12f194a40a187bbba68da36173c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dac611678f4949ae98051bdc975e083f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "db9ea237050045d8861d76bb3b995753": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "dc1a755aae6c44849d818fd96c046649": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "de7ac823a59d488995aa37bfbcec2e5b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e0f8b5f130064bd69f4f3298e9536b5a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e22b1706b6be49af8e31d2c512ccaaf6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f26f1de0f3f34010ac0d37d6f3ed74b6", "placeholder": "​", "style": "IPY_MODEL_0af61827920e415daf64cfcb1685ee63", "value": "generation_config.json: 100%"}}, "e4c1ff933c6e4ef49afbedffcdd99d5c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aecb331a01a44c9e92c74840faaf2616", "placeholder": "​", "style": "IPY_MODEL_caadc7751ff847b4a831dd6b2b53e60b", "value": "tokenizer.json: 100%"}}, "e4fe961d149b461fb1d168d226c8aa1d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e702e948bb34481d995c75be223c4e16": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e72ac7c9b7824c81a6e77e6fadad9f9c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e764888263e54f0ba2f603992d678096": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e7bd6c674af541d08c0acb5e47579156": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e9195f3a3e29475f941c302fe2a88a83": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_697fc92abb824cf4a9bc78709640ba54", "max": 519, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_eb6d552d6c1d4e82a521b29b36ae3db0", "value": 519}}, "ea4ebb90b708493c85fa81fecf4d60c0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3a484309bbe148abb2ef05452850728a", "max": 343805431, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5e601d00c0094d999a5b37b9555c111c", "value": 343805399}}, "eb6d552d6c1d4e82a521b29b36ae3db0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ed214eea73f54928af276883ddd53b5e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fc5321766b8742ca97c4a839ba30d421", "placeholder": "​", "style": "IPY_MODEL_c4256f50e38e481083f3d366bfd91d3f", "value": "added_tokens.json: 100%"}}, "f0dd55c8f71a4f47a91a7018ab89d092": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f26f1de0f3f34010ac0d37d6f3ed74b6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f3b068de4fa6466e88970b73c868de0a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a913ea3370404f5f972754715889aced", "placeholder": "​", "style": "IPY_MODEL_35db2f7869f44aeab422409d27d5f7ec", "value": " 1.05k/1.05k [00:00&lt;00:00, 68.1kB/s]"}}, "f5629e8c82c848218f69810e1b21b0e4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5244452af0734f3183a7384435f9e1fe", "IPY_MODEL_69f645decfb64baca11cfbdb9773a458", "IPY_MODEL_d1788fabe3e34f6eb60fecab3c7490ba"], "layout": "IPY_MODEL_8f423066ba8f421495f934a269053cc1"}}, "f5c7ab8d65c4418a943a51a401720f67": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3265b3490b11426a8aa44d541981a296", "max": 575, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_78a31b91439c4c99a07f8f8d8dfb548f", "value": 575}}, "f6aaa74bce474abcbc9daf0515b6ffbd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d2a3a751ac2f491c9e7cb7a4092ab0b6", "placeholder": "​", "style": "IPY_MODEL_b652bb727c1148c4aac9ed751920e3ae", "value": " 575/575 [00:00&lt;00:00, 52.0kB/s]"}}, "f7e4e90fb3fe4bbb85adb039404dd8e2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fad668efab014d28877087f38f3c034e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "fb9e17975b2349de81115173af6ac17a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_80e56300aee14b4386832aa1618c5d43", "max": 1049, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d096e33830754ea5b2e8f102548b805a", "value": 1049}}, "fc5321766b8742ca97c4a839ba30d421": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fccec1c2a389401eb614f5f1cfb15f0c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}}}}, "nbformat": 4, "nbformat_minor": 0}