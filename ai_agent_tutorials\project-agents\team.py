from dataclasses import dataclass
from typing import List, Dict, Any
from agno.team.team import Team
from agno.models.groq import Groq

@dataclass
class ArticleResult:
    content: str
    search_terms: List[str]
    sources: List[Dict[str, str]]
    metrics: Dict[str, Any]
    
    def __str__(self):
        """String representation for agno's expected_output formatting."""
        return (
            f"ArticleResult:\n"
            f"  Content: {self.content[:100]}...\n"
            f"  Search Terms: {', '.join(self.search_terms)}\n"
            f"  Sources: {len(self.sources)} sources\n"
            f"  Metrics: {self.metrics}"
        )

def create_editor_team(searcher, writer, groq_api_key: str) -> Team:
    """Create an editorial team to coordinate article generation."""
    team = Team(
        name="Editor",
        mode="coordinate",
        model=Groq(api_key=groq_api_key, id="llama3-70b-8192"),
        members=[searcher, writer],
        description=(
            "You are a senior NYT editor. Given a topic, your goal is to produce "
            "a NYT-worthy article by coordinating between researchers and writers."
        ),
        instructions=[
            "First, have the Searcher find the most relevant URLs for the topic.",
            "Review the URLs to ensure they meet NYT's quality standards.",
            "Then, have the Writer produce a draft article using those sources.",
            "Edit the article to ensure it meets NYT standards:",
            "- Engaging headline and lede",
            "- Logical flow and structure",
            "- Proper attribution of all facts",
            "- Balanced perspective",
            "- Formal but accessible tone",
            "The final article should be ready for publication.",
        ],
        add_datetime_to_instructions=True,
        enable_agentic_context=True,
        markdown=True,
        debug_mode=False,
        show_members_responses=False,
    )
    
    # Set expected output after initialization to avoid the strip() issue
    team.expected_output = str(ArticleResult(
        content="",
        search_terms=[],
        sources=[],
        metrics={}
    ))
    
    return team