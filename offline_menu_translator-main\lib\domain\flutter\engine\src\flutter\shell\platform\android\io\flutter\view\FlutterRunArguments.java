// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

package io.flutter.view;

/** A class containing arguments for entering a FlutterNativeView's isolate for the first time. */
public class FlutterRunArguments {
  public String bundlePath;
  public String entrypoint;
  public String libraryPath;
}
