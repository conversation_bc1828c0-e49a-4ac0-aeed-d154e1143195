# Environment Configuration for Doc-MC<PERSON>

# Required: API Keys
NEBIUS_API_KEY=your_nebius_api_key_here
GITHUB_API_KEY=your_github_token_here

# Required: Database Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/
DB_NAME=docmcp
COLLECTION_NAME=doc_rag
REPOS_COLLECTION_NAME=ingested_repos

# Vector Store Configuration
VECTOR_INDEX_NAME=vector_index
FTS_INDEX_NAME=fts_index
EMBEDDING_DIMENSIONS=4096

# GitHub Configuration
GITHUB_CONCURRENT_REQUESTS=10
GITHUB_TIMEOUT=30
GITHUB_RETRIES=3

# Processing Configuration
CHUNK_SIZE=3072
SIMILARITY_TOP_K=5

# UI Configuration
ENABLE_REPO_MANAGEMENT=true
SERVER_HOST=0.0.0.0
SERVER_PORT=7860
