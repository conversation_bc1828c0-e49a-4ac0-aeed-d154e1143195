{"cells": [{"cell_type": "markdown", "id": "be34d25b", "metadata": {"id": "8377c056591f"}, "source": ["Copyright 2024 Google LLC."]}, {"cell_type": "code", "execution_count": 1, "id": "6130c8e6", "metadata": {"cellView": "form", "id": "ca23c3f523a7"}, "outputs": [], "source": ["# @title Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "# https://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "metadata": {"id": "04367b4433c6"}, "source": ["# Fine-tune PaliGemma 2 with <PERSON><PERSON>\n", "\n", "<table class=\"tfo-notebook-buttons\" align=\"left\">\n", "<td>\n", "<a target=\"_blank\" href=\"https://colab.research.google.com/github/google-gemini/gemma-cookbook/blob/main/PaliGemma/[PaliGemma_2]Finetune_with_Keras.ipynb\"><img src=\"https://www.tensorflow.org/images/colab_logo_32px.png\" />Run in Google Colab</a>\n", "</td>\n", "<td>\n", "<a target=\"_blank\" href=\"https://github.com/google-gemini/gemma-cookbook/blob/main/PaliGemma/[PaliGemma_2]Finetune_with_Keras.ipynb\"><img src=\"https://www.tensorflow.org/images/GitHub-Mark-32px.png\" />View source on GitHub</a>\n", "</td>\n", "</table>"]}, {"cell_type": "markdown", "id": "edb02808", "metadata": {"id": "afe2be3c68c3"}, "source": ["This notebook shows how to fine-tune [PaliGemma 2](https://ai.google.dev/gemma/docs/paligemma) on a vision-language task with [<PERSON><PERSON>](https://keras.io/). Fine-tuning is a process that can improve your model's performance on specific tasks or help the model adhere to specific output requirements when instructions aren't sufficient and you have a set of examples that demonstrate the outputs you want. Gemma-based models like PaliGemma require fine-tuning to produce expected results.\n", "\n", "## Before you begin\n", "\n", "Before going through this notebook, you should be familiar with Python code, as well as how large language models (LLMs) are trained. You don't need to be familiar with Keras, but basic knowledge about Keras (or similar technologies) is helpful when reading through the example code.e."]}, {"cell_type": "markdown", "id": "c941724c", "metadata": {"id": "dfbbe167352c"}, "source": ["## Install KerasHub"]}, {"cell_type": "code", "execution_count": 2, "id": "468e967f", "metadata": {"id": "5341a3c6ebe7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting e<PERSON>ps\r\n", "  Downloading einops-0.8.0-py3-none-any.whl.metadata (12 kB)\r\n", "Downloading einops-0.8.0-py3-none-any.whl (43 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m43.2/43.2 kB\u001b[0m \u001b[31m1.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hInstalling collected packages: einops\r\n", "Successfully installed einops-0.8.0\r\n"]}], "source": ["!pip install -q -U keras-nlp\n", "!pip install -q -U keras>=3\n", "!pip install einops"]}, {"cell_type": "code", "execution_count": 3, "id": "b4eadf69", "metadata": {"id": "973190af9fb4"}, "outputs": [], "source": ["!pip install -q -U keras keras-hub\n", "\n", "import os\n", "# Set the backbend before importing Ke<PERSON>\n", "os.environ[\"KERAS_BACKEND\"] = \"jax\"\n", "# Avoid memory fragmentation on JAX backend.\n", "os.environ[\"XLA_PYTHON_CLIENT_MEM_FRACTION\"] = \"1.00\"\n", "\n", "# Set the fine-tuning variables\n", "BATCH_SIZE = 1\n", "TRAIN_EXAMPLES = 4\n", "LEARNING_RATE = 0.003\n", "\n", "TRAIN_STEPS = TRAIN_EXAMPLES // BATCH_SIZE\n", "EVAL_STEPS = TRAIN_STEPS // 4"]}, {"cell_type": "markdown", "id": "b7e49a04", "metadata": {"id": "0f0cc14c99cb"}, "source": ["## Download the training and validation data"]}, {"cell_type": "code", "execution_count": 4, "id": "6892fd5b", "metadata": {"id": "4fde23bb6f99"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_23/4203520384.py:15: DeprecationWarning: Importing display from IPython.core.display is deprecated since IPython 7.14, please import from IPython display\n", "  from IPython.core.display import display, HTML\n"]}], "source": ["import io\n", "import json\n", "import os\n", "import urllib\n", "\n", "import base64\n", "import html\n", "\n", "import numpy as np\n", "import keras\n", "import keras_hub\n", "import tensorflow as tf\n", "import matplotlib.pyplot as plt\n", "\n", "from IPython.core.display import display, HTML\n", "from PIL import Image\n", "\n", "train_file = urllib.request.urlopen(\n", "    \"https://storage.googleapis.com/longcap100/data_train90.jsonl\"\n", ")\n", "val_file = urllib.request.urlopen(\n", "    \"https://storage.googleapis.com/longcap100/data_val10.jsonl\"\n", ")\n", "\n", "# Crop the image to the desired dimensions.\n", "target_size = (224, 224)\n", "\n", "def load_image(image_url):\n", "    image = tf.io.decode_jpeg(urllib.request.urlopen(image_url).read())\n", "    return tf.image.resize(image, size=target_size)\n", "\n", "def load_dataset(file):\n", "    captions = []\n", "    images = []\n", "    for line in file:\n", "        sample = json.loads(line)\n", "        captions.append(sample[\"suffix\"])\n", "        image_name = sample[\"image\"]\n", "        image_url = f\"https://storage.googleapis.com/longcap100/{image_name}\"\n", "        images.append(load_image(image_url))\n", "    return tf.data.Dataset.from_tensor_slices({\n", "        \"images\": images,\n", "        \"prompts\": [\"caption en\\n\"] * len(images),\n", "        \"responses\": captions,\n", "    })\n", "\n", "train_data = load_dataset(train_file).shuffle(1000).batch(BATCH_SIZE)\n", "val_data = load_dataset(val_file).shuffle(1000).batch(BATCH_SIZE)"]}, {"cell_type": "markdown", "id": "ccba239c", "metadata": {"id": "f2480f920144"}, "source": ["## View training examples\n", "\n", "In this notebook, the training data contains 90 images that are paired with long descriptions of what's depicted in the image.\n", "\n", "**Note:** Normal training data sets that are meant to be used for practical use cases should contain more images, but this notebook limits the number of data points so that you can train the model in a reasonable amount of time for an example.\n", "\n", "The code below prints a random selection of images with their descriptions from the training data set so that you can see what the images and descriptions your model is trained on looks like. Each image is displayed in as a 128x128 pixel JPEG, with the description printed next to the image to the right."]}, {"cell_type": "code", "execution_count": 5, "id": "cd8d59b7", "metadata": {"id": "ee549257aa4f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training examples\n"]}, {"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A pile of tools sits on a brown carpet, including a green and black cordless drill, a yellow and black electronic device.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A white towel with a Happy Easter message written on it. The towel is white, and the eggs are scattered on the towel. There are four colors of eggs on the towel, yellow, pink, white and blue. The message is written in black, and the letters are black. The towel is clean.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A yellow train is stopped at a station. The train is on the tracks, and the lights are on. The platform has a white line and a white stripe on the platform. There is a person standing on the platform, and a person walking on the platform. The train has a number on its side. The train is moving, and the doors are closed.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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************************************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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A group of people stand next to a train on a platform. The train is parked on the tracks, and the platform is made of bricks. There are several people standing on the platform, including a woman wearing a red shirt, a woman wearing a black shirt, and a woman wearing a blue shirt. The train has a window on the side. The sky is clear, and the sun is shining.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A flamingo stands gracefully in the calm water, its long legs extending gracefully. The flamingo&#x27;s pink feathers and black tail feathers contrast beautifully against the blue water. Its long neck and slender legs are prominent features, while its beak, black as night, and eye, white as the moon, add a touch of whimsy. The flamingo&#x27;s reflection dances on the water&#x27;s surface, mirroring its graceful movement. The flamingo&#x27;s legs are pink.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A surfer rides a wave, his white shirt billowing in the wind. The wave is white and blue, and the water is clear and blue. The surfer&#x27;s board is white and blue, and his hair is dark. He is crouched on the board, his arm extended out to the side. The wave is crashing behind him, and the water is splashing in the air. The surfer is wearing a white shirt and black shorts, and his hair is wet.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A storefront with a sign that reads &quot;Segovia Coffee Bar&quot; sits on a city street. The store has a black awning with white lettering, a white sign on the building, and a window on the building. There is a tree in front of the building,. The sidewalk in front of the store is empty, and there is a shadow on the ground. The store has a window on the building, and the window is open.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCADgAOADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDCVMtjHNWBaMU3DrnGKjThgcdK1bOWF49jcN2NfSTbR4q1KAjkiPIxitmylNxaEE5ZRj60y4hV7M45KZIIpmjxyNclFJ+lZyalG5S0dja0LSkmbzbj7oPC+vNdhJ9nXaxRSV+7x0rPgh8u0VgmGxUP2pk3ZJwOa4ZtzdzqilFGFqLp/akk20CMcADjFZ7WKXU7OAoVuBmi6lM8jjODnkU7SWO942Jx1FdaTjG5zt3Zm3GlstyIlPynoaoPEUJB6jiu9ttG+0IlyzHcMjbXNX1jJFMVK8BjnA7etaU613YmcGtRttpaXB5LBMcNjvWrocTaXeMLgDaRwa39IhRrGCMKp+Xnio9eso1tVlOQwIANYSq8z5GaKnZcyNO31JWlAByCODTb1JLkqinANcdqGpW3h+Bbi+uhEDkxoDl5COyjv256cjJFedeIfGeq+KALdybaxwP9GjbIYjnLHA3c9unA4zzXHUnCD913OiEZSWp2+r+P9L0iVo7J/wC0Jl6eS37vt1fp0PbPIwcVy118Q/EV1IHtGhswCcCOMOSOwJfOcewFczFZkKMjmr+yG3T98wX2xnGe9c0q8pG6pxRot448WSNl9Uz9LaL/AOJra0n4reIbEhNRit9QhBJYlRFJ04AKjaBnnlSev4cR/a1q2VhjZihw2flx/OhNUt2do3iIIGcKQT/Ss+eRXKux67/wl2n+IkY2TyCTBLQyrh0AOORkj05BPUd67SJvmujuBjSUbQTznYp/qa+doGUzCaynKTpkgqcMP85/WvUfCnjdNStZLHUCItTUb89FuAFAyPRgAMj8R3AurV54pW2JhDlbfc5bxzAyRRlCnl2t48aBQvT5XAOM9NpHPOODjoOXvIYbO8SDTrtIWiLSgSkqG3c4L+vygfNjqOa6vxjJNdeF5ZJA2yTUH8vr8qkuDjPbdu4Hv71w89zFDcmLVIWLeUBHdQn5ipVsHB4btg8Hjr2rMsvXS6+ri4ksVvjhcJ9mWQbSCQQyj29e/vXReFrm7OrFLrSfsxaIp5ot2QEDaQMn2H6D0rkpbPTnMclvqcagoS0U8Lgq+045UMM59PwrY8LK/wDwlFnjVYZo13/ugswY/u2/vKB+tS9ho9Av/l09jgH96o5+jVjba19SkJjSDH3m359MDH/s1Z7Rkdq9vLX+4XzPMxf8U0bTzBbR+YoVscAenY/livLLeRIplg0kfatRmj2GcZXaT1EYOCoA4LHng/dr1O2ZngQsckDH4DgV53cxYe+sdPjW1tbeYrcTF8BuSvzt3HTCD1PBxXiyVqkl5nox1gmdZbwq7YapUtJC5CA8dKTa0Mg9wK07ORgwGMjGMV9HJtao8hahpsM1wrZUkDrW/o2lwpflyMADp71JpcKhMr8uT0q+Y/InVow2T1ArjqVG20jphC1mzWwhTGPasW/smyTGTy3StJSxQlgQaRZAWw+OlYRbTNXqcJeaVLbypMMlHbHvXT2GkpBGJdoO5RyR7UXc0cmpwQFRtySD79qu3Uxt7NlBwAnWtp1JSSRlGCTbK1tcAR+WrZUk4qtcWsb3a8biVOf/AK9Y9veSG56fIo59q3rWQSLvK5J9qJr2fvMIvn0HaexiXcy7ApIA9hWH4z8aWmhxJG0YnumG6ODPI/229B/P88V/GHiuPw3ZbIwsl7NkRxE8L/tN7fqenHJHjUstzqV5JdXUjSzStl2bqT/h/ICvPqVnUemiOuFPlWpY1C+vde1N729k3SOeg+6i9lUdgP8A65ycmrNpYl3RI1Lu3yhVGSSew96sadpc11KkMELSSyEKFX/P1P4Guh0jStW0fxlcCZA1lDbBYpVxhpG2k478fOM8cDtmlTpOo7LYJzUFdiaj4Ou7Lw7dXBmJvhFmKGDkqeDySOT1GB+Bqxpfgyzu7GyM+nN9rWBDcM0znL4G4n5sZzmunt0NzMEkJ+b1rq1tY4NPCqOdoGfWvQ9jTpdLs5PaTqLex55p3w68Oq975tvcMZ9zBjMR5JOfuYx0z/Fu6VzmqfDZYXlvNIvHkDjiC5wGOBzhhwSSBgEAc9a9PvIGtrcrEWZmwWYVj3jNEqx56CqWHpz6CdecOp4e0d1a380U8ckLxbcKylSCRnP8q0rXUnlmRlYR3UZBjkXoSOx/zg16Bqmj2utJ5cwVJThUuAvzJ6fUcnj37HmvNdS0W88O30trfIUkJ85WH3XU9GU9xx1+oOCCK4MTh3Rfkzro1lVXmdLJetL4MunkZCzam8rIu0YLKTjbktt4BDHGTkDO2sizF5FFOlui6lbFEIgdPMCP7p1HG7kcH1qK3na7spNr/vDhmQHAbHRsdM8ke2fen29rCbSCZL5LS4nkVv3rMoG3K5DgHb17+/Nc5sRGfSlsI0k0oxTeY25oLpl+7jswb1/Strws9nN4rsZIra5SRlkky1wpVcq4wVCDPA9fzosm8VXVtP8AZZkuYdx5WSGcEk9MkkgYJ49qsaNbeI7fXLVptNjEBkHmSC3iQqpyDyAD3z+FJ7f8EDv54w0TM38Azn8QP61luxfpWy8e+2uB/sDH/fS1lqgHNevlj/cv1PNxv8RehLZupiKjqpwfY9f5EV51rMN7capdPqU5s9NhuJVhTbhpAGZsomMEknlzgfN1NelRCPaCq4Y8ufU+v5Yrz3WNNs01u7l1W4aV3n3rbxuc7CwxvbHA254HOMcivLq6V5+rO+lrTj6HoUtsjRB15HbPWqtvI1tMOAeehqxHKFIycr3/AMaWSCOQZB57V7y00Z5O+qOh0u+jkAGAD0xW/GqttfriuBtS0EvGc9q6i01JlCnqvQ+1ctWnZ3R006l1qa8mdp4rIlmfftAx7mr91djyA6nr0rLkvY23HqRWcIsuTQya1eSQTKTkNkEdc1Pq1xbW2ltLd3EcUQ6u77RkDOPc4B/KoI7wTR7QCMk/h6VxviLw9L4p8Twxq8628cCo5VWCIwfJYkjacozAbSTuHIAyaKsnCPM+gQSk7I1vDupRa1czxWVpJLYIzLLds2xd2MhUGCWPIJ6ADvyM6niHXLXw1pTXkyF8YWKJOC7dhk9BwTn0z172QdO8NaKE3R21lax4yRwo/DqSc+5J7k14h4i1+68Tas88rt9nQkW8RGAieuMnk4BPJ/ICvPnVlWfvbHVCnGC0Kuo6lea/qkt9eEGR/wCFRhUXsqjsP17nJJNXLGyeWSNUUtI7BVUdyeAPzIqG1gCAE9On1r0Hwt4Uu4tQstYmu2gSAOXtDBy5Ksoy27jAOcY69fZwg5uy2FOaitTQ0jS4tH047SDdyD97MOMD+6voM4+uM+gDslvmYknuTWlLFveTj5QOKzpMhto7V7dKEYq0TyqsnJ3ZcsIjNdRLnGTwa6i4WSK228bs5rG0yxaIR3Rk28jIrTnvMhi5yo6cVlU96WhtT0jqVYEZ1ld1+UmsK9s2MrysQCT061tXL+bAUVuCOSpxWLKOdgBwDWlK97mdRq1iNoo/tTgKcA88YqPxr4Qj8S6MZreNf7RtrKFosE5dfm3Icdc9R7+mTWhHEDIMAySMcBFGSxxn+QJ/Cugt/wBxpvmSAhwgBDDBAGSAR7biK48fK8Ujpwas2z5itZjZyxshOFwMZ+8PStWeaySeL7TCXgwGjlifa23PIIIIPUeh96PGEKf8JfqMipgCYScNnl1DHPpkknH9Kj0q7KRyKYYpzEpdUljDqR3BB7Zx0weTXnHcyazsdAl+aXVpbcRnJWW1JL8k8lWPbArS0nSdGi1qxZNd8yVZ42SP7G43HcCBknjPH0rKW70eCOaXUdAidy4BSKeWLrk8jcfy4rW0rVdAuNYtTFopjm86PawvXYR/MoDdMHkjg8cfWh3sI9IvBIyx7CQAxLfTB/riqgX2rUZNykdflY/+OmoYokEW8nn0xXp5ZK1JrzODGq816ECBQfkz+NeZ+LVeTxddRRqzM3lgBepPlrXp+x/O84keWyhFX0IyT/6EPyrgvEc2ov4kubbTLbbKyp5k0Y+cqUHVj91eMduR15rz8R/vEjsofwonVKxH0qVTjucUirkHJpwUY5r6I8ZMmhlCyqWbAzyfStuQpbQBs5L9qwI03NirvzyqoYnK8DNZTjdmkJ2RZvNViW3YO4jVB95jgfn2qrZ6lpDIsz6raFAPmP2hcDn61zniCW7S8Njb6dJeSXVqUXZIV8o71+ZhypXpncvBCsGUqDXE6bouqahZwi0tTMs+5E25BzyOcjaBnvnA7159XEezk4RR20qPOlKTPd7LyXtBLCVlVlBRlOQQeQQfTBqwQlureWgBYknHf3PvWJ4V0JPC+gJaMY5LpiZJpEHDOewzzgDA7dCcDJFYHxB8UtpVkun2cjLfXK5LhfuRHIJB7EkEDHTk8HBrhrVnVdlsdEKagcv488WNrN6dNs51exgI3OnPmuPQ91HbHBPPI2mudtbXGNw9zVSzh3NnH0rrPDWkNrGr29oOI9waUggEJuGSM9+R27+lTboi2dL4G8NC5uF1W5DpDC+YAVwJGHf6A46d+/BFeiItvC8Shh5jsSFY43464B6gdamt4IrJre1gjIhjiIUkltuNoAyfb+VY/iKUJf6JMQfJFwVZ+yklcZP4H8qalJKyYnFbmTa3css+rq7ErHePszyAC7jr+A4q9FpPyebIxOeRt6Vg3M4jj1OSM4Y6luLAccmXv0x0rZ0a/lvtL/fMpZG2DaMZwAcn3yT0r0cNWvHkW5xV6XvczNXzGMO0HAzUd1NBGxiDqZQoZlzyAcgH8cH8qhWfEbbvXNZECSrr+rXUn7yGUwrCTu4CxgkDPYMx6d91btNSSsRG0oSbdrL9UjQuULRjB+71xVJvrk1pPMrwF9hA4HWqtqsDtKZVkYxr5hVB/CPvHPt9f5itJVI0480jDkc5WiZmorqkmjX0Wm3t3aylA++3KLypGNzsRsX1YH8COK24k+yeFIYknmuFkYbXlnWZiCoODIow3ORnvVNGiGty2c9uZ43jE0KpAJgEEkQwpzhiDljx8oIIJwM72k2q3GjaUjrtEGJXTy1jIKgjDIvCNuPKjgHI7V5GJqKrPmienh4OnDlkeTeLrOZdS8TWpSSS2iFu6kq5SGYRwA4/g3OGI7nCfn55aO9rP5pO75ycex7flXrfjjR4T4l1+cPILifSxLJlQEABUDB3bif3RzhcDjnkCvIrnz/PfeuJPM+fIxWJsdlDruqW11KIb58Mu4FiJAQF6jOcA47VOvizWCeboMO/7tOP0+tYulLaXVspvL37L5QMe/YZC3pwORwSPwrTe10NmDDXSFYbh/ojk/5z/KpsB6kJEjGWbCsCmfdhgfqantrJZZeR+7x1z0qu0YcqpBI3qce4OasxyFGwp69a78uvySSOPF25k2Vb22+zSqAwKsTjH4VwPja2v5r6MJcxWun+Rl5JpcKXBOQFHLNgDHBrvr+TE1uhB5DkH6bf8a888dWunz6tDJf3jxqsKfuYU3SON7ZwThR17nseK5cT/vDv/Wh0Uf4SOsC5p201IFqWNNzAEgc9TX0FzxSONDuyOta1pD8hLphuw9aLW1gZiGfDAZBFaCRhoAijJU/pWFSd9Dopwtqc14nZYpQd/lo0Pzlp/Kjzh8bsfO3sF/wp3gXQX0rw/by6lBJDdYb5HlY7VLZHyHhW554zjrgkitSHTTJqUd3LsMUKEKpQEsxz3PIAyfrkVqL87YH3Qe9eLiZWqNI9KlrBMzdf1eDRNKm1C5KnYMJGTtMjfwqPc464OBk44rwa7urjWNTnvLlt007lm5OB6AZ7AYA9hXR+PvEp13VzaW0rGwtWKoAwKyuMgvx1HYcnjkY3EVhWVvgqT65NZxVlc0LVtAUGB1B+o/CvVPDXhqPQ9Lj10wNPqHkbvLjc/wCrbDbcHAXopJwTwcE8CvMLvzreGJo0cDzFyQOMHcQM++w/ka9ku1T/AIQKFXVCj29sm2WF5ByUH3VCsTzxgA57CtE3FXIeug2bWNRGptaR3MKxRXtso8uEyyvE4JIYfdUEgDIwQATg5XNf4nsR4URlZlYXSEESbOzH73apIZ997ayFrhILmWx2s0i28TNsZ8qBy5PygoRnO3oBlYPicMeEo+nN1Gp+Tf2bt3qVuUcLpBMnhHxKZBO8qG1OSwSQfvMZz3PzHBJyenaus8EXbxeGlkYiRpNRWEicrGSDsB5P8QBPT72OOSK880y+8vQ9dslWMwSwQzZWMuAVuFGAOoGHJI7dRXc+BJyPBNxIJIVEWocf6Q0S9Y+M4JXJ4245zj+Kqba2JsnuaWiX0moWCyO28lsZOMkEBgeOOjUmuTvYWk8bSzrK0CzW4giKsXEir1bAcZIBCg8HnqKyvBEsl3p9whuGLIyqJDIJcYG0EEYyMKDzg11c0VufFelbztZImyVwOXmgwTn3UV04ivNU4RT3MKNKLqSk+hh6b4mtNccW0avFcIm9o2QrkfKCw6gDJxjOa3Xk8uK1QxM7qNigEE/MSWxkjAAUMevC9M4xx/hG1l/4Sa7klHJjkU5GORIo7cdQfz79ur8UExpp0sCDd9thEpCjnJKk8j0IpV6sp4dX7jo01DEO3Y5nXbSb/hLNQimgtF09NMZEFwzmNkEg5Kw/Mp3cYHXZ7iu20i6gt7SySWXZ5MTxtH9mMJTBUopj5KDb0DckYJ61ymoaxbQ+LL5I7nyg1t5CObw27D94kmfNIyBtJ6DPXHzMAOl8KLCnh23dfs2WgEqtbGQoUYnp5vzn7v8AFzXJ9lHS9zk/F19bS6/qMAjJJ0GSRZEZV4LsGBG3JJwuDnAweDuyPIZorprRQySS20ZJDgFlUHA5PbOAMGvUtcl1C58TasyR3D6edJYswUtErkyBd38IJAbGfevNrO0a6hlMN3EsfIeKaURs2eg5IBAIB/KmAzQbmFrmWCa2+0QuCGhJ27yvOMggjjPPritSLXPDD5QeHmQbe17L0UZHb/Oa5yNzbzqyqodH3AMOMg969FgvvHN6rSWrmS0Zj5UgMOGXPvzQDO/sZVukt51Pyy7XH0PNSBSOaqWpuoNGgkm5vUgVpM4/1gXnpx19OK6MaDqU00vlWoESL0clWZuuFBGCPfPWurAVI0+bmfY5cXTlPl5TCu8mNSex4/WuE8bmwtruxnubZ7iaVXRI/MKphecnAzwWHQivX5fB99PbR7HhjY4ZllJBU+ny5H61x/i3wV4pc2/9l6VbXkkQY+czRkxnKkFd5HPB7HpWOJkp1uaOxrQi40+WW4KAD61IByDgV5HDeXlorrbzTQhuSI3KZ/IiuxTxpZ2VtbwPFcTyJCoeTj5mA9znr3/nXpU8XCW+hw1MLKO2p2EUjxMWQ9evFXrOaWS52gA7vvZGQBXK2/i7RpNhknkgVxkGSI4Pr0z+fSu5tbdbSI5+83WliMRCMLx1YUaM3Kz0RYfaI9u3rXFfELxIui6ONOs3xd3qkZUjMcfRj688gH64PFdPf39vYWNze3TbYIULMQecDsPc9AO5IrwPVtUn17WLjULgkNM5KrkHy0/hXoM4GBnv17148Vd3Z6ZUhTLZxn6CteANFE5VGkZVLkKpJIAyentyfTmq8EQABC9eB7V0OnafZXFn85eSfcWYKfkVccD68H1HI75FUwOd1pgwtQJC0yRmTI4IJZQCPyPSvZJdRs7LwBZmWdlVreCBGiJUmQLwu6MELypBI4FZunTf2foH2X7Id0tn5/2locgBpSrqWPfYeB16+gzDfWhPhBiZGeAeVJEgJwpDTMxweAT5ijI6gDPSjm6BYle9iWaWa2NtK0F3Zu32ZWu5z+6ZOWIwTkYDAjjJ+8cHU+J8Z/4RBpAWAinRyRJsxwy9f+BD+VeU/Y9XkbVtQsZXis4glu0iSbC2RGduBzgbu/HNbtnqGrX5ttB8RyqbW9Nsq8x+Z5bSKVIIB56klsnnt1phY5STzI7dAA+6YNG/mHZhVKP94e/f860dE8SX9lBHp0V2sFnLdb5S84jDBmUEecAWUYXG4cjJ9BU+q6XFpOtQ2Vuj+W1sZw3BO+SCNjx04JOP19+aWR4GaUM0Th87ziNgd3XOCM+4FVuSeq/DVVurW8zL5rIUVnMqyA43AcgD+HB556Vt63HcveTLZJm7MELRZV2GUmB5CqSeo6c8cA1j/CUNI2q5YuymGMksj5K7x1XB6betdi8SvDNI3k7jAI8v2+979M4zx6dO5Xd4w+YqStKfyON+0z6X4j1GWDTTc3rbw1sXEWAywNkk8j5jIPXPp1rXllur3SBcXUOyRri3IiRhIExInGSBn1Jx69awrq/U+MI7VowiyKzjCgZHlRkDIx02twRgdvbptML3WgkzspjF0xjKnOQNjDkqoI6DjPHc5rKUnaxokr3MLxFLaDxGcSeVPk3YbzET5x5nO98qpC5+ds7c5HIrc0a6J8JzS+c8zJsgaR79Lx933vmkTv8AvMc84APeuX1yzvNQ8Q6fACqFrWVIQltHdM+wkvmN8KSQSME9ACOwrutFto0tru0kikhi/tBmVJIYoflXDDCx9VOOC3zHqeop9BdTgPFulSw+Lrcx7MQaMRIGdVJLC4ztBYMxyRwAeMk47+b2stlMzxTyS2jzMnzLHvXHXIGQQMr717b4xjtJPFuhCWOY3DpcBXRwEAC8hhtJb75xyMc9a8Ps5YI5FOoafHd/NsVfMZGzknkjr+XOaYFPULa3i1Nhb3PnxRn5ZNhXflRng8jn+Veg/DzwH/wncjTyamLa3svLS5RYcu4JOAp4AO1cbue3B5rjtet9Otpilm227EjrLAJhKiKFQqQwHUkuCOcbRzzV3wv4r1rw9pl/Hpd6LZrhUd8gfKRn5s8c5wvXHzdDTA+qtK0bTfDmlwww4WO2hWMzzMNxVQBlm4A6A8YGewpuoeKdC0tEe91S1hRjgMXBGfcjpXyLfeM9e1K4kmuNXvZncYbbKUB4wDx19/oOaylu7hCduM+pRTn884oA+sL34r+ErKYR/b2uARkNbL5gP5HI/EVz+ofHrwvbP/oyT3aDrtUq34AjB/Ovm77VceZ5gcK4yd2B6Y6Y4qoQ245I78AY60gPXNE+H2r61HHPtgtbQtxJIcsy92UD34wStcrquny6fq97ZvOsr28zxbtm3dtJGepx0969PsvF+mp4JkspLnyr37LMgi2MRuw2OcY54/OududFXW/Fl2IXicXFy8qssqn92zE7sZz0OcdaalJbi0HfDnw39tuY9Wu4QsdlM5jZZSRK5VcAqegTBYYA5fOeK9OkZ3fy06dz6VFa2cNhZQ2VsNsEShQuAM+pPuepPc1R8Q6zF4b0C4v2AaX7kCsMh5Tnbnkcdzz0BxWTbkxnBfE7XxLPHoNrKWihxJckH7z9VU/QHPpkjuK4ayiyQxzz09/WolMt1cvNK7SSuxZ3c5LMTkkk1r2sIVAQDWlrKwFy1e1gnhN3uMO/lFGSw646jr0z2zV5r2Ge1BtbX7IgUjykctn6nAz170kX9mxQxifT5Lydic5lKBR7Y6/jTrllknKrCkOcLsUdM8VLAu6m+rJLZ2S/a002G3RmKxFYixXBBbHOSemSOK3dM0u5i0rW7iZAY7yygNuFOWbZCqkYx13N9f66N3oq3WjXNzJqktvc2UbTrABvie3VU3Ex7huKkFs8n5iOeAOSh1a3a9SCGNmVYd8rm6SPPG3aAA+TwOjk4+mKLCbZZ0hMeFdZtJEdJ5bnfGjKRuASPofqpH4VzMuj31xcyi41CPMfBRpGaRF/hULj0xx6ewrXnu9YAcC2tbSbAMIljdyPmAXYZHKM/wB4EhSeMY64gQ3Wq3cFpHia+nZYsyqkZkLHooVVB5YknkjI54zTWgrtl6HwPdXENtdpcwmFVaPEsZyxZRuGM8YBXIOM7hjvjHufAms2tq628dvK6O7DyJMblOMEbxx0ORz7ZrpvE/iI6Xqsem6dcuLfTYhap8rN5xHzPJzuBBYnvkAcYBqivjC9UiRBHPEW6suMj0GDwPc569Bjku0Owmhaxr/g2/mni0xpobvAledQAAnKhSAGztY8EnGe9ei6NOsGlae5kVbmOCICLzPlyF657gFevv71zdhrkt5FfIYoxLDhImDjZJIxIVcEg87WORnhT0OKiu9WNlqNuI4AW3TRsolLgKsbP8zN1JCDrnpxnqZk27FRVrmHqV0NR8Y2d0skfnrC0byQsXTzDGG4IbcRl/Xp3613ugQxQWxkIdpmHltlidwx8v3iT1ZuT+fFeaTKia9cynYIGZjsIBUcAdMAHhQPT27V2Npe3Vp4avrq1iMF0s3yuiEM3Kgn5ecgcYGcYFJjW5N4pmj0/U9BuE+yn/RbwkXW90X5dxZlX5iAV6DOPwFbPhS3t7XRrIJapCHuTI3lWzQo58ojcA58wjgDc/Jx6YrO1A/a9fs5Ft54hDbXERntyUf5o1UMH5AIJYjj0wDg4u6CBpdpcGWMedcSb2C3Ms56AAs8nJbg5wAOlUnoS9zmviF4oTRfEWkSTyy/ZkE7SxRk/OxCBeMgEjnrwMnvzXkmlXeoGYpZhnEmFZUjEg2dyQQeOler+LfB8Xiy7tpbm8eAQuxIRQSyttyMnofl689elM0j4faHpN2Lq3+2GVVABNwy856nbjOfTp7U+ZWCx45LZyWcqGaKWF3zxIpG76Z96sWQ82WSBQGMkbDbjJyBuAHvkCvaH8J6QLC6tbayt4HuEceeY97KWzzk8nB5xn8q8TtSkF2k5Uth1Zlz1x2ovcZrweD9du3dV02WMoBky4QHI4xnr+HTvWnB8NdXntFllltoGJGY2YkqO+cAjPtnn1FekrrelvbiWTULWJtoZo2nUMvGSCM9R6VUk8ZeHY9PwL/c5+6ohfLc+4xU3YHL2vwwtElV7i/kljA+ZFQLk/XJwPb9a0rbwHoVsrA2z3BY53TOcgegximSfEfR4LdikE7yfwq21R+JBOPyqhN8SS1rJLa2EeVIBLSFh+QA/nS94Dn4IL4Oscit8x4zGa9d8M+HjoYu5p3SS5lkKK4QDbErELjBOMjBIz6DqK5XwvpNjrWorcxFTBaENIoQgM2cqOfpz/8AXr0ViWfb0pzl0EiSMb3z2rxj4g+IRrviD7NbMGs7PMaEEEO+fmbOOnAA5I+XI616F4417/hH/DsghfbeXeYoCDgrkfMw5BGB3HQla8TgTL5oguoy3bRr16AVfaUW0asYy+87SqkdO/X8vxqO2jAT5gT9aVhvlJcb1zwM4xjGO3qM/jViOgsrm5Fopil8uPALZ2559abDtnvYV3kl5VyevesxLhVj2NGxUjHB6VNZXfk6hFLsICOG9agqyPQdV16DT9Z06/tCxu7Rm/ceU0QaM/K2Sy8ltpGRnufSjxX4U0JNSmmi0xTBJprT20wyoyq/KoxgfKFHHoVzXMPdJqN7FLb6dK53Azyxh5CefpkDr7c16Dod4niXTtS8OXCwxajBFJLauIWjAWQYbcuTjlgTyc7s4+WnZkXR5/onh/S9Q8O6xNc2u6W3lcxOsjKVxArDoRkZJOPetn4XaWI49S1h7YyTW0xg06UuSwlKOJPc4Qg4PHTHNblv4TuNAnh0AT/aX1r7RJJOqELbKsUaEkc7vb7vJA75rbTRhpGoaFo1uZF00tOUnQDzBKQXyzEYzxx7ZGOM1Si1uUlzO0TyfxUEPiiRY1UjYwGeQP3QBP8AOsRY5PJCI5LvuwSOvXn9M8V6j4/8O6V9gvfEGn3xN9G7oyRyI0e7Z86nHRtoJxnqea3vD+g+G9Jjt9ImitH1uOOOS4mOHeOZhxtcjI5BKgfUjLfNO7a7CbsjhXb+zNS0zQ7S4lCK8ck/mPnMjOGC9wSoKA4PJB962LzwzbS32mg28lyJbgxuGG4HEMpH48Z9scVr6ZoLS+GvEFsWZp4NUlmieIEsXVVOB3JzuUZ9c47UovrjS/CnhS8lVbi4W+kKrLcbA5aG5CAu2cHlR356U1DmSkTGd9htn4Mt0uWaHSP3pAJeZSefYvwD9Kk0K3i1u1E+lyQPajADqcAHGduOoOD0xx3rC1r4t6rY+EvDutwWdip1T7UZI5FdwgjkCrjDDsef6UyLULzwf8MvDllo3lDUtXX7S1z5KqqB8OzMScZAdFDNkEL24FHIupTbsaPinUE8KeU1/DO6TEhHhTKk+mTgZ9utc/pfjNNc1SOx0jTpp3dgHaZxGI8uqZJGeNzKPxq1Lrf/AAm3wavNR1SJm1DTpvJZ4kUGSRSuCOv3g65Axk5xgYqL4feAtb8L+KNK1K5kiWO4knilWKXerRtF5i8jg/MvuPlz2BJy62KSur/1oV/HWt6p4L1Q24sk+xsx8iaTe4cFiQAxIyVXaD15PU9a4p/iRrk73LwPFAI4w22NFI6j+8Ce9eyfEPwrL411aw0wI8cNpbzXLTMp2OzYVUB6bsgH6GvDtX8L3Gga3qmjmeC6nhs9zPEMBj8pKjPXHOf900+VDlqlIqzeNNek1Ig6jcKSc5SQpjHTAGAOnasZnPmqFA2lmU+2P/1VAtzHHJvdd0nB3bR3Aqa3kWQsVBAJzQQTahcMXYCUsSAzHdnJMeT+ufxFR27slijSEjMpxu9MLT75n+zwPHFlUjIZj6h8n/0NafLGH8JW96AQ5u5429MBYSv/AKE36UDMhYJSm4Lx061oWu+OwvAUIG0OPwOD/MVRS5kHBIAwe3etXRme4sdbR1DkWG5cqPlImi5H4ZH40O4H0Loekx+H9Ehso8F1G6Vx/E56noMjsPYCtGAbju9eaikbzGx261y3xD10aP4e+wQt/pV6GjH+zH/Ee/Y47dSR0rFasDzvxhr58ReIZp43JtYv3VuMnGwH73b7x59eg7VnWsXHIB7c1Vt03Nn0rVhXaASenrWwgmuBaw/dZieNq4z+tVf7ThDYZJFPcEDjr7+1QahOItQaG4cROvysrA5QgkEMMZBHp1rPB8+dzA/mMxCrGgYs+TwAMcklhx14osCNaXWIowoWNyT2OBgcf410Pgi/Fx4jtJXjWNEZgWL8Kdjkc4/2a5yfwzqMWYpLVhcBFfaJoyNrHhgAxZvuk8DOBXSW3h2TQbP7XMZj+4eaSUW4Ko+7ywipIVLtteQ7TjJCkZANCinogbO/8Taw1hpUQtrmOORJ1ViVycEOeh+n6Vd1XStY8KWdl4kkuY5dRsZtlwsJAW6gJJZNxx2AxleCuQM4Neb23jW/fULXTLyxsL0MlpKEdC0km+NCSp4AJ3dNpwWwOBXrd41+4cxiKZn/AIpbhlP57SaTg4vm7jbvHlsZur2g0298c65Zm4OofZ2gVYWClFMEDeYCPmypOf8AgP5Znh65vNG8KPc6zDLZWulala3MbFmbbG7CJ1KkltoViQB1J710KQtP418RJcODpt2BaSqJSrKWt4ssMDuMDOR69q434haW+ieB9P8ACsF1PqF8902oXbx7Yy8Rds7l3E8tINo+b/Vsf4a0ktSacrJd9TpfGOnaZFZRWT3wMV5q82oTso4VEUrIoI6EEqvrkk44IGF411TVNN8etHpqv50uo2zxgR7hK7QKiAnGBghhyT9/OOK3odR02Sx1mz1bUrCBoNYuXtQ0sayLG0jHgnkEsZCSf4fbBHP+MfEnhuTxl4T8QRXljOIJHW/dJTIsYUqVJVGPzDLspwSduOcYrKCu+VdLfcOV73Or1zWbvSNT+0W5IiHiaCC4UBiDFJZoGyq8nlgQOfmAODWb8RrK/svCqQ2kghZNYMttIoVQkXkyOepwAvzDtwuazpNZ8P3XgO+NxqunQ395qqXs8K3sczxlp4yQN2Q2yP5eBtOw8dap+NfHWkX/AIQ0u2stX827tr1RKYcLIYgjoXHAXJDD0GT0A6bxdlYysZ2r+Gb3UfhP4NjtYvNuEkuIUTGwOHZpAw3AcFY8jOMg1p/EDSZP+Fe+Dnu5fs/2CBIJhnJ3+UuVGM8/u256cHrxmtrnxE0I+AtFgs7ib7fbXBlW3SN4ygCTIpBB27QWXgMeMjB5FJqXj3Q9c8BahokE101zM0TRW5jmkdv3ysw3Zb06Z74HYVNr6GnNbUu+E/DmpeGvh34hlvLIxXWozLbRWZlVlVWYIrBgSM5lPX+4K9W0KLZ4dsLa1uSWtoooXeRMsdgUMGGeCQDzk9cjI6+SS/E/QP7H0Wya+muVtLZJLoRwtmacKAASwAYA7nP+0IyDwav6b8WvDe6SG6ub1LKWF1eLyz947QOV5Bxu5BH8sFrML6HrCwyreyzmdmikjRFhxwpUsSw9zuA/4CK8W1TwdHr/AIqutesLkW9ldpLs3qzO5dXHmYOMAltwHcY6EmtbxJ8XvDOqaRdafBJebZWRSyxEb49yl1PII3KHX8eo7L4f8WWHiUXDWiXCCDbu85QM7s9ME/3TSkEdjiT8FlL5Ovk8f8+eP/Z6wfFvhO28IW2mRJcNcyzyTNJNs2ZUCMKuMkcZY59/avbyyno36VwfxVUHwtFIiMzR3aF2C9FKuAfpnA/EeoqdRnIeENC07xPLc2V7JdxpEnmKbZsbssoIYbTkZCn2x+XdxfD3QLfSBpjTXj24uDc/PIAwYqFxkKOMKK4b4a6h9n1yQZ/dPA/m4BJAHzZGOc8frXsE89pGrK8kO4D5lYheozjmpd+g1a+pzv8Awrvwpt3nS9xJ6meTJ/DdU1r4K8N2Lym20woZYzHJmWQhlOMjBb2Fa0V/azSPFDIsjxYDIjhinpkDpU5nRcZik54HHWlr1B26FmECOMyOwAAyxPQCvCvE+tP4h164vTnys+XAp7Rjp+fJPuTXofxH13+z9GTTIHxcXufM2nlYh179+noRurymBN7j2ogtLgW7aLCgVtaPGsusWavay3EJlwUjC8sFZwDvIXB2nIPUA4rMiUfTvUcOt6nZyyR2Lwxxl+GMaswbaRkE9OC35n1qxHr8OmaUmpS6mNPRLyUDc7ImQck5BGcE7uT3wPSuYstH1PTdVvLx7mAWc2ovdyb5flWPzFPzE9GwAemPlIJHGeC+26y0DQvqlx5TZyokOOarQWHmwy5lcrGSCpPFNaAe23HivQrGF5pL61CZ52vvJ/AZNcj438YaFrHha60+0nWS4m2CMiJvlw6secccA1wAsYlUAJn6mpHtilpI0KfOqnJ9AcD+poW+gHY3t7Y2fiWfWI9Pks7+3gWGG0McSGFhHtR8MRuwrr8gHYelJ4g8Y2viOwisr61e38qcSrItzARuAIAKs3Iwx5/TtWNqt9d6nr4uFV1ie3g80KxA3Oi8/XBAH0r0W6tLLX5NM0u0tLa2me5Ekv2eNYmMQDBkyuCFOR+lDuHQ86Sw8Kzbnlup/NZssBd28YGeSQBnp6AYrUg8N+DJI1Zr+7+pniO73yBW5F4VguPGV1pL3t6beF1ITzDtwYgw5+pqr4x8OTaDZWt/Y6lfS2bEpIrSk7TjIYexxj8qbTEmmZUXhbwkzukWqySEdPMuYwRj6JRc+ENBSOUxXD7chhvuwgXAx/zyOOvv+Fbem+HEksNXvL2/1BYrW7eGFTMwJUY6j1wRWVe6FcWieGkfU7xZ9VlIkKysdikqBx6/NUq7dhvQoppOhQvskTSpME/LJq7g/jtiHpVpbHQomw2m6DxnIOqz/j1jpq6JfRWGty3Wp3a3WmyQR4SY7W8x9pP5dKuP4SvYvCU+svql+kizbYoQ5O5DIEB+vJPFOzFdFVbbQVYFNN0A5JJH9qznrj/Y9v5+9RyWugbUB0/w/hTkY1efPBPX5KZeaHqNj4cg1S41e7SV7wWzRFmwuYy+c59sdKLXw9rlxb6hdfbblYbGN3cyO3zFc5A59jSSHccU0Fh/yDdC/wDBtN/RKcIvD2Nq6VoefbWZ/wD4moxoGvy6Jp+px6pcSW10zI2ZD+6YMQM89Dtxn1IHcVd0Xwxr+q35tJNWnt1CF1dsuCQRx144J6+lO2thXKgi0Jj/AMgnQ8E4wNYm/wDiat6df2Gk+Z9jtNATzCNwfUnk6Z6bozjqa0J/BWqwafJeReIjLGtv9oz5RClSCQc5PYHoCfzFcqw1OG4EbX0u4HqCCPzoaaGtTqP+Et2khrfw6AOhN6MfpHXB6r4q1PV/PsJRavbvL/q4I1AOGyAGCgkdPrxWi0usrGMXpkU8cque/tVCWxkFpqF1OF3ho8HoQWY5wPw/l60JhY6Kwl0PS7lLmxsNJhmXjc2rSuPfIKYP5Vtt42lWPy0m0wEjh21F2I79THmuEt9d1NpAZLmSYDPyt3/KrF5rV/dPIVLwx7EAQHoQACfxIz+NSB0//CVNHPLNFPpkcspXzHS8O59vTcfK544GaVvGHmRlJm06Q+rXh9P+uWKxDr1kzZazuwPa4NSR6/Y/dFteAN1zKT/WgZleINXk13W7i+YsEY7Ylb+BBwoxk49TjuTUVum1fc1WiTcwHatGGPnPpQIjuZhb25O7DEYFULNS1uHHOJefxBqxfKZpcE/KoOM+tEEJhgxnqc4qktAJRimooUuAfvNk/pUaP5jsBn5Tg1JErNl2BC9sj3osO5fS3BA5FX7WwMolj3EBkIO0Zzjnp+FZyynaOMY961tL1BLG0nu5g21CFGzrz9alNp3QiXSbFDq20f6xoto2nBwE2/yrtPD06aT4e1DXbraxj/dRqrD7wPTr15H6VyGlBZfEu20Lpl2VDnLbiduRn3Oa6a/vdFnvV8KT6gsFjbRiNnDqu6UfO7ZOQOcDnvuAp3bEx8t09t4q8Z3EfEkFgsiH0Pkg/wBKivblfEPwraCzHmXYaC1MW7pIJIwDk4+8GHt+RqzqT6ZYeIvt/wBthntdYxb3isysip5e1eR0BOMk0y00vT/CwtNMS5LzX19ExZ0y+1Gyg4IHUKM/7THHarfcIWdkHi+2e38OGPSpkaCO+LXeGBYFmPBOexYDHXkdK53x1qDHxckcI8o6WIxAQcgHAcHHTjI/IV0WraKljpniGea7DpfSrKF27du1y+3OeSc4HTP41QvvD0/i2PR9VW7jG+3SO8yNpLKTuIwOTksPwFTu7CdtCz4msJYpPFZQGQ3o0940Vec+Zsx7klc/jW3r0kdt4Ta1WbzXtZbVJGC4JIljOfqev41HbXMF5r+ti4dXih+zNljgIULMPphhmsKe6kn0fWLqZjtlurVwrNkqDKhA49AQPwrRWcbkJ3INWkluPA63gdXNjq0c3IxnCgY/N61Z7iWbwKIkYJLqTvCScYAw2Rz6qhH45qtq63H/AAgOr26W8zyNcRbU8llJG6HOB1OMHn2NVUae88JRNGV87S7vzJFC5LIQd/GePlkY9/u1nbTQ1Vr6l3w6V1HwadHVEVnt2EO5SFV1x8x/4GVb8zW94OCPo0WpbWEl2m4Kw+6voPXnv34rmvA1p9mtBdSzRmKFJYy3QD5wc56YwtT6TrQsdLtNHhKxsYP3cz3CsQxO5sLnIAUsw7cAdxlxezYmuiO3faoEGwCIxlSewAwAPyJrzHxFYwaZq8cCblDjzEbr1J6+2f8APau28RasdLtLe7aIyWy3CrcYBO2Mgjdx77f5V5l8QdRt9S1G1ntZnAEZjZGBHAY/N7g5Pvx9KctdAinuXYYoCgaTDy7QGAbjNZ3iiWOPRTGoAZ5FUgdu5/lXIzyGFU+zPL1JMg+XPsKYb26miWCaZ3RTuAY55rNRKNTw1Eh1mMShWXa3BGe3vXbQWenOWElpEwxxkV55a3bWk3moSHAwK0ovEGokb1DsqcsyLnAz37UNNgdQdJ0xp3BtV25OAGYY5+tQy6HphJ2wtH/uuf65pbTWdOKQi/lMDsCz3BJMeMADagUsW5BzkAHPGBg4114kS3v5fJuHvIP4W2CNR9ARn8/ypcshJoy7ZDjNXUUhSBjJ6UyFMACknn8twi+nOKYD1tAyBZZRxn8zTZWhjWNRlyW25z7E1Ue6CnLEA/nVeSYqvm7SVYnaT6jr/MVYrE8MzeUjKoXcctgex/8ArULI+wbmwe+aiyRbRFWO/cQy46ABcHP4t+VbOk2+npbyzXUym52/u4mHTIyDk9TjoP69AZVDYyK39BiuZbST7M0KSFiSbj7mAM9Op6ds9awWdMds5rdsIZW0xDaRJIfNHmB+QF9R71AE3hnUIIbu51WaOEPDGPItkX/loxGCF/urgntg7axzbyb/ADBdRNIxLNI6vvJ7knaeuat6dbi5ursvASfs7uYAvLEEHaB65FJEgaxuGiJUkM0fzdFDx5H/AI8P1piZSlguUcH/AEecscn9+Bnv3wc0qLcRPH8ghcHcrrID07/KTjHrVNtRmhkZBtcFM/MOc5xVy1tdQuLeS6aGKOJULlnBBZQM4HrTsLU39QM3iDTbDVEnJuIT9mutzknevKPyxPzLnJwMsp4p1hey6HbyLHetHHJy4OAufUZ6Htke3pXKWPiiWCRoIo1EdwyJIpYleuQcDAyOfzPrWLNfXN9LmWVn9hwtHK7gd7p/iRLi4fTbGRSt2pWR9u4YAJP9auaoD/Yd/ZPjyJNjMyEh8IQ2cnjOQenGMd81xvhuNk1O2aQsDhsAkZ6N2H0rsNQcNpl5uIIaJhj0G3GP8+taSjaipLuTHWpbyNi68fi0lVX01mB3ciYdmK+ntmk0/XAfEd3cRoRBd7WZTgFdsYGf/HT+dcjqB/dieUjPmToMv0U78deB8wJwOv1NathptlbSreWyumI3wBISCGUjnP1qIqTV10LfKnZ9Tf8AEXiBE0S6isGzK0bZYJgKM89e55rA06BpRaWsiSt5kLsZTaBV+aPbgSDkYA69wRUtz5S2d4GXPmW5ChQOpGT344B+v4iqOlqtteRkvEzpbrLhJW3DKqfmQnHRjyP0pXvqO1tDe8U62bvwn5DCEyzReZMscmdm1kYDHUZBzg/r1rz/AFzm5tmdDF+5H7sg5Xk8ckn8ya6rXdskNxEOPLtXLfRVJA/WuN1KVJfIkEQiQKQI0yBjefUk9s9TRe4aWsh0N4IBshs1kDYBWTLbhnPQdKk1xdOEVpNZWtxbSuH8+KblQRjBRsDIOTx1GO+arJLeSqywBkUnHyDGPqar3aSqw86XezZJG/cR9aANTw54eufE9xcWtoyCaKLzhuONwBxge/zCtpvhh4miO6OGM7T8p81c/wA6o+BNYfQ9aa4RVbzYzEY2z8w+8cHsRtFepp4/06OB5LqKeIou4gDcD7D8++KOtgdzyYeDdcNxLAtkZJYNokRFyybuRkDpmlk8FeIWAUaTOD1yIzit/S9Vv49WvPEVjFAj3UztJC5+YpuztHQckjJ65j6jJB6+x+I9hctJDd281tcIMqhGd49R6fjTcWldi5r7HmSKFQsegGTWRKWeVixJJNdXpenRXwlWcFoguBg4OT0P4f4da2ToNu0nmwFEkOOHX+o/wopqLfvOwpNrZHBR6Zc3cUaJBhQzNu24POBgn04/U1rW/hvdtE8gJB5VB/U1066bcRSNuQEDoRz+NNVwilwM5OK7oUqW61OeU5+hzuqabHZpD5CshyQSE355HUGsSzhVrORipYq+c8YHHWt7XJd4tydh2scAkgnlehHSsC12rbyEhOSeoOenauaurVGbU/hK32iRlCM3AGBjrWha6jeWyN9lmkGzBI5I/lj86yucc9KlR1Akzg7fmIIHTP6dfUVlY0NT+1rmCd7r93JI/wAreYuQc4PT8K6DS1W60h5TsR7kvtOP9WCwyB7fIK4W8mdoWxlQrZJxzjp7+vrXXeHXKaHbLk8b/f8Ajajl0Fcr2cKwNJIY2LtZuwYxhwDk9Ovbt3zW3JMW8PnkYNn2GP4PSsS2TEcZVGMf2WRcxOSM5Y8Z78f0rZZduiOG3E/Zf4v9zvSA86hieQhudqkENjA69q3tAgiXVRHJGrhlZcOAffp07VXskV7yMOCwZlByeSCRVvRjjWUPH8XT6Gm2MvpGkGrQmNAiuhIAiMY/jHQ81fumVrCYHOCy7sdcZGe47e/41lyuTeWjHrgLneW/jYdTyavzOFiYcZyD8ybh19P/AK1bcrlQaXczulUu+wzW1WLS0YIC29ixPPUtj8s1d0+6K6dEnVmiUZzjtzWVeKb28kgWY+QCD1yQvPBPr+Aq0T9nTagyFXaB14xiqoQfs5PyJqNcy9RbvUTJpl4R/BKsWMjjgrwPxz/+uotIaaf7N5izbCjhSIVaNRyPvDlCQB9aZewxCxu4efMc+YCVA7Z/Xils44LYWkUBtjMyq4eQt5hLKxPQY45x19+1ckdjdm3cRG4/tTdk74jEox6r/wDXrib9UWG3dSWXLhdyhTjcSMgE+vTJ+tdgJLoIzsYtpc5x1POD/KuR1CaSa3tZJBEHwQBEiKvbHCAL0/XrzmmhFbZJPHvmcImcLuOB+AqtN5asBG+8YGTjHP41LsUDfLIc5HCjJ/8ArVBLs80+Xu29t3WmM0dGZRewgqSdzYIPT5fSunCpM22RVdcZwwyOormNMkKTQqFXBJJbbz0I610cEgDkeop0/wCJEmfwsvpxVW+I+0KR3UfzNWoGRsqT81Z17KpuwFOcKP5mu/GO9M5qCtI6HT7UWloqsSGb5mGMYJ/yBUN/4gsNMBEsq+YB9wfMx/Dt+NcNqHi3Ur4lY5PIj7LHwfbLdfyxWCWJOWNcEafc6WzsL/x3eynbYqIF7O2GY/h0H61hHW9TMpka+nZic/O5YfkeKzhnsMe9W7HTbvUJNlvEXx1boo+prRWjsTqy1Lrc9xHGkwB2d04zyOSDkHp7VPp1rd3lrO1rbTyKmWJU8cAcD1PPQc1t6b4UtrdVluz9okzwo4QHj8//AK/SuutLUxxBVwkYGAqgAAfSonO7uVFWPGbxrq3leCaJ7eRcbkcFWGRnnPtS6fLIjOY2YMecrnt64INe23MdpPEsN1BFcIDu2TRh1z64Nc3ceBdGuAWtvNtG2bflPmJ16kPnP5ikpA0ee3EiSxylFAbcCRhT3HAyA3r1z/Wup8Py508JJFhYmZcqfvck/wBaluvBmoWkEgshHcKWJAjOGx/unufQGmaVbzWguoriGSGTcDtkQq2COOCK0p2k7MiV0ronNlLFsKiKRFhdSSuw5O707cgfmauykDR5E2qpEBG3dnb8vTPtTXl3INhG7cM59MjP6ZqHUW/4l8uCRkAcemRWT0djQ5yyidbm3cocNKuM+xGasaWpGoo5JPXgemDU6sAunn0dv/QhTrKIW8oeQneOAOwrWlFyZM3ZF8onmq20ZA2g+g/yaeQME9aZu3uSCDz2oadYVZ5AdowDgepA/rXcmorQ5rOTETy1LEAZPp3psz5GBUULg28TDAyg6fSkbOc9qpNWE9yS4kMiFlkBZUIClB83XjOPeo7TzYXtlQzvEh/efvF2g57r14z1z/SmD5qimuYY7y2jfyiB94lMspzxgjkduPb3rjrUYwjdG0JNuzNq6kMdtt5DMxI/FjXI3EjPY2oIAKggbSPb0/rzXTSMZLmBd3GA2K5WVgbCLaGG0lTlgefbA4/ye9cqNxFWJQN2WOM4HAqrKcyHAwCTx6VIpJ/AYqPrQBetJGR4UViBuHH161tqcMp9D/SsSFFWaL99HkMOM81sryfzpwdppilsy4rtguDyarTHLgnvTRIRxnimyvnbx616GJ1pM5qXxH//2Q==\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A large sign in the shape of a crown stands proudly in the center of a city square. The sign is illuminated by the reflection of the sun on the water, creating a vibrant display. A tall building casts long shadows on the ground, while a flag on top of a building waves proudly. People stroll along the sidewalk. The sky is clear and blue, with fluffy white clouds drifting above. The reflection of the city in the water is a mirror image of the city itself, showcasing the beauty and diversity of this urban landscape.</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def render_inline(image, resize=(224, 224)):\n", "  \"\"\"Convert image into inline html.\"\"\"\n", "  image = tf.keras.preprocessing.image.array_to_img(image)\n", "  image.resize(resize)\n", "  with io.BytesIO() as buffer:\n", "    image.save(buffer, format='jpeg')\n", "    image_b64 = str(base64.b64encode(buffer.getvalue()), \"utf-8\")\n", "    return f\"data:image/jpeg;base64,{image_b64}\"\n", "\n", "def render_example(image, caption):\n", "  image = np.asarray(image)\n", "  return f\"\"\"\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"{render_inline(image, resize=(64,64))}\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">{html.escape(caption)}</p>\n", "    </div>\n", "    \"\"\"\n", "\n", "html_out = \"\"\n", "\n", "for element in train_data.take(8):\n", "  caption = tf.compat.as_str_any(element[\"responses\"].numpy()[0])\n", "  html_out += render_example(element[\"images\"].numpy()[0], caption)\n", "\n", "print(\"Training examples\")\n", "display(HTML(html_out))"]}, {"cell_type": "markdown", "id": "53c0034b", "metadata": {"id": "e90446ab2251"}, "source": ["## Load Model"]}, {"cell_type": "code", "execution_count": 6, "id": "10967820", "metadata": {"id": "39c82b20d38a"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["normalizer.cc(51) LOG(INFO) precompiled_charsmap is empty. use identity normalization.\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Preprocessor: \"pali_gemma_causal_lm_preprocessor\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mPreprocessor: \"pali_gemma_causal_lm_preprocessor\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                                                  </span>┃<span style=\"font-weight: bold\">                                   Config </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ pali_gemma_tokenizer (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">PaliGemmaTokenizer</span>)                     │                      Vocab size: <span style=\"color: #00af00; text-decoration-color: #00af00\">257,152</span> │\n", "├───────────────────────────────────────────────────────────────┼──────────────────────────────────────────┤\n", "│ pali_gemma_image_converter (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">PaliGemmaImageConverter</span>)          │                   Image size: (<span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>) │\n", "└───────────────────────────────────────────────────────────────┴──────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                                                 \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m                                  Config\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ pali_gemma_tokenizer (\u001b[38;5;33mPaliGemmaTokenizer\u001b[0m)                     │                      Vocab size: \u001b[38;5;34m257,152\u001b[0m │\n", "├───────────────────────────────────────────────────────────────┼──────────────────────────────────────────┤\n", "│ pali_gemma_image_converter (\u001b[38;5;33mPaliGemmaImageConverter\u001b[0m)          │                   Image size: (\u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m) │\n", "└───────────────────────────────────────────────────────────────┴──────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"pali_gemma_causal_lm\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"pali_gemma_causal_lm\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                  </span>┃<span style=\"font-weight: bold\"> Output Shape              </span>┃<span style=\"font-weight: bold\">         Param # </span>┃<span style=\"font-weight: bold\"> Connected to               </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ images (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)           │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">3</span>)       │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ padding_mask (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>)              │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ response_mask (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>)              │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_ids (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)        │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>)              │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ pali_gemma_backbone           │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">2304</span>)        │   <span style=\"color: #00af00; text-decoration-color: #00af00\">3,032,094,960</span> │ images[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>],              │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">PaliGemmaBackbone</span>)           │                           │                 │ padding_mask[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>],        │\n", "│                               │                           │                 │ response_mask[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>],       │\n", "│                               │                           │                 │ token_ids[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_embedding               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">257152</span>)      │     <span style=\"color: #00af00; text-decoration-color: #00af00\">592,478,208</span> │ pali_gemma_backbone[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]  │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReversibleEmbedding</span>)         │                           │                 │                            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ get_item (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">GetItem</span>)            │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">257152</span>)      │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ token_embedding[<span style=\"color: #00af00; text-decoration-color: #00af00\">1</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]      │\n", "└───────────────────────────────┴───────────────────────────┴─────────────────┴────────────────────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                 \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape             \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m        Param #\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mConnected to              \u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ images (\u001b[38;5;33mInputLayer\u001b[0m)           │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m3\u001b[0m)       │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ padding_mask (\u001b[38;5;33mInputLayer\u001b[0m)     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m)              │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ response_mask (\u001b[38;5;33mInputLayer\u001b[0m)    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m)              │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_ids (\u001b[38;5;33mInputLayer\u001b[0m)        │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m)              │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ pali_gemma_backbone           │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m2304\u001b[0m)        │   \u001b[38;5;34m3,032,094,960\u001b[0m │ images[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m],              │\n", "│ (\u001b[38;5;33mPaliGemmaBackbone\u001b[0m)           │                           │                 │ padding_mask[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m],        │\n", "│                               │                           │                 │ response_mask[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m],       │\n", "│                               │                           │                 │ token_ids[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_embedding               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mN<PERSON>\u001b[0m, \u001b[38;5;34m257152\u001b[0m)      │     \u001b[38;5;34m592,478,208\u001b[0m │ pali_gemma_backbone[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]  │\n", "│ (\u001b[38;5;33mReversibleEmbedding\u001b[0m)         │                           │                 │                            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ get_item (\u001b[38;5;33mGetItem\u001b[0m)            │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m257152\u001b[0m)      │               \u001b[38;5;34m0\u001b[0m │ token_embedding[\u001b[38;5;34m1\u001b[0m][\u001b[38;5;34m0\u001b[0m]      │\n", "└───────────────────────────────┴───────────────────────────┴─────────────────┴────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">3,032,094,960</span> (11.30 GB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m3,032,094,960\u001b[0m (11.30 GB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">3,032,094,960</span> (11.30 GB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m3,032,094,960\u001b[0m (11.30 GB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["pali_gemma_lm = keras_hub.models.PaliGemmaCausalLM.from_preset(\n", "    \"/kaggle/input/paligemma2/keras/pali_gemma2_pt_3b_224/1\"\n", "#    \"kaggle://keras/paligemma2/keras/pali_gemma2_pt_3b_224\"\n", ")\n", "pali_gemma_lm.summary()"]}, {"cell_type": "markdown", "id": "dd4fe0c2", "metadata": {"id": "edeab21cb990"}, "source": ["## Inference before fine tuning"]}, {"cell_type": "code", "execution_count": 7, "id": "6355a8e9", "metadata": {"id": "ab780f60d802"}, "outputs": [{"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "a cow on the beach</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Inference Result\n"]}, {"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "a series of white hangers</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "person in a red blazer and black pants</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "the top is a mix of fabrics .</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "love will save us sweatshirt in pink</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["test_image_url = 'https://storage.googleapis.com/keras-cv/models/paligemma/cow_beach_1.png'\n", "test_image = load_image(test_image_url)\n", "\n", "def inference_test(image):\n", "  prompt = 'caption en\\n'\n", "  output = pali_gemma_lm.generate(\n", "      inputs={\n", "          \"images\": image,\n", "          \"prompts\": prompt,\n", "      }\n", "  )\n", "  return render_example(image, output)\n", "\n", "display(HTML(inference_test(test_image)))\n", "\n", "\n", "def make_predictions():\n", "  html_out = \"\"\n", "  for element in val_data.take(4):\n", "    html_out += inference_test(element[\"images\"].numpy()[0])\n", "\n", "  print(\"\\nInference Result\")\n", "  display(HTML(html_out))\n", "\n", "make_predictions()"]}, {"cell_type": "markdown", "id": "eaea17d7", "metadata": {"id": "7ba37a64794f"}, "source": ["## <PERSON><PERSON> Fine-tuning"]}, {"cell_type": "code", "execution_count": 8, "id": "40d24252", "metadata": {"id": "0a61883f4a81"}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Preprocessor: \"pali_gemma_causal_lm_preprocessor\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mPreprocessor: \"pali_gemma_causal_lm_preprocessor\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                                                  </span>┃<span style=\"font-weight: bold\">                                   Config </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ pali_gemma_tokenizer (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">PaliGemmaTokenizer</span>)                     │                      Vocab size: <span style=\"color: #00af00; text-decoration-color: #00af00\">257,152</span> │\n", "├───────────────────────────────────────────────────────────────┼──────────────────────────────────────────┤\n", "│ pali_gemma_image_converter (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">PaliGemmaImageConverter</span>)          │                   Image size: (<span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>) │\n", "└───────────────────────────────────────────────────────────────┴──────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                                                 \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m                                  Config\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ pali_gemma_tokenizer (\u001b[38;5;33mPaliGemmaTokenizer\u001b[0m)                     │                      Vocab size: \u001b[38;5;34m257,152\u001b[0m │\n", "├───────────────────────────────────────────────────────────────┼──────────────────────────────────────────┤\n", "│ pali_gemma_image_converter (\u001b[38;5;33mPaliGemmaImageConverter\u001b[0m)          │                   Image size: (\u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m) │\n", "└───────────────────────────────────────────────────────────────┴──────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"pali_gemma_causal_lm\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"pali_gemma_causal_lm\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                  </span>┃<span style=\"font-weight: bold\"> Output Shape              </span>┃<span style=\"font-weight: bold\">         Param # </span>┃<span style=\"font-weight: bold\"> Connected to               </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ images (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)           │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">3</span>)       │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ padding_mask (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>)              │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ response_mask (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>)              │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_ids (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)        │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>)              │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ pali_gemma_backbone           │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">2304</span>)        │   <span style=\"color: #00af00; text-decoration-color: #00af00\">3,035,023,600</span> │ images[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>],              │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">PaliGemmaBackbone</span>)           │                           │                 │ padding_mask[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>],        │\n", "│                               │                           │                 │ response_mask[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>],       │\n", "│                               │                           │                 │ token_ids[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_embedding               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">257152</span>)      │     <span style=\"color: #00af00; text-decoration-color: #00af00\">592,478,208</span> │ pali_gemma_backbone[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]  │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReversibleEmbedding</span>)         │                           │                 │                            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ get_item (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">GetItem</span>)            │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">257152</span>)      │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ token_embedding[<span style=\"color: #00af00; text-decoration-color: #00af00\">1</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]      │\n", "└───────────────────────────────┴───────────────────────────┴─────────────────┴────────────────────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                 \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape             \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m        Param #\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mConnected to              \u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ images (\u001b[38;5;33mInputLayer\u001b[0m)           │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m3\u001b[0m)       │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ padding_mask (\u001b[38;5;33mInputLayer\u001b[0m)     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m)              │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ response_mask (\u001b[38;5;33mInputLayer\u001b[0m)    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m)              │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_ids (\u001b[38;5;33mInputLayer\u001b[0m)        │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m)              │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ pali_gemma_backbone           │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m2304\u001b[0m)        │   \u001b[38;5;34m3,035,023,600\u001b[0m │ images[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m],              │\n", "│ (\u001b[38;5;33mPaliGemmaBackbone\u001b[0m)           │                           │                 │ padding_mask[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m],        │\n", "│                               │                           │                 │ response_mask[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m],       │\n", "│                               │                           │                 │ token_ids[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_embedding               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mN<PERSON>\u001b[0m, \u001b[38;5;34m257152\u001b[0m)      │     \u001b[38;5;34m592,478,208\u001b[0m │ pali_gemma_backbone[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]  │\n", "│ (\u001b[38;5;33mReversibleEmbedding\u001b[0m)         │                           │                 │                            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ get_item (\u001b[38;5;33mGetItem\u001b[0m)            │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m257152\u001b[0m)      │               \u001b[38;5;34m0\u001b[0m │ token_embedding[\u001b[38;5;34m1\u001b[0m][\u001b[38;5;34m0\u001b[0m]      │\n", "└───────────────────────────────┴───────────────────────────┴─────────────────┴────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">3,035,023,600</span> (11.31 GB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m3,035,023,600\u001b[0m (11.31 GB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">2,928,640</span> (11.17 MB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m2,928,640\u001b[0m (11.17 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">3,032,094,960</span> (11.30 GB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m3,032,094,960\u001b[0m (11.30 GB)\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Enable lora to freeze most of the model and save memory.\n", "pali_gemma_lm.backbone.enable_lora(4)\n", "pali_gemma_lm.summary()\n", "\n", "# Lower our sequence length to further save memory.\n", "pali_gemma_lm.preprocessor.sequence_length = 64\n", "\n", "# Use Cosine Decay Scheduler with Warm up\n", "#cosine_decay_scheduler = tf.keras.optimizers.schedules.CosineDecay(\n", "#    initial_learning_rate = 0,\n", "#    decay_steps = TRAIN_EXAMPLES,\n", "#    warmup_target = LEARNING_RATE,\n", "#    warmup_steps = TRAIN_EXAMPLES / 10\n", "#)\n", "\n", "def plot_scheduler(step, scheduler):\n", "  x = range(step)\n", "  y = []\n", "  for step in x:\n", "    y.append(scheduler(step))\n", "  plt.plot(x, y, label=scheduler.name)\n", "  plt.xlabel('Epoch')\n", "  plt.ylabel('Learning Rate')\n", "  plt.legend()\n", "  plt.show()\n", "\n", "#plot_scheduler(TRAIN_EXAMPLES, cosine_decay_scheduler)\n", "\n", "# Use AdamW (a common optimizer for transformer models).\n", "#optimizer = keras.optimizers.SGD(learning_rate=cosine_decay_scheduler)\n", "optimizer = keras.optimizers.AdamW(learning_rate=LEARNING_RATE)\n", "\n", "pali_gemma_lm.compile(\n", "    loss=keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "    optimizer=optimizer,\n", "    weighted_metrics=[keras.metrics.SparseCategoricalAccuracy()],\n", ")"]}, {"cell_type": "markdown", "id": "da88646d", "metadata": {"id": "016ed3975263"}, "source": ["## Fine-tune the model"]}, {"cell_type": "code", "execution_count": 9, "id": "56c00057", "metadata": {"id": "17867028751a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/4\n", "\u001b[1m90/90\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m117s\u001b[0m 877ms/step - loss: 1.6715 - sparse_categorical_accuracy: 0.5523\n", "Epoch 2/4\n", "\u001b[1m90/90\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m80s\u001b[0m 567ms/step - loss: 0.9483 - sparse_categorical_accuracy: 0.6871\n", "Epoch 3/4\n", "\u001b[1m90/90\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m52s\u001b[0m 555ms/step - loss: 0.7245 - sparse_categorical_accuracy: 0.7587\n", "Epoch 4/4\n", "\u001b[1m90/90\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m52s\u001b[0m 555ms/step - loss: 0.5710 - sparse_categorical_accuracy: 0.8049\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["class CustomCallback(keras.callbacks.Callback):\n", "    def on_epoch_end(self, epoch, logs=None):\n", "        keys = list(logs.keys())\n", "        if(epoch % EVAL_STEPS == EVAL_STEPS-1):\n", "          # Evaluate\n", "          display(HTML(inference_test(test_image)))\n", "          make_predictions()\n", "\n", "#history = pali_gemma_lm.fit(train_data, epochs=TRAIN_STEPS, callbacks=[CustomCallback()])\n", "history = pali_gemma_lm.fit(train_data, epochs=TRAIN_STEPS)\n", "plt.plot(history.history['loss'])\n", "plt.show()"]}, {"cell_type": "markdown", "id": "dbfa8168", "metadata": {"id": "37fa359da1d9"}, "source": ["## Output\n", "\n", "The validation data for this notebook consists of just 10 images. In normal code, you would likely have many more data points for validation, but for this notebook, run the following code to generate descriptions for all 10 images. After tuning the model, these descriptions should be very similar in form and content coverage to the descriptions included with the training data that you looked at earlier in this notebook.\n", "\n", "Run the below code to generate descriptions for the validation data set."]}, {"cell_type": "code", "execution_count": 10, "id": "35f5080d", "metadata": {"id": "321ca68e4b44"}, "outputs": [{"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "A brown cow stands proudly on a beach, its ears pricks. The cow has a white spot on its face. The cow has a white spot on its face. The grass is brown and the sky is clear. The cow is standing on the beach, its ears pricks. The</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Inference Result\n"]}, {"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "A person wearing a red blazer and black pants, a red blazer, and a white shirt. The blazer is long and the pants are long. The blazer has a long red button. The blazer has a long red button. The pants are black. The blazer has a long red button.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "A man stands in a park, wearing a denim jacket and white shoes. The man wears a white shirt, brown pants, and white shoes. The jacket is blue. The pants are brown. The shoes are white. The jacket is blue. The shirt is white. The man is standing</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "A woman wearing a white dress with a red flower pattern, a black skirt, and a brown belt. The dress is long and has a white skirt. The woman is wearing a brown belt, a brown leather purse, a brown paper bag, a brown basket, and a brown paper basket</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "A person holds a pink sweater with a red heart on it. The sweater has a white collar, long sleeves, and long sleeves. The sweater has a red heart on it. The sweater has a black heart on it. The sweater has a red heart on it. The sweater has a</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(HTML(inference_test(test_image)))\n", "make_predictions()"]}], "metadata": {"colab": {"name": "[PaliGemma_2]Finetune_with_Keras.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}