import streamlit as st
import os
import pandas as pd
import docx
import textract
from PyPDF2 import PdfReader
from langchain_community.document_loaders import PyPDFLoader
from langchain_community.vectorstores import Chroma
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_ollama import OllamaLLM, OllamaEmbeddings
from langchain_groq import ChatGroq

# Supported file types
SUPPORTED_FILE_TYPES = {
    'PDF': ['.pdf'],
    'Word Document': ['.doc', '.docx'],
    'Text File': ['.txt']
}

# Configurações e constantes
PERSIST_DIRECTORY = 'chroma'
TEMP_DIR = './content/'

# Configuração dos modelos disponíveis
AVAILABLE_MODELS = {
    'Ollama (Local)': {
        'llama3.2-3b': 'llama3.2:latest',
        'llama-3.2-1b': 'llama3.2:1b',
        'deepseek': 'deepseek-r1:1.5b'
    },
    'Groq (API)': {
        'mixtral-8x7b': 'mixtral-8x7b-32768',
        'llama-3.3-70b': 'llama-3.3-70b-versatile',
        'deepseek-r1-70b': 'deepseek-r1-distill-llama-70b',
        'llama-3.2-3b': 'llama-3.2-3b-preview',
        'llama-3.2-1b': 'llama-3.2-1b-preview'
    }
}

def clear_session_state():
    """Limpa todos os dados da sessão de forma segura"""
    keys_to_clear = [key for key in st.session_state.keys()]
    for key in keys_to_clear:
        del st.session_state[key]
    st.session_state.processed_data = None
    st.session_state.processing_complete = False

# Inicialização do estado da sessão
if 'processed_data' not in st.session_state:
    st.session_state.processed_data = None
if 'processing_complete' not in st.session_state:
    st.session_state.processing_complete = False

# Configuração da página
st.set_page_config(
    page_title='Synthetic Data Generator',
    layout='wide',
    initial_sidebar_state='expanded'
)

# Estilização personalizada
st.markdown("""
    <style>
    .main .block-container {
        padding-top: 2rem;
    }
    .stProgress .st-bo {
        background-color: #00a0dc;
    }
    .stButton button {
        width: 100%;
    }
    div.row-widget.stButton {
        margin-bottom: 1rem;
    }
    .upload-section {
        padding: 2rem;
        border-radius: 0.5rem;
        border: 2px dashed #ccc;
        margin-bottom: 2rem;
        text-align: center;
    }
    </style>
""", unsafe_allow_html=True)

def save_file(upload_file):
    """Salva o arquivo carregado em um diretório temporário"""
    os.makedirs(TEMP_DIR, exist_ok=True)
    file_path = os.path.join(TEMP_DIR, upload_file.name)
    with open(file_path, 'wb') as f:
        f.write(upload_file.getbuffer())
    return file_path

def extract_text_from_file(file_path):
    """
    Extrai texto de diferentes formatos de arquivo
    
    Args:
        file_path (str): Caminho para o arquivo
    
    Returns:
        str: Texto extraído do arquivo
    """
    file_extension = os.path.splitext(file_path)[1].lower()
    
    try:
        if file_extension in SUPPORTED_FILE_TYPES['PDF']:
            # Extração de PDF
            reader = PdfReader(file_path)
            text = "\n".join([page.extract_text() for page in reader.pages if page.extract_text()])
        
        elif file_extension in SUPPORTED_FILE_TYPES['Word Document']:
            # Extração de documento Word
            doc = docx.Document(file_path)
            text = "\n".join([paragraph.text for paragraph in doc.paragraphs])
        
        elif file_extension in SUPPORTED_FILE_TYPES['Text File']:
            # Extração de arquivo de texto
            with open(file_path, 'r', encoding='utf-8') as file:
                text = file.read()
        
        else:
            raise ValueError(f"Tipo de arquivo não suportado: {file_extension}")
        
        return text
    
    except Exception as e:
        st.error(f"❌ Erro ao extrair texto: {str(e)}")
        return ""

def text_split(raw_text):
    """Divide o texto em chunks menores"""
    with st.spinner('🔄 Processando o texto...'):
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=80)
        chunks = text_splitter.split_text(raw_text)
        st.success(f'✅ Texto dividido em {len(chunks)} partes!')
        return chunks

def get_embedding_function():
    """Retorna a função de embedding"""
    with st.spinner('🔄 Inicializando modelo de embedding...'):
        embedding_function = OllamaEmbeddings(model="nomic-embed-text:latest")
        st.success('✅ Modelo de embedding carregado!')
        return embedding_function

def get_model(provider, model_name, api_key=None):
    """Retorna o modelo LLM baseado no provedor e nome do modelo"""
    if provider == 'Ollama (Local)':
        return OllamaLLM(model=AVAILABLE_MODELS[provider][model_name])
    elif provider == 'Groq (API)':
        if not api_key:
            raise ValueError("API Key é necessária para usar modelos Groq")
        return ChatGroq(
            model=AVAILABLE_MODELS[provider][model_name],
            groq_api_key=api_key,
            temperature=0.7
        )

def process_data(file, qa_provider, qa_model, response_provider, response_model, groq_api_key=None):
    """Processa o arquivo e gera os dados sintéticos"""
    try:
        file_path = save_file(file)
        raw_text = extract_text_from_file(file_path)
        
        if not raw_text.strip():
            st.error("❌ Erro: Nenhum texto extraído do arquivo.")
            return None, 0
            
        chunks = text_split(raw_text)
        
        with st.expander("📊 Detalhes do Processamento", expanded=False):
            st.write(f'Número total de chunks: {len(chunks)}')
            st.write(f'Tamanho médio dos chunks: {sum(len(chunk) for chunk in chunks)/len(chunks):.0f} caracteres')

        progress_bar = st.progress(0)
        status_text = st.empty()

        # Inicialização dos modelos
        with st.spinner('🔄 Inicializando modelos...'):
            embedding_function = get_embedding_function()
            vectorstore = Chroma.from_texts(
                chunks,
                collection_name='data_generator',
                embedding=embedding_function,
                persist_directory=PERSIST_DIRECTORY
            )
            
            question_answer_model = get_model(qa_provider, qa_model, groq_api_key)
            response_model = get_model(response_provider, response_model, groq_api_key)

        # Templates e chains
        question_prompt = ChatPromptTemplate.from_template(
            """You are an AI system that constructs question-answer pairs based on a given context.\n\n"""
            "Your response should always have:\n"
            "Question: [Insert question here]\n"
            "Answer: [Insert answer here]\n\n"
            "Context:\n{context}"
        )
        question_chain = question_prompt | question_answer_model | StrOutputParser()
        
        response_prompt = ChatPromptTemplate.from_template(
            """Use the following context and question to answer:\n\n"""
            "Question: {question}\n"
            "Context: {context}"
        )
        answer_chain = response_prompt | response_model | StrOutputParser()

        # Geração dos dados
        data = []
        total_chunks = len(chunks)
        
        for idx, chunk in enumerate(chunks):
            status_text.text(f"Processando chunk {idx + 1} de {total_chunks}")
            progress_bar.progress((idx + 1) / total_chunks)
            
            question_answer = question_chain.invoke({"context": chunk}).split('\n')
            if len(question_answer) >= 2:
                question = question_answer[0].split(':', 1)[-1].strip()
                reference_answer = question_answer[1].split(':', 1)[-1].strip()
                response = answer_chain.invoke({"context": chunk, "question": question})
                data.append({
                    "Context": chunk,
                    "Question": question,
                    "Reference Answer": reference_answer,
                    "Response": response
                })

        status_text.empty()
        progress_bar.empty()
        
        return pd.DataFrame(data), total_chunks

    except Exception as e:
        st.error(f"❌ Erro durante o processamento: {str(e)}")
        return None, 0

def main():
    # Sidebar - apenas configurações
    with st.sidebar:
        st.title("⚙️ Configurações")
        st.markdown("---")
        
        # Tipos de arquivo suportados
        st.subheader("📄 Tipos de Arquivo Suportados")
        for file_type, extensions in SUPPORTED_FILE_TYPES.items():
            st.text(f"{file_type}: {', '.join(extensions)}")
        
        st.markdown("---")
        
        # Configuração dos modelos
        st.subheader("🔧 Modelos")
        
        qa_provider = st.selectbox(
            "Provedor (Perguntas)",
            options=list(AVAILABLE_MODELS.keys()),
            key="qa_provider"
        )
        qa_model = st.selectbox(
            "Modelo (Perguntas)",
            options=list(AVAILABLE_MODELS[qa_provider].keys()),
            key="qa_model"
        )
        
        response_provider = st.selectbox(
            "Provedor (Respostas)",
            options=list(AVAILABLE_MODELS.keys()),
            key="response_provider"
        )
        response_model = st.selectbox(
            "Modelo (Respostas)",
            options=list(AVAILABLE_MODELS[response_provider].keys()),
            key="response_model"
        )
        
        if qa_provider == 'Groq (API)' or response_provider == 'Groq (API)':
            groq_api_key = st.text_input(
                "Groq API Key",
                type="password",
                help="Necessário para usar modelos Groq"
            )
        else:
            groq_api_key = None

    # Main content
    st.title("🤖 Synthetic Data Generator")
    
    # Botões de ação principais
    col1, col2 = st.columns([1, 5])
    with col1:
        if st.button('🔄 Limpar', help='Limpa todos os dados e reinicia o aplicativo'):
            clear_session_state()
            st.rerun()
    
    st.markdown("---")
    
    # Seção de upload e processamento
    if not st.session_state.processing_complete:
        st.markdown("### 📁 Upload de Arquivo")
        st.markdown("Faça upload de um arquivo PDF, DOCX ou TXT para gerar dados sintéticos.")
        
        upload_col, info_col = st.columns([2, 1])
        with upload_col:
            file = st.file_uploader(
                "Escolha um arquivo",
                type=[ext for exts in SUPPORTED_FILE_TYPES.values() for ext in exts],
                help="Arraste e solte ou clique para selecionar um arquivo PDF, DOCX ou TXT",
                label_visibility="collapsed"
            )
            
        with info_col:
            st.markdown("""
            **Formatos aceitos:**
            - PDF (*.pdf)
            - Word (*.doc, *.docx)
            - Texto (*.txt)
            """)
        
        if file:
            # Validar tipo de arquivo
            file_extension = os.path.splitext(file.name)[1].lower()
            supported_extensions = [ext for exts in SUPPORTED_FILE_TYPES.values() for ext in exts]
            
            if file_extension in supported_extensions:
                st.success(f"✅ Arquivo carregado: {file.name}")
                if st.button('▶️ Iniciar Processamento', key='process_button'):
                    st.session_state.processed_data, total_chunks = process_data(
                        file, qa_provider, qa_model, 
                        response_provider, response_model, 
                        groq_api_key
                    )
                    if st.session_state.processed_data is not None:
                        st.session_state.processing_complete = True
                        st.session_state.total_chunks = total_chunks
                        st.rerun()
            else:
                st.error("❌ Tipo de arquivo não suportado")
    
    # Resultados após processamento
    if st.session_state.processing_complete and st.session_state.processed_data is not None:
        st.markdown("### 📊 Resultados")
        
        # Informações do processamento
        st.markdown(f"""
        **Resumo do processamento:**
        - Chunks processados: {st.session_state.total_chunks}
        - Pares Q&A gerados: {len(st.session_state.processed_data)}
        - Modelos utilizados:
          - Perguntas: {qa_model} ({qa_provider})
          - Respostas: {response_model} ({response_provider})
        """)
        
        st.markdown("---")
        
        # Download dos resultados
        st.markdown("### 📥 Download dos Dados")
        st.download_button(
            label='📥 Baixar Dados Sintéticos (CSV)',
            data=st.session_state.processed_data.to_csv(index=False),
            file_name='Synthetic_Data.csv',
            mime='text/csv',
            help='Clique para baixar os dados gerados em formato CSV'
        )
        
        # Prévia dos dados
        st.markdown("### 👁️ Prévia dos Dados")
        st.dataframe(
            st.session_state.processed_data.head(),
            use_container_width=True
        )

    else:
        st.markdown("---")
        st.info('👆 Por favor, faça upload de um arquivo para começar.')
        
if __name__ == "__main__":
    main()