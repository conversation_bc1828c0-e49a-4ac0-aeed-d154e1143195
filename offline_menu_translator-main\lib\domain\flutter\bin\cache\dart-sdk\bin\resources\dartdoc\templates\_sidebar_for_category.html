<ol>
  {{ #self.hasPublicLibraries }}
    <li class="section-title"><a href="{{{ self.href }}}#libraries">Libraries</a></li>
    {{ #self.publicLibrariesSorted }}
      <li>{{{ linkedName }}}</li>
    {{ /self.publicLibrariesSorted }}
  {{ /self.hasPublicLibraries }}

  {{ #self.hasPublicClasses }}
    <li class="section-title"><a href="{{{ self.href }}}#classes">Classes</a></li>
    {{ #self.publicClassesSorted }}
      <li>{{{ linkedName }}}</li>
    {{ /self.publicClassesSorted }}
  {{ /self.hasPublicClasses }}

  {{ #self.hasPublicEnums }}
    <li class="section-title"><a href="{{{ self.href }}}#enums">Enums</a></li>
    {{ #self.publicEnumsSorted }}
      <li>{{{ linkedName }}}</li>
    {{ /self.publicEnumsSorted }}
  {{ /self.hasPublicEnums }}

  {{ #self.hasPublicMixins }}
    <li class="section-title"><a href="{{{ self.href }}}#mixins">Mixins</a></li>
    {{ #self.publicMixinsSorted }}
      <li>{{{ linkedName }}}</li>
    {{ /self.publicMixinsSorted }}
  {{ /self.hasPublicMixins }}

  {{ #self.hasPublicExtensionTypes }}
    <li class="section-title"><a href="{{{ self.href }}}#extension-types">Extension Types</a></li>
    {{ #self.publicExtensionTypesSorted }}
      <li>{{{ linkedName }}}</li>
    {{ /self.publicExtensionTypesSorted }}
  {{ /self.hasPublicExtensionTypes }}

  {{ #self.hasPublicConstants}}
    <li class="section-title"><a href="{{{ self.href }}}#constants">Constants</a></li>
    {{ #self.publicConstantsSorted }}
      <li>{{{ linkedName }}}</li>
    {{ /self.publicConstantsSorted }}
  {{ /self.hasPublicConstants }}

  {{ #self.hasPublicProperties }}
    <li class="section-title"><a href="{{{ self.href }}}#properties">Properties</a></li>
    {{ #self.publicPropertiesSorted }}
      <li>{{{ linkedName }}}</li>
    {{ /self.publicPropertiesSorted }}
  {{ /self.hasPublicProperties }}

  {{ #self.hasPublicFunctions }}
    <li class="section-title"><a href="{{{ self.href }}}#functions">Functions</a></li>
    {{ #self.publicFunctionsSorted }}
      <li>{{{ linkedName }}}</li>
    {{ /self.publicFunctionsSorted }}
  {{ /self.hasPublicFunctions }}

  {{ #self.hasPublicTypedefs }}
  <li class="section-title"><a href="{{{ self.href }}}#typedefs">Typedefs</a></li>
  {{ #self.publicTypedefsSorted }}
  <li>{{{ linkedName }}}</li>
  {{ /self.publicTypedefsSorted }}
  {{ /self.hasPublicTypedefs }}

  {{ #self.hasPublicExceptions }}
  <li class="section-title"><a href="{{{ self.href }}}#exceptions">Exceptions</a></li>
  {{ #self.publicExceptionsSorted }}
  <li>{{{ linkedName }}}</li>
  {{ /self.publicExceptionsSorted }}
  {{ /self.hasPublicExceptions }}

  {{ #self.hasPublicExtensions }}
  <li class="section-title"><a href="{{{ self.href }}}#extensions">Extensions</a></li>
  {{ #self.publicExtensionsSorted }}
  <li>{{{ linkedName }}}</li>
  {{ /self.publicExtensionsSorted }}
  {{ /self.hasPublicExtensions }}
</ol>
