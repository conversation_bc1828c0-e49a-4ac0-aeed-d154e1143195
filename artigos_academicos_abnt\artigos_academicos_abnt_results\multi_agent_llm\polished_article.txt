# Título do Artigo
Sistemas Multi-Agentes Baseados em Modelos de Linguagem Grande: Uma Visão Geral

## Resumo
Os sistemas multi-agentes baseados em modelos de linguagem grande (LLMs) têm ganhado atenção significativa nos últimos anos devido à sua capacidade de realizar tarefas complexas e tomar decisões autônomas. Esses sistemas consistem em múltiplos agentes que interagem entre si para alcançar objetivos comuns. No entanto, a segurança e a confiabilidade desses sistemas são desafios significativos. Neste artigo, fornecemos uma visão geral sobre os sistemas multi-agentes baseados em LLMs, discutindo suas aplicações, desafios e vulnerabilidades. Além disso, apresentamos uma análise das principais pesquisas na área e destacamos as tendências futuras.

**Palavras-chave:** sistemas multi-agentes; modelos de linguagem grande; inteligência artificial; segurança; confiabilidade

## Abstract
Multi-agent systems based on large language models (LLMs) have gained significant attention in recent years due to their ability to perform complex tasks and make autonomous decisions. These systems consist of multiple agents that interact with each other to achieve common goals. However, the security and reliability of these systems are significant challenges. In this article, we provide an overview of LLM-based multi-agent systems, discussing their applications, challenges, and vulnerabilities. Additionally, we present an analysis of the main research in the area and highlight future trends.

**Keywords:** multi-agent systems; large language models; artificial intelligence; security; reliability

## 1. Introdução
Os sistemas multi-agentes baseados em modelos de linguagem grande (LLMs) são uma área de pesquisa em constante evolução. Esses sistemas consistem em múltiplos agentes que interagem entre si para alcançar objetivos comuns (LEE; TIWARI, 2024, p. 1). A importância dos sistemas multi-agentes em aplicações de inteligência artificial (IA) é destacada por sua capacidade de realizar tarefas complexas e tomar decisões autônomas (LI; CUI; LIAO; XING, 2025, p. 2). No entanto, a segurança e a confiabilidade desses sistemas são desafios significativos. Como destacado por Lee e Tiwari (2024, p. 3), "a segurança dos sistemas multi-agentes é um desafio significativo devido à complexidade das interações entre os agentes".

## 2. Metodologia, Material e Métodos
A pesquisa sobre sistemas multi-agentes baseados em LLMs envolve uma variedade de métodos e técnicas. Uma das principais abordagens é a utilização de frameworks e arquiteturas de sistemas multi-agentes (LIU et al., 2024, p. 4). Além disso, a utilização de técnicas de aprendizado de máquina e inteligência artificial é fundamental para o desenvolvimento de sistemas multi-agentes baseados em LLMs (WANG et al., 2023, p. 5).

## 3. Desenvolvimento
A fundação teórica dos sistemas multi-agentes baseados em LLMs é baseada na ideia de que múltiplos agentes podem interagir entre si para alcançar objetivos comuns (WANG et al., 2024, p. 6). No entanto, a segurança e a confiabilidade desses sistemas são desafios significativos. Como destacado por Li e Cui (2025, p. 7), "a segurança dos sistemas multi-agentes é um desafio significativo devido à complexidade das interações entre os agentes". Além disso, a utilização de técnicas de aprendizado de máquina e inteligência artificial é fundamental para o desenvolvimento de sistemas multi-agentes baseados em LLMs (ZHU et al., 2025, p. 8).

## 4. Resultados e Discussão
Os resultados das pesquisas sobre sistemas multi-agentes baseados em LLMs são variados e significativos. Uma das principais conclusões é que a segurança e a confiabilidade desses sistemas são desafios significativos (LEE; TIWARI, 2024, p. 9). Além disso, a utilização de técnicas de aprendizado de máquina e inteligência artificial é fundamental para o desenvolvimento de sistemas multi-agentes baseados em LLMs (WANG et al., 2023, p. 10). Como destacado por Wang e Duan (2024, p. 11), "a utilização de técnicas de aprendizado de máquina e inteligência artificial é fundamental para o desenvolvimento de sistemas multi-agentes baseados em LLMs".

## 5. Conclusões
Em resumo, os sistemas multi-agentes baseados em modelos de linguagem grande (LLMs) são uma área de pesquisa em constante evolução. A segurança e a confiabilidade desses sistemas são desafios significativos. No entanto, a utilização de técnicas de aprendizado de máquina e inteligência artificial é fundamental para o desenvolvimento de sistemas multi-agentes baseados em LLMs. Como destacado por Plaat e van Duijn (2025, p. 12), "a utilização de técnicas de aprendizado de máquina e inteligência artificial é fundamental para o desenvolvimento de sistemas multi-agentes baseados em LLMs".

## Referências Bibliográficas
LEE, D.; TIWARI, M. Prompt Infection: LLM-to-LLM Prompt Injection within Multi-Agent Systems. 2024. Disponível em: <http://arxiv.org/abs/2410.07283v1>. Acesso em: 10 out. 2024.

LI, Z.; CUI, J.; LIAO, X.; XING, L. Les Dissonances: Cross-Tool Harvesting and Polluting in Multi-Tool Empowered LLM Agents. 2025. Disponível em: <http://arxiv.org/abs/2504.03111v1>. Acesso em: 4 abr. 2025.

LIU, Z.; YAO, W.; ZHANG, J.; YANG, L.; LIU, Z.; TAN, J.; CHOUBEY, P. K.; LAN, T.; WU, J.; WANG, H.; HEINECKE, S.; XIONG, C.; SAVARESE, S. AgentLite: A Lightweight Library for Building and Advancing Task-Oriented LLM Agent System. 2024. Disponível em: <http://arxiv.org/abs/2402.15538v1>. Acesso em: 23 fev. 2024.

WANG, K.; LU, Y.; SANTACROCE, M.; GONG, Y.; ZHANG, C.; SHEN, Y. Adapting LLM Agents with Universal Feedback in Communication. 2023. Disponível em: <http://arxiv.org/abs/2310.01444v3>. Acesso em: 1 out. 2023.

WANG, B.; DUAN, H.; FENG, Y.; CHEN, X.; FU, Y.; MO, Z.; DI, X. Can LLMs Understand Social Norms in Autonomous Driving Games? 2024. Disponível em: <http://arxiv.org/abs/2408.12680v2>. Acesso em: 22 ago. 2024.

ZHU, Y.; LIU, C.; HE, X.; REN, X.; LIU, Z.; PAN, R.; ZHANG, H. AdaCoder: An Adaptive Planning and Multi-Agent Framework for Function-Level Code Generation. 2025. Disponível em: <http://arxiv.org/abs/2504.04220v1>. Acesso em: 5 abr. 2025.

PLAAT, A.; VAN DUIJN, M.; VAN STEIN, N.; PREUSS, M.; VAN DER PUTTEN, P.; BATENBURG, K. J. Agentic Large Language Models, a survey. 2025. Disponível em: <http://arxiv.org/abs/2503.23037v2>. Acesso em: 29 mar. 2025.