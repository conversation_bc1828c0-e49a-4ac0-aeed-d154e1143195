"""
Script de teste para verificar se o Streamlit está funcionando corretamente.
"""

import streamlit as st

def main():
    """Função principal para testar o Streamlit"""
    st.title("Teste do Streamlit")
    
    # Testar sidebar
    with st.sidebar:
        st.header("Sidebar")
        st.write("Este é um teste do sidebar")
        
        # Testar expander no sidebar
        with st.expander("Expander no sidebar"):
            st.write("Conteúdo do expander no sidebar")
    
    # Testar expander na área principal
    with st.expander("Expander na área principal"):
        st.write("Conteúdo do expander na área principal")
    
    # Testar colunas
    col1, col2 = st.columns(2)
    
    with col1:
        st.header("Coluna 1")
        st.write("Conteúdo da coluna 1")
    
    with col2:
        st.header("Coluna 2")
        st.write("Conteúdo da coluna 2")
    
    # Testar formulário
    with st.form("test_form"):
        st.text_input("Campo de texto")
        st.form_submit_button("Enviar")

if __name__ == "__main__":
    main()
