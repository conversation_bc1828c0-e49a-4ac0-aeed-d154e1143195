{"_comment": ["The builds defined in this file should not contain tests, ", "and the file should not contain builds that are essentially tests. ", "The only builds in this file should be the builds necessary to produce ", "release artifacts. ", "Tests to run on linux hosts should go in one of the other linux_ build ", "definition files."], "luci_flags": {"delay_collect_builds": true, "parallel_download_builds": true}, "builds": [{"archives": [{"name": "ci/android_profile", "type": "gcs", "base_path": "out/ci/android_profile/zip_archives/", "include_paths": ["out/ci/android_profile/zip_archives/android-arm-profile/artifacts.zip", "out/ci/android_profile/zip_archives/android-arm-profile/linux-x64.zip", "out/ci/android_profile/zip_archives/android-arm-profile/symbols.zip", "out/ci/android_profile/zip_archives/download.flutter.io"], "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Linux"], "gclient_variables": {"use_rbe": true}, "gn": ["--target-dir", "ci/android_profile", "--runtime-mode", "profile", "--android", "--android-cpu", "arm", "--rbe", "--no-goma"], "name": "ci/android_profile", "description": "Produces profile mode artifacts to target 32-bit arm Android from a Linux host.", "ninja": {"config": "ci/android_profile", "targets": ["default", "clang_x64/gen_snapshot", "flutter/shell/platform/android:embedding_jars", "flutter/shell/platform/android:abi_jars"]}}, {"archives": [{"name": "ci/android_release", "type": "gcs", "base_path": "out/ci/android_release/zip_archives/", "include_paths": ["out/ci/android_release/zip_archives/android-arm-release/artifacts.zip", "out/ci/android_release/zip_archives/android-arm-release/linux-x64.zip", "out/ci/android_release/zip_archives/android-arm-release/symbols.zip", "out/ci/android_release/zip_archives/download.flutter.io"], "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Linux"], "gclient_variables": {"use_rbe": true}, "gn": ["--target-dir", "ci/android_release", "--runtime-mode", "release", "--android", "--android-cpu", "arm", "--rbe", "--no-goma"], "name": "ci/android_release", "description": "Produces release mode artifacts to target 32-bit arm Android from a Linux host.", "ninja": {"config": "ci/android_release", "targets": ["default", "clang_x64/gen_snapshot", "flutter/shell/platform/android:embedding_jars", "flutter/shell/platform/android:abi_jars"]}}, {"archives": [{"name": "ci/android_release_arm64", "type": "gcs", "base_path": "out/ci/android_release_arm64/zip_archives/", "include_paths": ["out/ci/android_release_arm64/zip_archives/android-arm64-release/artifacts.zip", "out/ci/android_release_arm64/zip_archives/android-arm64-release/linux-x64.zip", "out/ci/android_release_arm64/zip_archives/android-arm64-release/symbols.zip", "out/ci/android_release_arm64/zip_archives/android-arm64-release/analyze-snapshot-linux-x64.zip", "out/ci/android_release_arm64/zip_archives/download.flutter.io"], "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Linux"], "gclient_variables": {"use_rbe": true}, "gn": ["--target-dir", "ci/android_release_arm64", "--runtime-mode", "release", "--android", "--android-cpu", "arm64", "--rbe", "--no-goma"], "name": "ci/android_release_arm64", "description": "Produces release mode artifacts to target 64-bit arm Android from a Linux host.", "ninja": {"config": "ci/android_release_arm64", "targets": ["default", "clang_x64/gen_snapshot", "flutter/shell/platform/android:abi_jars", "flutter/shell/platform/android:analyze_snapshot"]}, "tests": [{"name": "Generate treemap for android_release_arm64", "language": "bash", "script": "flutter/ci/binary_size_treemap.sh", "parameters": ["../../src/out/ci/android_release_arm64/libflutter.so", "${FLUTTER_LOGS_DIR}"]}]}, {"archives": [{"name": "ci/android_profile_arm64", "type": "gcs", "base_path": "out/ci/android_profile_arm64/zip_archives/", "include_paths": ["out/ci/android_profile_arm64/zip_archives/android-arm64-profile/artifacts.zip", "out/ci/android_profile_arm64/zip_archives/android-arm64-profile/linux-x64.zip", "out/ci/android_profile_arm64/zip_archives/android-arm64-profile/symbols.zip", "out/ci/android_profile_arm64/zip_archives/android-arm64-profile/analyze-snapshot-linux-x64.zip", "out/ci/android_profile_arm64/zip_archives/download.flutter.io"], "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Linux"], "gclient_variables": {"use_rbe": true}, "gn": ["--target-dir", "ci/android_profile_arm64", "--android", "--runtime-mode", "profile", "--android-cpu", "arm64", "--rbe", "--no-goma"], "name": "ci/android_profile_arm64", "description": "Produces profile mode artifacts to target 64-bit arm Android from a Linux host.", "ninja": {"config": "ci/android_profile_arm64", "targets": ["clang_x64/gen_snapshot", "default", "flutter/shell/platform/android:abi_jars", "flutter/shell/platform/android:analyze_snapshot"]}}, {"archives": [{"name": "ci/android_profile_x64", "type": "gcs", "base_path": "out/ci/android_profile_x64/zip_archives/", "include_paths": ["out/ci/android_profile_x64/zip_archives/android-x64-profile/artifacts.zip", "out/ci/android_profile_x64/zip_archives/android-x64-profile/linux-x64.zip", "out/ci/android_profile_x64/zip_archives/android-x64-profile/symbols.zip", "out/ci/android_profile_x64/zip_archives/android-x64-profile/analyze-snapshot-linux-x64.zip", "out/ci/android_profile_x64/zip_archives/download.flutter.io"], "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Linux"], "gclient_variables": {"use_rbe": true}, "gn": ["--target-dir", "ci/android_profile_x64", "--runtime-mode", "profile", "--android", "--android-cpu", "x64", "--rbe", "--no-goma"], "name": "ci/android_profile_x64", "description": "Produces profile mode artifacts to target x64 Android from a Linux host.", "ninja": {"config": "ci/android_profile_x64", "targets": ["default", "clang_x64/gen_snapshot", "flutter/shell/platform/android:abi_jars", "flutter/shell/platform/android:analyze_snapshot"]}}, {"archives": [{"name": "ci/android_release_x64", "type": "gcs", "base_path": "out/ci/android_release_x64/zip_archives/", "include_paths": ["out/ci/android_release_x64/zip_archives/android-x64-release/artifacts.zip", "out/ci/android_release_x64/zip_archives/android-x64-release/linux-x64.zip", "out/ci/android_release_x64/zip_archives/android-x64-release/symbols.zip", "out/ci/android_release_x64/zip_archives/android-x64-release/analyze-snapshot-linux-x64.zip", "out/ci/android_release_x64/zip_archives/download.flutter.io"], "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Linux"], "gclient_variables": {"use_rbe": true}, "gn": ["--target-dir", "ci/android_release_x64", "--runtime-mode", "release", "--android", "--android-cpu", "x64", "--rbe", "--no-goma"], "name": "ci/android_release_x64", "description": "Produces release mode artifacts to target x64 Android from a Linux host.", "ninja": {"config": "ci/android_release_x64", "targets": ["default", "clang_x64/gen_snapshot", "flutter/shell/platform/android:abi_jars", "flutter/shell/platform/android:analyze_snapshot"]}}], "generators": {"tasks": [{"name": "Verify-export-symbols-release-binaries", "parameters": ["src/out/ci", "src/flutter/buildtools"], "script": "flutter/testing/symbols/verify_exported.dart", "language": "dart"}]}}