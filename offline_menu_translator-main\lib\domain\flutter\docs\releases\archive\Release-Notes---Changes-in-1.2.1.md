The following issues were addressed between 1.0.0 and 1.2.1:

## In the framework and engine (this also includes Dart and Skia rolls, which are not broken out here.)

| Label | Number of Issues & Issue Links |
| --- | --- |
| framework | 87 issues with this label|
|   | [(flutter/flutter#25788) Add <PERSON>’s easing functions](https://github.com/flutter/flutter/pull/25788) |
|   | [(flutter/flutter#25984) use RRect to draw avatar check on chip](https://github.com/flutter/flutter/pull/25984) |
|   | [(flutter/flutter#23424) Teach drag start behaviors to DragGestureRecognizer](https://github.com/flutter/flutter/pull/23424) |
|   | [(flutter/flutter#26209) Revert "Teach drag start behaviors to DragGestureRecognizer (#23424)"](https://github.com/flutter/flutter/pull/26209) |
|   | [(flutter/flutter#26199) Fix tristate checkbox false to null transition, test ALL transitions](https://github.com/flutter/flutter/pull/26199) |
|   | [(flutter/flutter#25790) Clarify doc for AnimatedContainer](https://github.com/flutter/flutter/pull/25790) |
|   | [(flutter/flutter#23860) Clearing pendingImages when the cache is cleared or evicted.](https://github.com/flutter/flutter/pull/23860) |
|   | [(flutter/flutter#26021) Fix SliverAppBar title opacity and test all cases](https://github.com/flutter/flutter/pull/26021) |
|   | [(flutter/flutter#26101) Fix a floating snapping SliverAppBar crash](https://github.com/flutter/flutter/pull/26101) |
|   | [(flutter/flutter#24779) Skip formatters if text has not changed](https://github.com/flutter/flutter/pull/24779) |
|   | [(flutter/flutter#26089) Explain that BoxDecoration doesn't do clip.](https://github.com/flutter/flutter/pull/26089) |
|   | [(flutter/flutter#26088) Fix typos introduced with the TextField.onTap PR, udpated debugFillProperties](https://github.com/flutter/flutter/pull/26088) |
|   | [(flutter/flutter#26042) Update material spec references in BottomSheet et al., Scaffold](https://github.com/flutter/flutter/pull/26042) |
|   | [(flutter/flutter#25994) Simplify ImageStream(Completer).removeListener](https://github.com/flutter/flutter/pull/25994) |
|   | [(flutter/flutter#25980) Ensure all errors thrown by image providers can be caught by developers.](https://github.com/flutter/flutter/pull/25980) |
|   | [(flutter/flutter#25977) Add didSendFirstFrameEvent service extension.](https://github.com/flutter/flutter/pull/25977) |
|   | [(flutter/flutter#25992) Add docs to TextStyle for fontFamilyFallback/Custom font fallback](https://github.com/flutter/flutter/pull/25992) |
|   | [(flutter/flutter#25159) fix #25143 Successive calls to `precacheImage()` throw an exception](https://github.com/flutter/flutter/pull/25159) |
|   | [(flutter/flutter#23118) Setting icon color to first ListTile in ExpansionTile. Fixes #23053](https://github.com/flutter/flutter/pull/23118) |
|   | [(flutter/flutter#25799) Make LicensePage respect the notch](https://github.com/flutter/flutter/pull/25799) |
|   | [(flutter/flutter#7318) ListItem should call out that it's intentionally fixed height](https://github.com/flutter/flutter/issues/7318) |
|   | [(flutter/flutter#25792) Actively reject UiKitView gestures.](https://github.com/flutter/flutter/pull/25792) |
|   | [(flutter/flutter#25055) Include cursor in textfield intrinsic width measurement](https://github.com/flutter/flutter/pull/25055) |
|   | [(flutter/flutter#24736) [H] Provide some more locations for the FAB.](https://github.com/flutter/flutter/pull/24736) |
|   | [(flutter/flutter#24511) [H] Undeprecate BigInteger support, but document what it actually does.](https://github.com/flutter/flutter/pull/24511) |
|   | [(flutter/flutter#24816) [H] ClipPath.shape and related fixes](https://github.com/flutter/flutter/pull/24816) |
|   | [(flutter/flutter#24848) [H] Handle errors in `compute()` by propagating them to the Future.](https://github.com/flutter/flutter/pull/24848) |
|   | [(flutter/flutter#25718) Fix merge conflict.](https://github.com/flutter/flutter/pull/25718) |
|   | [(flutter/flutter#24643) [H] Some minor tweaks to InputDecoration (mainly docs).](https://github.com/flutter/flutter/pull/24643) |
|   | [(flutter/flutter#25585) Expose font fallback API in TextStyle, Roll engine 54a3577c0139..215ca1560088 (8 commits)](https://github.com/flutter/flutter/pull/25585) |
|   | [(flutter/flutter#25604) no period after an alone reference in see also section](https://github.com/flutter/flutter/pull/25604) |
|   | [(flutter/flutter#24457) Revise Android and iOS gestures on Material TextField](https://github.com/flutter/flutter/pull/24457) |
|   | [(flutter/flutter#25594) Switch over to the new name for compilation trace native function](https://github.com/flutter/flutter/pull/25594) |
|   | [(flutter/flutter#25584) Fix material reference in CupertinoPicker doc](https://github.com/flutter/flutter/pull/25584) |
|   | [(flutter/flutter#25593) Let CupertinoTabScaffold handle keyboard insets too](https://github.com/flutter/flutter/pull/25593) |
|   | [(flutter/flutter#25229) Right aligned backspace bug](https://github.com/flutter/flutter/pull/25229) |
|   | [(flutter/flutter#25573) Update DayPicker,DatePicker doc "see also" sections](https://github.com/flutter/flutter/pull/25573) |
|   | [(flutter/flutter#24554) Adds force press gesture detector and recognizer](https://github.com/flutter/flutter/pull/24554) |
|   | [(flutter/flutter#25574) Use full textspan tree instead of top level textspan](https://github.com/flutter/flutter/pull/25574) |
|   | [(flutter/flutter#25339) [Material] Theme-able TextStyles for AlertDialog](https://github.com/flutter/flutter/pull/25339) |
|   | [(flutter/flutter#23759) Adds CupertinoTheme](https://github.com/flutter/flutter/pull/23759) |
|   | [(flutter/flutter#25477) TransitionRoute.canTransitionFrom,To() doc update](https://github.com/flutter/flutter/pull/25477) |
|   | [(flutter/flutter#25237) Fix typo](https://github.com/flutter/flutter/pull/25237) |
|   | [(flutter/flutter#24797) Iterate through potential grapheme cluster lengths in text painter](https://github.com/flutter/flutter/pull/24797) |
|   | [(flutter/flutter#25345) assert(elevation >= 0.0) and doc clarifications](https://github.com/flutter/flutter/pull/25345) |
|   | [(flutter/flutter#24993) Add InputDecoration alignLabelWithHint parameter](https://github.com/flutter/flutter/pull/24993) |
|   | [(flutter/flutter#25473) TextField.onChanged() doc update](https://github.com/flutter/flutter/pull/25473) |
|   | [(flutter/flutter#25474) fix some formatting issues](https://github.com/flutter/flutter/pull/25474) |
|   | [(flutter/flutter#25381) Add cull opacity perf test to device lab](https://github.com/flutter/flutter/pull/25381) |
|   | [(flutter/flutter#25342) Revert "Revert "obscureText and enableInteractiveSelection defaults""](https://github.com/flutter/flutter/pull/25342) |
|   | [(flutter/flutter#23919) Allow detection of taps on TabBar](https://github.com/flutter/flutter/pull/23919) |
|   | [(flutter/flutter#24527) obscureText and enableInteractiveSelection defaults](https://github.com/flutter/flutter/pull/24527) |
|   | [(flutter/flutter#25335) Revert "obscureText and enableInteractiveSelection defaults"](https://github.com/flutter/flutter/pull/25335) |
|   | [(flutter/flutter#25394) Update localizations](https://github.com/flutter/flutter/pull/25394) |
|   | [(flutter/flutter#25239) Call mark* methods before attaching child](https://github.com/flutter/flutter/pull/25239) |
|   | [(flutter/flutter#25395) Reland "Call mark* methods before attaching child (#25239)"](https://github.com/flutter/flutter/pull/25395) |
|   | [(flutter/flutter#25301) Flutter tool support for automatic saving of JIT compilation trace](https://github.com/flutter/flutter/pull/25301) |
|   | [(flutter/flutter#24932) Fixed Typography null factory constructor](https://github.com/flutter/flutter/pull/24932) |
|   | [(flutter/flutter#25384) Adds support for floating cursor](https://github.com/flutter/flutter/pull/25384) |
|   | [(flutter/flutter#24999) Remove TextField.noMaxLength, use maxLength = -1 instead](https://github.com/flutter/flutter/pull/24999) |
|   | [(flutter/flutter#25390) Revert "drop/restore focus when app becomes invisible/visible"](https://github.com/flutter/flutter/pull/25390) |
|   | [(flutter/flutter#25228) IntrinsicWidth stepWidth or stepHeight == 0.0](https://github.com/flutter/flutter/pull/25228) |
|   | [(flutter/flutter#24744) drop/restore focus when app becomes invisible/visible](https://github.com/flutter/flutter/pull/24744) |
|   | [(flutter/flutter#25382) Revert "Call mark* methods before attaching child (#25239)"](https://github.com/flutter/flutter/pull/25382) |
|   | [(flutter/flutter#24761) Adds support for floating cursor](https://github.com/flutter/flutter/pull/24761) |
|   | [(flutter/flutter#25352) Revert "Adds support for floating cursor"](https://github.com/flutter/flutter/pull/25352) |
|   | [(flutter/flutter#24449) Text field style merge](https://github.com/flutter/flutter/pull/24449) |
|   | [(flutter/flutter#24169) [Material] Theme-able elevation on dialogs.](https://github.com/flutter/flutter/pull/24169) |
|   | [(flutter/flutter#24587) Validate style in TextField](https://github.com/flutter/flutter/pull/24587) |
|   | [(flutter/flutter#24930) Run flutter tests through mini test engine when run directly (flutter run -t test_file)](https://github.com/flutter/flutter/pull/24930) |
|   | [(flutter/flutter#24976) Support TextField multi-line hint text #20941](https://github.com/flutter/flutter/pull/24976) |
|   | [(flutter/flutter#25183) Add navigatorKey to CupertinoTabView](https://github.com/flutter/flutter/pull/25183) |
|   | [(flutter/flutter#25051) Do not fade out text for pinned & floating AppBar](https://github.com/flutter/flutter/pull/25051) |
|   | [(flutter/flutter#24892) Handle a TabBarView special case: last tab deleted before animation ends](https://github.com/flutter/flutter/pull/24892) |
|   | [(flutter/flutter#25217) Fixed Spelling.](https://github.com/flutter/flutter/pull/25217) |
|   | [(flutter/flutter#24635) TextFormField cursor params](https://github.com/flutter/flutter/pull/24635) |
|   | [(flutter/flutter#25168) Fixed an InheritedWidget code sample typo](https://github.com/flutter/flutter/pull/25168) |
|   | [(flutter/flutter#25046) Add a check for build methods that return context.widget](https://github.com/flutter/flutter/pull/25046) |
|   | [(flutter/flutter#25048) Don't crash if pinned & floating AppBar has less than minExtent remainingPaintExtent](https://github.com/flutter/flutter/pull/25048) |
|   | [(flutter/flutter#24942) Fix debugPrint(null) to not crash](https://github.com/flutter/flutter/pull/24942) |
|   | [(flutter/flutter#24881) Remove offstage wording from KeepAlive](https://github.com/flutter/flutter/pull/24881) |
|   | [(flutter/flutter#24941) Update Switch doc: disabled state](https://github.com/flutter/flutter/pull/24941) |
|   | [(flutter/flutter#25120) Replace deprecated link (to design-principles page) in comment](https://github.com/flutter/flutter/pull/25120) |
|   | [(flutter/flutter#25096) Change network image URL in doc](https://github.com/flutter/flutter/pull/25096) |
|   | [(flutter/flutter#24862) Fix semantics compiler for offstage children](https://github.com/flutter/flutter/pull/24862) |
|   | [(flutter/flutter#25091) Add animations to SliverAppBar doc](https://github.com/flutter/flutter/pull/25091) |
|   | [(flutter/flutter#25003) Fix typo in documentation](https://github.com/flutter/flutter/pull/25003) |
| tool | 49 issues with this label |
|   | [(flutter/flutter#26249) Revert "Replace netls and netaddr with dev_finder"](https://github.com/flutter/flutter/pull/26249) |
|   | [(flutter/flutter#26090) Replace netls and netaddr with dev_finder](https://github.com/flutter/flutter/pull/26090) |
|   | [(flutter/flutter#26235) rev the min dart sdk dep in the templates to 2.1.0](https://github.com/flutter/flutter/pull/26235) |
|   | [(flutter/flutter#26201) Prevent calls to view.uiIsolate.flutterExit on devices which do not support it](https://github.com/flutter/flutter/pull/26201) |
|   | [(flutter/flutter#26107) Better error messages for flutter tool --dynamic flag.](https://github.com/flutter/flutter/pull/26107) |
|   | [(flutter/flutter#26104) Put correct VM snapshot in APK when using cached engine](https://github.com/flutter/flutter/pull/26104) |
|   | [(flutter/flutter#26039) Report hot reload statistics.](https://github.com/flutter/flutter/pull/26039) |
|   | [(flutter/flutter#26084) Improve message when saving compilation training data](https://github.com/flutter/flutter/pull/26084) |
|   | [(flutter/flutter#25863) Friendlier messages when using dynamic patching](https://github.com/flutter/flutter/pull/25863) |
|   | [(flutter/flutter#25872) Optimize cocoapods logic in flutter doctor.](https://github.com/flutter/flutter/pull/25872) |
|   | [(flutter/flutter#7314) Flutter crash on startup (metabug)](https://github.com/flutter/flutter/issues/7314) |
|   | [(flutter/flutter#7313) Will want some way to validate cache files](https://github.com/flutter/flutter/issues/7313) |
|   | [(flutter/flutter#7310) Don't time out during debugging](https://github.com/flutter/flutter/issues/7310) |
|   | [(flutter/flutter#25796) Allow dynamic patches without a patch number.](https://github.com/flutter/flutter/pull/25796) |
|   | [(flutter/flutter#25586) Report devfs stats](https://github.com/flutter/flutter/pull/25586) |
|   | [(flutter/flutter#25645) Friendlier flags for Dart compilation training.](https://github.com/flutter/flutter/pull/25645) |
|   | [(flutter/flutter#25479) Depend on the goldens repo through git.](https://github.com/flutter/flutter/pull/25479) |
|   | [(flutter/flutter#25238) [OR] Update links for China help](https://github.com/flutter/flutter/pull/25238) |
|   | [(flutter/flutter#24440) Adding support for android app bundle - Issue #17829](https://github.com/flutter/flutter/pull/24440) |
|   | [(flutter/flutter#23531) [O] Remove many timeouts.](https://github.com/flutter/flutter/pull/23531) |
|   | [(flutter/flutter#25646) Revert "[O] Remove many timeouts."](https://github.com/flutter/flutter/pull/25646) |
|   | [(flutter/flutter#25631) Default baseline build options](https://github.com/flutter/flutter/pull/25631) |
|   | [(flutter/flutter#25595) Don't parse APK unless explicitly requested](https://github.com/flutter/flutter/pull/25595) |
|   | [(flutter/flutter#25576) Flutter tool support for building dynamic patches on Android](https://github.com/flutter/flutter/pull/25576) |
|   | [(flutter/flutter#24580) Remove code signing special casing for Googlers round 2](https://github.com/flutter/flutter/pull/24580) |
|   | [(flutter/flutter#23889) Flutter doctor error message lookup](https://github.com/flutter/flutter/pull/23889) |
|   | [(flutter/flutter#25520) Fix flutter tool to actually honor --build-number/--build-name flags](https://github.com/flutter/flutter/pull/25520) |
|   | [(flutter/flutter#25472) Read correct cached VM snapshot in dynamic mode (PRODUCT vs RELEASE)](https://github.com/flutter/flutter/pull/25472) |
|   | [(flutter/flutter#25484) Fix gradle local.properties tests that were never excersized](https://github.com/flutter/flutter/pull/25484) |
|   | [(flutter/flutter#25512) Fix failed assert when running `flutter test` with `--start-paused`](https://github.com/flutter/flutter/pull/25512) |
|   | [(flutter/flutter#25440) don't warn for non-matching device discoverers](https://github.com/flutter/flutter/pull/25440) |
|   | [(flutter/flutter#25470) Support Java 1.8](https://github.com/flutter/flutter/pull/25470) |
|   | [(flutter/flutter#25443) fix the daemon device.getDevices call](https://github.com/flutter/flutter/pull/25443) |
|   | [(flutter/flutter#25332) [fuchsia] Get Dart VM service ports from The Hub](https://github.com/flutter/flutter/pull/25332) |
|   | [(flutter/flutter#25221) Support ANDROID_SDK_ROOT in addition to ANDROID_HOME](https://github.com/flutter/flutter/pull/25221) |
|   | [(flutter/flutter#25301) Flutter tool support for automatic saving of JIT compilation trace](https://github.com/flutter/flutter/pull/25301) |
|   | [(flutter/flutter#25344) Add fuchsia devices to daemon command](https://github.com/flutter/flutter/pull/25344) |
|   | [(flutter/flutter#25303) Add ipv6 and observatory port support to the attach command](https://github.com/flutter/flutter/pull/25303) |
|   | [(flutter/flutter#25288) Revert "Add ipv6 and observatory port support to the attach command."](https://github.com/flutter/flutter/pull/25288) |
|   | [(flutter/flutter#25269) Make doctor output consistent between VS Code/IntelliJ/Android Studio when plugins are missing](https://github.com/flutter/flutter/pull/25269) |
|   | [(flutter/flutter#24537) Add ipv6 and observatory port support to the attach command.](https://github.com/flutter/flutter/pull/24537) |
|   | [(flutter/flutter#24515) Add some a basic debug stepping tests](https://github.com/flutter/flutter/pull/24515) |
|   | [(flutter/flutter#25240) Revert "Ensure that cache dirs and files have appropriate permissions"](https://github.com/flutter/flutter/pull/25240) |
|   | [(flutter/flutter#24953) Fuchsia multiple devices and target](https://github.com/flutter/flutter/pull/24953) |
|   | [(flutter/flutter#25154) Don't require the AVD folder to exist in order to run `flutter emulators`](https://github.com/flutter/flutter/pull/25154) |
|   | [(flutter/flutter#25058) ensure lastBuildTimestamp is set before early return](https://github.com/flutter/flutter/pull/25058) |
|   | [(flutter/flutter#24632) Include error message in crash reports](https://github.com/flutter/flutter/pull/24632) |
|   | [(flutter/flutter#24669) Ensure that cache dirs and files have appropriate permissions](https://github.com/flutter/flutter/pull/24669) |
|   | [(flutter/flutter#24878) Add a flutter-attach entry point for fuchsia](https://github.com/flutter/flutter/pull/24878) |
| f: material design | 38 issues with this label |
|   | [(flutter/flutter#25984) use RRect to draw avatar check on chip](https://github.com/flutter/flutter/pull/25984) |
|   | [(flutter/flutter#23424) Teach drag start behaviors to DragGestureRecognizer](https://github.com/flutter/flutter/pull/23424) |
|   | [(flutter/flutter#26209) Revert "Teach drag start behaviors to DragGestureRecognizer (#23424)"](https://github.com/flutter/flutter/pull/26209) |
|   | [(flutter/flutter#26199) Fix tristate checkbox false to null transition, test ALL transitions](https://github.com/flutter/flutter/pull/26199) |
|   | [(flutter/flutter#26021) Fix SliverAppBar title opacity and test all cases](https://github.com/flutter/flutter/pull/26021) |
|   | [(flutter/flutter#26101) Fix a floating snapping SliverAppBar crash](https://github.com/flutter/flutter/pull/26101) |
|   | [(flutter/flutter#26088) Fix typos introduced with the TextField.onTap PR, udpated debugFillProperties](https://github.com/flutter/flutter/pull/26088) |
|   | [(flutter/flutter#26042) Update material spec references in BottomSheet et al., Scaffold](https://github.com/flutter/flutter/pull/26042) |
|   | [(flutter/flutter#23118) Setting icon color to first ListTile in ExpansionTile. Fixes #23053](https://github.com/flutter/flutter/pull/23118) |
|   | [(flutter/flutter#25799) Make LicensePage respect the notch](https://github.com/flutter/flutter/pull/25799) |
|   | [(flutter/flutter#7318) ListItem should call out that it's intentionally fixed height](https://github.com/flutter/flutter/issues/7318) |
|   | [(flutter/flutter#24736) [H] Provide some more locations for the FAB.](https://github.com/flutter/flutter/pull/24736) |
|   | [(flutter/flutter#24816) [H] ClipPath.shape and related fixes](https://github.com/flutter/flutter/pull/24816) |
|   | [(flutter/flutter#25718) Fix merge conflict.](https://github.com/flutter/flutter/pull/25718) |
|   | [(flutter/flutter#24643) [H] Some minor tweaks to InputDecoration (mainly docs).](https://github.com/flutter/flutter/pull/24643) |
|   | [(flutter/flutter#25585) Expose font fallback API in TextStyle, Roll engine 54a3577c0139..215ca1560088 (8 commits)](https://github.com/flutter/flutter/pull/25585) |
|   | [(flutter/flutter#24457) Revise Android and iOS gestures on Material TextField](https://github.com/flutter/flutter/pull/24457) |
|   | [(flutter/flutter#25573) Update DayPicker,DatePicker doc "see also" sections](https://github.com/flutter/flutter/pull/25573) |
|   | [(flutter/flutter#25339) [Material] Theme-able TextStyles for AlertDialog](https://github.com/flutter/flutter/pull/25339) |
|   | [(flutter/flutter#23759) Adds CupertinoTheme](https://github.com/flutter/flutter/pull/23759) |
|   | [(flutter/flutter#25345) assert(elevation >= 0.0) and doc clarifications](https://github.com/flutter/flutter/pull/25345) |
|   | [(flutter/flutter#24993) Add InputDecoration alignLabelWithHint parameter](https://github.com/flutter/flutter/pull/24993) |
|   | [(flutter/flutter#25342) Revert "Revert "obscureText and enableInteractiveSelection defaults""](https://github.com/flutter/flutter/pull/25342) |
|   | [(flutter/flutter#23919) Allow detection of taps on TabBar](https://github.com/flutter/flutter/pull/23919) |
|   | [(flutter/flutter#24932) Fixed Typography null factory constructor](https://github.com/flutter/flutter/pull/24932) |
|   | [(flutter/flutter#25384) Adds support for floating cursor](https://github.com/flutter/flutter/pull/25384) |
|   | [(flutter/flutter#24999) Remove TextField.noMaxLength, use maxLength = -1 instead](https://github.com/flutter/flutter/pull/24999) |
|   | [(flutter/flutter#25352) Revert "Adds support for floating cursor"](https://github.com/flutter/flutter/pull/25352) |
|   | [(flutter/flutter#24449) Text field style merge](https://github.com/flutter/flutter/pull/24449) |
|   | [(flutter/flutter#24169) [Material] Theme-able elevation on dialogs.](https://github.com/flutter/flutter/pull/24169) |
|   | [(flutter/flutter#24587) Validate style in TextField](https://github.com/flutter/flutter/pull/24587) |
|   | [(flutter/flutter#24976) Support TextField multi-line hint text #20941](https://github.com/flutter/flutter/pull/24976) |
|   | [(flutter/flutter#25051) Do not fade out text for pinned & floating AppBar](https://github.com/flutter/flutter/pull/25051) |
|   | [(flutter/flutter#24892) Handle a TabBarView special case: last tab deleted before animation ends](https://github.com/flutter/flutter/pull/24892) |
|   | [(flutter/flutter#24635) TextFormField cursor params](https://github.com/flutter/flutter/pull/24635) |
|   | [(flutter/flutter#24941) Update Switch doc: disabled state](https://github.com/flutter/flutter/pull/24941) |
|   | [(flutter/flutter#25120) Replace deprecated link (to design-principles page) in comment](https://github.com/flutter/flutter/pull/25120) |
|   | [(flutter/flutter#25091) Add animations to SliverAppBar doc](https://github.com/flutter/flutter/pull/25091) |
| team | 31 issues with this label |
|   | [(flutter/flutter#26153) Remove the cc @Hixie from no-response template](https://github.com/flutter/flutter/pull/26153) |
|   | [(flutter/flutter#25515) Write snippets index file when generating docs](https://github.com/flutter/flutter/pull/25515) |
|   | [(flutter/flutter#25579) fix doc-comment snippets](https://github.com/flutter/flutter/pull/25579) |
|   | [(flutter/flutter#25922) update lint list](https://github.com/flutter/flutter/pull/25922) |
|   | [(flutter/flutter#26031) Clean gallery about page post-1.0](https://github.com/flutter/flutter/pull/26031) |
|   | [(flutter/flutter#26030) Turn clipping on for Card Demo](https://github.com/flutter/flutter/pull/26030) |
|   | [(flutter/flutter#26041) Update dartdoc to 0.27.0](https://github.com/flutter/flutter/pull/26041) |
|   | [(flutter/flutter#26024) Workaround the Gradle crash due to non ASCII characters.](https://github.com/flutter/flutter/pull/26024) |
|   | [(flutter/flutter#25514) fix typo](https://github.com/flutter/flutter/pull/25514) |
|   | [(flutter/flutter#25995) Address code review comment.](https://github.com/flutter/flutter/pull/25995) |
|   | [(flutter/flutter#26015) [fuchsia] Fix flutter_gallery BUILD.gn](https://github.com/flutter/flutter/pull/26015) |
|   | [(flutter/flutter#25817) fix flutter run in dev/manual_tests](https://github.com/flutter/flutter/pull/25817) |
|   | [(flutter/flutter#25857) Mark flutter_gallery__back_button_memory as flaky.](https://github.com/flutter/flutter/pull/25857) |
|   | [(flutter/flutter#25854) Fix analyzer "prefer const" warning.](https://github.com/flutter/flutter/pull/25854) |
|   | [(flutter/flutter#25798) remove early-stage from Gallery's About screen](https://github.com/flutter/flutter/pull/25798) |
|   | [(flutter/flutter#25674) Updated Shrine demo](https://github.com/flutter/flutter/pull/25674) |
|   | [(flutter/flutter#25642) Revert dependency upgrade to see if it helps with build times and APK size](https://github.com/flutter/flutter/pull/25642) |
|   | [(flutter/flutter#23531) [O] Remove many timeouts.](https://github.com/flutter/flutter/pull/23531) |
|   | [(flutter/flutter#25582) Add missing dependency to fix the build](https://github.com/flutter/flutter/pull/25582) |
|   | [(flutter/flutter#25569) Reland: Update examples to match the new version of generated build.gradle (#25483)](https://github.com/flutter/flutter/pull/25569) |
|   | [(flutter/flutter#25521) fix indentation in doc comments](https://github.com/flutter/flutter/pull/25521) |
|   | [(flutter/flutter#25568) Fix the build](https://github.com/flutter/flutter/pull/25568) |
|   | [(flutter/flutter#25563) Revert "Update examples to match the new version of generated build.gradle (#25483)"](https://github.com/flutter/flutter/pull/25563) |
|   | [(flutter/flutter#25513) make "See also" sections uniform](https://github.com/flutter/flutter/pull/25513) |
|   | [(flutter/flutter#25474) fix some formatting issues](https://github.com/flutter/flutter/pull/25474) |
|   | [(flutter/flutter#25184) Add imports section to sample code templates, and more docs.](https://github.com/flutter/flutter/pull/25184) |
|   | [(flutter/flutter#7227) Figure out why license script didn't pick up the license for the Ahem font](https://github.com/flutter/flutter/issues/7227) |
|   | [(flutter/flutter#25416) try disabling flutter run test](https://github.com/flutter/flutter/pull/25416) |
|   | [(flutter/flutter#25243) Allow snippets tool to be run from arbitrary CWDs](https://github.com/flutter/flutter/pull/25243) |
|   | [(flutter/flutter#25178) Adds favicon to Dash/Zeal docset, adds OpenSearch metadata.](https://github.com/flutter/flutter/pull/25178) |
|   | [(flutter/flutter#24890) Remove deprecated lint "prefer_bool_in_asserts".](https://github.com/flutter/flutter/pull/24890) |
| a: text input | 18 issues with this label |
|   | [(flutter/flutter#23424) Teach drag start behaviors to DragGestureRecognizer](https://github.com/flutter/flutter/pull/23424) |
|   | [(flutter/flutter#26209) Revert "Teach drag start behaviors to DragGestureRecognizer (#23424)"](https://github.com/flutter/flutter/pull/26209) |
|   | [(flutter/flutter#24779) Skip formatters if text has not changed](https://github.com/flutter/flutter/pull/24779) |
|   | [(flutter/flutter#25055) Include cursor in textfield intrinsic width measurement](https://github.com/flutter/flutter/pull/25055) |
|   | [(flutter/flutter#24457) Revise Android and iOS gestures on Material TextField](https://github.com/flutter/flutter/pull/24457) |
|   | [(flutter/flutter#25229) Right aligned backspace bug](https://github.com/flutter/flutter/pull/25229) |
|   | [(flutter/flutter#25237) Fix typo](https://github.com/flutter/flutter/pull/25237) |
|   | [(flutter/flutter#24797) Iterate through potential grapheme cluster lengths in text painter](https://github.com/flutter/flutter/pull/24797) |
|   | [(flutter/flutter#25473) TextField.onChanged() doc update](https://github.com/flutter/flutter/pull/25473) |
|   | [(flutter/flutter#25342) Revert "Revert "obscureText and enableInteractiveSelection defaults""](https://github.com/flutter/flutter/pull/25342) |
|   | [(flutter/flutter#24527) obscureText and enableInteractiveSelection defaults](https://github.com/flutter/flutter/pull/24527) |
|   | [(flutter/flutter#25335) Revert "obscureText and enableInteractiveSelection defaults"](https://github.com/flutter/flutter/pull/25335) |
|   | [(flutter/flutter#25384) Adds support for floating cursor](https://github.com/flutter/flutter/pull/25384) |
|   | [(flutter/flutter#25390) Revert "drop/restore focus when app becomes invisible/visible"](https://github.com/flutter/flutter/pull/25390) |
|   | [(flutter/flutter#24744) drop/restore focus when app becomes invisible/visible](https://github.com/flutter/flutter/pull/24744) |
|   | [(flutter/flutter#24761) Adds support for floating cursor](https://github.com/flutter/flutter/pull/24761) |
|   | [(flutter/flutter#25352) Revert "Adds support for floating cursor"](https://github.com/flutter/flutter/pull/25352) |
|   | [(flutter/flutter#24635) TextFormField cursor params](https://github.com/flutter/flutter/pull/24635) |
| d: api docs | 17 issues with this label |
|   | [(flutter/flutter#25515) Write snippets index file when generating docs](https://github.com/flutter/flutter/pull/25515) |
|   | [(flutter/flutter#25790) Clarify doc for AnimatedContainer](https://github.com/flutter/flutter/pull/25790) |
|   | [(flutter/flutter#26089) Explain that BoxDecoration doesn't do clip.](https://github.com/flutter/flutter/pull/26089) |
|   | [(flutter/flutter#26042) Update material spec references in BottomSheet et al., Scaffold](https://github.com/flutter/flutter/pull/26042) |
|   | [(flutter/flutter#26041) Update dartdoc to 0.27.0](https://github.com/flutter/flutter/pull/26041) |
|   | [(flutter/flutter#25992) Add docs to TextStyle for fontFamilyFallback/Custom font fallback](https://github.com/flutter/flutter/pull/25992) |
|   | [(flutter/flutter#25604) no period after an alone reference in see also section](https://github.com/flutter/flutter/pull/25604) |
|   | [(flutter/flutter#25584) Fix material reference in CupertinoPicker doc](https://github.com/flutter/flutter/pull/25584) |
|   | [(flutter/flutter#25573) Update DayPicker,DatePicker doc "see also" sections](https://github.com/flutter/flutter/pull/25573) |
|   | [(flutter/flutter#25477) TransitionRoute.canTransitionFrom,To() doc update](https://github.com/flutter/flutter/pull/25477) |
|   | [(flutter/flutter#25237) Fix typo](https://github.com/flutter/flutter/pull/25237) |
|   | [(flutter/flutter#25473) TextField.onChanged() doc update](https://github.com/flutter/flutter/pull/25473) |
|   | [(flutter/flutter#25243) Allow snippets tool to be run from arbitrary CWDs](https://github.com/flutter/flutter/pull/25243) |
|   | [(flutter/flutter#25120) Replace deprecated link (to design-principles page) in comment](https://github.com/flutter/flutter/pull/25120) |
|   | [(flutter/flutter#25096) Change network image URL in doc](https://github.com/flutter/flutter/pull/25096) |
|   | [(flutter/flutter#25091) Add animations to SliverAppBar doc](https://github.com/flutter/flutter/pull/25091) |
|   | [(flutter/flutter#25003) Fix typo in documentation](https://github.com/flutter/flutter/pull/25003) |
| a: tests | 15 issues with this label |
|   | [(flutter/flutter#25888) Extract TestBorder into a utility file](https://github.com/flutter/flutter/pull/25888) |
|   | [(flutter/flutter#25817) fix flutter run in dev/manual_tests](https://github.com/flutter/flutter/pull/25817) |
|   | [(flutter/flutter#25857) Mark flutter_gallery__back_button_memory as flaky.](https://github.com/flutter/flutter/pull/25857) |
|   | [(flutter/flutter#25674) Updated Shrine demo](https://github.com/flutter/flutter/pull/25674) |
|   | [(flutter/flutter#25678) Pin the goldens repo to a specific commit in the android_views test.](https://github.com/flutter/flutter/pull/25678) |
|   | [(flutter/flutter#25479) Depend on the goldens repo through git.](https://github.com/flutter/flutter/pull/25479) |
|   | [(flutter/flutter#23531) [O] Remove many timeouts.](https://github.com/flutter/flutter/pull/23531) |
|   | [(flutter/flutter#25646) Revert "[O] Remove many timeouts."](https://github.com/flutter/flutter/pull/25646) |
|   | [(flutter/flutter#25568) Fix the build](https://github.com/flutter/flutter/pull/25568) |
|   | [(flutter/flutter#25489) Video demo instrumentation](https://github.com/flutter/flutter/pull/25489) |
|   | [(flutter/flutter#25381) Add cull opacity perf test to device lab](https://github.com/flutter/flutter/pull/25381) |
|   | [(flutter/flutter#25482) Mark flaky tests as such](https://github.com/flutter/flutter/pull/25482) |
|   | [(flutter/flutter#25305) Use stderr instead of stdout to contain errors in flutter attach test](https://github.com/flutter/flutter/pull/25305) |
|   | [(flutter/flutter#25380) Revert "Use stderr instead of stdout to contain errors in flutter attach test"](https://github.com/flutter/flutter/pull/25380) |
|   | [(flutter/flutter#24930) Run flutter tests through mini test engine when run directly (flutter run -t test_file)](https://github.com/flutter/flutter/pull/24930) |
| team: gallery | 13 issues with this label |
|   | [(flutter/flutter#26209) Revert "Teach drag start behaviors to DragGestureRecognizer (#23424)"](https://github.com/flutter/flutter/pull/26209) |
|   | [(flutter/flutter#26031) Clean gallery about page post-1.0](https://github.com/flutter/flutter/pull/26031) |
|   | [(flutter/flutter#26030) Turn clipping on for Card Demo](https://github.com/flutter/flutter/pull/26030) |
|   | [(flutter/flutter#26015) [fuchsia] Fix flutter_gallery BUILD.gn](https://github.com/flutter/flutter/pull/26015) |
|   | [(flutter/flutter#25857) Mark flutter_gallery__back_button_memory as flaky.](https://github.com/flutter/flutter/pull/25857) |
|   | [(flutter/flutter#25798) remove early-stage from Gallery's About screen](https://github.com/flutter/flutter/pull/25798) |
|   | [(flutter/flutter#25674) Updated Shrine demo](https://github.com/flutter/flutter/pull/25674) |
|   | [(flutter/flutter#25642) Revert dependency upgrade to see if it helps with build times and APK size](https://github.com/flutter/flutter/pull/25642) |
|   | [(flutter/flutter#25582) Add missing dependency to fix the build](https://github.com/flutter/flutter/pull/25582) |
|   | [(flutter/flutter#25563) Revert "Update examples to match the new version of generated build.gradle (#25483)"](https://github.com/flutter/flutter/pull/25563) |
|   | [(flutter/flutter#23759) Adds CupertinoTheme](https://github.com/flutter/flutter/pull/23759) |
|   | [(flutter/flutter#25489) Video demo instrumentation](https://github.com/flutter/flutter/pull/25489) |
|   | [(flutter/flutter#25186) Temporarily add back filtered bintray ExoPlayer repository](https://github.com/flutter/flutter/pull/25186) |
| f: cupertino | 10 issues with this label |
|   | [(flutter/flutter#23424) Teach drag start behaviors to DragGestureRecognizer](https://github.com/flutter/flutter/pull/23424) |
|   | [(flutter/flutter#26209) Revert "Teach drag start behaviors to DragGestureRecognizer (#23424)"](https://github.com/flutter/flutter/pull/26209) |
|   | [(flutter/flutter#24457) Revise Android and iOS gestures on Material TextField](https://github.com/flutter/flutter/pull/24457) |
|   | [(flutter/flutter#25584) Fix material reference in CupertinoPicker doc](https://github.com/flutter/flutter/pull/25584) |
|   | [(flutter/flutter#25593) Let CupertinoTabScaffold handle keyboard insets too](https://github.com/flutter/flutter/pull/25593) |
|   | [(flutter/flutter#23759) Adds CupertinoTheme](https://github.com/flutter/flutter/pull/23759) |
|   | [(flutter/flutter#25474) fix some formatting issues](https://github.com/flutter/flutter/pull/25474) |
|   | [(flutter/flutter#25384) Adds support for floating cursor](https://github.com/flutter/flutter/pull/25384) |
|   | [(flutter/flutter#25352) Revert "Adds support for floating cursor"](https://github.com/flutter/flutter/pull/25352) |
|   | [(flutter/flutter#25183) Add navigatorKey to CupertinoTabView](https://github.com/flutter/flutter/pull/25183) |
| t: gradle | 9 issues with this label |
|   | [(flutter/flutter#25796) Allow dynamic patches without a patch number.](https://github.com/flutter/flutter/pull/25796) |
|   | [(flutter/flutter#24440) Adding support for android app bundle - Issue #17829](https://github.com/flutter/flutter/pull/24440) |
|   | [(flutter/flutter#23531) [O] Remove many timeouts.](https://github.com/flutter/flutter/pull/23531) |
|   | [(flutter/flutter#25576) Flutter tool support for building dynamic patches on Android](https://github.com/flutter/flutter/pull/25576) |
|   | [(flutter/flutter#25569) Reland: Update examples to match the new version of generated build.gradle (#25483)](https://github.com/flutter/flutter/pull/25569) |
|   | [(flutter/flutter#25563) Revert "Update examples to match the new version of generated build.gradle (#25483)"](https://github.com/flutter/flutter/pull/25563) |
|   | [(flutter/flutter#25520) Fix flutter tool to actually honor --build-number/--build-name flags](https://github.com/flutter/flutter/pull/25520) |
|   | [(flutter/flutter#25484) Fix gradle local.properties tests that were never excersized](https://github.com/flutter/flutter/pull/25484) |
|   | [(flutter/flutter#25470) Support Java 1.8](https://github.com/flutter/flutter/pull/25470) |
| d: examples | 6 issues with this label |
|   | [(flutter/flutter#23424) Teach drag start behaviors to DragGestureRecognizer](https://github.com/flutter/flutter/pull/23424) |
|   | [(flutter/flutter#26209) Revert "Teach drag start behaviors to DragGestureRecognizer (#23424)"](https://github.com/flutter/flutter/pull/26209) |
|   | [(flutter/flutter#25483) Update examples to match the new version of generated build.gradle](https://github.com/flutter/flutter/pull/25483) |
|   | [(flutter/flutter#25569) Reland: Update examples to match the new version of generated build.gradle (#25483)](https://github.com/flutter/flutter/pull/25569) |
|   | [(flutter/flutter#25563) Revert "Update examples to match the new version of generated build.gradle (#25483)"](https://github.com/flutter/flutter/pull/25563) |
|   | [(flutter/flutter#25300) Remove uses-material-design from hello_world example](https://github.com/flutter/flutter/pull/25300) |
| ▣ platform-android | 5 issues with this label |
|   | [(flutter/flutter#7315) Android Emulator does not show box shadows](https://github.com/flutter/flutter/issues/7315) |
|   | [(flutter/flutter#24440) Adding support for android app bundle - Issue #17829](https://github.com/flutter/flutter/pull/24440) |
|   | [(flutter/flutter#25221) Support ANDROID_SDK_ROOT in addition to ANDROID_HOME](https://github.com/flutter/flutter/pull/25221) |
|   | [(flutter/flutter#25154) Don't require the AVD folder to exist in order to run `flutter emulators`](https://github.com/flutter/flutter/pull/25154) |
| f: gestures | 4 issues with this label |
|   | [(flutter/flutter#23424) Teach drag start behaviors to DragGestureRecognizer](https://github.com/flutter/flutter/pull/23424) |
|   | [(flutter/flutter#26209) Revert "Teach drag start behaviors to DragGestureRecognizer (#23424)"](https://github.com/flutter/flutter/pull/26209) |
|   | [(flutter/flutter#25792) Actively reject UiKitView gestures.](https://github.com/flutter/flutter/pull/25792) |
|   | [(flutter/flutter#24554) Adds force press gesture detector and recognizer](https://github.com/flutter/flutter/pull/24554) |
| ○ platform-fuchsia | 4 issues with this label |
|   | [(flutter/flutter#26249) Revert "Replace netls and netaddr with dev_finder"](https://github.com/flutter/flutter/pull/26249) |
|   | [(flutter/flutter#26090) Replace netls and netaddr with dev_finder](https://github.com/flutter/flutter/pull/26090) |
|   | [(flutter/flutter#26201) Prevent calls to view.uiIsolate.flutterExit on devices which do not support it](https://github.com/flutter/flutter/pull/26201) |
|   | [(flutter/flutter#25332) [fuchsia] Get Dart VM service ports from The Hub](https://github.com/flutter/flutter/pull/25332) |
| a: animation | 3 issues with this label |
|   | [(flutter/flutter#25788) Add Robert Penner’s easing functions](https://github.com/flutter/flutter/pull/25788) |
|   | [(flutter/flutter#25790) Clarify doc for AnimatedContainer](https://github.com/flutter/flutter/pull/25790) |
|   | [(flutter/flutter#25474) fix some formatting issues](https://github.com/flutter/flutter/pull/25474) |
| t: hot reload | 3 issues with this label |
|   | [(flutter/flutter#26039) Report hot reload statistics.](https://github.com/flutter/flutter/pull/26039) |
|   | [(flutter/flutter#7314) Flutter crash on startup (metabug)](https://github.com/flutter/flutter/issues/7314) |
|   | [(flutter/flutter#7307) Sending SIGUSR1 and pressing 'R' simultaneously locks up the flutter tool](https://github.com/flutter/flutter/issues/7307) |
| a: typography | 2 issues with this label |
|   | [(flutter/flutter#25585) Expose font fallback API in TextStyle, Roll engine 54a3577c0139..215ca1560088 (8 commits)](https://github.com/flutter/flutter/pull/25585) |
|   | [(flutter/flutter#24797) Iterate through potential grapheme cluster lengths in text painter](https://github.com/flutter/flutter/pull/24797) |
| customer: fuchsia | 2 issues with this label |
|   | [(flutter/flutter#25344) Add fuchsia devices to daemon command](https://github.com/flutter/flutter/pull/25344) |
|   | [(flutter/flutter#24953) Fuchsia multiple devices and target](https://github.com/flutter/flutter/pull/24953) |
| d: website - content | 2 issues with this labe |
|   | [(flutter/flutter#7317) flutter/website uses a very old version of jekyll (2.4.0) which is incompatible with Ruby 2.4](https://github.com/flutter/flutter/issues/7317) |
|   | [(flutter/flutter#7306) Broken links on website](https://github.com/flutter/flutter/issues/7306) |
| e: device-specific | 2 issues with this label |
|   | [(flutter/flutter#7315) Android Emulator does not show box shadows](https://github.com/flutter/flutter/issues/7315) |
| engine | 2 issues with this label |
|   | [(flutter/flutter#7227) Figure out why license script didn't pick up the license for the Ahem font](https://github.com/flutter/flutter/issues/7227) |
| f: date/time picker | 2 issues with this label |
|   | [(flutter/flutter#23424) Teach drag start behaviors to DragGestureRecognizer](https://github.com/flutter/flutter/pull/23424) |
|   | [(flutter/flutter#25573) Update DayPicker,DatePicker doc "see also" sections](https://github.com/flutter/flutter/pull/25573) |
| f: scrolling | 2 issues with this label |
|   | [(flutter/flutter#23424) Teach drag start behaviors to DragGestureRecognizer](https://github.com/flutter/flutter/pull/23424) |
|   | [(flutter/flutter#26209) Revert "Teach drag start behaviors to DragGestureRecognizer (#23424)"](https://github.com/flutter/flutter/pull/26209) |
| severe: crash | 1 issue with this label |
|   | [(flutter/flutter#7314) Flutter crash on startup (metabug)](https://github.com/flutter/flutter/issues/7314) |
| t: flutter doctor | 2 issues with this label |
|   | [(flutter/flutter#23889) Flutter doctor error message lookup](https://github.com/flutter/flutter/pull/23889) |
|   | [(flutter/flutter#25269) Make doctor output consistent between VS Code/IntelliJ/Android Studio when plugins are missing](https://github.com/flutter/flutter/pull/25269) |
| waiting for tree to go green | 2 issues with this label |
|   | [(flutter/flutter#25159) fix #25143 Successive calls to `precacheImage()` throw an exception](https://github.com/flutter/flutter/pull/25159) |
|   | [(flutter/flutter#25872) Optimize cocoapods logic in flutter doctor.](https://github.com/flutter/flutter/pull/25872) |
| ⌺‬ platform-ios | 2 issues with this label |
|   | [(flutter/flutter#25872) Optimize cocoapods logic in flutter doctor.](https://github.com/flutter/flutter/pull/25872) |
|   | [(flutter/flutter#24580) Remove code signing special casing for Googlers round 2](https://github.com/flutter/flutter/pull/24580) |
| a: debugging | 1 issue with this label |
|   | [(flutter/flutter#24515) Add some a basic debug stepping tests](https://github.com/flutter/flutter/pull/24515) |
| a: internationalization | 1 issue with this label |
|   | [(flutter/flutter#25394) Update localizations](https://github.com/flutter/flutter/pull/25394) |
| a: platform-views | 1 issue with this label |
|   | [(flutter/flutter#25792) Actively reject UiKitView gestures.](https://github.com/flutter/flutter/pull/25792) |
| a:bug | 1 issue with this label |
|   | [(gradle/gradle#3117) Gradle Build daemon crashes if the environment variables contain non-ASCII characters](https://github.com/gradle/gradle/issues/3117) |
| cla: no | 1 issue with this label |
|   | [(flutter/flutter#24440) Adding support for android app bundle - Issue #17829](https://github.com/flutter/flutter/pull/24440) |
| customer: dream (g3) | 1 issue with this label |
|   | [(flutter/flutter#24537) Add ipv6 and observatory port support to the attach command.](https://github.com/flutter/flutter/pull/24537) |
| customer: gold | 1 issue with this label |
|   | [(flutter/flutter#25872) Optimize cocoapods logic in flutter doctor.](https://github.com/flutter/flutter/pull/25872) |
| dependency: skia | 1 issue with this label |
|   | [(flutter/flutter#7315) Android Emulator does not show box shadows](https://github.com/flutter/flutter/issues/7315) |
| f: routes | 1 issue with this label |
|   | [(flutter/flutter#25477) TransitionRoute.canTransitionFrom,To() doc update](https://github.com/flutter/flutter/pull/25477) |
| from:contributor | 1 issue with this label |
|   | [(gradle/gradle#3117) Gradle Build daemon crashes if the environment variables contain non-ASCII characters](https://github.com/gradle/gradle/issues/3117) |
| p: framework | 1 issue with this label |
|   | [(flutter/flutter#7312) Support Swift in  ios template project](https://github.com/flutter/flutter/issues/7312) |
| p: tooling | 1 issue with this label |
|   | [(flutter/flutter#7312) Support Swift in  ios template project](https://github.com/flutter/flutter/issues/7312) |
| plugin | 1 issue with this label |
|   | [(flutter/flutter#7312) Support Swift in  ios template project](https://github.com/flutter/flutter/issues/7312) |
| severe: API break | 1 issue with this label |
|   | [(flutter/flutter#7318) ListItem should call out that it's intentionally fixed height](https://github.com/flutter/flutter/issues/7318) |
| severe: performance | 1 issue with this label |
|   | [(flutter/flutter#25381) Add cull opacity perf test to device lab](https://github.com/flutter/flutter/pull/25381) |
| t: flutter driver | 1 issue with this label |
|   | [(flutter/flutter#25646) Revert "[O] Remove many timeouts."](https://github.com/flutter/flutter/pull/25646) |

## In the plugins repository
The following issues were addressed in 426 commits.

| PR | Summary |
|---|---|
| [flutter/plugins#691](https://github.com/flutter/plugins/pull/691) | Fix cloud functions podspec. |
| [flutter/plugins#956](https://github.com/flutter/plugins/pull/956) | Make the description for webview_flutter longer. |
| [flutter/plugins#949](https://github.com/flutter/plugins/pull/949) | Fixes: 'webview_flutter/WebviewFlutterPlugin.h' file not found |
| [flutter/plugins#375](https://github.com/flutter/plugins/pull/375) | add a dynamic get method to shared_preferences |
| [flutter/plugins#946](https://github.com/flutter/plugins/pull/946) | [google_maps_flutter] Relax Flutter version requirement to 0.11.9 |
| [flutter/plugins#945](https://github.com/flutter/plugins/pull/945) | Bump maps plugin version. |
| [flutter/plugins#381](https://github.com/flutter/plugins/pull/381) | Make video plugin ready for republication |
| [flutter/plugins#942](https://github.com/flutter/plugins/pull/942) | Camera uses the device orientation on Android |
| [flutter/plugins#943](https://github.com/flutter/plugins/pull/943) | Keep a reference to the plugin registrar in `FLTGoogleMapController`. |
| [flutter/plugins#940](https://github.com/flutter/plugins/pull/940) | Set a minimal Flutter version for the webview plugin. |
| [flutter/plugins#939](https://github.com/flutter/plugins/pull/939) | Bump google_maps_flutter's version to 0.0.3. |
| [flutter/plugins#938](https://github.com/flutter/plugins/pull/938) | Bring back the google_maps_flutter pub badge. |
| [flutter/plugins#937](https://github.com/flutter/plugins/pull/937) | Don't export dart:async by the Google Maps plugin. |
| [flutter/plugins#935](https://github.com/flutter/plugins/pull/935) | Remove unused map id fields. |
| [flutter/plugins#382](https://github.com/flutter/plugins/pull/382) | Camera: Improve ressource cleanup on Android |
| [flutter/plugins#933](https://github.com/flutter/plugins/pull/933) | Update the IAP README |
| [flutter/plugins#932](https://github.com/flutter/plugins/pull/932) | Bump google_maps_flutter version to 0.0.2 (pre release). |
| [flutter/plugins#928](https://github.com/flutter/plugins/pull/928) | Introduce credentials to firebase_auth |
| [flutter/plugins#384](https://github.com/flutter/plugins/pull/384) | Camera: Avoid capture double result |
| [flutter/plugins#924](https://github.com/flutter/plugins/pull/924) | Adding closeWebView feature to url_launcher |
| [flutter/plugins#925](https://github.com/flutter/plugins/pull/925) | Delete reference to the pub plugin |
| [flutter/plugins#385](https://github.com/flutter/plugins/pull/385) | Enable swift apps depending on plugins with static dependencies |
| [flutter/plugins#922](https://github.com/flutter/plugins/pull/922) | [android_alarm_manager] bump firebase_auth dependencies |
| [flutter/plugins#920](https://github.com/flutter/plugins/pull/920) | Update Google Maps Plugin README. |
| [flutter/plugins#775](https://github.com/flutter/plugins/pull/775) | Fixes error handling in firebase_auth on Android and iOS |
| [flutter/plugins#915](https://github.com/flutter/plugins/pull/915) | Set all gradle-wrapper versions to 4.10.2 |
| [flutter/plugins#921](https://github.com/flutter/plugins/pull/921) | Use context() instead of activity() |
| [flutter/plugins#919](https://github.com/flutter/plugins/pull/919) | docs: Improves READMEs styles |
| [flutter/plugins#916](https://github.com/flutter/plugins/pull/916) | Upgrade Android Gradle Plugin to 3.2.1 |
| [flutter/plugins#914](https://github.com/flutter/plugins/pull/914) | [webview_flutter] Don't static import classes. |
| [flutter/plugins#913](https://github.com/flutter/plugins/pull/913) | reformat FirebaseAuthPlugin.m |
| [flutter/plugins#912](https://github.com/flutter/plugins/pull/912) | Update firebase_auth version for release |
| [flutter/plugins#907](https://github.com/flutter/plugins/pull/907) | [firebase_auth] Adding support for reauthenticate. |
| [flutter/plugins#908](https://github.com/flutter/plugins/pull/908) | [video_player] ExoPlayer 2.9.1, Android 9, Demo fixes |
| [flutter/plugins#911](https://github.com/flutter/plugins/pull/911) | Shuffle text selection TODOs. |
| [flutter/plugins#910](https://github.com/flutter/plugins/pull/910) | Don't depend on the Android support library in google_maps_flutter. |
| [flutter/plugins#898](https://github.com/flutter/plugins/pull/898) | Adding My Location parameter to the Google Maps plugin |
| [flutter/plugins#393](https://github.com/flutter/plugins/pull/393) | Fix Dart 2 type error in share plugin |
| [flutter/plugins#906](https://github.com/flutter/plugins/pull/906) | Update location_background |
| [flutter/plugins#903](https://github.com/flutter/plugins/pull/903) | [firebase_auth] adding return to verifyPhoneNumber |
| [flutter/plugins#899](https://github.com/flutter/plugins/pull/899) | Fix null language code and no detected text bug |
| [flutter/plugins#875](https://github.com/flutter/plugins/pull/875) | [firebase_messaging] adding support for deleteInstanceId and setAutoInitEnabled |
| [flutter/plugins#872](https://github.com/flutter/plugins/pull/872) | Fix crash when repeating detection |
| [flutter/plugins#891](https://github.com/flutter/plugins/pull/891) | Increase play-services-auth dependency |
| [flutter/plugins#880](https://github.com/flutter/plugins/pull/880) | Update docs/example for AdMob |
| [flutter/plugins#394](https://github.com/flutter/plugins/pull/394) | Fix Dart 2 type error in package_info |
| [flutter/plugins#395](https://github.com/flutter/plugins/pull/395) | Fix Dart 2 type error in local_auth |
| [flutter/plugins#890](https://github.com/flutter/plugins/pull/890) | Initial iOS implementation for webview_flutter. |
| [flutter/plugins#888](https://github.com/flutter/plugins/pull/888) | [firebase_analytics] Allow setUserID input to be null |
| [flutter/plugins#396](https://github.com/flutter/plugins/pull/396) | Fix Dart 2 type errors in device_info |
| [flutter/plugins#780](https://github.com/flutter/plugins/pull/780) | Set iOS deployment target to 8.0, fixes compilation errors |
| [flutter/plugins#777](https://github.com/flutter/plugins/pull/777) | Resolved compiler warnings in CloudFirestorePlugin.m |
| [flutter/plugins#859](https://github.com/flutter/plugins/pull/859) | Change google-services.json and GoogleService-Info.plist of example |
| [flutter/plugins#399](https://github.com/flutter/plugins/pull/399) | Fix Dart 2 type errors in firebase plugins |
| [flutter/plugins#862](https://github.com/flutter/plugins/pull/862) | Surpress unchecked warnings |
| [flutter/plugins#889](https://github.com/flutter/plugins/pull/889) | Run cleartoken task in background |
| [flutter/plugins#400](https://github.com/flutter/plugins/pull/400) | Fix Dart 2 type errors in cloud_firestore |
| [flutter/plugins#783](https://github.com/flutter/plugins/pull/783) | Allow quick_actions to register more than once |
| [flutter/plugins#882](https://github.com/flutter/plugins/pull/882) | Only update state for the registrar's activity |
| [flutter/plugins#401](https://github.com/flutter/plugins/pull/401) | Fix Dart 2 type errors in firebase_admob |
| [flutter/plugins#881](https://github.com/flutter/plugins/pull/881) | Update gradle dependencies. |
| [flutter/plugins#865](https://github.com/flutter/plugins/pull/865) | Bump firebase_auth version |
| [flutter/plugins#867](https://github.com/flutter/plugins/pull/867) | Make FirebaseApp final/private and change way to get auth instance  |
| [flutter/plugins#877](https://github.com/flutter/plugins/pull/877) | Camera version bump for #689 |
| [flutter/plugins#689](https://github.com/flutter/plugins/pull/689) | Fix preview and video size with satisfying conditions of multiple outputs. |
| [flutter/plugins#785](https://github.com/flutter/plugins/pull/785) | Update example with interactable link |
| [flutter/plugins#824](https://github.com/flutter/plugins/pull/824) | Use METHOD value instead of SIGN_UP_METHOD |
| [flutter/plugins#869](https://github.com/flutter/plugins/pull/869) | [firebase_messaging] changing getToken to rely on platform's getToken (2) |
| [flutter/plugins#868](https://github.com/flutter/plugins/pull/868) | Rev version |
| [flutter/plugins#873](https://github.com/flutter/plugins/pull/873) | Get rid of unneeded Firebase import |
| [flutter/plugins#781](https://github.com/flutter/plugins/pull/781) | FirebaseAuth multi app support |
| [flutter/plugins#858](https://github.com/flutter/plugins/pull/858) | enable lint prefer_void_to_null |
| [flutter/plugins#773](https://github.com/flutter/plugins/pull/773) | Fix a crash in the snapshot callback |
| [flutter/plugins#404](https://github.com/flutter/plugins/pull/404) |  Exposing GoogleSignIn Delegate as an interface |
| [flutter/plugins#765](https://github.com/flutter/plugins/pull/765) | Timestamps firestore |
| [flutter/plugins#407](https://github.com/flutter/plugins/pull/407) | Fix url_launcher for iOS <10 |
| [flutter/plugins#759](https://github.com/flutter/plugins/pull/759) | Correct usage of StreamBuilder |
| [flutter/plugins#857](https://github.com/flutter/plugins/pull/857) | Use exoplayer's preferred API for MediaSources |
| [flutter/plugins#813](https://github.com/flutter/plugins/pull/813) | video_player: Fixed null exception when file has no width or height. |
| [flutter/plugins#853](https://github.com/flutter/plugins/pull/853) | Change android invites dependency to dynamic links |
| [flutter/plugins#408](https://github.com/flutter/plugins/pull/408) | Fix new formatting errors |
| [flutter/plugins#818](https://github.com/flutter/plugins/pull/818) | remove unnecessary new from samples |
| [flutter/plugins#828](https://github.com/flutter/plugins/pull/828) | Reformat the marker id getter comment. |
| [flutter/plugins#845](https://github.com/flutter/plugins/pull/845) | Add biometry type detection to local_auth plugin |
| [flutter/plugins#843](https://github.com/flutter/plugins/pull/843) | Add clearMarkers() functionality to GoogleMapController |
| [flutter/plugins#406](https://github.com/flutter/plugins/pull/406) | Firestore: Allow null document snapshot data when document does not exist |
| [flutter/plugins#414](https://github.com/flutter/plugins/pull/414) | Remove Gradle artifacts from repo |
| [flutter/plugins#847](https://github.com/flutter/plugins/pull/847) | Bump firebase_performance Android dependencies to latest. |
| [flutter/plugins#840](https://github.com/flutter/plugins/pull/840) | Bump firebase_dynamic_links Android dependencies |
| [flutter/plugins#841](https://github.com/flutter/plugins/pull/841) | bump firebase_messaging android dependencies to latest |
| [flutter/plugins#412](https://github.com/flutter/plugins/pull/412) | Set SDK constraints to match Flutter beta |
| [flutter/plugins#774](https://github.com/flutter/plugins/pull/774) | Update firebase_ml_vision for latest Firebase MLKit API |
| [flutter/plugins#417](https://github.com/flutter/plugins/pull/417) | Fix Dart 2 runtime error in the camera plugin |
| [flutter/plugins#835](https://github.com/flutter/plugins/pull/835) | Bump firebase_admob Android dependencies to latest |
| [flutter/plugins#834](https://github.com/flutter/plugins/pull/834) | Bump firebase_storage Android dependencies to latest |
| [flutter/plugins#420](https://github.com/flutter/plugins/pull/420) | Fixed deprecation warnings |
| [flutter/plugins#829](https://github.com/flutter/plugins/pull/829) | bump cloud_firestore android dependencies |
| [flutter/plugins#830](https://github.com/flutter/plugins/pull/830) | Bump android dependencies to latest |
| [flutter/plugins#825](https://github.com/flutter/plugins/pull/825) | fix CI |
| [flutter/plugins#786](https://github.com/flutter/plugins/pull/786) | Added getter for Marker ID in maps package |
| [flutter/plugins#821](https://github.com/flutter/plugins/pull/821) | Bump firebase_core Android dependencies to latest |
| [flutter/plugins#419](https://github.com/flutter/plugins/pull/419) | Fix a Dart 2 runtime cast failure in firebase_database test |
| [flutter/plugins#697](https://github.com/flutter/plugins/pull/697) |  adding support auth link for Twitter |
| [flutter/plugins#812](https://github.com/flutter/plugins/pull/812) | Set http version to be compatible with flutter_test |
| [flutter/plugins#639](https://github.com/flutter/plugins/pull/639) | Added android device id to android device info |
| [flutter/plugins#810](https://github.com/flutter/plugins/pull/810) | Set http version to be compatible with flutter_test |
| [flutter/plugins#811](https://github.com/flutter/plugins/pull/811) | Add page names for firebaseopensource.com |
| [flutter/plugins#425](https://github.com/flutter/plugins/pull/425) | Fix Dart 2 type error and deprecation |
| [flutter/plugins#808](https://github.com/flutter/plugins/pull/808) | Bump version for #795 |
| [flutter/plugins#422](https://github.com/flutter/plugins/pull/422) | Incremental Build Script |
| [flutter/plugins#805](https://github.com/flutter/plugins/pull/805) | Handle user cancel when signing in |
| [flutter/plugins#802](https://github.com/flutter/plugins/pull/802) | Bump camera version |
| [flutter/plugins#796](https://github.com/flutter/plugins/pull/796) | add option to enable javascript in android webview |
| [flutter/plugins#804](https://github.com/flutter/plugins/pull/804) | Allow latest version for MLVision on ios |
| [flutter/plugins#410](https://github.com/flutter/plugins/pull/410) | add fetchProvidersForEmail wrapper |
| [flutter/plugins#800](https://github.com/flutter/plugins/pull/800) | Move SystemChrome update to platform side |
| [flutter/plugins#799](https://github.com/flutter/plugins/pull/799) | Add ability to clear auth cache on Android |
| [flutter/plugins#678](https://github.com/flutter/plugins/pull/678) | Add updatePassword functionality to firebase_auth plugin. |
| [flutter/plugins#798](https://github.com/flutter/plugins/pull/798) | Ban string values which clash with special prefixes |
| [flutter/plugins#797](https://github.com/flutter/plugins/pull/797) | use WidgetsBindingObserver as a mixin |
| [flutter/plugins#427](https://github.com/flutter/plugins/pull/427) | bump minor version and add changelog entry |
| [flutter/plugins#771](https://github.com/flutter/plugins/pull/771) | Add support for handling UserRecoverableAuthException |
| [flutter/plugins#701](https://github.com/flutter/plugins/pull/701) | Enable lint unnecessary_new |
| [flutter/plugins#779](https://github.com/flutter/plugins/pull/779) | Mark textureId as @visibleForTesting. |
| [flutter/plugins#769](https://github.com/flutter/plugins/pull/769) | Update VideoPlayer widget when the attached controller changes |
| [flutter/plugins#402](https://github.com/flutter/plugins/pull/402) | AdSizes for AdMob banner ads |
| [flutter/plugins#755](https://github.com/flutter/plugins/pull/755) | Add a gestureRecognizers parameter to GoogleMap. |
| [flutter/plugins#753](https://github.com/flutter/plugins/pull/753) |  Add an initialUrl and a javaScriptMode parameters to WebView |
| [flutter/plugins#767](https://github.com/flutter/plugins/pull/767) | Rebuild VideoPlayer once VideoPlayerController finishes initialization. |
| [flutter/plugins#732](https://github.com/flutter/plugins/pull/732) | Update Text Recognition for android to 17.0.0 |
| [flutter/plugins#764](https://github.com/flutter/plugins/pull/764) | Bump firebase_auth version number |
| [flutter/plugins#733](https://github.com/flutter/plugins/pull/733) | Update ios MLKit Text Recognition to '5.6.0' |
| [flutter/plugins#762](https://github.com/flutter/plugins/pull/762) | Removed Google Guava dependency from firebase_auth. |
| [flutter/plugins#760](https://github.com/flutter/plugins/pull/760) | Firestore FieldValues and persistence |
| [flutter/plugins#428](https://github.com/flutter/plugins/pull/428) | Updated to 0.5.0 |
| [flutter/plugins#685](https://github.com/flutter/plugins/pull/685) | Adding getWifiName to connectivity plugin |
| [flutter/plugins#672](https://github.com/flutter/plugins/pull/672) | Firebase Messaging: fix Android notification body and title not sent to Flutter |
| [flutter/plugins#650](https://github.com/flutter/plugins/pull/650) | listener on MobileAd shouldn't be final |
| [flutter/plugins#531](https://github.com/flutter/plugins/pull/531) | Add monitoring and management to uploads |
| [flutter/plugins#754](https://github.com/flutter/plugins/pull/754) | cloud_functions readme: Fix Dart source formatting |
| [flutter/plugins#752](https://github.com/flutter/plugins/pull/752) | Initial (Android only) implementation of a WebView widget. |
| [flutter/plugins#954](https://github.com/flutter/plugins/pull/954) | add new plugins reference to README.md |
| [flutter/plugins#430](https://github.com/flutter/plugins/pull/430) | [AndroidAlarmManager] Update README.md |
| [flutter/plugins#431](https://github.com/flutter/plugins/pull/431) | Cut a new firebase_database version with dart2 runtime cast fix |
| [flutter/plugins#749](https://github.com/flutter/plugins/pull/749) | Add support for firebaseopensource.com |
| [flutter/plugins#748](https://github.com/flutter/plugins/pull/748) | Use api to allow support-v4 version resolution. |
| [flutter/plugins#432](https://github.com/flutter/plugins/pull/432) | Fix Dart 2 type error |
| [flutter/plugins#746](https://github.com/flutter/plugins/pull/746) | Plugin template for webview_flutter |
| [flutter/plugins#745](https://github.com/flutter/plugins/pull/745) | Fix Java lint warnings in google maps plugin |
| [flutter/plugins#744](https://github.com/flutter/plugins/pull/744) | Remove iOS instructions from the maps plugin readme. |
| [flutter/plugins#686](https://github.com/flutter/plugins/pull/686) | [image_picker] White line after resize - fixed by adding floor() |
| [flutter/plugins#434](https://github.com/flutter/plugins/pull/434) | Fix Dart 2 runtime type error in msg handler |
| [flutter/plugins#715](https://github.com/flutter/plugins/pull/715) | Improve image_picker android dependencies |
| [flutter/plugins#665](https://github.com/flutter/plugins/pull/665) | Remove a wrong assertion. |
| [flutter/plugins#739](https://github.com/flutter/plugins/pull/739) | Release VideoPlayer resources when FlutterView is gone |
| [flutter/plugins#740](https://github.com/flutter/plugins/pull/740) | Updated README with examples |
| [flutter/plugins#638](https://github.com/flutter/plugins/pull/638) | Adds nonPersonalizedAds option to MobileAdTargetingInfo for firebase_admob |
| [flutter/plugins#429](https://github.com/flutter/plugins/pull/429) | Extend StandardMessageCodec for Cloud Firestore |
| [flutter/plugins#699](https://github.com/flutter/plugins/pull/699) | _dynamicLink variable was cleared after user get dynamicLink (iOS) |
| [flutter/plugins#710](https://github.com/flutter/plugins/pull/710) | Fixed type error for firebase_auth photoUrl on iOS |
| [flutter/plugins#736](https://github.com/flutter/plugins/pull/736) | Fix typo in Application.java url |
| [flutter/plugins#722](https://github.com/flutter/plugins/pull/722) | Add go to settings action in androidIntent plugin. |
| [flutter/plugins#737](https://github.com/flutter/plugins/pull/737) | upgraded com.google.firebase:firebase-messaging to v17.3.0 in order t… |
| [flutter/plugins#707](https://github.com/flutter/plugins/pull/707) | Add example query to Firestore documentation |
| [flutter/plugins#361](https://github.com/flutter/plugins/pull/361) | Batch support for Firestore |
| [flutter/plugins#725](https://github.com/flutter/plugins/pull/725) | Set MLVision pod version to avoid breaking changes |
| [flutter/plugins#438](https://github.com/flutter/plugins/pull/438) | Bump version for re-publication |
| [flutter/plugins#708](https://github.com/flutter/plugins/pull/708) | Fix cloud_functions README typo |
| [flutter/plugins#439](https://github.com/flutter/plugins/pull/439) | Fix Dart 2 runtime type errors |
| [flutter/plugins#443](https://github.com/flutter/plugins/pull/443) |  Secure fetchProvidersForEmail (no providers) |
| [flutter/plugins#713](https://github.com/flutter/plugins/pull/713) | [android_alarm_manager] Prep for version update |
| [flutter/plugins#656](https://github.com/flutter/plugins/pull/656) | Added iOS location plugin to showcase background execution APIs |
| [flutter/plugins#642](https://github.com/flutter/plugins/pull/642) | Updated `android_alarm_manager` to work after engine refactor. |
| [flutter/plugins#698](https://github.com/flutter/plugins/pull/698) | Enable lint unnecessary_statements. Codebase is compliant. |
| [flutter/plugins#702](https://github.com/flutter/plugins/pull/702) | fix missing_return warnings |
| [flutter/plugins#696](https://github.com/flutter/plugins/pull/696) | enable lint prefer_equal_for_default_values |
| [flutter/plugins#445](https://github.com/flutter/plugins/pull/445) | [Image Picker] Import foundation for the @required annotations |
| [flutter/plugins#694](https://github.com/flutter/plugins/pull/694) | Fix incomplete README for FirebaseAuth |
| [flutter/plugins#447](https://github.com/flutter/plugins/pull/447) | Remove yourcompany.com from cloud_firestore |
| [flutter/plugins#452](https://github.com/flutter/plugins/pull/452) | Hide Firestore codec class and move each class into separate files |
| [flutter/plugins#629](https://github.com/flutter/plugins/pull/629) | Update docs to reflect field paths separated by '.' |
| [flutter/plugins#673](https://github.com/flutter/plugins/pull/673) | Update barcode detector to handle options |
| [flutter/plugins#677](https://github.com/flutter/plugins/pull/677) | Android and iOS BarcodeDetector options |
| [flutter/plugins#684](https://github.com/flutter/plugins/pull/684) | Adding support for FirebaseUser.delete() |
| [flutter/plugins#668](https://github.com/flutter/plugins/pull/668) | Android and iOS implementations of LabelDetector |
| [flutter/plugins#683](https://github.com/flutter/plugins/pull/683) | Adding support to setLanguageCode to FirebaseAuth |
| [flutter/plugins#617](https://github.com/flutter/plugins/pull/617) | Add high resolution iOS photo capture for file |
| [flutter/plugins#681](https://github.com/flutter/plugins/pull/681) | Fix formatting |
| [flutter/plugins#675](https://github.com/flutter/plugins/pull/675) | Bump Firebase Android dependencies |
| [flutter/plugins#669](https://github.com/flutter/plugins/pull/669) | Update README with new detectors |
| [flutter/plugins#670](https://github.com/flutter/plugins/pull/670) | Fix auto phone auth |
| [flutter/plugins#661](https://github.com/flutter/plugins/pull/661) | Android and iOS FaceDetector |
| [flutter/plugins#667](https://github.com/flutter/plugins/pull/667) | Fix plugin formatting |
| [flutter/plugins#666](https://github.com/flutter/plugins/pull/666) | bump firebase_auth version |
| [flutter/plugins#606](https://github.com/flutter/plugins/pull/606) | Add phone auth |
| [flutter/plugins#662](https://github.com/flutter/plugins/pull/662) | Dart side of label detector |
| [flutter/plugins#454](https://github.com/flutter/plugins/pull/454) | Fix behavior of constructor for named FirebaseApps |
| [flutter/plugins#649](https://github.com/flutter/plugins/pull/649) | Dart side of face detector |
| [flutter/plugins#455](https://github.com/flutter/plugins/pull/455) | Fix FirebaseApp projectID on iOS |
| [flutter/plugins#646](https://github.com/flutter/plugins/pull/646) | Add barcode detector for ML Vision |
| [flutter/plugins#456](https://github.com/flutter/plugins/pull/456) | Bump Flutter SDK requirement |
| [flutter/plugins#444](https://github.com/flutter/plugins/pull/444) | Don't fail an incremental build if can't find a merge base |
| [flutter/plugins#453](https://github.com/flutter/plugins/pull/453) | [Image Picker] - Use native Android APIs for taking images with camera and remove unneeded 3rd party dependency |
| [flutter/plugins#620](https://github.com/flutter/plugins/pull/620) | Fixed bug in handleStopListeningAuthState method |
| [flutter/plugins#437](https://github.com/flutter/plugins/pull/437) | Enable playing video from local asset. |
| [flutter/plugins#462](https://github.com/flutter/plugins/pull/462) | Play video from file descriptor |
| [flutter/plugins#635](https://github.com/flutter/plugins/pull/635) | Configured macOS builds on Cirrus CI |
| [flutter/plugins#634](https://github.com/flutter/plugins/pull/634) | Fixed typo REVERSED_CLIENT_ID |
| [flutter/plugins#613](https://github.com/flutter/plugins/pull/613) | video_player: autodetect video source format |
| [flutter/plugins#624](https://github.com/flutter/plugins/pull/624) | video_player: Allow audio playback in silent mode |
| [flutter/plugins#622](https://github.com/flutter/plugins/pull/622) | video_player: frame accurate seekTo on iOS |
| [flutter/plugins#596](https://github.com/flutter/plugins/pull/596) | Add receiving dynamic link capability to Firebase Dynamic Links Plugin |
| [flutter/plugins#633](https://github.com/flutter/plugins/pull/633) | image_picker record video iOS needed setup README update |
| [flutter/plugins#636](https://github.com/flutter/plugins/pull/636) | Added comments explaining the time it take to see results |
| [flutter/plugins#632](https://github.com/flutter/plugins/pull/632) | Add cloud_functions to README |
| [flutter/plugins#623](https://github.com/flutter/plugins/pull/623) | Shard plugins for example app builds |
| [flutter/plugins#627](https://github.com/flutter/plugins/pull/627) | Setup ios text detector |
| [flutter/plugins#464](https://github.com/flutter/plugins/pull/464) | rename the analysis options file |
| [flutter/plugins#630](https://github.com/flutter/plugins/pull/630) | Fix CI breakage |
| [flutter/plugins#465](https://github.com/flutter/plugins/pull/465) | Fix firebase_database dependencies |
| [flutter/plugins#625](https://github.com/flutter/plugins/pull/625) | add didReceiveRegistrationToken to firebase_messaging on ios |
| [flutter/plugins#621](https://github.com/flutter/plugins/pull/621) | Make sensors package Dart 2 compliant. |
| [flutter/plugins#612](https://github.com/flutter/plugins/pull/612) | video_player: iOS add missing observer removals  |
| [flutter/plugins#614](https://github.com/flutter/plugins/pull/614) | Setup dart side of local MLVision text detection |
| [flutter/plugins#618](https://github.com/flutter/plugins/pull/618) | Add camera plugin to readme |
| [flutter/plugins#615](https://github.com/flutter/plugins/pull/615) | Ensure we fail if we cannot close file |
| [flutter/plugins#513](https://github.com/flutter/plugins/pull/513) | use temp image file in cacheDir to allow picking from remote resources |
| [flutter/plugins#463](https://github.com/flutter/plugins/pull/463) | Add map camera control API |
| [flutter/plugins#575](https://github.com/flutter/plugins/pull/575) | Add test for Remote Config setDefaults |
| [flutter/plugins#609](https://github.com/flutter/plugins/pull/609) | Initial 'flutter create' for Firebase ML Vision plugin |
| [flutter/plugins#610](https://github.com/flutter/plugins/pull/610) | Update metadata for republish |
| [flutter/plugins#600](https://github.com/flutter/plugins/pull/600) | image_picker fixes: file suffix, permissions redirect, video resize |
| [flutter/plugins#467](https://github.com/flutter/plugins/pull/467) | Added getAll for debugging purposes |
| [flutter/plugins#602](https://github.com/flutter/plugins/pull/602) | Hotfix - iOS Sharing |
| [flutter/plugins#468](https://github.com/flutter/plugins/pull/468) | Make shared preferences ready for publication |
| [flutter/plugins#477](https://github.com/flutter/plugins/pull/477) | Fix macOS build |
| [flutter/plugins#597](https://github.com/flutter/plugins/pull/597) | video_player: use exoplayer for better video compatibility |
| [flutter/plugins#594](https://github.com/flutter/plugins/pull/594) | Add Infowindow tapped handler; also rev. google play services to 15.+… |
| [flutter/plugins#471](https://github.com/flutter/plugins/pull/471) | Readme.md should use gender enum constants |
| [flutter/plugins#457](https://github.com/flutter/plugins/pull/457) | Add download url support to Firebase Storage plugin |
| [flutter/plugins#599](https://github.com/flutter/plugins/pull/599) | Fixed dynamic link dartdoc generation |
| [flutter/plugins#601](https://github.com/flutter/plugins/pull/601) | Add static_framework to Google Maps pod |
| [flutter/plugins#598](https://github.com/flutter/plugins/pull/598) | Fixed incorrect homepage link in pubspec. |
| [flutter/plugins#589](https://github.com/flutter/plugins/pull/589) | Upgrade Gradle dependencies to match Android Studio 3.1.2 |
| [flutter/plugins#459](https://github.com/flutter/plugins/pull/459) | Add unit test for firebase_list |
| [flutter/plugins#563](https://github.com/flutter/plugins/pull/563) | Firebase dynamic links |
| [flutter/plugins#474](https://github.com/flutter/plugins/pull/474) | Bump Flutter SDK constraint |
| [flutter/plugins#590](https://github.com/flutter/plugins/pull/590) | Fix test for sync-async semantics |
| [flutter/plugins#479](https://github.com/flutter/plugins/pull/479) | Add id getter to Firestore CollectionReference and move path getter to CollectionReference |
| [flutter/plugins#588](https://github.com/flutter/plugins/pull/588) | Consolidate .gitignore, remove Podfile |
| [flutter/plugins#583](https://github.com/flutter/plugins/pull/583) | Add video player buffering status |
| [flutter/plugins#577](https://github.com/flutter/plugins/pull/577) | Performance metrics |
| [flutter/plugins#585](https://github.com/flutter/plugins/pull/585) | Update .gitignore to new `dart_tool` pub cache |
| [flutter/plugins#586](https://github.com/flutter/plugins/pull/586) | Update README.md |
| [flutter/plugins#581](https://github.com/flutter/plugins/pull/581) | Firebase Messaging : On Android, when the Activity is killed, the onLaunch is never using the correct Intent |
| [flutter/plugins#580](https://github.com/flutter/plugins/pull/580) | Add stub for In-app purchase plugin |
| [flutter/plugins#579](https://github.com/flutter/plugins/pull/579) | Document Google OAuth scopes |
| [flutter/plugins#537](https://github.com/flutter/plugins/pull/537) | Release several FlutterFire plugins as 1.0 |
| [flutter/plugins#458](https://github.com/flutter/plugins/pull/458) | Camera plugin first working version (IOS and Android). |
| [flutter/plugins#509](https://github.com/flutter/plugins/pull/509) | Add gravity-independent-accelerometer support to sensors. |
| [flutter/plugins#576](https://github.com/flutter/plugins/pull/576) | google_maps_flutter: Remove listeners on State.dispose |
| [flutter/plugins#573](https://github.com/flutter/plugins/pull/573) | Added App name data to package_info |
| [flutter/plugins#574](https://github.com/flutter/plugins/pull/574) | Remove useless code that was causing a crash |
| [flutter/plugins#555](https://github.com/flutter/plugins/pull/555) |  Await RemoteConfig#setDefaults and README fix |
| [flutter/plugins#478](https://github.com/flutter/plugins/pull/478) | Support for firebase_core interoperating with native code that configures Firebase apps. |
| [flutter/plugins#466](https://github.com/flutter/plugins/pull/466) | Prevent crash on share on iPads, add ability to specify popover origin |
| [flutter/plugins#567](https://github.com/flutter/plugins/pull/567) | Fixed a bug that may not be initialized after video started on iOS |
| [flutter/plugins#482](https://github.com/flutter/plugins/pull/482) | Fix firebase_core project id on Android. |
| [flutter/plugins#556](https://github.com/flutter/plugins/pull/556) | Support custom metadata |
| [flutter/plugins#571](https://github.com/flutter/plugins/pull/571) | Update metadata for republication |
| [flutter/plugins#570](https://github.com/flutter/plugins/pull/570) | Update metadata for republish |
| [flutter/plugins#558](https://github.com/flutter/plugins/pull/558) | Adding null-safe handling for Transaction.get in cloud_firestore |
| [flutter/plugins#561](https://github.com/flutter/plugins/pull/561) | New with 0.7 |
| [flutter/plugins#569](https://github.com/flutter/plugins/pull/569) | Fix for iOS Firebase API changes |
| [flutter/plugins#481](https://github.com/flutter/plugins/pull/481) | Add marker API |
| [flutter/plugins#559](https://github.com/flutter/plugins/pull/559) | upgrade cocoapods on travis |
| [flutter/plugins#557](https://github.com/flutter/plugins/pull/557) | Fix for 2 bugs in the Video Player plugin |
| [flutter/plugins#560](https://github.com/flutter/plugins/pull/560) | Don't do reentrant message channel calls. |
| [flutter/plugins#542](https://github.com/flutter/plugins/pull/542) | Add signinWithTwitter |
| [flutter/plugins#476](https://github.com/flutter/plugins/pull/476) | Multiple app support for cloud_firestore |
| [flutter/plugins#550](https://github.com/flutter/plugins/pull/550) | Split files into library structure. |
| [flutter/plugins#552](https://github.com/flutter/plugins/pull/552) | Add missing await to for transaction in Firestore README |
| [flutter/plugins#551](https://github.com/flutter/plugins/pull/551) | Update Remote Config plugin |
| [flutter/plugins#376](https://github.com/flutter/plugins/pull/376) | Support for Query.getDocuments |
| [flutter/plugins#485](https://github.com/flutter/plugins/pull/485) | Add required argument to Firebase admob readme |
| [flutter/plugins#545](https://github.com/flutter/plugins/pull/545) | API documentation and cleanup |
| [flutter/plugins#546](https://github.com/flutter/plugins/pull/546) | Add firebase_remote_config to the README.md and FlutterFire.md |
| [flutter/plugins#548](https://github.com/flutter/plugins/pull/548) | Update firebase storage version and changelog |
| [flutter/plugins#532](https://github.com/flutter/plugins/pull/532) | Add timeout properties to FirebaseStorage |
| [flutter/plugins#442](https://github.com/flutter/plugins/pull/442) | Add Firestore transaction example |
| [flutter/plugins#536](https://github.com/flutter/plugins/pull/536) | Breaking changes to signature of snapshots and setData in cloud_firestore |
| [flutter/plugins#533](https://github.com/flutter/plugins/pull/533) | Add support to reload firebase user |
| [flutter/plugins#473](https://github.com/flutter/plugins/pull/473) | Add Blob support to cloud_firestore |
| [flutter/plugins#492](https://github.com/flutter/plugins/pull/492) | Add ui configuration api |
| [flutter/plugins#493](https://github.com/flutter/plugins/pull/493) | Add constructor VideoPlayerController.file() |
| [flutter/plugins#530](https://github.com/flutter/plugins/pull/530) | Add test for Facebook link |
| [flutter/plugins#529](https://github.com/flutter/plugins/pull/529) | Add send verification mail |
| [flutter/plugins#527](https://github.com/flutter/plugins/pull/527) | Add link with facebook credential |
| [flutter/plugins#524](https://github.com/flutter/plugins/pull/524) | Support FirebaseStorage with custom Firebase app |
| [flutter/plugins#498](https://github.com/flutter/plugins/pull/498) | Updated plugin channel names to 'plugins.flutter.io/<plugin>' |
| [flutter/plugins#495](https://github.com/flutter/plugins/pull/495) | Firestore readme fix |
| [flutter/plugins#496](https://github.com/flutter/plugins/pull/496) | Video player fix for looping value on ios |
| [flutter/plugins#521](https://github.com/flutter/plugins/pull/521) | Run java tests on Travis and Cirrus CI |
| [flutter/plugins#517](https://github.com/flutter/plugins/pull/517) | Remove Gradle artifacts from repo |
| [flutter/plugins#499](https://github.com/flutter/plugins/pull/499) | AdMob Readme Enhancement |
| [flutter/plugins#511](https://github.com/flutter/plugins/pull/511) | Add ability to create games sign in |
| [flutter/plugins#461](https://github.com/flutter/plugins/pull/461) | [Image Picker] Add tests to Android implementation of ImagePickerDelegate |
| [flutter/plugins#502](https://github.com/flutter/plugins/pull/502) | Add some support for StorageMetadata. |
| [flutter/plugins#514](https://github.com/flutter/plugins/pull/514) | Fix Dart2 Types |
| [flutter/plugins#516](https://github.com/flutter/plugins/pull/516) | Add flutter_test dev dependency |
| [flutter/plugins#512](https://github.com/flutter/plugins/pull/512) | Support StorageReference#writeToFile |
| [flutter/plugins#515](https://github.com/flutter/plugins/pull/515) | Add environment to Remote Config plugin pubspec |
| [flutter/plugins#391](https://github.com/flutter/plugins/pull/391) | Add Firebase Remote Config support |
| [flutter/plugins#510](https://github.com/flutter/plugins/pull/510) | Add more getters to StorageReference |
| [flutter/plugins#505](https://github.com/flutter/plugins/pull/505) | [Image Picker] - Add contract tests for Android implementation |
| [flutter/plugins#506](https://github.com/flutter/plugins/pull/506) | Add support for putData on StorageReference |
| [flutter/plugins#504](https://github.com/flutter/plugins/pull/504) | Allow null value for startAt, endAt and equalTo database queries on Android |
| [flutter/plugins#503](https://github.com/flutter/plugins/pull/503) | Add support for updateMetadata |
| [flutter/plugins#538](https://github.com/flutter/plugins/pull/538) | Graduate admob plugin to beta |
