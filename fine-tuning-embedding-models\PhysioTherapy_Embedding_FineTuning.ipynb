{"cells": [{"cell_type": "markdown", "id": "0e544b3e", "metadata": {}, "source": ["# Fine-Tuning Embeddings for Physiotherapy Domain\n", "\n", "This notebook implements a specialized embedding model for the physiotherapy medical domain using transfer learning from the `all-MiniLM-L6-v2` base model. The implementation consists of two main phases:\n", "\n", "1. Data Generation, Curation and Preparation\n", "2. Domain-Specific Embedding Model Development\n", "\n", "Let's start by installing the required dependencies:"]}, {"cell_type": "code", "execution_count": null, "id": "27726154", "metadata": {}, "outputs": [], "source": ["!pip install sentence-transformers datasets scikit-learn torch transformers nltk spacy graphviz matp<PERSON><PERSON>b seaborn -q\n", "!python -m spacy download pt_core_news_sm"]}, {"cell_type": "markdown", "id": "16017b2a", "metadata": {}, "source": ["## Phase 1: Data Generation, Curation and Preparation\n", "\n", "In this phase we will:\n", "1. Generate/curate physiotherapy domain corpus\n", "2. Implement preprocessing pipeline\n", "3. Prepare training data\n", "\n", "First, let's import the required libraries:"]}, {"cell_type": "code", "execution_count": null, "id": "ca411d32", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "from typing import List, Dict, Tuple\n", "import spacy\n", "import nltk\n", "from nltk.tokenize import sent_tokenize\n", "from transformers import pipeline\n", "from torch.optim import AdamW\n", "from torch.utils.data import DataLoader\n", "import matplotlib.pyplot as plt\n", "\n", "# Download required NLTK data\n", "nltk.download('punkt')\n", "\n", "# Carrega o modelo spaCy em português\n", "nlp = spacy.load('pt_core_news_sm')"]}, {"cell_type": "markdown", "id": "cd2b36a4", "metadata": {}, "source": ["### 1.1 Coleta de Dados e Geração de Dados Sintéticos\n", "\n", "Vamos criar uma classe para lidar com a geração de dados sintéticos para diferentes especialidades fisioterapêuticas:"]}, {"cell_type": "code", "execution_count": null, "id": "77982a2e", "metadata": {}, "outputs": [], "source": ["class PhysioDataGenerator:\n", "    def __init__(self):\n", "        self.specialties = {\n", "            'ortopedica': ['mobilização articular', 'terapia manual', 'exercício terapêuti<PERSON>', 'avaliação postural', 'alongamento muscular'],\n", "            'neurologica': ['treino de equilíbrio', 'an<PERSON><PERSON><PERSON> da marcha', 'controle motor', 'integração sensorial', 'coordenação motora'],\n", "            'cardiorrespiratoria': ['exercícios respiratórios', 'fisioterapia torácica', 'reabilitação pulmonar', 'drenagem postural'],\n", "            'pediatrica': ['avaliação do desenvolvimento', 'terapia lúdica', 'desenvolvimento motor', 'estimulação precoce'],\n", "            'geriatrica': ['prevenção de quedas', 'assistência à mobilidade', 'treino de equilíbrio', 'fortalecimento muscular']\n", "        }\n", "        \n", "    def generate_synthetic_document(self, specialty: str) -> str:\n", "        \"\"\"Gera um documento sintético para uma especialidade específica\"\"\"\n", "        # Este é um placeholder - na prática você usaria métodos mais sofisticados\n", "        # de geração de texto ou documentos reais curados\n", "        procedures = self.specialties.get(specialty, [])\n", "        document = f\"Avaliação e tratamento fisioterapêutico na especialidade {specialty}. \"\n", "        document += f\"Os procedimentos comuns incluem: {', '.join(procedures)}\"\n", "        return document\n", "    \n", "    def generate_corpus(self, num_documents: int = 100) -> List[str]:\n", "        \"\"\"Gera um corpus de documentos fisioterapêuticos\"\"\"\n", "        corpus = []\n", "        for _ in range(num_documents):\n", "            specialty = np.random.choice(list(self.specialties.keys()))\n", "            document = self.generate_synthetic_document(specialty)\n", "            corpus.append(document)\n", "        return corpus"]}, {"cell_type": "markdown", "id": "1f3b3f7c", "metadata": {}, "source": ["### 1.2 Data Preprocessing Pipeline"]}, {"cell_type": "code", "execution_count": null, "id": "13fa1a78", "metadata": {}, "outputs": [], "source": ["class PhysioPreprocessor:\n", "    def __init__(self):\n", "        self.nlp = spacy.load('en_core_web_sm')\n", "    \n", "    def preprocess_text(self, text: str) -> str:\n", "        \"\"\"Pré-processa um documento de texto\"\"\"\n", "        # Remove informações pessoalmente identificáveis\n", "        doc = self.nlp(text)\n", "        text = ' '.join([token.text for token in doc if not token.ent_type_ in ['PER', 'ORG']])\n", "        \n", "        # Normaliza o texto\n", "        text = text.lower()\n", "        \n", "        # Remove espaços em branco extras\n", "        text = ' '.join(text.split())\n", "        \n", "        return text\n", "    \n", "    def create_training_pairs(self, corpus: List[str]) -> List[Tuple[str, str, float]]:\n", "        \"\"\"Create training pairs for similarity learning\"\"\"\n", "        training_pairs = []\n", "        \n", "        # Split documents into sentences\n", "        sentences = []\n", "        for doc in corpus:\n", "            sentences.extend(sent_tokenize(doc))\n", "        \n", "        # Create positive pairs (sentences from same document)\n", "        for doc in corpus:\n", "            doc_sentences = sent_tokenize(doc)\n", "            if len(doc_sentences) >= 2:\n", "                for i in range(len(doc_sentences)-1):\n", "                    training_pairs.append((doc_sentences[i], doc_sentences[i+1], 1.0))\n", "        \n", "        # Create negative pairs (random sentences from different documents)\n", "        for _ in range(len(training_pairs)):\n", "            sent1, sent2 = np.random.choice(sentences, 2, replace=False)\n", "            training_pairs.append((sent1, sent2, 0.0))\n", "        \n", "        return training_pairs"]}, {"cell_type": "markdown", "id": "41320e6c", "metadata": {}, "source": ["Let's generate our training data:"]}, {"cell_type": "code", "execution_count": null, "id": "3e5dddbc", "metadata": {}, "outputs": [], "source": ["# Gera corpus sintético\n", "data_generator = PhysioDataGenerator()\n", "corpus = data_generator.generate_corpus(num_documents=1000)\n", "\n", "# Pré-processa o corpus\n", "preprocessor = PhysioPreprocessor()\n", "processed_corpus = [preprocessor.preprocess_text(doc) for doc in corpus]\n", "\n", "# Cria pares de treinamento\n", "training_pairs = preprocessor.create_training_pairs(processed_corpus)\n", "\n", "print(f\"Gerados {len(training_pairs)} pares de treinamento\")"]}, {"cell_type": "markdown", "id": "83c3325f", "metadata": {}, "source": ["## Phase 2: Domain-Specific Embedding Model Development\n", "\n", "Now we'll fine-tune the base model using our physiotherapy domain data:"]}, {"cell_type": "code", "execution_count": null, "id": "0e53a0d8", "metadata": {}, "outputs": [], "source": ["from sentence_transformers import SentenceTransformer, InputExample, losses\n", "from torch.utils.data import DataLoader\n", "\n", "# Initialize the base model\n", "model = SentenceTransformer('all-MiniLM-L6-v2')\n", "\n", "# Prepare training examples\n", "train_examples = [InputExample(texts=[p[0], p[1]], label=p[2]) for p in training_pairs]\n", "train_dataloader = DataLoader(train_examples, shuffle=True, batch_size=16)\n", "\n", "# Define the training loss\n", "train_loss = losses.CosineSimilarityLoss(model)\n", "\n", "# Train the model\n", "model.fit(train_objectives=[(train_dataloader, train_loss)],\n", "         epochs=3,\n", "         warmup_steps=100,\n", "         show_progress_bar=True)"]}, {"cell_type": "markdown", "id": "131477e3", "metadata": {}, "source": ["### Model Evaluation"]}, {"cell_type": "code", "execution_count": null, "id": "bb605311", "metadata": {}, "outputs": [], "source": ["def evaluate_model(model, test_pairs):\n", "    \"\"\"Evaluate model performance on test pairs\"\"\"\n", "    correct = 0\n", "    total = len(test_pairs)\n", "    \n", "    for sent1, sent2, label in test_pairs:\n", "        # Compute embeddings\n", "        emb1 = model.encode(sent1)\n", "        emb2 = model.encode(sent2)\n", "        \n", "        # Compute similarity\n", "        similarity = np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))\n", "        \n", "        # Compare with label\n", "        pred_label = 1.0 if similarity > 0.5 else 0.0\n", "        if pred_label == label:\n", "            correct += 1\n", "    \n", "    return correct / total\n", "\n", "# Split pairs into train/test\n", "test_size = int(0.2 * len(training_pairs))\n", "test_pairs = training_pairs[:test_size]\n", "\n", "# Evaluate model\n", "accuracy = evaluate_model(model, test_pairs)\n", "print(f\"Model accuracy: {accuracy:.2f}\")"]}, {"cell_type": "markdown", "id": "8a648c8f", "metadata": {}, "source": ["### Save the Fine-tuned Model"]}, {"cell_type": "code", "execution_count": null, "id": "a7a4428f", "metadata": {}, "outputs": [], "source": ["# Save the model\n", "model.save('physio-embedding-model')\n", "\n", "print(\"Model saved successfully!\")"]}, {"cell_type": "markdown", "id": "fe8086ff", "metadata": {}, "source": ["### Carregando e Testando o Modelo Salvo\n", "\n", "Vamos carregar o modelo e testá-lo com alguns exemplos de textos fisioterapêuticos:"]}, {"cell_type": "code", "execution_count": null, "id": "75a89728", "metadata": {}, "outputs": [], "source": ["from sentence_transformers import SentenceTransformer, util\n", "\n", "# Carrega o modelo salvo\n", "model_carregado = SentenceTransformer('physio-embedding-model')\n", "\n", "# Textos de exemplo para teste\n", "textos_teste = [\n", "    \"Paciente apresenta dor lombar crônica e limitação de movimento\",\n", "    \"Avaliação de dor na região da coluna lombar com restrição de mobilidade\",\n", "    \"Tratamento de fratura no tornozelo direito\",\n", "    \"Exercícios respiratórios para paciente com DPOC\",\n", "    \"Programa de reabilitação cardíaca pós-infarto\"\n", "]\n", "\n", "# Calcula embeddings para todos os textos\n", "embeddings = model_carregado.encode(textos_teste)\n", "\n", "# Testa similaridade entre os textos\n", "print(\"Testando similaridades entre textos:\\n\")\n", "\n", "# Compara os dois primeiros textos (devem ser similares - ambos sobre dor lombar)\n", "similaridade = util.cos_sim(embeddings[0], embeddings[1])\n", "print(f\"Similaridade entre textos sobre dor lombar: {similaridade[0][0]:.4f}\")\n", "\n", "# Compara texto de dor lombar com reabilitação cardíaca (devem ser diferentes)\n", "similaridade = util.cos_sim(embeddings[0], embeddings[4])\n", "print(f\"Similaridade entre dor lombar e reabilitação cardíaca: {similaridade[0][0]:.4f}\")\n", "\n", "# Compara os dois textos sobre problemas respiratórios/cardíacos (devem ter alguma similaridade)\n", "similaridade = util.cos_sim(embeddings[3], embeddings[4])\n", "print(f\"Similaridade entre DPOC e reabilitação cardíaca: {similaridade[0][0]:.4f}\")"]}, {"cell_type": "markdown", "id": "48bf9336", "metadata": {}, "source": ["### Buscando Textos Similares\n", "\n", "Agora vamos testar a busca de textos similares a uma consulta específica:"]}, {"cell_type": "code", "execution_count": null, "id": "d9e21284", "metadata": {}, "outputs": [], "source": ["# Consulta de exemplo\n", "consulta = \"Paciente idoso com histórico de quedas frequentes necessita avaliação\"\n", "\n", "# Codifica a consulta\n", "consulta_embedding = model_carregado.encode(consulta)\n", "\n", "# Calcula similaridade com todos os textos de teste\n", "similaridades = util.cos_sim(consulta_embedding, embeddings)\n", "\n", "# Mostra os resultados ordenados por similaridade\n", "print(\"Resultados para a consulta:\", consulta, \"\\n\")\n", "resultados_ordenados = sorted([(score, texto) for score, texto in zip(similaridades[0], textos_teste)], reverse=True)\n", "\n", "for score, texto in resultados_ordenados:\n", "    print(f\"Similaridade {score:.4f}: {texto}\")"]}, {"cell_type": "markdown", "id": "c5fb9c19", "metadata": {}, "source": ["## Visualizações do Pipeline e Resultados\n", "\n", "### Fluxograma do Pipeline\n", "Vamos criar um fluxograma visual do nosso pipeline de fine-tuning:"]}, {"cell_type": "code", "execution_count": null, "id": "c8694a2f", "metadata": {}, "outputs": [], "source": ["import graphviz\n", "\n", "# Cria um novo grafo direcionado\n", "dot = graphviz.Digraph(comment='Pipeline de Fine-tuning para Embeddings Fisioterapêuticos')\n", "dot.attr(rankdir='TB')\n", "\n", "# Adiciona nós para as fases principais\n", "with dot.subgraph(name='cluster_0') as c:\n", "    c.attr(label='Fase 1: Geração e Preparação de Dados')\n", "    c.attr(style='filled', color='lightgrey')\n", "    c.node('dados_sinteticos', 'Geração de\\nDados Sintéticos')\n", "    c.node('preprocessamento', 'Pré-processamento\\nde Texto')\n", "    c.node('pares_treino', 'Criação de\\nPares de Treino')\n", "    \n", "    # Conexões dentro da F<PERSON> 1\n", "    c.edge('dados_sinteticos', 'preprocessamento')\n", "    c.edge('preprocessamento', 'pares_treino')\n", "\n", "with dot.subgraph(name='cluster_1') as c:\n", "    c.attr(label='Fase 2: Desenvolvimento do Modelo')\n", "    c.attr(style='filled', color='lightblue')\n", "    c.node('modelo_base', 'Modelo Base\\nall-MiniLM-L6-v2')\n", "    c.node('fine_tuning', 'Fine-tuning')\n", "    c.node('avaliacao', 'Avaliação')\n", "    c.node('modelo_final', 'Modelo\\nEspecializado')\n", "    \n", "    # Conexõ<PERSON> den<PERSON> da <PERSON> 2\n", "    c.edge('modelo_base', 'fine_tuning')\n", "    c.edge('fine_tuning', 'avaliacao')\n", "    c.edge('avaliacao', 'modelo_final')\n", "\n", "# Conexõ<PERSON> entre as fases\n", "dot.edge('pares_treino', 'fine_tuning')\n", "\n", "# Renderiza o gráfico\n", "dot.render('pipeline_fluxograma', format='png', cleanup=True)\n", "print(\"Fluxograma salvo como 'pipeline_fluxograma.png'\")"]}, {"cell_type": "markdown", "id": "5596443f", "metadata": {}, "source": ["### Visualização dos Resultados de Similaridade\n", "Vamos criar gráficos para visualizar as similaridades entre os diferentes tipos de textos:"]}, {"cell_type": "code", "execution_count": null, "id": "ae016d1e", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "\n", "# Configurações visuais\n", "plt.style.use('seaborn')\n", "plt.figure(figsize=(12, 6))\n", "\n", "# Cria matriz de similaridade para todos os textos\n", "matriz_similaridade = util.cos_sim(embeddings, embeddings).numpy()\n", "\n", "# Cria mapa de calor\n", "sns.heatmap(matriz_similaridade, \n", "            annot=True, \n", "            cmap='YlOrRd', \n", "            xticklabels=['Lombar 1', 'Lombar 2', 'Tor<PERSON>zel<PERSON>', 'DPOC', 'Cardíaco'],\n", "            yticklabels=['Lombar 1', 'Lombar 2', 'Tor<PERSON>zel<PERSON>', 'DPOC', 'Cardíaco'],\n", "            fmt='.2f')\n", "\n", "plt.title('<PERSON><PERSON> de Similaridade entre Textos Fisioterapêuticos')\n", "plt.tight_layout()\n", "plt.savefig('matriz_similaridade.png')\n", "plt.close()\n", "\n", "# Gráfico de barras para similaridades específicas\n", "categorias = ['<PERSON><PERSON><PERSON> vs Lombar', 'Lombar vs Cardíaco', 'DPOC vs Cardíaco']\n", "similaridades = [\n", "    similaridade_lombar := matriz_similaridade[0,1],\n", "    similaridade_lombar_cardiaco := matriz_similaridade[0,4],\n", "    similaridade_dpoc_cardiaco := matriz_similaridade[3,4]\n", "]\n", "\n", "plt.figure(figsize=(10, 5))\n", "bars = plt.bar(categorias, similaridades)\n", "plt.title('Comparação de Similaridades entre Diferentes Tipos de Textos')\n", "plt.ylabel('Similaridade (Cosine)')\n", "plt.ylim(0, 1)\n", "\n", "# Adiciona valores nas barras\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height,\n", "             f'{height:.2f}',\n", "             ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.savefig('comparacao_similaridades.png')\n", "plt.close()\n", "\n", "print(\"Gráficos salvos como 'matriz_similaridade.png' e 'comparacao_similaridades.png'\")"]}, {"cell_type": "markdown", "id": "9abaad45", "metadata": {}, "source": ["### Comparação entre Modelo Original e Fine-tuned\n", "\n", "Vamos carregar o modelo original e comparar seu desempenho com nosso modelo especializado:"]}, {"cell_type": "code", "execution_count": null, "id": "afb0d26d", "metadata": {}, "outputs": [], "source": ["# Carrega o modelo original\n", "modelo_original = SentenceTransformer('all-MiniLM-L6-v2')\n", "\n", "# Calcula embeddings com ambos os modelos\n", "embeddings_original = modelo_original.encode(textos_teste)\n", "embeddings_finetuned = model_carregado.encode(textos_teste)\n", "\n", "# Função para calcular matriz de similaridade\n", "def calcular_matriz_similaridade(embeddings):\n", "    return util.cos_sim(embeddings, embeddings).numpy()\n", "\n", "# Calcula matrizes de similaridade\n", "matriz_original = calcular_matriz_similaridade(embeddings_original)\n", "matriz_finetuned = calcular_matriz_similaridade(embeddings_finetuned)\n", "\n", "# Configuração para visualização lado a lado\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))\n", "\n", "# Mapa de calor para modelo original\n", "sns.heatmap(matriz_original, \n", "            annot=True, \n", "            cmap='YlOrRd', \n", "            xticklabels=['Lombar 1', 'Lombar 2', 'Tor<PERSON>zel<PERSON>', 'DPOC', 'Cardíaco'],\n", "            yticklabels=['Lombar 1', 'Lombar 2', 'Tor<PERSON>zel<PERSON>', 'DPOC', 'Cardíaco'],\n", "            fmt='.2f',\n", "            ax=ax1)\n", "ax1.set_title('Similaridades - Modelo Original')\n", "\n", "# Mapa de calor para modelo fine-tuned\n", "sns.heatmap(matriz_finetuned, \n", "            annot=True, \n", "            cmap='YlOrRd', \n", "            xticklabels=['Lombar 1', 'Lombar 2', 'Tor<PERSON>zel<PERSON>', 'DPOC', 'Cardíaco'],\n", "            yticklabels=['Lombar 1', 'Lombar 2', 'Tor<PERSON>zel<PERSON>', 'DPOC', 'Cardíaco'],\n", "            fmt='.2f',\n", "            ax=ax2)\n", "ax2.set_title('Similaridades - Modelo Fine-tuned')\n", "\n", "plt.tight_layout()\n", "plt.savefig('comparacao_modelos.png')\n", "plt.close()\n", "\n", "# Gráfico de barras comparativo para casos específicos\n", "fig, ax = plt.subplots(figsize=(12, 6))\n", "\n", "casos = ['<PERSON><PERSON><PERSON> vs Lombar', 'Lombar vs Cardíaco', 'DPOC vs Cardíaco']\n", "original_scores = [\n", "    matriz_original[0,1],\n", "    matriz_original[0,4],\n", "    matriz_original[3,4]\n", "]\n", "finetuned_scores = [\n", "    matriz_finetuned[0,1],\n", "    matriz_finetuned[0,4],\n", "    matriz_finetuned[3,4]\n", "]\n", "\n", "x = np.arange(len(casos))\n", "width = 0.35\n", "\n", "ax.bar(x - width/2, original_scores, width, label='Modelo Original')\n", "ax.bar(x + width/2, finetuned_scores, width, label='Modelo Fine-tuned')\n", "\n", "ax.set_ylabel('Similaridade (Cosine)')\n", "ax.set_title('Comparação de Similaridades entre Modelos')\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(casos)\n", "ax.legend()\n", "\n", "# Adiciona valores nas barras\n", "def autolabel(rects):\n", "    for rect in rects:\n", "        height = rect.get_height()\n", "        ax.annotate(f'{height:.2f}',\n", "                    xy=(rect.get_x() + rect.get_width()/2, height),\n", "                    xytext=(0, 3),  # 3 points vertical offset\n", "                    textcoords=\"offset points\",\n", "                    ha='center', va='bottom')\n", "\n", "rects1 = ax.bar(x - width/2, original_scores, width, label='Modelo Original')\n", "rects2 = ax.bar(x + width/2, finetuned_scores, width, label='Modelo Fine-tuned')\n", "autolabel(rects1)\n", "autolabel(rects2)\n", "\n", "plt.tight_layout()\n", "plt.savefig('comparacao_similaridades_modelos.png')\n", "plt.close()\n", "\n", "print(\"Análise comparativa dos modelos:\")\n", "for caso, orig, fine in zip(casos, original_scores, finetuned_scores):\n", "    diff = fine - orig\n", "    print(f\"\\n{caso}:\")\n", "    print(f\"  Modelo Original: {orig:.4f}\")\n", "    print(f\"  Modelo Fine-tuned: {fine:.4f}\")\n", "    print(f\"  Diferença: {diff:+.4f}\")"]}, {"cell_type": "markdown", "id": "e02b6961", "metadata": {}, "source": ["### Avaliação Quantitativa\n", "\n", "Vamos calcular algumas métricas quantitativas para comparar os modelos:"]}, {"cell_type": "code", "execution_count": null, "id": "7c8d4a83", "metadata": {}, "outputs": [], "source": ["# Função para calcular métricas\n", "def calcular_metricas_modelo(embeddings):\n", "    matriz_sim = util.cos_sim(embeddings, embeddings).numpy()\n", "    \n", "    # Métrica 1: Contraste entre similaridades esperadas (alta vs baixa)\n", "    contraste = (matriz_sim[0,1] - matriz_sim[0,4])  # diff entre lombar-lombar e lombar-cardíaco\n", "    \n", "    # Métrica 2: <PERSON>er<PERSON>ncia entre casos similares\n", "    coerencia = np.mean([matriz_sim[0,1], matriz_sim[3,4]])  # média das similaridades esperadas\n", "    \n", "    # Métrica 3: Distinção entre especialidades diferentes\n", "    distincao = 1 - np.mean([matriz_sim[0,4], matriz_sim[1,4], matriz_sim[2,3]])  # diferença média entre especialidades\n", "    \n", "    return {\n", "        'contraste': contraste,\n", "        'coerencia': coerencia,\n", "        'distincao': distincao\n", "    }\n", "\n", "# Calcula métricas para ambos os modelos\n", "metricas_original = calcular_metricas_modelo(embeddings_original)\n", "metricas_finetuned = calcular_metricas_modelo(embeddings_finetuned)\n", "\n", "# <PERSON><PERSON> as m<PERSON><PERSON><PERSON>\n", "metricas = ['Con<PERSON><PERSON>', 'Coerência', 'Distin<PERSON>']\n", "valores_original = [metricas_original[k] for k in ['contraste', 'coerencia', 'distincao']]\n", "valores_finetuned = [metricas_finetuned[k] for k in ['contraste', 'coerencia', 'distincao']]\n", "\n", "plt.figure(figsize=(10, 6))\n", "x = np.arange(len(metricas))\n", "width = 0.35\n", "\n", "plt.bar(x - width/2, valores_original, width, label='Modelo Original')\n", "plt.bar(x + width/2, valores_finetuned, width, label='Modelo Fine-tuned')\n", "\n", "plt.ylabel('Pontuação')\n", "plt.title('Comparação de Métricas entre Modelos')\n", "plt.xticks(x, metricas)\n", "plt.legend()\n", "\n", "# Adiciona valores nas barras\n", "for i, v in enumerate(valores_original):\n", "    plt.text(i - width/2, v, f'{v:.2f}', ha='center', va='bottom')\n", "for i, v in enumerate(valores_finetuned):\n", "    plt.text(i + width/2, v, f'{v:.2f}', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.savefig('comparacao_metricas.png')\n", "plt.close()\n", "\n", "print(\"\\nMétricas de Avaliação:\")\n", "for metrica, orig, fine in zip(metricas, valores_original, valores_finetuned):\n", "    diff = fine - orig\n", "    print(f\"\\n{metrica}:\")\n", "    print(f\"  Modelo Original: {orig:.4f}\")\n", "    print(f\"  Modelo Fine-tuned: {fine:.4f}\")\n", "    print(f\"  Melhoria: {diff:+.4f} ({(diff/orig)*100:+.1f}%)\")"]}, {"cell_type": "markdown", "id": "d993c619", "metadata": {}, "source": ["## Monitoramento do Processo de Fine-tuning\n", "\n", "Vamos adicionar monitoramento do processo de treinamento para acompanhar a evolução do modelo:"]}, {"cell_type": "code", "execution_count": null, "id": "7511483b", "metadata": {}, "outputs": [], "source": ["import torch.nn as nn\n", "from transformers import get_linear_schedule_with_warmup\n", "from torch.optim import AdamW\n", "from torch.utils.data import DataLoader\n", "import matplotlib.pyplot as plt\n", "\n", "# Define cabeça de classificação\n", "class ClassificationHead(nn.Module):\n", "    def __init__(self, embedding_dim, num_classes):\n", "        super(<PERSON><PERSON><PERSON>, self).__init__()\n", "        self.linear = nn.Linear(embedding_dim, num_classes)\n", "        \n", "    def forward(self, features):\n", "        x = features['sentence_embedding']\n", "        x = self.linear(x)\n", "        return x\n", "\n", "# Número de classes (especialidades fisioterapêuticas)\n", "num_classes = len(data_generator.specialties)\n", "classification_head = ClassificationHead(model.get_sentence_embedding_dimension(), num_classes)\n", "\n", "# Combina o modelo de embedding com a cabeça de classificação\n", "class SentenceTransformerWithHead(nn.Module):\n", "    def __init__(self, transformer, head):\n", "        super(SentenceTransformerWithHead, self).__init__()\n", "        self.transformer = transformer\n", "        self.head = head\n", "    \n", "    def forward(self, input):\n", "        features = self.transformer(input)\n", "        logits = self.head(features)\n", "        return logits\n", "\n", "model_with_head = SentenceTransformerWithHead(model, classification_head)\n", "\n", "# Parâmetros de treinamento otimizados\n", "train_params = {\n", "    'num_epochs': 5,\n", "    'batch_size': 16,\n", "    'learning_rate': 2e-5,\n", "    'warmup_steps': 100,\n", "    'weight_decay': 0.01\n", "}\n", "\n", "# Prepara os dados de treino\n", "train_examples = [InputExample(texts=[doc], label=idx) \n", "                 for doc, idx in zip(processed_corpus, range(len(processed_corpus)))]\n", "\n", "# Função de coleta personalizada\n", "def collate_fn(batch):\n", "    texts = [example.texts[0] for example in batch]\n", "    labels = torch.tensor([example.label for example in batch])\n", "    return texts, labels\n", "\n", "train_dataloader = DataLoader(train_examples, \n", "                            shuffle=True, \n", "                            batch_size=train_params['batch_size'],\n", "                            collate_fn=collate_fn)\n", "\n", "# Otimizador com weight decay\n", "optimizer = AdamW(model_with_head.parameters(),\n", "                 lr=train_params['learning_rate'],\n", "                 weight_decay=train_params['weight_decay'])\n", "\n", "# Scheduler com warmup\n", "total_steps = len(train_dataloader) * train_params['num_epochs']\n", "scheduler = get_linear_schedule_with_warmup(optimizer,\n", "                                          num_warmup_steps=train_params['warmup_steps'],\n", "                                          num_training_steps=total_steps)\n", "\n", "# Treinamento com monitoramento\n", "loss_fn = nn.CrossEntropyLoss()\n", "epoch_losses = []\n", "step_losses = []\n", "\n", "print(\"Iniciando treinamento...\")\n", "for epoch in range(train_params['num_epochs']):\n", "    model_with_head.train()\n", "    total_loss = 0\n", "    steps = 0\n", "    \n", "    for step, (texts, labels) in enumerate(train_dataloader):\n", "        labels = labels.to(model.device)\n", "        optimizer.zero_grad()\n", "        \n", "        # Codifica o texto\n", "        inputs = model.tokenize(texts)\n", "        input_ids = inputs['input_ids'].to(model.device)\n", "        attention_mask = inputs['attention_mask'].to(model.device)\n", "        \n", "        # Forward pass\n", "        model_with_head = model_with_head.to(model.device)\n", "        logits = model_with_head({'input_ids': input_ids, 'attention_mask': attention_mask})\n", "        \n", "        # Calcula loss\n", "        loss = loss_fn(logits, labels)\n", "        step_losses.append(loss.item())\n", "        \n", "        # Backward pass\n", "        loss.backward()\n", "        optimizer.step()\n", "        scheduler.step()\n", "        \n", "        total_loss += loss.item()\n", "        steps += 1\n", "        \n", "        if step % 10 == 0:\n", "            print(f\"Epoch {epoch+1}/{train_params['num_epochs']}, Step {step}, Loss: {loss.item():.4f}\")\n", "    \n", "    # Calcula loss médio da época\n", "    epoch_loss = total_loss / steps\n", "    epoch_losses.append(epoch_loss)\n", "    print(f\"Época {epoch+1} finalizada. Loss médio: {epoch_loss:.4f}\")\n", "    \n", "    # Salva checkpoint do modelo\n", "    model.save(f'checkpoints/epoch-{epoch+1}')\n", "\n", "# Plot do progresso do treinamento\n", "plt.figure(figsize=(12, 5))\n", "\n", "# Plot de loss por step\n", "plt.subplot(1, 2, 1)\n", "plt.plot(step_losses)\n", "plt.title('Loss por Step')\n", "plt.xlabel('Step')\n", "plt.ylabel('Loss')\n", "\n", "# Plot de loss por época\n", "plt.subplot(1, 2, 2)\n", "plt.plot(epoch_losses, 'ro-')\n", "plt.title('Loss Médio por Época')\n", "plt.xlabel('Época')\n", "plt.ylabel('Loss Médio')\n", "\n", "plt.tight_layout()\n", "plt.savefig('training_progress.png')\n", "plt.close()\n", "\n", "print(\"\\nTreinamento finalizado! Gráficos salvos em 'training_progress.png'\")"]}, {"cell_type": "markdown", "id": "7e1bd0ed", "metadata": {}, "source": ["### Avaliação do Modelo em Diferentes Etapas do Treinamento\n", "\n", "Vamos avaliar o desempenho do modelo em diferentes checkpoints para entender sua evolução:"]}, {"cell_type": "code", "execution_count": null, "id": "1e58a31b", "metadata": {}, "outputs": [], "source": ["def avaliar_checkpoint(checkpoint_path, textos_teste):\n", "    \"\"\"Avalia um checkpoint específico do modelo\"\"\"\n", "    modelo_checkpoint = SentenceTransformer(checkpoint_path)\n", "    embeddings = modelo_checkpoint.encode(textos_teste)\n", "    matriz_sim = util.cos_sim(embeddings, embeddings).numpy()\n", "    return matriz_sim\n", "\n", "# Avalia os checkpoints\n", "evolucao_similaridades = {}\n", "for epoch in range(1, train_params['num_epochs'] + 1):\n", "    checkpoint_path = f'checkpoints/epoch-{epoch}'\n", "    matriz_sim = avaliar_checkpoint(checkpoint_path, textos_teste)\n", "    evolucao_similaridades[f'Época {epoch}'] = matriz_sim\n", "\n", "# Visualiza a evolução das similaridades\n", "fig, axes = plt.subplots(1, len(evolucao_similaridades), figsize=(20, 4))\n", "fig.suptitle('Evolução da Matriz de Similaridade Durante o Treinamento')\n", "\n", "for idx, (epoca, matriz) in enumerate(evolucao_similaridades.items()):\n", "    sns.heatmap(matriz, \n", "                ax=axes[idx],\n", "                annot=True,\n", "                cmap='YlOrRd',\n", "                xticklabels=['Lombar 1', 'Lombar 2', 'Tor<PERSON>zel<PERSON>', 'DPOC', 'Cardíaco'],\n", "                yticklabels=['Lombar 1', 'Lombar 2', 'Tor<PERSON>zel<PERSON>', 'DPOC', 'Cardíaco'],\n", "                fmt='.2f')\n", "    axes[idx].set_title(epoca)\n", "\n", "plt.tight_layout()\n", "plt.savefig('evolucao_similaridades.png')\n", "plt.close()\n", "\n", "print(\"Visualização da evolução do modelo salva em 'evolucao_similaridades.png'\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}