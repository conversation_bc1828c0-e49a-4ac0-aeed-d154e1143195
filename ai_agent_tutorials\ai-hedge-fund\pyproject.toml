[tool.poetry]
name = "ai-hedge-fund"
version = "0.1.0"
description = "An AI-powered hedge fund that uses multiple agents to make trading decisions"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [
    { include = "src", from = "." }
]
[tool.poetry.dependencies]
python = "^3.9"
langchain = "0.3.0"
langchain-openai = "0.3"
langgraph = "0.2.56"
pandas = "^2.1.0"
numpy = "^1.24.0"
python-dotenv = "1.0.0"
matplotlib = "^3.9.2"
tabulate = "^0.9.0"
colorama = "^0.4.6"
questionary = "^2.1.0"
rich = "^13.9.4"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
black = "^23.7.0"
isort = "^5.12.0"
flake8 = "^6.1.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 420
target-version = ['py39']
include = '\.pyi?$'