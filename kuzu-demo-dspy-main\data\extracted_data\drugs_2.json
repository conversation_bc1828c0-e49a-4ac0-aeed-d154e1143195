[{"condition": "Heart Rhythm Problems", "drug": [{"generic_name": "Amiodarone", "brand_names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"generic_name": "Digoxin", "brand_names": ["<PERSON><PERSON><PERSON>", "Lanoxin"]}, {"generic_name": "Propranolol", "brand_names": ["Inderal"]}], "side_effects": ["Dizziness", "Headache", "Throwing up", "Upset stomach"]}, {"condition": "Lowers Blood Pressure and Heart Rate", "drug": [{"generic_name": "Diltiazem", "brand_names": ["Cardizem", "Cartia XT", "<PERSON><PERSON><PERSON>", "Dilacor XR"]}, {"generic_name": "Atenolol", "brand_names": ["<PERSON><PERSON><PERSON>"]}, {"generic_name": "<PERSON><PERSON><PERSON><PERSON>", "brand_names": ["<PERSON><PERSON>"]}, {"generic_name": "Metoprolol", "brand_names": ["Lopressor", "Toprol XL"]}], "side_effects": ["Dizziness", "Drowsiness", "Headache", "Lightheadedness", "Upset stomach", "Swelling", "Throwing up"]}, {"condition": "Lowers Blood Pressure", "drug": [{"generic_name": "Benazepril", "brand_names": ["Lotensin"]}, {"generic_name": "<PERSON><PERSON><PERSON>", "brand_names": ["Capoten"]}, {"generic_name": "Enalapril", "brand_names": ["Vasotec"]}, {"generic_name": "Lisinopril", "brand_names": ["Prinivil", "Zestril"]}, {"generic_name": "Quinapril", "brand_names": ["Accupril"]}, {"generic_name": "<PERSON><PERSON><PERSON>", "brand_names": ["Altace"]}, {"generic_name": "Candesartan", "brand_names": ["Atacand"]}, {"generic_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "brand_names": ["Avapro"]}, {"generic_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "brand_names": ["Benicar"]}, {"generic_name": "Valsartan", "brand_names": ["<PERSON><PERSON><PERSON>"]}, {"generic_name": "Losartan", "brand_names": ["<PERSON><PERSON>"]}], "side_effects": ["<PERSON><PERSON>", "Dizziness", "Lightheadedness"]}, {"condition": "Antibiotic to <PERSON><PERSON><PERSON> and Prevent Bacterial Infections", "drug": [{"generic_name": "Amoxicillin/Clavulanate", "brand_names": ["Augmentin"]}, {"generic_name": "Co-Trimoxazole", "brand_names": []}, {"generic_name": "Trimethoprim", "brand_names": ["Bactrim", "Bactrim DS", "Septra", "Septra DS"]}, {"generic_name": "Ciprofloxacin", "brand_names": ["Cipro"]}, {"generic_name": "Clindamycin", "brand_names": ["Cleocin"]}, {"generic_name": "Levofloxacin", "brand_names": ["<PERSON><PERSON><PERSON>"]}, {"generic_name": "Metronidazole", "brand_names": ["<PERSON><PERSON>"]}, {"generic_name": "Moxifloxacin", "brand_names": ["Avelox"]}, {"generic_name": "Vancomycin", "brand_names": ["<PERSON><PERSON><PERSON>"]}], "side_effects": ["Diarrhea", "Headache", "Rash/flushing", "Throwing up", "Upset stomach"]}, {"condition": "Helps With Inflammation (swelling)", "drug": [{"generic_name": "Celecoxib", "brand_names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"generic_name": "Dexamethasone", "brand_names": ["Decadron"]}, {"generic_name": "Ibuprofen", "brand_names": ["<PERSON><PERSON><PERSON>"]}, {"generic_name": "Ketorolac", "brand_names": ["Toradol"]}, {"generic_name": "Meloxicam", "brand_names": ["Mobic"]}, {"generic_name": "Naproxen", "brand_names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}], "side_effects": ["Risk of bleeding", "Sleeplessness", "Swelling", "Upset stomach"]}, {"condition": "Calms Nerves or Makes You Sleepy", "drug": [{"generic_name": "Alprazolam", "brand_names": ["Xanax"]}, {"generic_name": "<PERSON><PERSON><PERSON>", "brand_names": ["Valium"]}, {"generic_name": "Lorazepam", "brand_names": ["Ativan"]}, {"generic_name": "Zolpidem", "brand_names": ["Ambien"]}], "side_effects": ["Confusion", "Dizziness", "Drowsiness", "Headache"]}]