import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_gemma/flutter_gemma.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../models/translation_result.dart';
import 'gemma_downloader_service.dart';

/// Local Gemma-3N Translation Service
/// Based on the flutter_gemma package example for offline AI translation
class LocalGemmaTranslationService {
  static final LocalGemmaTranslationService _instance =
      LocalGemmaTranslationService._internal();
  factory LocalGemmaTranslationService() => _instance;
  LocalGemmaTranslationService._internal();

  static LocalGemmaTranslationService get instance => _instance;

  // Service state
  bool _isInitialized = false;
  bool _isModelLoaded = false;
  InferenceModel? _inferenceModel;
  Chat? _chat;

  final GemmaDownloaderService _downloader = GemmaDownloaderService.instance;

  /// Initialize the local Gemma-3N translation service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🚀 Initializing Local Gemma-3N Translation Service...');
      }

      // Check if model exists locally
      final modelExists = await _downloader.checkModelExistence();

      if (!modelExists) {
        if (kDebugMode) {
          print('❌ Gemma-3N model not found locally');
          print('💡 Model needs to be downloaded first');
        }
        _isInitialized = true;
        return;
      }

      // Load the model
      await _loadGemmaModel();

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Local Gemma-3N Translation Service initialized successfully!');
        print('🧠 Model: Gemma-3N-E2B-it');
        print('📱 Platform: ${defaultTargetPlatform.name}');
        print('🌐 Mode: Offline Local AI');
        print('📦 Model loaded: $_isModelLoaded');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Local Gemma-3N service: $e');
      }
      _isInitialized = true; // Mark as initialized even if failed
    }
  }

  /// Load the Gemma-3N model for inference
  Future<void> _loadGemmaModel() async {
    try {
      if (kDebugMode) {
        print('🔄 Loading Gemma-3N model...');
      }

      final gemma = FlutterGemmaPlugin.instance;

      // Create the inference model with multimodal support
      _inferenceModel = await gemma.createModel(
        modelType: ModelType.gemmaIt,
        supportImage: true, // Enable image support for multimodal translation
        maxTokens: 2048,
      );

      // Create a chat session from the loaded model
      _chat = await _inferenceModel!.createChat(supportImage: true);

      _isModelLoaded = true;

      if (kDebugMode) {
        print('✅ Gemma-3N model loaded successfully!');
        print('🖼️ Image support: enabled');
        print('💬 Chat session: created');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to load Gemma-3N model: $e');
      }
      _isModelLoaded = false;
      throw e;
    }
  }

  /// Download the Gemma-3N model if needed
  Future<bool> downloadModelIfNeeded({Function(double)? onProgress}) async {
    try {
      // Check if model already exists
      final modelExists = await _downloader.checkModelExistence();
      if (modelExists) {
        if (kDebugMode) {
          print('✅ Gemma-3N model already exists locally');
        }
        return true;
      }

      // Get Hugging Face access token from environment
      final accessToken = dotenv.env['HUGGING_FACE_TOKEN'] ?? '';

      if (accessToken.isEmpty ||
          accessToken == 'YOUR_HUGGING_FACE_TOKEN_HERE') {
        if (kDebugMode) {
          print('❌ HUGGING_FACE_TOKEN not configured in .env file');
          print('💡 Please add your Hugging Face access token to .env');
          print('🔗 Get token at: https://huggingface.co/settings/tokens');
        }
        return false;
      }

      if (kDebugMode) {
        print('🚀 Starting Gemma-3N model download...');
      }

      // Download the model
      final success = await _downloader.downloadModel(
        accessToken: accessToken,
        onProgress: onProgress,
      );

      if (success) {
        // Load the model after successful download
        await _loadGemmaModel();
      }

      return success;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to download Gemma-3N model: $e');
      }
      return false;
    }
  }

  /// Translate text using local Gemma-3N model
  Future<TranslationResult> translateText({
    required String text,
    required String targetLanguage,
    String? sourceLanguage,
    String? context,
    String? domain,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final startTime = DateTime.now();

    try {
      if (kDebugMode) {
        print('🔄 Translating text with Local Gemma-3N...');
        print(
            '📝 Text: ${text.length > 50 ? '${text.substring(0, 50)}...' : text}');
        print('🌍 From: ${sourceLanguage ?? 'auto'} → To: $targetLanguage');
      }

      if (!_isModelLoaded || _chat == null) {
        throw Exception(
            'Gemma-3N model not loaded. Please download the model first.');
      }

      // Build translation prompt
      final prompt = _buildTranslationPrompt(
          text, sourceLanguage ?? 'auto', targetLanguage, context, domain);

      if (kDebugMode) {
        print('📝 Prompt: ${prompt.substring(0, 100)}...');
      }

      // Create message for the chat
      final userMessage = Message.text(
        text: prompt,
        isUser: true,
      );

      // Add the query to chat
      await _chat!.addQueryChunk(userMessage);

      // Generate response
      final responseStream = _chat!.generateChatResponseAsync();
      final responseBuffer = StringBuffer();

      await for (final token in responseStream) {
        responseBuffer.write(token);
      }

      final response = responseBuffer.toString();
      final translatedText = _extractTranslation(response);

      final processingTime = DateTime.now().difference(startTime);

      if (kDebugMode) {
        print('✅ LOCAL TRANSLATION SUCCESS: "$text" → "$translatedText"');
        print('⏱️ Processing time: ${processingTime.inMilliseconds}ms');
      }

      return TranslationResult(
        originalText: text,
        translatedText: translatedText,
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: 0.95, // High confidence for local model
        timestamp: DateTime.now(),
        processingTime: processingTime,
        metadata: {
          'translationEngine': 'Gemma-3N-Local',
          'isOffline': true,
          'modelType': 'local',
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ LOCAL TRANSLATION FAILED: $e');
      }

      final processingTime = DateTime.now().difference(startTime);

      return TranslationResult(
        originalText: text,
        translatedText: 'Translation failed: $e',
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: 0.0,
        timestamp: DateTime.now(),
        processingTime: processingTime,
        metadata: {
          'translationEngine': 'Gemma-3N-Local',
          'isOffline': true,
          'error': e.toString(),
        },
      );
    }
  }

  /// Build translation prompt for Gemma-3N
  String _buildTranslationPrompt(String text, String sourceLanguage,
      String targetLanguage, String? context, String? domain) {
    final buffer = StringBuffer();

    buffer.writeln(
        'You are a professional translator with expertise in multiple languages.');
    buffer.writeln('Your task is to provide accurate, natural translations.');
    buffer.writeln('');

    if (sourceLanguage.toLowerCase() == 'auto') {
      buffer.writeln(
          'TASK: Detect the language of the input text and translate it to $targetLanguage.');
    } else {
      buffer
          .writeln('TASK: Translate from $sourceLanguage to $targetLanguage.');
    }

    if (context != null && context.isNotEmpty) {
      buffer.writeln('CONTEXT: $context');
    }

    if (domain != null && domain.isNotEmpty) {
      buffer.writeln('DOMAIN: $domain');
    }

    buffer.writeln('');
    buffer.writeln('RULES:');
    buffer.writeln('1. Provide ONLY the translation, no explanations');
    buffer.writeln('2. Maintain the original tone and style');
    buffer.writeln('3. Preserve formatting (line breaks, punctuation)');
    buffer.writeln('4. Use natural, fluent language in the target language');
    buffer.writeln(
        '5. If the text is already in the target language, return it unchanged');
    buffer.writeln('');
    buffer.writeln('TEXT TO TRANSLATE:');
    buffer.writeln('"$text"');
    buffer.writeln('');
    buffer.writeln('TRANSLATION:');

    return buffer.toString();
  }

  /// Extract translation from Gemma-3N response
  String _extractTranslation(String response) {
    String cleaned = response.trim();

    if (kDebugMode) {
      print('🔍 Raw Gemma-3N response: "$response"');
    }

    // Remove common prefixes
    cleaned = cleaned.replaceAll(
        RegExp(r'^(Translation:|TRANSLATION:)\s*', caseSensitive: false), '');

    // Remove quotes if present
    if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }

    // Clean extra whitespace
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ');

    final result = cleaned.trim();

    if (kDebugMode) {
      print('✨ Extracted translation: "$result"');
    }

    return result;
  }

  /// Check if service is ready for translation
  bool get isReady => _isInitialized && _isModelLoaded;

  /// Check if model is downloaded
  Future<bool> get isModelDownloaded => _downloader.checkModelExistence();

  /// Get download progress
  double get downloadProgress => _downloader.downloadProgress;

  /// Check if model is downloading
  bool get isDownloading => _downloader.isDownloading;

  /// Translate image using local Gemma-3N model with multimodal support
  Future<TranslationResult> translateImage({
    required Uint8List imageBytes,
    required String targetLanguage,
    String? sourceLanguage,
    String? additionalContext,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final startTime = DateTime.now();

    try {
      if (kDebugMode) {
        print('🔄 Translating image with Local Gemma-3N...');
        print('🖼️ Image size: ${imageBytes.length} bytes');
        print('🌍 Target language: $targetLanguage');
      }

      if (!_isModelLoaded || _chat == null) {
        throw Exception(
            'Gemma-3N model not loaded. Please download the model first.');
      }

      // Build image translation prompt
      final prompt = _buildImageTranslationPrompt(
          sourceLanguage ?? 'auto', targetLanguage, additionalContext);

      if (kDebugMode) {
        print('📝 Image prompt: ${prompt.substring(0, 100)}...');
      }

      // Create multimodal message with image and text
      final userMessage = Message.withImage(
        text: prompt,
        imageBytes: imageBytes,
        isUser: true,
      );

      // Add the query to chat
      await _chat!.addQueryChunk(userMessage);

      // Generate response
      final responseStream = _chat!.generateChatResponseAsync();
      final responseBuffer = StringBuffer();

      await for (final token in responseStream) {
        responseBuffer.write(token);
      }

      final response = responseBuffer.toString();
      final result = _parseImageTranslationResponse(response);

      final processingTime = DateTime.now().difference(startTime);

      if (kDebugMode) {
        print(
            '✅ LOCAL IMAGE TRANSLATION SUCCESS: "${result['extractedText']}" → "${result['translatedText']}"');
        print('⏱️ Processing time: ${processingTime.inMilliseconds}ms');
      }

      return TranslationResult(
        originalText: result['extractedText'] ?? 'Text found in image',
        translatedText: result['translatedText'] ?? 'Translation not available',
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: 0.95, // High confidence for local model
        timestamp: DateTime.now(),
        isImageTranslation: true,
        processingTime: processingTime,
        metadata: {
          'translationEngine': 'Gemma-3N-Local-Vision',
          'isOffline': true,
          'modelType': 'local-multimodal',
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ LOCAL IMAGE TRANSLATION FAILED: $e');
      }

      final processingTime = DateTime.now().difference(startTime);

      return TranslationResult(
        originalText: 'Error extracting text from image',
        translatedText: 'Image translation failed: $e',
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: 0.0,
        timestamp: DateTime.now(),
        isImageTranslation: true,
        processingTime: processingTime,
        metadata: {
          'translationEngine': 'Gemma-3N-Local-Vision',
          'isOffline': true,
          'error': e.toString(),
        },
      );
    }
  }

  /// Build image translation prompt for Gemma-3N
  String _buildImageTranslationPrompt(
      String sourceLanguage, String targetLanguage, String? additionalContext) {
    final buffer = StringBuffer();

    buffer.writeln('You are a professional OCR and translation expert.');
    buffer.writeln('Analyze this image and perform the following tasks:');
    buffer.writeln('');
    buffer
        .writeln('1. EXTRACT: Find and extract ALL text visible in the image');
    buffer.writeln('2. TRANSLATE: Translate the extracted text');
    buffer.writeln('');

    if (sourceLanguage.toLowerCase() == 'auto') {
      buffer.writeln(
          'TASK: Detect the language of any text in the image and translate it to $targetLanguage.');
    } else {
      buffer.writeln(
          'TASK: Extract text in $sourceLanguage and translate it to $targetLanguage.');
    }

    if (additionalContext != null && additionalContext.isNotEmpty) {
      buffer.writeln('CONTEXT: $additionalContext');
    }

    buffer.writeln('');
    buffer.writeln('RESPONSE FORMAT:');
    buffer.writeln('EXTRACTED_TEXT: [the text you found in the image]');
    buffer.writeln('TRANSLATED_TEXT: [the translation of that text]');
    buffer.writeln('');
    buffer.writeln('RULES:');
    buffer.writeln(
        '1. If no text is found, respond with "EXTRACTED_TEXT: No text detected"');
    buffer.writeln('2. Provide accurate OCR extraction');
    buffer.writeln('3. Provide natural, fluent translation');
    buffer.writeln('4. Preserve formatting and structure');
    buffer.writeln('5. Use the exact format specified above');

    return buffer.toString();
  }

  /// Parse the response from Gemma-3N for image translation
  Map<String, String> _parseImageTranslationResponse(String response) {
    if (kDebugMode) {
      print('🔍 Parsing Gemma-3N Vision response: "$response"');
    }

    String extractedText = 'No text detected';
    String translatedText = 'No text detected';

    // Parse structured format
    final lines = response.split('\n');
    for (final line in lines) {
      if (line.startsWith('EXTRACTED_TEXT:')) {
        extractedText = line.substring('EXTRACTED_TEXT:'.length).trim();
      } else if (line.startsWith('TRANSLATED_TEXT:')) {
        translatedText = line.substring('TRANSLATED_TEXT:'.length).trim();
      }
    }

    // Fallback parsing if structured format not found
    if (extractedText == 'No text detected' &&
        translatedText == 'No text detected') {
      final cleanResponse = response.trim();
      if (cleanResponse.isNotEmpty &&
          !cleanResponse.toLowerCase().contains('no text')) {
        extractedText = 'Text found in image';
        translatedText = cleanResponse;
      }
    }

    return {
      'extractedText': extractedText,
      'translatedText': translatedText,
    };
  }

  /// Get model information
  Map<String, dynamic> getModelInfo() => _downloader.getModelInfo();
}
