"""
Aplicativo de geração de artigos acadêmicos ABNT.
Gera artigos acadêmicos no estilo Wikipedia usando a biblioteca Agno para construção de agentes,
API Groq e busca de dados no arxiv.org, seguindo as normas ABNT (Associação Brasileira de Normas Técnicas)
para citações e referências bibliográficas.
"""

import os
import json
import logging
import streamlit as st
import matplotlib.pyplot as plt

# Importar módulos
from modules.plagiarism import PlagiarismChecker
from modules.pdf_export import PDFExporter
from modules.export import ExportManager
from modules.history import HistoryManager
from modules.examples import get_format_example
from modules.agents import AgentTeam
from modules.arxiv_search import ArxivSearch
from modules.web_search import WebSearch
from modules.mixed_search import MixedSearch
from modules.chapter_ui import render_chapter_generator_ui
from modules.utils import (
    save_article, display_article, display_sources
)

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_word_distribution_chart(word_counts):
    """
    Cria um gráfico de barras para a distribuição de palavras.

    Args:
        word_counts (dict): Dicionário com as contagens de palavras por seção.

    Returns:
        fig: Figura do matplotlib com o gráfico.
    """
    # Preparar dados
    sections = ["Resumo/Abstract", "Introdução", "Metodologia",
                "Desenvolvimento", "Resultados", "Conclusões"]
    values = [word_counts["resumo"]*2, word_counts["intro"], word_counts["metodo"],
              word_counts["desenv"], word_counts["result"], word_counts["concl"]]

    # Criar gráfico
    fig, ax = plt.subplots(figsize=(10, 5))
    bars = ax.bar(sections, values, color='skyblue')

    # Adicionar valores no topo das barras
    for bar in bars:
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{int(height)}', ha='center', va='bottom')

    # Estilizar gráfico
    ax.set_title('Distribuição de Palavras por Seção')
    ax.set_ylabel('Número de Palavras')
    ax.set_ylim(0, max(values) * 1.15)  # Espaço para os números
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()

    return fig

def main():
    """Função principal do aplicativo"""
    # Configurar a página
    st.set_page_config(
        page_title="Gerador de Artigos Acadêmicos ABNT",
        page_icon="📚",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Adicionar CSS personalizado
    st.markdown("""
    <style>
    .fullscreen-button {
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        cursor: pointer;
    }
    .fullscreen-button:hover {
        background-color: #45a049;
    }
    .dark-mode {
        background-color: #1E1E1E;
        color: #E0E0E0;
    }
    .dark-mode h1, .dark-mode h2, .dark-mode h3, .dark-mode h4, .dark-mode h5, .dark-mode h6 {
        color: #FFFFFF;
    }
    .dark-mode .stMarkdown a {
        color: #8AB4F8;
    }
    .dark-mode .stMarkdown blockquote {
        background-color: #2D2D2D;
        border-left: 5px solid #555555;
    }
    .dark-mode .stMarkdown code {
        background-color: #2D2D2D;
    }
    .download-button {
        display: inline-block;
        padding: 8px 16px;
        background-color: #4CAF50;
        color: white;
        text-decoration: none;
        border-radius: 4px;
        margin: 10px 0;
    }
    .download-button:hover {
        background-color: #45a049;
    }
    </style>
    """, unsafe_allow_html=True)

    # Título e descrição
    st.title("📚 Gerador de Artigos Acadêmicos com Groq e Arxiv")
    st.markdown(
        """
        Este aplicativo gera esboços de artigos acadêmicos no estilo Wikipedia usando a API Groq e busca de dados no arxiv.org,
        seguindo as normas ABNT (Associação Brasileira de Normas Técnicas) para citações e referências bibliográficas.
        """
    )

    # Aviso sobre limitações de taxa
    st.info(
        """
        **Nota sobre limitações da API Groq**: Este aplicativo utiliza a API Groq, que pode ter limitações de taxa (rate limits).
        Se você receber erros 429 (Too Many Requests), o sistema tentará automaticamente novamente com um intervalo crescente entre as tentativas.
        Em caso de persistência do erro, aguarde alguns minutos antes de tentar novamente.
        """
    )

    # Verificar se a chave da API Groq está configurada
    if "GROQ_API_KEY" not in st.secrets:
        st.error(
            "Chave da API Groq não encontrada. Adicione sua chave no arquivo .streamlit/secrets.toml:"
            "\n\n```toml\nGROQ_API_KEY = 'sua-chave-da-api-groq-aqui'\n```"
        )
        return

    # Configurações no sidebar
    st.sidebar.header("Configurações")

    # Formato do artigo
    st.sidebar.subheader("Formato do Artigo")
    article_format = st.sidebar.radio(
        "Escolha o formato:",
        ["Padrão (estilo Wikipedia)", "Acadêmico (com resumo, abstract, etc.)"],
        index=0,
        help="Escolha o formato do artigo a ser gerado"
    )

    # Botão para visualizar exemplo do formato
    format_type = "standard" if article_format == "Padrão (estilo Wikipedia)" else "academic"
    if st.sidebar.button(f"Ver exemplo de formato {article_format}", key="preview_format"):
        with st.sidebar.expander("Exemplo de formato", expanded=True):
            st.markdown(f"### Exemplo de artigo no formato {article_format}")
            st.info("Este é um exemplo simplificado para ilustrar a estrutura do formato escolhido.")
            example = get_format_example(format_type)
            # Mostrar apenas as primeiras 20 linhas para não sobrecarregar a interface
            preview_lines = example.split('\n')[:20]
            preview_text = '\n'.join(preview_lines) + '\n\n... (continua)'
            st.markdown(preview_text)

    # Opções avançadas para o formato acadêmico
    if article_format == "Acadêmico (com resumo, abstract, etc.)":
        st.sidebar.subheader("Contagem de Palavras")
        word_count_option = st.sidebar.radio(
            "Distribuição de palavras:",
            ["Automática (balanceada)", "Personalizada"],
            index=0
        )

        if word_count_option == "Personalizada":
            st.sidebar.info("Defina a quantidade aproximada de palavras para cada seção do artigo.")

            # Definir contagens para cada seção
            resumo_words = st.sidebar.number_input("Resumo/Abstract", min_value=100, max_value=500, value=200, step=50,
                                        help="Número de palavras para o resumo e abstract (cada um)")
            intro_words = st.sidebar.number_input("Introdução", min_value=200, max_value=1000, value=400, step=50)
            metodo_words = st.sidebar.number_input("Metodologia", min_value=200, max_value=1000, value=400, step=50)
            desenv_words = st.sidebar.number_input("Desenvolvimento", min_value=300, max_value=2000, value=600, step=100)
            result_words = st.sidebar.number_input("Resultados e Discussão", min_value=300, max_value=2000, value=600, step=100)
            concl_words = st.sidebar.number_input("Conclusões", min_value=100, max_value=500, value=200, step=50)

            # Calcular e mostrar o total
            total_words = resumo_words * 2 + intro_words + metodo_words + desenv_words + result_words + concl_words
            st.sidebar.info(f"Total aproximado: {total_words} palavras (excluindo título e referências)")
        else:
            # Valores padrão para contagem automática
            resumo_words = 200
            intro_words = 400
            metodo_words = 400
            desenv_words = 600
            result_words = 600
            concl_words = 200

    # Configurações de busca
    st.sidebar.subheader("Configurações de Busca")
    search_results = st.sidebar.slider("Número de resultados de busca", 3, 40, 10, help="Defina quantos resultados de busca serão obtidos. Valores maiores podem resultar em artigos mais completos, mas aumentam o tempo de processamento.")

    # Fonte de dados para pesquisa
    search_source = st.sidebar.radio(
        "Fonte de dados para pesquisa:",
        ["Arxiv (padrão)", "Internet", "Mista (Arxiv + Internet)"],
        index=0,
        help="Escolha de onde os dados serão obtidos para a pesquisa."
    )

    # Configurações do modelo
    st.sidebar.subheader("Configurações do modelo")
    model = st.sidebar.selectbox(
        "Modelo Groq",
        ["llama-3.3-70b-versatile", "llama-3.1-8b-instant", "meta-llama/llama-4-scout-17b-16e-instruct", "meta-llama/llama-4-maverick-17b-128e-instruct", "qwen-qwq-32b", "deepseek-r1-distill-qwen-32b", "deepseek-r1-distill-llama-70b", "mistral-saba-24b", "gemma2-9b-it"],
        index=0
    )

    # Configurações avançadas
    st.sidebar.subheader("Configurações avançadas")

    # Informações sobre os agentes Agno
    st.sidebar.info("🤖 Este aplicativo usa a biblioteca Agno para construção de agentes inteligentes que trabalham em conjunto para gerar artigos acadêmicos de alta qualidade.")

    # Etapas do processo
    st.sidebar.subheader("Etapas do processo")
    do_research = st.sidebar.checkbox("Realizar pesquisa", value=True)
    do_generate_outline = st.sidebar.checkbox("Gerar esboço", value=True)
    do_generate_article = st.sidebar.checkbox("Gerar artigo", value=True)
    do_polish_article = st.sidebar.checkbox("Polir artigo", value=True)

    # Diretório de saída
    output_dir = "artigos_academicos_abnt_results"
    os.makedirs(output_dir, exist_ok=True)

    # Inicializar o gerenciador de histórico
    history_manager = HistoryManager()

    # Abas para criar novo artigo, gerar capítulos, ver artigos existentes e favoritos
    tab1, tab2, tab3, tab4 = st.tabs(["Criar Novo Artigo", "Gerar Capítulos", "Meus Artigos", "Favoritos"])

    # Aba para criar novo artigo
    with tab1:
        st.header("Criar Novo Artigo")

        # Formulário para criar novo artigo
        with st.form(key="new_article_form"):
            topic = st.text_input("Tópico do artigo:", placeholder="Ex: Inteligência Artificial")

            # Exibir informações sobre o formato selecionado
            format_name = "Acadêmico estruturado" if article_format == "Acadêmico (com resumo, abstract, etc.)" else "Padrão (estilo Wikipedia)"
            st.info(f"Formato selecionado: {format_name}")

            # Se for formato acadêmico com contagem personalizada, mostrar resumo
            if article_format == "Acadêmico (com resumo, abstract, etc.)" and 'word_count_option' in locals() and word_count_option == "Personalizada":
                total_words = resumo_words * 2 + intro_words + metodo_words + desenv_words + result_words + concl_words
                st.caption(f"Contagem personalizada: ~{total_words} palavras no total")

            submit_button = st.form_submit_button(label="Gerar Artigo")

        # Processar o formulário quando enviado
        if submit_button and topic:
            # Iniciar o processo de geração do artigo

            # Container para exibir o progresso
            with st.container():
                progress_placeholder = st.empty()

                try:
                    # Inicializar a equipe de agentes Agno
                    progress_placeholder.markdown("🤖 **Inicializando agentes Agno...**")
                    agent_team = AgentTeam(
                        api_key=st.secrets["GROQ_API_KEY"],
                        model_id=model
                    )
                    progress_placeholder.markdown("✅ **Agentes inicializados com sucesso!**")

                    # Inicializar o mecanismo de busca de acordo com a fonte selecionada
                    if search_source == "Arxiv (padrão)":
                        search_engine = ArxivSearch(max_results=search_results)
                        progress_placeholder.markdown("🔍 **Usando busca no Arxiv...**")
                    elif search_source == "Internet":
                        search_engine = WebSearch(max_results=search_results)
                        progress_placeholder.markdown("🔍 **Usando busca na Internet...**")
                    else:  # Mista
                        search_engine = MixedSearch(max_results=search_results)
                        progress_placeholder.markdown("🔍 **Usando busca mista (Arxiv + Internet)...**")

                    # Determinar o formato do artigo
                    format_type = "academic" if article_format == "Acadêmico (com resumo, abstract, etc.)" else "standard"

                    # Preparar contagens de palavras se for formato acadêmico
                    word_counts = None
                    if format_type == "academic":
                        # Verificar se a contagem é personalizada
                        if 'word_count_option' in locals() and word_count_option == "Personalizada":
                            word_counts = {
                                "resumo": resumo_words,
                                "intro": intro_words,
                                "metodo": metodo_words,
                                "desenv": desenv_words,
                                "result": result_words,
                                "concl": concl_words
                            }

                            # Exibir mensagem com o formato escolhido e contagem personalizada
                            total_words = sum(word_counts.values()) + word_counts["resumo"]  # Adicionar o abstract
                            progress_placeholder.markdown(f"📄 **Gerando artigo acadêmico com contagem personalizada (total: ~{total_words} palavras)...**")
                        else:
                            # Valores padrão para contagem automática
                            word_counts = {
                                "resumo": 200,
                                "intro": 400,
                                "metodo": 400,
                                "desenv": 600,
                                "result": 600,
                                "concl": 200
                            }
                            # Exibir mensagem com o formato escolhido
                            progress_placeholder.markdown(f"📄 **Gerando artigo no formato acadêmico estruturado...**")
                    else:
                        # Exibir mensagem para formato padrão
                        progress_placeholder.markdown(f"📄 **Gerando artigo no formato padrão (estilo Wikipedia)...**")

                    # Gerar o artigo completo usando a equipe de agentes
                    progress_placeholder.markdown("👨‍💻 **Iniciando processo de geração com agentes Agno...**")

                    # Realizar a pesquisa usando o mecanismo selecionado, se necessário
                    custom_research_results = []
                    if do_research:
                        progress_placeholder.markdown("🔍 **Realizando pesquisa...**")
                        custom_research_results = search_engine.search(topic)
                        progress_placeholder.markdown(f"📜 **Encontrados {len(custom_research_results)} resultados.**")

                    # Usar o AgentTeam para gerar o artigo completo
                    results = agent_team.generate_complete_article(
                        topic=topic,
                        num_results=search_results,
                        format_type=format_type,
                        word_counts=word_counts,
                        do_research=False,  # Já realizamos a pesquisa acima
                        do_generate_outline=do_generate_outline,
                        do_generate_article=do_generate_article,
                        do_polish_article=do_polish_article
                    )

                    # Substituir os resultados da pesquisa pelos nossos resultados personalizados
                    if do_research and custom_research_results:
                        results["research_results"] = custom_research_results

                    # Extrair os resultados
                    research_results = results.get("research_results", [])
                    outline = results.get("outline", f"# {topic}\n\n## Introdução\n\n## Desenvolvimento\n\n## Conclusão")
                    article = results.get("article", f"# {topic}\n\nArtigo não gerado.")
                    polished_article = results.get("polished_article")

                    # Atualizar mensagens de progresso
                    if do_research:
                        progress_placeholder.markdown(f"✅ **Pesquisa concluída!** Encontrados {len(research_results)} resultados.")
                    else:
                        progress_placeholder.markdown("⏩ **Etapa de pesquisa ignorada.**")

                    if do_generate_outline:
                        progress_placeholder.markdown("✅ **Esboço gerado com sucesso!**")
                    else:
                        progress_placeholder.markdown("⏩ **Etapa de geração de esboço ignorada.**")

                    if do_generate_article:
                        progress_placeholder.markdown("✅ **Artigo gerado com sucesso!**")
                    else:
                        progress_placeholder.markdown("⏩ **Etapa de geração de artigo ignorada.**")

                    if do_polish_article and do_generate_article:
                        progress_placeholder.markdown("✅ **Artigo polido com sucesso!**")
                    elif do_generate_article:
                        progress_placeholder.markdown("⏩ **Etapa de polimento do artigo ignorada.**")

                    # Salvar os resultados
                    progress_placeholder.markdown("💾 **Salvando os resultados...**")
                    article_path = save_article(
                        output_dir=output_dir,
                        topic=topic,
                        research_results=research_results,
                        outline=outline,
                        article=article,
                        polished_article=polished_article,
                        history_manager=history_manager,
                        article_format=format_type if 'format_type' in locals() else "standard",
                        word_counts=word_counts if 'word_counts' in locals() and word_counts else None
                    )

                    # Exibir mensagem de sucesso com o caminho do artigo
                    progress_placeholder.success(f"Artigo salvo com sucesso em: {article_path}")

                    # Exibir mensagem de sucesso
                    progress_placeholder.markdown("✅ **Artigo gerado e salvo com sucesso!** Veja-o na aba 'Meus Artigos'.")

                except Exception as e:
                    st.error(f"Ocorreu um erro: {str(e)}")
                    logger.exception(f"Ocorreu um erro: {str(e)}")

    # Função para exibir um artigo
    def display_article_ui(article_info, is_favorite=False, tab_id="history", index=0):
        # Verificar se o artigo existe
        article_path = article_info.get("final_article_path")
        if not article_path or not os.path.exists(article_path):
            st.warning("Artigo não encontrado ou ainda não foi gerado completamente.")
            return

        # Obter o título e o ID do artigo
        title = article_info.get("title", "Artigo sem título")
        article_id = article_info.get("id")

        # Caminhos para os arquivos do artigo
        article_dir = article_info.get("path")
        sources_path = os.path.join(article_dir, "url_to_info.json")

        # Exibir o artigo e as fontes em colunas
        col1, col2 = st.columns([2, 1])

        with col1:
            # Título e botões de ação
            title_col, actions_col = st.columns([3, 1])
            with title_col:
                st.header(title)

                # Exibir o formato do artigo se disponível
                article_format = article_info.get("article_format", "standard")
                format_name = "Acadêmico estruturado" if article_format == "academic" else "Padrão (estilo Wikipedia)"

                # Exibir informações sobre o formato
                if article_format == "academic" and "word_counts" in article_info:
                    # Calcular total de palavras
                    word_counts = article_info["word_counts"]
                    total_words = sum(word_counts.values()) + word_counts["resumo"]  # Adicionar o abstract
                    st.caption(f"Formato: {format_name} | Total: ~{total_words} palavras | Distribuição personalizada")

                    # Exibir detalhes da distribuição diretamente, sem usar expander aninhado
                    show_distribution = st.checkbox("Ver distribuição de palavras", key=f"show_dist_{tab_id}_{article_id}_{index}")
                    if show_distribution:
                        st.markdown("#### Distribuição de palavras por seção")

                        # Usar abas para mostrar tabela e gráfico
                        dist_tab1, dist_tab2 = st.tabs(["Tabela", "Gráfico"])

                        with dist_tab1:
                            # Usar uma tabela simples em vez de colunas
                            distribution_table = f"""
                            | Seção | Palavras |
                            |--------|--------|
                            | Resumo/Abstract | {word_counts['resumo']} (cada) |
                            | Introdução | {word_counts['intro']} |
                            | Metodologia | {word_counts['metodo']} |
                            | Desenvolvimento | {word_counts['desenv']} |
                            | Resultados e Discussão | {word_counts['result']} |
                            | Conclusões | {word_counts['concl']} |
                            | **Total** | **{total_words}** |
                            """
                            st.markdown(distribution_table)

                        with dist_tab2:
                            # Adicionar visualização gráfica
                            try:
                                chart = create_word_distribution_chart(word_counts)
                                st.pyplot(chart)
                            except Exception as e:
                                st.error(f"Erro ao gerar gráfico: {str(e)}")
                else:
                    st.caption(f"Formato: {format_name}")

            with actions_col:
                # Botão de favorito - usar tab_id e index para garantir chaves únicas
                if is_favorite:
                    if st.button("⭐ Remover dos favoritos", key=f"{tab_id}_unfav_{article_id}_{index}"):
                        history_manager.remove_from_favorites(article_id)
                        st.success("Removido dos favoritos!")
                        st.rerun()
                else:
                    if st.button("☆ Adicionar aos favoritos", key=f"{tab_id}_fav_{article_id}_{index}"):
                        history_manager.add_to_favorites(article_id)
                        st.success("Adicionado aos favoritos!")
                        st.rerun()

            # Adicionar controles de visualização
            view_col1, view_col2 = st.columns(2)
            with view_col1:
                # Botão para alternar modo claro/escuro
                if 'dark_mode' not in st.session_state:
                    st.session_state.dark_mode = False

                if st.button("🌙 Modo Escuro" if not st.session_state.dark_mode else "☀️ Modo Claro", key=f"{tab_id}_theme_{article_id}_{index}"):
                    st.session_state.dark_mode = not st.session_state.dark_mode
                    st.experimental_rerun()

            with view_col2:
                # Botão para visualização em tela cheia
                if st.button("📺 Tela Cheia", key=f"{tab_id}_fullscreen_{article_id}_{index}"):
                    fullscreen_script = f"""
                    <script>
                    function toggleFullScreen() {{
                        if (!document.fullscreenElement) {{
                            document.documentElement.requestFullscreen();
                        }} else {{
                            if (document.exitFullscreen) {{
                                document.exitFullscreen();
                            }}
                        }}
                    }}
                    toggleFullScreen();
                    </script>
                    """
                    st.markdown(fullscreen_script, unsafe_allow_html=True)

            # Conteúdo do artigo
            article_content = display_article(article_path)

            # Aplicar modo escuro se ativado
            if st.session_state.get('dark_mode', False):
                article_container = f"<div class='dark-mode'>{article_content}</div>"
                st.markdown(article_container, unsafe_allow_html=True)
            else:
                st.markdown(article_content)

            # Botões de ação
            st.subheader("Exportar Artigo")
            export_tabs = st.tabs(["PDF", "DOCX", "LaTeX", "HTML", "Diretório"])

            # Aba para exportar para PDF
            with export_tabs[0]:
                if st.button("Gerar PDF", key=f"{tab_id}_pdf_{article_id}_{index}"):
                    try:
                        with st.spinner("Gerando PDF..."):
                            # Gerar o PDF
                            pdf_bytes = PDFExporter.markdown_to_pdf(article_content, title=title)

                            # Criar link de download
                            download_link = PDFExporter.get_download_link(
                                pdf_bytes,
                                filename=f"{article_info.get('sanitized_title', 'artigo')}.pdf"
                            )

                            # Exibir link de download
                            st.markdown(download_link, unsafe_allow_html=True)
                            st.success("PDF gerado com sucesso! Clique no link acima para baixar.")
                    except Exception as e:
                        st.error(f"Erro ao gerar PDF: {str(e)}")

            # Aba para exportar para DOCX
            with export_tabs[1]:
                if st.button("Gerar DOCX (Word)", key=f"{tab_id}_docx_{article_id}_{index}"):
                    try:
                        with st.spinner("Gerando DOCX..."):
                            try:
                                # Gerar o DOCX
                                docx_bytes = ExportManager.markdown_to_docx(article_content, title=title)

                                # Criar link de download
                                download_link = ExportManager.get_download_link(
                                    docx_bytes,
                                    filename=f"{article_info.get('sanitized_title', 'artigo')}.docx",
                                    mime_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                                )

                                # Exibir link de download
                                st.markdown(download_link, unsafe_allow_html=True)
                                st.success("DOCX gerado com sucesso! Clique no link acima para baixar.")
                            except ImportError:
                                st.warning("A exportação para DOCX requer a instalação do pypandoc. Execute `pip install pypandoc` para habilitar esta funcionalidade.")
                    except Exception as e:
                        st.error(f"Erro ao gerar DOCX: {str(e)}")

            # Aba para exportar para LaTeX
            with export_tabs[2]:
                if st.button("Gerar LaTeX", key=f"{tab_id}_latex_{article_id}_{index}"):
                    try:
                        with st.spinner("Gerando LaTeX..."):
                            try:
                                # Gerar o LaTeX
                                latex_content = ExportManager.markdown_to_latex(article_content, title=title)

                                # Criar arquivo em bytes
                                latex_bytes = latex_content.encode('utf-8')

                                # Criar link de download
                                download_link = ExportManager.get_download_link(
                                    latex_bytes,
                                    filename=f"{article_info.get('sanitized_title', 'artigo')}.tex",
                                    mime_type="application/x-tex"
                                )

                                # Exibir link de download
                                st.markdown(download_link, unsafe_allow_html=True)
                                st.success("LaTeX gerado com sucesso! Clique no link acima para baixar.")
                            except ImportError:
                                st.warning("A exportação para LaTeX requer a instalação do pypandoc. Execute `pip install pypandoc` para habilitar esta funcionalidade.")
                    except Exception as e:
                        st.error(f"Erro ao gerar LaTeX: {str(e)}")

            # Aba para exportar para HTML
            with export_tabs[3]:
                if st.button("Gerar HTML", key=f"{tab_id}_html_{article_id}_{index}"):
                    try:
                        with st.spinner("Gerando HTML..."):
                            # Gerar o HTML
                            html_content = ExportManager.markdown_to_html(article_content, title=title)

                            # Criar arquivo em bytes
                            html_bytes = html_content.encode('utf-8')

                            # Criar link de download
                            download_link = ExportManager.get_download_link(
                                html_bytes,
                                filename=f"{article_info.get('sanitized_title', 'artigo')}.html",
                                mime_type="text/html"
                            )

                            # Exibir link de download
                            st.markdown(download_link, unsafe_allow_html=True)
                            st.success("HTML gerado com sucesso! Clique no link acima para baixar.")
                    except Exception as e:
                        st.error(f"Erro ao gerar HTML: {str(e)}")

            # Aba para exportar para diretório
            with export_tabs[4]:
                if st.button("Exportar para diretório", key=f"{tab_id}_export_{article_id}_{index}"):
                    export_dir = st.text_input("Digite o caminho do diretório de destino:", key=f"{tab_id}_export_dir_{article_id}_{index}")
                    if export_dir and st.button("Confirmar exportação", key=f"{tab_id}_confirm_export_{article_id}_{index}"):
                        try:
                            with st.spinner("Exportando artigo..."):
                                if history_manager.export_article(article_id, export_dir):
                                    st.success(f"Artigo exportado com sucesso para {export_dir}")
                                else:
                                    st.error("Erro ao exportar artigo.")
                        except Exception as e:
                            st.error(f"Erro ao exportar artigo: {str(e)}")

        with col2:
            # Abas para fontes, verificação de plágio, metadados e ações
            source_tab, plagiarism_tab, metadata_tab, actions_tab = st.tabs(["Fontes", "Verificação de Plágio", "Tags e Categorias", "Ações"])

            with source_tab:
                if os.path.exists(sources_path):
                    sources_content = display_sources(sources_path)
                    st.markdown(sources_content)
                else:
                    st.info("Fontes não disponíveis para este artigo.")

            with plagiarism_tab:
                if os.path.exists(sources_path) and article_content:
                    # Botão para verificar plágio
                    if st.button("Verificar Plágio", key=f"{tab_id}_plagiarism_{article_id}_{index}"):
                        with st.spinner("Verificando plágio..."):
                            try:
                                # Carregar as fontes
                                with open(sources_path, 'r', encoding='utf-8') as f:
                                    sources = json.load(f)

                                # Converter para o formato esperado pelo verificador
                                research_results = []
                                for url, info in sources.items():
                                    info['url'] = url
                                    research_results.append(info)

                                # Verificar plágio
                                plagiarism_results = PlagiarismChecker.check_plagiarism(
                                    article_content, research_results
                                )

                                # Exibir resultados
                                report = PlagiarismChecker.format_plagiarism_report(plagiarism_results)
                                st.markdown(report)
                            except Exception as e:
                                st.error(f"Erro ao verificar plágio: {str(e)}")
                else:
                    st.info("Fontes ou artigo não disponíveis para verificação de plágio.")

            with metadata_tab:
                # Exibir tags atuais
                if "tags" in article_info and article_info["tags"]:
                    st.markdown("### Tags atuais")
                    for tag in article_info["tags"]:
                        tag_col1, tag_col2 = st.columns([3, 1])
                        with tag_col1:
                            st.markdown(f"- {tag}")
                        with tag_col2:
                            if st.button("Remover", key=f"remove_tag_{tag}_{article_id}_{index}"):
                                if history_manager.remove_tag(article_id, tag):
                                    st.success(f"Tag '{tag}' removida!")
                                    st.rerun()
                                else:
                                    st.error("Erro ao remover tag.")

                # Adicionar nova tag
                st.markdown("### Adicionar tag")
                new_tag = st.text_input("Nova tag:", key=f"new_tag_{article_id}_{index}")
                if st.button("Adicionar Tag", key=f"add_tag_{article_id}_{index}"):
                    if new_tag.strip():
                        if history_manager.add_tag(article_id, new_tag.strip()):
                            st.success(f"Tag '{new_tag}' adicionada!")
                            st.rerun()
                        else:
                            st.error("Erro ao adicionar tag.")
                    else:
                        st.warning("Digite uma tag válida.")

                # Gerenciar categoria
                st.markdown("### Categoria")
                current_category = article_info.get("category", "Sem categoria")
                st.markdown(f"**Categoria atual:** {current_category}")

                # Obter todas as categorias existentes
                all_categories = history_manager.get_all_categories()
                if "Sem categoria" not in all_categories:
                    all_categories.append("Sem categoria")

                # Adicionar opção para nova categoria
                all_categories.append("+ Nova categoria")

                selected_category = st.selectbox(
                    "Selecione uma categoria:",
                    all_categories,
                    index=all_categories.index(current_category) if current_category in all_categories else 0,
                    key=f"category_select_{article_id}_{index}"
                )

                if selected_category == "+ Nova categoria":
                    new_category = st.text_input("Nome da nova categoria:", key=f"new_category_{article_id}_{index}")
                    if st.button("Criar Categoria", key=f"create_category_{article_id}_{index}"):
                        if new_category.strip():
                            if history_manager.set_category(article_id, new_category.strip()):
                                st.success(f"Categoria alterada para '{new_category}'!")
                                st.rerun()
                            else:
                                st.error("Erro ao definir categoria.")
                        else:
                            st.warning("Digite um nome válido para a categoria.")
                elif selected_category != current_category:
                    if st.button("Atualizar Categoria", key=f"update_category_{article_id}_{index}"):
                        if history_manager.set_category(article_id, selected_category):
                            st.success(f"Categoria alterada para '{selected_category}'!")
                            st.rerun()
                        else:
                            st.error("Erro ao definir categoria.")

            with actions_tab:
                st.warning("⚠️ Ações irreversíveis")

                # Botão para deletar o artigo
                st.markdown("### 🗑️ Deletar artigo")
                st.markdown("Esta ação irá remover permanentemente o artigo e todos os seus arquivos.")
                st.markdown("⚠️ **Atenção:** Esta ação não pode ser desfeita!")

                # Usar colunas em vez de expander para evitar aninhamento
                delete_col1, delete_col2 = st.columns([1, 1])

                with delete_col1:
                    delete_confirm = st.checkbox("Confirmo que desejo deletar este artigo", key=f"confirm_delete_{article_id}_{index}")

                with delete_col2:
                    if delete_confirm:
                        if st.button("🗑️ Deletar Permanentemente", key=f"delete_{article_id}_{index}", type="primary"):
                            with st.spinner("Deletando artigo..."):
                                if history_manager.delete_article(article_id):
                                    st.success("✅ Artigo deletado com sucesso!")
                                    # Adicionar um botão para voltar à lista de artigos
                                    if st.button("Voltar à lista de artigos"):
                                        st.rerun()
                                    # Recarregar a página após 3 segundos
                                    st.markdown("<meta http-equiv='refresh' content='3'>", unsafe_allow_html=True)
                                    st.markdown("Redirecionando para a lista de artigos em 3 segundos...")
                                else:
                                    st.error("❌ Erro ao deletar o artigo. Tente novamente.")
                    else:
                        st.button("🗑️ Deletar Permanentemente", key=f"delete_disabled_{article_id}_{index}", disabled=True)

    # Aba para ver artigos existentes
    # Aba para geração de capítulos
    with tab2:
        render_chapter_generator_ui()

    # Aba para artigos existentes
    with tab3:
        st.header("Meus Artigos")

        # Obter o histórico de artigos
        history = history_manager.get_history()

        if not history:
            # Verificar se há artigos no diretório, mas não no histórico
            article_dirs = [d for d in os.listdir(output_dir) if os.path.isdir(os.path.join(output_dir, d))]

            if not article_dirs:
                st.info("Nenhum artigo encontrado. Crie um novo artigo na aba 'Criar Novo Artigo'.")
            else:
                st.warning("Encontrados artigos no diretório, mas não no histórico. Estes artigos podem ter sido criados com uma versão anterior do aplicativo.")

                # Mostrar os artigos encontrados
                for article_dir in article_dirs:
                    st.write(f"- {article_dir.replace('_', ' ').title()}")
        else:
            # Exibir opções de filtragem e ordenação
            col_filter, col_sort, col_tags = st.columns(3)

            with col_filter:
                search_term = st.text_input("Filtrar por título:", key="history_search")

                # Filtrar por categoria
                categories = ["Todas as categorias"] + history_manager.get_all_categories()
                selected_category = st.selectbox("Filtrar por categoria:", categories, index=0, key="filter_category")

            with col_sort:
                sort_option = st.selectbox(
                    "Ordenar por:",
                    ["Mais recentes", "Mais antigos", "Título (A-Z)", "Título (Z-A)"],
                    index=0
                )

            with col_tags:
                # Filtrar por tags
                all_tags = history_manager.get_all_tags()
                if all_tags:
                    selected_tags = st.multiselect("Filtrar por tags:", all_tags, key="filter_tags")
                else:
                    st.info("Nenhuma tag disponível")
                    selected_tags = []

            # Aplicar filtragem por título
            filtered_history = history
            if search_term:
                filtered_history = [a for a in filtered_history if search_term.lower() in a.get("title", "").lower()]

            # Aplicar filtragem por categoria
            if selected_category != "Todas as categorias":
                filtered_history = [a for a in filtered_history if a.get("category", "Sem categoria") == selected_category]

            # Aplicar filtragem por tags
            if selected_tags:
                filtered_history = [a for a in filtered_history if
                                  "tags" in a and
                                  all(tag in a["tags"] for tag in selected_tags)]

            # Aplicar ordenação
            if sort_option == "Mais recentes":
                filtered_history = sorted(filtered_history, key=lambda x: x.get("created_at", ""), reverse=True)
            elif sort_option == "Mais antigos":
                filtered_history = sorted(filtered_history, key=lambda x: x.get("created_at", ""))
            elif sort_option == "Título (A-Z)":
                filtered_history = sorted(filtered_history, key=lambda x: x.get("title", "").lower())
            elif sort_option == "Título (Z-A)":
                filtered_history = sorted(filtered_history, key=lambda x: x.get("title", "").lower(), reverse=True)

            # Exibir os artigos
            if not filtered_history:
                st.info("Nenhum artigo encontrado com os filtros aplicados.")
            else:
                # Exibir os artigos em uma lista
                for i, article in enumerate(filtered_history):
                    # Criar um layout com título e botões de ação
                    article_id = article.get("id")
                    title_col, actions_col = st.columns([5, 1])

                    with title_col:
                        expander_title = f"{article.get('title', 'Artigo sem título')}"
                        if "category" in article and article["category"] != "Sem categoria":
                            expander_title += f" [{article['category']}]"

                    with actions_col:
                        # Botão de exclusão rápida
                        if st.button("🗑️", key=f"quick_delete_{article_id}_{i}", help="Deletar artigo"):
                            st.session_state[f"confirm_quick_delete_{article_id}_{i}"] = True

                    # Confirmação de exclusão
                    if st.session_state.get(f"confirm_quick_delete_{article_id}_{i}", False):
                        st.warning("⚠️ Tem certeza que deseja deletar este artigo? Esta ação não pode ser desfeita.")
                        confirm_col1, confirm_col2 = st.columns([1, 1])
                        with confirm_col1:
                            if st.button("Cancelar", key=f"cancel_delete_{article_id}_{i}"):
                                st.session_state[f"confirm_quick_delete_{article_id}_{i}"] = False
                                st.rerun()
                        with confirm_col2:
                            if st.button("🗑️ Confirmar Exclusão", key=f"confirm_delete_{article_id}_{i}", type="primary"):
                                if history_manager.delete_article(article_id):
                                    st.success("✅ Artigo deletado com sucesso!")
                                    st.rerun()
                                else:
                                    st.error("❌ Erro ao deletar o artigo.")

                    with st.expander(expander_title, expanded=(i == 0)):
                        # Verificar se é favorito
                        is_favorite = history_manager.is_favorite(article.get("id"))

                        # Exibir o artigo
                        display_article_ui(article, is_favorite=is_favorite, tab_id="history", index=i)

    # Aba para favoritos
    with tab4:
        st.header("Artigos Favoritos")

        # Obter os favoritos
        favorites = history_manager.get_favorites()

        if not favorites:
            st.info("Nenhum artigo favorito encontrado. Adicione artigos aos favoritos na aba 'Meus Artigos'.")
        else:
            # Exibir os favoritos
            for i, article in enumerate(favorites):
                # Criar um layout com título e botões de ação
                article_id = article.get("id")
                title_col, actions_col = st.columns([5, 1])

                with title_col:
                    expander_title = f"{article.get('title', 'Artigo sem título')}"
                    if "category" in article and article["category"] != "Sem categoria":
                        expander_title += f" [{article['category']}]"

                with actions_col:
                    # Botão de exclusão rápida
                    if st.button("🗑️", key=f"fav_quick_delete_{article_id}_{i}", help="Deletar artigo"):
                        st.session_state[f"fav_confirm_quick_delete_{article_id}_{i}"] = True

                # Confirmação de exclusão
                if st.session_state.get(f"fav_confirm_quick_delete_{article_id}_{i}", False):
                    st.warning("⚠️ Tem certeza que deseja deletar este artigo? Esta ação não pode ser desfeita.")
                    confirm_col1, confirm_col2 = st.columns([1, 1])
                    with confirm_col1:
                        if st.button("Cancelar", key=f"fav_cancel_delete_{article_id}_{i}"):
                            st.session_state[f"fav_confirm_quick_delete_{article_id}_{i}"] = False
                            st.rerun()
                    with confirm_col2:
                        if st.button("🗑️ Confirmar Exclusão", key=f"fav_confirm_delete_{article_id}_{i}", type="primary"):
                            if history_manager.delete_article(article_id):
                                st.success("✅ Artigo deletado com sucesso!")
                                st.rerun()
                            else:
                                st.error("❌ Erro ao deletar o artigo.")

                with st.expander(expander_title, expanded=(i == 0)):
                    # Exibir o artigo
                    display_article_ui(article, is_favorite=True, tab_id="favorites", index=i)

if __name__ == "__main__":
    main()
