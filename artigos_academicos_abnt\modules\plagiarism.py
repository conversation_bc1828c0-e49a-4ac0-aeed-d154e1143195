"""
Módulo de verificação de plágio para o aplicativo de artigos acadêmicos ABNT.
Implementa algoritmos para detectar trechos potencialmente plagiados em artigos.
"""

import re

class PlagiarismChecker:
    """Classe para verificar plágio em artigos acadêmicos com algoritmos avançados"""
    
    @staticmethod
    def _normalize_text(text):
        """Normaliza o texto para comparação (remove pontuação, converte para minúsculas)"""
        # Remover pontuação e converter para minúsculas
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        # Remover espaços extras
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    @staticmethod
    def _get_ngrams(text, n=5):
        """Extrai n-gramas de um texto"""
        words = text.split()
        return [' '.join(words[i:i+n]) for i in range(len(words)-n+1)]
    
    @staticmethod
    def _calculate_similarity(text1, text2, n=5):
        """Calcula a similaridade entre dois textos usando n-gramas"""
        # Normalizar os textos
        norm_text1 = PlagiarismChecker._normalize_text(text1)
        norm_text2 = PlagiarismChecker._normalize_text(text2)
        
        # Extrair n-gramas
        ngrams1 = set(PlagiarismChecker._get_ngrams(norm_text1, n))
        ngrams2 = set(PlagiarismChecker._get_ngrams(norm_text2, n))
        
        # Calcular a interseção
        intersection = ngrams1.intersection(ngrams2)
        
        # Calcular a similaridade de Jaccard
        union = ngrams1.union(ngrams2)
        similarity = len(intersection) / len(union) if union else 0
        
        return similarity, intersection
    
    @staticmethod
    def _find_matching_fragments(text, intersection_ngrams):
        """Encontra os fragmentos correspondentes no texto original"""
        matching_fragments = []
        
        # Para cada n-grama na interseção, encontrar o contexto no texto original
        for ngram in intersection_ngrams:
            # Encontrar a posição do n-grama no texto original
            norm_text = PlagiarismChecker._normalize_text(text)
            ngram_pos = norm_text.find(ngram)
            
            if ngram_pos >= 0:
                # Encontrar o início da sentença
                start_pos = text.rfind('. ', 0, ngram_pos)
                if start_pos < 0:
                    start_pos = 0
                else:
                    start_pos += 2  # Pular o ponto e o espaço
                
                # Encontrar o fim da sentença
                end_pos = text.find('. ', ngram_pos)
                if end_pos < 0:
                    end_pos = len(text)
                else:
                    end_pos += 1  # Incluir o ponto
                
                # Extrair o fragmento
                fragment = text[start_pos:end_pos].strip()
                
                # Adicionar à lista se não for muito curto
                if len(fragment) > 20 and fragment not in [f['text'] for f in matching_fragments]:
                    matching_fragments.append({
                        'text': fragment,
                        'position': start_pos
                    })
        
        return matching_fragments
    
    @staticmethod
    def check_plagiarism(text, research_results, similarity_threshold=0.3, ngram_size=5):
        """Verifica se há trechos plagiados no texto comparando com as fontes de pesquisa"""
        # Inicializar resultados
        plagiarism_results = []
        
        # Dividir o texto em parágrafos
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        
        # Para cada fonte de pesquisa
        for result in research_results:
            description = result.get('description', '')
            if not description or len(description) < 100:  # Ignorar descrições muito curtas
                continue
            
            # Para cada parágrafo
            for i, paragraph in enumerate(paragraphs):
                # Ignorar parágrafos muito curtos ou títulos
                if len(paragraph) < 100 or paragraph.startswith('#'):
                    continue
                
                # Calcular similaridade
                similarity, intersection = PlagiarismChecker._calculate_similarity(
                    paragraph, description, ngram_size
                )
                
                # Se a similaridade estiver acima do limiar
                if similarity > similarity_threshold and intersection:
                    # Encontrar os fragmentos correspondentes
                    matching_fragments = PlagiarismChecker._find_matching_fragments(
                        paragraph, intersection
                    )
                    
                    # Adicionar aos resultados
                    for fragment in matching_fragments:
                        plagiarism_results.append({
                            'paragraph_index': i,
                            'paragraph': paragraph,
                            'fragment': fragment['text'],
                            'similarity': similarity,
                            'source': result.get('title', 'Fonte desconhecida'),
                            'source_url': result.get('url', '#'),
                            'authors': result.get('authors', [])
                        })
        
        # Ordenar por similaridade (decrescente)
        plagiarism_results.sort(key=lambda x: x['similarity'], reverse=True)
        
        return plagiarism_results
    
    @staticmethod
    def format_plagiarism_report(plagiarism_results):
        """Formata os resultados da verificação de plágio em um relatório legível"""
        if not plagiarism_results:
            return "✅ **Nenhum plágio detectado.**"
            
        report = "### ⚠️ Possíveis trechos plagiados detectados\n\n"
        report += f"**Total de trechos suspeitos:** {len(plagiarism_results)}\n\n"
        
        # Agrupar por fonte
        sources = {}
        for result in plagiarism_results:
            source = result['source']
            if source not in sources:
                sources[source] = []
            sources[source].append(result)
        
        # Gerar relatório por fonte
        for source, results in sources.items():
            report += f"## Fonte: [{source}]({results[0]['source_url']})\n\n"
            
            if results[0]['authors']:
                report += f"**Autores:** {', '.join(results[0]['authors'])}\n\n"
            
            report += f"**Trechos suspeitos:** {len(results)}\n\n"
            
            for i, result in enumerate(results, 1):
                report += f"### Trecho {i}\n\n"
                report += f"**Similaridade:** {result['similarity']*100:.1f}%\n\n"
                report += f"**Texto suspeito:**\n```\n{result['fragment']}\n```\n\n"
                
                # Adicionar contexto do parágrafo se for diferente do fragmento
                if len(result['paragraph']) > len(result['fragment']) + 20:
                    report += f"**Contexto do parágrafo:**\n```\n{result['paragraph'][:200]}...\n```\n\n"
            
            report += "---\n\n"
        
        report += "\n### Como interpretar este relatório\n\n"
        report += "* **Similaridade:** Indica o grau de semelhança entre o texto e a fonte. Valores acima de 50% são altamente suspeitos.\n"
        report += "* **Texto suspeito:** O trecho específico que foi identificado como potencialmente plagiado.\n"
        report += "* **Contexto:** O parágrafo onde o trecho foi encontrado, para ajudar a entender o contexto.\n\n"
        report += "**Nota:** Esta verificação usa algoritmos de comparação de texto e pode conter falsos positivos. "
        report += "Sempre revise manualmente os trechos indicados e certifique-se de citar adequadamente as fontes utilizadas."
        
        return report
