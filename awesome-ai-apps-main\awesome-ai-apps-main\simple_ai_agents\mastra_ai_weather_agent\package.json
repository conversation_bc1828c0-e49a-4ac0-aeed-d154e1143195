{"name": "examples-using-a-tool", "type": "module", "private": true, "scripts": {"start": "pnpx tsx index.ts"}, "dependencies": {"@ai-sdk/openai": "latest", "@ai-sdk/openai-compatible": "^0.2.14", "@mastra/core": "latest", "dotenv": "^16.4.5", "zod": "^3.24.3"}, "version": "0.0.1", "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}