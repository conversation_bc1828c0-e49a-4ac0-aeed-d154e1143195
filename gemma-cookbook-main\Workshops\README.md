# Workshops and technical talks

This folder provides resources from workshops and technical talks centered around Gemma models. Here, you'll find in-depth notebooks that go beyond basic usage and explore advanced techniques like context window manipulation.

| Notebook Name | Description |
| --------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Workshop_How_to_Fine_tuning_Gemma.ipynb](Workshop_How_to_Fine_tuning_Gemma.ipynb) | Recommended finetuning notebook for getting started                                                                                                |
| [Workshop_How_to_Fine_tuning_Gemma_Transformers_Edition.ipynb](Workshop_How_to_Fine_tuning_Gemma_Transformers_Edition.ipynb) | Transformers version of the finetuning notebook                                                                                               |
| [[Gemma_1]Self_extend.ipynb]([Gemma_1]Self_extend.ipynb)                                                                    | Self-extend context window for Gemma in the I/O 2024 [Keras talk](https://www.youtube.com/watch?v=TV7qCk1dBWA)                                     |
| [[Gemma_2]control_vectors.ipynb]([Gemma_2]control_vectors.ipynb)                                                            | Implement [control vectors](https://arxiv.org/abs/2310.01405) with Gemma in the I/O 2024 [Keras talk](https://www.youtube.com/watch?v=TV7qCk1dBWA) |