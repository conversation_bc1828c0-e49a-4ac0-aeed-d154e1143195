# Managing template image assets

Image assets used in templates in the `flutter` tool are maintained in the [flutter_template_images][fti_pkg] package on [Pub.dev][pub] and are not checked into the main [flutter/flutter][flutter_repo] repository.

Flutter's presubmit checks prevent binaries from being checked in to the repository. Binary files add significant size to the repository and produce large diffs when updated. This negatively impacts Flutter users when running `flutter upgrade`. Downloading a pub package does not involve a full sync of the git history, and is therefore significantly lighter weight.

## Adding/Updating template image assets

In order to add or update image assets, two patches are required: one to the `flutter/packages` repository and one to the `flutter/flutter` repository.

The [flutter_template_images][fti_repo] package repository contains a [`templates`][package_templates_dir] directory laid out identically to the [`templates`][tools_templates_dir] directory in the [flutter_tools][tools_repo] package in the main repository. Assets are first added to the flutter_template_images repo, which is then rolled into the main repository, where the template files and manifest must be updated.

### Add the assets to flutter_template_images

First, add the images to the flutter_template_images package:

1. Fork the [flutter/packages][packages_repo] repo on GitHub.
2. Add the images at the relevant path under the `templates` directory. Filenames should be the exact final filenames as they will appear in a project generated by the `flutter` tool. They should _not_ include a `.img.tmpl` suffix.
3. Update the version in [`pubspec.yaml`](https://github.com/flutter/packages/blob/main/packages/flutter_template_images/pubspec.yaml). For new assets, increment the _minor_ version in the `major.minor.patch` version number; for updates to existing assets, increment the _patch_ version.
4. Add new heading for this version in [`CHANGELOG.md`](https://github.com/flutter/packages/blob/main/packages/flutter_template_images/CHANGELOG.md) and add a line describing your change. Follow the [Changelog Style](../ecosystem/contributing/README.md#changelog-style) guidelines.
5. Commit your changes and send a pull request.
6. Once your changes are landed, a post-submit job will automatically publish the new version on Pub. You can verify this has occurred under the _Versions_ tab of the [flutter_template_images][fti_pkg] page.

### Update Flutter package dependencies

Once the updated package has been published on Pub, roll your changes into the main Flutter repository.

1. Fork the [flutter/flutter][flutter_repo] repo on GitHub.
2. Edit [`packages/flutter_tools/lib/src/commands/update_packages.dart`](https://github.com/flutter/flutter/blob/main/packages/flutter_tools/lib/src/commands/update_packages.dart) and update the pinned version number for the `flutter_template_images` to the new version.
3. From within the repo directory, run `flutter update-packages --force-upgrade`.
4. For each image that you added to `flutter_template_images`, add a zero-byte file with a `.img.tmpl` suffix in the corresponding location under the [`templates`][tools_templates_dir] directory of the `flutter_tools` package.
5. Edit [`packages/flutter_tools/templates/template_manifest.json`](https://github.com/flutter/flutter/blob/main/packages/flutter_tools/templates/template_manifest.json) and add an entry for each file you added, including the `.img.tmpl` suffix.
6. Commit your changes, and perform a test run to be sure they're emitted successfully by the `flutter` tool.
7. Assuming everything looks good, send a pull request.

_(This page is referenced by an error message in the Flutter presubmit checks.)_

[fti_pkg]: https://pub.dev/packages/flutter_template_images
[fti_repo]: https://github.com/flutter/packages/tree/main/packages/flutter_template_images
[pub]: https://pub.dev
[flutter_repo]: https://github.com/flutter/flutter
[packages_repo]: https://github.com/flutter/packages
[package_templates_dir]: https://github.com/flutter/packages/tree/main/packages/flutter_template_images/templates
[tools_templates_dir]: https://github.com/flutter/flutter/tree/main/packages/flutter_tools/templates
[tools_repo]: https://github.com/flutter/flutter/tree/main/packages/flutter_tools
