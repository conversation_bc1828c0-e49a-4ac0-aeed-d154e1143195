import os
from dotenv import load_dotenv
from textwrap import dedent
from phi.assistant import Assistant
from phi.llm.base import LLM
from phi.tools.serpapi_tools import SerpApiTools
from phi.tools.newspaper4k import Newspaper4k as NewspaperToolkit
import streamlit as st
from groq import Groq
from pydantic import Field, PrivateAttr
from typing import List, Dict

load_dotenv()

class GroqChat(LLM):
    model: str = Field(default="llama-3.1-8b-instant")
    api_key: str = Field(...)

    _client: Groq = PrivateAttr()

    def __init__(self, api_key: str, model: str = "llama-3.1-8b-instant"):
        super().__init__(model=model, api_key=api_key)
        self._client = Groq(api_key=api_key)

    def chat(self, messages: List[Dict[str, str]]) -> str:
        completion = self._client.chat.completions.create(
            messages=messages,
            model=self.model
        )
        return completion.choices[0].message.content

    def complete(self, prompt: str) -> str:
        return self.chat([{"role": "user", "content": prompt}])

    def response(self, messages: List[Dict[str, str]]) -> str:
        return self.chat(messages)

st.title("AI Startup Trend Analysis Agent 📈")
st.caption("Get the latest trend analysis and startup opportunities based on your topic of interest in a click!.")

topic = st.text_input("Enter the area of interest for your Startup:")
groq_api_key = st.sidebar.text_input("Enter Groq API Key", value=os.getenv("GROQ_API_KEY", ""), type="password")
serp_api_key = st.sidebar.text_input("Enter Serp API Key", value=os.getenv("SERP_API_KEY", ""), type="password")

if st.button("Generate Analysis"):
    if not groq_api_key or not serp_api_key:
        st.warning("Please enter the required API keys.")
    else:
        with st.spinner("Processing your request..."):
            try:
                llm = GroqChat(api_key=groq_api_key)

                # Define News Collector Assistant
                searcher = Assistant(
                    name="News Collector",
                    role="Collects recent news articles on the given topic",
                    llm=llm,
                    description=dedent(
                        """You are an AI assistant that collects recent news articles on a given topic. 
                        Search the web using SerpApiTools and return a list of relevant URLs."""
                    ),
                    instructions=[
                        "Given a topic, generate search terms to find the most relevant news articles.",
                        "Use the SerpApi tools to perform the search and collect the most relevant URLs.",
                        "Return a list of URLs with a brief description of the article for each."],
                    tools=[SerpApiTools(api_key=serp_api_key)],
                    add_datetime_to_instructions=True,
                )

                # Define Summary Writer Assistant
                writer = Assistant(
                    name="Summary Writer",
                    role="Summarizes collected news articles",
                    llm=llm,
                    description=dedent(
                        """You are an AI assistant that summarizes articles. Given a list of URLs, 
                        retrieve their content and generate concise summaries."""
                    ),
                    instructions=[
                        "Given a list of URLs, fetch their content using the Newspaper Toolkit.",
                        "Generate concise summaries for each article, focusing on key points and insights.",
                        "Ensure the summaries are well-structured and informative."],
                    tools=[NewspaperToolkit()],
                    add_datetime_to_instructions=True,
                )

                # Define Trend Analyzer Assistant
                analyzer = Assistant(
                    name="Trend Analyzer",
                    role="Analyzes trends from summaries",
                    llm=llm,
                    description=dedent(
                        """You are an AI assistant that identifies emerging trends and startup opportunities 
                        based on summarized news articles."""
                    ),
                    instructions=[
                        "Analyze the provided summaries to identify emerging trends.",
                        "Highlight potential startup opportunities based on the trends.",
                        "Prepare a detailed and actionable report."],
                    add_datetime_to_instructions=True,
                )

                # Define Editor Assistant to orchestrate the workflow
                editor = Assistant(
                    name="Trend Analysis Editor",
                    llm=llm,
                    team=[searcher, writer, analyzer],
                    description="Coordinates the workflow to generate trend analysis and startup opportunities.",
                    instructions=[
                        "Start by using the News Collector to find relevant articles based on the topic.",
                        "Pass the URLs to the Summary Writer to generate summaries of the articles.",
                        "Finally, provide the summaries to the Trend Analyzer to produce a detailed analysis report."],
                    markdown=True,
                )

                # Execute the workflow
                response = editor.run(f"Analyze trends for the topic: {topic}", stream=False)

                st.subheader("Trend Analysis and Potential Startup Opportunities")
                st.write(response)

            except Exception as e:
                st.error(f"An error occurred: {e}")
else:
    st.info("Enter the topic and API keys, then click 'Generate Analysis' to start.")
