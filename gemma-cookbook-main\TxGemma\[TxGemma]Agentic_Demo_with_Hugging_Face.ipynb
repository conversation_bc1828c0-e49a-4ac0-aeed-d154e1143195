{"cells": [{"cell_type": "markdown", "metadata": {"id": "1fgVWTMK9SNz"}, "source": ["~~~\n", "Copyright 2025 Google LLC\n", "\n", "Licensed under the Apache License, Version 2.0 (the \"License\");\n", "you may not use this file except in compliance with the License.\n", "You may obtain a copy of the License at\n", "\n", "    https://www.apache.org/licenses/LICENSE-2.0\n", "\n", "Unless required by applicable law or agreed to in writing, software\n", "distributed under the License is distributed on an \"AS IS\" BASIS,\n", "WITHOUT WARRANTIES OR <PERSON>NDITIONS OF ANY KIND, either express or implied.\n", "See the License for the specific language governing permissions and\n", "limitations under the License.\n", "~~~\n", "\n", "# Agentic-Tx Demo with Hugging Face\n", "\n", "<table><tbody><tr>\n", "  <td style=\"text-align: center\">\n", "    <a href=\"https://colab.research.google.com/github/google-gemini/gemma-cookbook/blob/main/TxGemma/[TxGemma]Agentic_Demo_with_Hugging_Face.ipynb\">\n", "      <img alt=\"Google Colab logo\" src=\"https://www.tensorflow.org/images/colab_logo_32px.png\" width=\"32px\"><br> Run in Google Colab\n", "    </a>\n", "  </td>\n", "  <td style=\"text-align: center\">\n", "    <a href=\"https://github.com/google-gemini/gemma-cookbook/blob/main/TxGemma/%5BTxGemma%5DAgentic_Demo_with_Hugging_Face.ipynb\">\n", "      <img alt=\"GitHub logo\" src=\"https://cloud.google.com/ml-engine/images/github-logo-32px.png\" width=\"32px\"><br> View on GitHub\n", "    </a>\n", "  </td>\n", "  <td style=\"text-align: center\">\n", "    <a href=\"https://huggingface.co/collections/google/txgemma-release-67dd92e931c857d15e4d1e87\">\n", "      <img alt=\"HuggingFace logo\" src=\"https://huggingface.co/front/assets/huggingface_logo-noborder.svg\" width=\"32px\"><br> View on HuggingFace\n", "    </a>\n", "  </td>\n", "</tr></tbody></table>\n", "\n", "This Colab notebook provides a basic demo of using Agentic-Tx, a therapeutics-focused LLM agent. Agentic-Tx builds on TxGemma, a collection of large language models built upon Gemma 2, that generates predictions, classifications or text based on therapeutic related data.\n", "\n", "Learn more about TxGemma at [this page](https://developers.google.com/health-ai-developer-foundations/txgemma)."]}, {"cell_type": "markdown", "metadata": {"id": "t9xt2XZgaaH2"}, "source": ["## Setup\n", "\n", "To complete this tutorial, you'll need to have a Colab runtime with sufficient resources to run the TxGemma model. Choose an appropriate runtime when starting your Colab session.\n", "\n", "You can try out TxGemma 2B and 9B* for free using a T4 GPU:\n", "\n", "1. In the upper-right of the Colab window, select **▾ (Additional connection options)**.\n", "2. Select **Change runtime type**.\n", "3. Under **Hardware accelerator**, select **T4 GPU**.\n", "\n", "*To run the demo with both TxGemma 2B predict and 9B chat models on a T4 GPU, use 4-bit quantization to reduce memory usage and speed up inference. Note that the performance of quantized versions has not been evaluated."]}, {"cell_type": "markdown", "metadata": {"id": "L9ITcQtdal7J"}, "source": ["### Get access to TxGemma\n", "\n", "Before you get started, make sure that you have access to TxGemma models on Hugging Face:\n", "\n", "1. If you don't already have a Hugging Face account, you can create one for free by clicking [here](https://huggingface.co/join).\n", "2. Head over to the [TxGemma model page](https://huggingface.co/google/txgemma-2b-predict) and accept the usage conditions."]}, {"cell_type": "markdown", "metadata": {"id": "qRFQnPL2a9Dj"}, "source": ["### Configure your HF token and Gemini token\n", "\n", "Add your Hugging Face & Gemini token to the Colab Secrets manager to securely store it.\n", "\n", "1. Ensure you have a [Gemini API key](https://ai.google.dev/gemini-api/docs/api-key) and a [HF_Token](https://huggingface.co/docs/hub/en/security-tokens)\n", "2. Open your Google Colab notebook and click on the 🔑 Secrets tab in the left panel. <img src=\"https://storage.googleapis.com/generativeai-downloads/images/secrets.jpg\" alt=\"The Secrets tab is found on the left panel.\" width=50%>\n", "\n", "3. Create two new secrets with the name `HF_TOKEN` and `GEMINI_API_KEY`.\n", "4. Copy/paste your token key into the Value input box of `HF_TOKEN` and `GEMINI_API_KEY` .\n", "5. Toggle the button on the left to allow notebook access to the secret."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "SJRgvl-Wh_VM"}, "outputs": [], "source": ["import os, re\n", "from google.colab import userdata\n", "import google.generativeai as genai\n", "# Note: `userdata.get` is a Colab API. If you're not using Colab, set the env\n", "# vars as appropriate for your system.\n", "\n", "os.environ[\"HF_TOKEN\"] = userdata.get(\"HF_TOKEN\")\n", "genai.configure(api_key=userdata.get(\"GEMINI_API_KEY\"))"]}, {"cell_type": "markdown", "metadata": {"id": "qocWBSYmb0MA"}, "source": ["### Install dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "JyPXnIjML6go"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.4/44.4 kB\u001b[0m \u001b[31m2.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m9.7/9.7 MB\u001b[0m \u001b[31m77.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m345.1/345.1 kB\u001b[0m \u001b[31m40.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.1/76.1 MB\u001b[0m \u001b[31m30.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m469.0/469.0 kB\u001b[0m \u001b[31m53.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["! pip install --upgrade --quiet accelerate bitsandbytes huggingface_hub transformers"]}, {"cell_type": "markdown", "metadata": {"id": "_z8YZ8pT1QD5"}, "source": ["### Load prompt template\n", "\n", "First, load a JSON file that contains the prompt format for various TDC tasks."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "tUhpMxJq1yNi"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "11d94039521046af9b281319ea5c3c6e", "version_major": 2, "version_minor": 0}, "text/plain": ["tdc_prompts.json:   0%|          | 0.00/768k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "from huggingface_hub import hf_hub_download\n", "\n", "tdc_prompts_filepath = hf_hub_download(\n", "    repo_id=\"google/txgemma-2b-predict\",\n", "    filename=\"tdc_prompts.json\",\n", ")\n", "\n", "with open(tdc_prompts_filepath, \"r\") as f:\n", "    tdc_prompts_json = json.load(f)"]}, {"cell_type": "markdown", "metadata": {"id": "4KUYpBH1cZA1"}, "source": ["### Download the prediction and chat model from Hugging Face Hub\n", "\n", "Here, we are going on HuggingFace to download and load both the prediction and chat version of TxGemma. These will later be transformed into tools.\n", "\n", "You can select the variants to use for Agentic-Tx at the dropdown and whether you want to include a chat variant. Make sure your runtime has enough RAM to run all the models."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "kTNyftcqF3YO"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The cache for model files in Transformers v4.22.0 has been updated. Migrating your old cache. This is a one-time only operation. You can interrupt this and resume the migration later on by calling `transformers.utils.move_cache()`.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d5828dcaf8ea4f57ad7ecae05bbe8453", "version_major": 2, "version_minor": 0}, "text/plain": ["0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0ca45e2f8a074635adfc5916fb63bbc2", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/46.4k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a0f1acc440b94bada19d2e6ef1c373f4", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.model:   0%|          | 0.00/4.24M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6e7369fdce024bbba2ee390edbcb332c", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/17.5M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5b7c3965c53d462d98b17fa113973a9b", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/636 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7a7aebb35eb746a182a73adf45540b27", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/818 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1237983a15914c9c901350e47778ddfd", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors.index.json:   0%|          | 0.00/24.2k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a14413b8a2814fcc93f7da3a51f6fde2", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading shards:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e219c1fa503345f9b1852c9fbde4e4c8", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00001-of-00003.safetensors:   0%|          | 0.00/4.99G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7c32e73d32724d58a1275f60889b9e30", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00002-of-00003.safetensors:   0%|          | 0.00/4.98G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "65402d1325874c04ac286cd5c1934cd1", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00003-of-00003.safetensors:   0%|          | 0.00/481M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d586dff8ec4b4d4287d8f9be84bd4d58", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b980cca76216477e9511800229d1e588", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/168 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6f397ae478c94323819f2515810a52e9", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/47.0k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c8a5515dbefe4a5e82b5010bd297a668", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.model:   0%|          | 0.00/4.24M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6876d5ddaa0e4f289fb14b9651334cf1", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/17.5M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7da5c8d3111b4b3299799a1c73e60947", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/636 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f02195dfa24f483cb12242fcfaef5439", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/852 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "00214dfa1fb248f49eb65030ff3b8203", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors.index.json:   0%|          | 0.00/39.1k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0e7bf2459d0f4d978f05549c7c0f5a61", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading shards:   0%|          | 0/4 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0e2d7a21a5e042e19c73b8bc8c1e3b35", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00001-of-00004.safetensors:   0%|          | 0.00/4.90G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5fedd941473642c3bf831e0b3781cd25", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00002-of-00004.safetensors:   0%|          | 0.00/4.95G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bc18e1ef7cd84a0ba7daa45505df9b74", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00003-of-00004.safetensors:   0%|          | 0.00/4.96G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ce9d02c94efe4027844eabc345edf17a", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00004-of-00004.safetensors:   0%|          | 0.00/3.67G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d40d746a3fd34dc68bd05db261e18185", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7d3637ab8af1485f96cc79495e8d2e26", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/168 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig\n", "\n", "PREDICT_VARIANT = \"2b-predict\"  # @param [\"2b-predict\", \"9b-predict\", \"27b-predict\"]\n", "CHAT_VARIANT = \"9b-chat\" # @param [\"9b-chat\", \"27b-chat\"]\n", "USE_CHAT = True # @param {type: \"boolean\"}\n", "\n", "quantization_config = BitsAndBytesConfig(load_in_4bit=True)\n", "\n", "predict_tokenizer = AutoTokenizer.from_pretrained(f\"google/txgemma-{PREDICT_VARIANT}\")\n", "predict_model = AutoModelForCausalLM.from_pretrained(\n", "    f\"google/txgemma-{PREDICT_VARIANT}\",\n", "    device_map=\"auto\",\n", "    quantization_config=quantization_config,\n", ")\n", "\n", "if USE_CHAT:\n", "    chat_tokenizer = AutoTokenizer.from_pretrained(f\"google/txgemma-{CHAT_VARIANT}\")\n", "    chat_model = AutoModelForCausalLM.from_pretrained(\n", "        f\"google/txgemma-{CHAT_VARIANT}\",\n", "        device_map=\"auto\",\n", "        quantization_config=quantization_config,\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "oBmbHe2Hg4Y_"}, "source": ["### Run inference on a sample binary classification task\n", "\n", "Let's first try making predictions with both the models to make sure they are working. We're just going to use a sample TDC task with a sample drug."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "UgEN-mgbO6Gm"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The 'batch_size' attribute of HybridCache is deprecated and will be removed in v4.49. Use the more precisely named 'self.max_batch_size' attribute instead.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Prediction model response: Instructions: Answer the following question about drug properties.\n", "Context: As a membrane separating circulating blood and brain extracellular fluid, the blood-brain barrier (BBB) is the protection layer that blocks most foreign drugs. Thus the ability of a drug to penetrate the barrier to deliver to the site of action forms a crucial challenge in development of drugs for central nervous system.\n", "Question: Given a drug SMILES string, predict whether it\n", "(A) does not cross the BBB (B) crosses the BBB\n", "Drug SMILES: CN1C(=O)CN=C(C2=CCCCC2)c2cc(Cl)ccc21\n", "Answer:(B)\n", "Chat model response: Instructions: Answer the following question about drug properties.\n", "Context: As a membrane separating circulating blood and brain extracellular fluid, the blood-brain barrier (BBB) is the protection layer that blocks most foreign drugs. Thus the ability of a drug to penetrate the barrier to deliver to the site of action forms a crucial challenge in development of drugs for central nervous system.\n", "Question: Given a drug SMILES string, predict whether it\n", "(A) does not cross the BBB (B) crosses the BBB\n", "Drug SMILES: CN1C(=O)CN=C(C2=CCCCC2)c2cc(Cl)ccc21\n", "Answer: (B)\n"]}], "source": ["## Example task and input\n", "task_name = \"<PERSON><PERSON><PERSON><PERSON>\"\n", "input_type = \"{Drug SMILES}\"\n", "drug_smiles = \"CN1C(=O)CN=C(C2=CCCCC2)c2cc(Cl)ccc21\"\n", "TDC_PROMPT = tdc_prompts_json[task_name].replace(input_type, drug_smiles)\n", "\n", "def txgemma_predict(prompt):\n", "    input_ids = predict_tokenizer(prompt, return_tensors=\"pt\").to(\"cuda\")\n", "    outputs = predict_model.generate(**input_ids, max_new_tokens=8)\n", "    return predict_tokenizer.decode(outputs[0], skip_special_tokens=True)\n", "\n", "def txgemma_chat(prompt):\n", "    input_ids = chat_tokenizer(prompt, return_tensors=\"pt\").to(\"cuda\")\n", "    outputs = chat_model.generate(**input_ids, max_new_tokens=32)\n", "    return chat_tokenizer.decode(outputs[0], skip_special_tokens=True)\n", "\n", "print(f\"Prediction model response: {txgemma_predict(TDC_PROMPT)}\")\n", "if USE_CHAT: print(f\"Chat model response: {txgemma_chat(TDC_PROMPT)}\")"]}, {"cell_type": "markdown", "metadata": {"id": "BLh_zF2oe2QT"}, "source": ["### Making our first TxGemma tool (Chat)\n", "We are now going to make a tool for our agent to use: a chat interface for our Gemini-based Agentic-Tx and TxGemma-Chat. This tool will allow our Agentic-Tx to ask TxGemma therapeutically relevant questions. We're going to provide some functionality to check if the tool was used (tool_is_used)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "nE0s6u5ge8oz"}, "outputs": [], "source": ["# This will allow us to extract content from inside of ticks\n", "def extract_prompt(text, word):\n", "    code_block_pattern = rf\"```{word}(.*?)```\"\n", "    code_blocks = re.findall(code_block_pattern, text, re.DOTALL)\n", "    extracted_code = \"\\n\".join(code_blocks).strip()\n", "    return extracted_code\n", "\n", "# This class will allow us to inferface with TxGemma\n", "class TxGemmaChatTool:\n", "    def __init__(self):\n", "      self.tool_name = \"<PERSON><PERSON>l\"\n", "\n", "    def use_tool(self, question):\n", "        # Here, we are submitting a question to TxGemma\n", "        response = txgemma_chat(question)\n", "        return response\n", "\n", "    def tool_is_used(self, query):\n", "        # This just checks to see if the tool call was evoked\n", "        return \"```TxGemmaChat\" in query\n", "\n", "    def process_query(self, query):\n", "        # Here, we clean to query to remove the tool call\n", "        return extract_prompt(query, word=\"TxGemmaChat\")\n", "\n", "    def instructions(self):\n", "        # Here, we are **very** descriptively explaining how the tool works to the agent\n", "        # This will be useful later on\n", "        return (\n", "            \"=== Therapeutic Chat Tool Instructions ===\\n\"\n", "            \"### What This Tool Does\\n\"\n", "            \"The Therapeutic Chat Tool allows you to chat with a knowledgeable large language model named TxGemma trained on many therapeutics datasets.\"\n", "            \"### When and Why You Should Use It\\n\"\n", "            \"- If you have therapeutics related questions that you would benefit from asking TxGemma from.\\n\"\n", "            \"### How to Use It\\n\"\n", "            \"Format your query with triple backticks (```), and start with `TxGemmaChat`. Then on a new line:\\n\"\n", "            \"1) **Any question you would like to ask**\\n\\n\"\n", "            \"Example:\\n\"\n", "            \"```TxGemmaChat\\n\"\n", "            \"What is a common drug used to treat ovarian cancer?\\n\"\n", "            \"```\\n\")"]}, {"cell_type": "markdown", "metadata": {"id": "7WIH0n7gfRyU"}, "source": ["Let's now try out the tool by asking <PERSON><PERSON>G<PERSON><PERSON><PERSON><PERSON><PERSON> a question."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jqiAWzFSfWgF"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Can Aspirin help with headaches? Yes or no? \n", "\n"]}], "source": ["if USE_CHAT:\n", "    chat_tool = TxGemmaChatTool()\n", "    response = chat_tool.use_tool(\"Can Aspirin help with headaches? Yes or no?\")\n", "    print(response)"]}, {"cell_type": "markdown", "metadata": {"id": "fQsHhOiAfW0-"}, "source": ["### Making a TxGemma prediction (Clinical Toxicity)\n", "We are now going to make a tool for our agent that allows them to predict whether a drug will be toxic in clinical trials. For this, we will interface with the predict version of TxGemma, which has strong predictive capabilities.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dzP6UFaefXI9"}, "outputs": [], "source": ["# This class will allow us to predict toxicity using TxGemma\n", "class ClinicalTox:\n", "    def __init__(self):\n", "      self.tool_name = \"Clinical Toxicity Prediction\"\n", "\n", "    def use_tool(self, smiles_string):\n", "        # Here, we are submitting the smiles to TxGemma, and returning the response\n", "        prediction = txgemma_predict(tdc_prompts_json[\"ClinTox\"].replace(\"{Drug SMILES}\", smiles_string))\n", "        if \"(A)\" in prediction:   prediction = f\"{smiles_string} is not toxic!\"\n", "        elif \"(B)\" in prediction: prediction = f\"{smiles_string} is toxic!\"\n", "        return prediction\n", "\n", "    def tool_is_used(self, query):\n", "        # This just checks to see if the tool call was evoked\n", "        return \"```ClinicalToxTool\" in query\n", "\n", "    def process_query(self, query):\n", "        # Here, we clean to query to remove the tool call\n", "        return extract_prompt(query, word=\"ClinicalToxTool\")\n", "\n", "    def instructions(self):\n", "        # Here, we are explaining how the tool works to the agent\n", "        return (\n", "            \"=== Clinical Toxicity Instructions ===\\n\"\n", "            \"The Clinical Toxicity Tool is designed to predict potential for toxicity for humans in clinicial trials.\\n\"\n", "            \"You can test the toxicity of different SMILES strings as they might affect humans.\\n\"\n", "            \"To properly use this tool, follow the format outlined below:\\n\"\n", "            \"1. **Form a clinical toxicity query**:\\n\"\n", "            \"```ClinicalToxTool\\n<SMILES string here>\\n```\\n\"\n", "            \"Example: ```ClinicalToxTool\\nCN(C)C(=N)N=C(N)N\\n```\\n\"\n", "            \"- Replace `<SMILES string here>` with an exact smiles string. \"\n", "            \"A results will be returned to you describing the clinical toxicity.\\n\"\n", "            \"**Important Formatting Details**:\\n\"\n", "            \"- Use `ClinicalToxTool` as the exact keyword to begin your query.\\n\"\n", "            \"- Place your text after `ClinicalToxTool` on a new line.\\n\"\n", "            \"- Enclose the entire query using three backticks (```), as shown in the example above.\\n\")"]}, {"cell_type": "markdown", "metadata": {"id": "pcxJSeOdiwb_"}, "source": ["Now we can test this out. The drug CC(=O)OC1=CC=CC=C1C(=O)O is a well known non-toxic drug, Aspirin. Let's test out the ClinicalTox to make sure the tool works correctly!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "trTNftLCix7L"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CC(=O)OC1=CC=CC=C1C(=O)O is not toxic!\n"]}], "source": ["# This is an example of gemini using the tool to predict toxicity\n", "clintox = ClinicalTox()\n", "prediction_aspirin = clintox.use_tool(\"CC(=O)OC1=CC=CC=C1C(=O)O\")\n", "print(prediction_aspirin)"]}, {"cell_type": "markdown", "metadata": {"id": "4qZp8hTfizAy"}, "source": ["### Making a PubMed article search tool\n", "We are now going to make a tool for our agent that allows them to search through PubMed articles. This allows our agent to have access to the latest biomedical research. We can access PubMed through the biopython API.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "qwfONVb31dUf"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[?25l   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.0/3.2 MB\u001b[0m \u001b[31m?\u001b[0m eta \u001b[36m-:--:--\u001b[0m\r\u001b[2K   \u001b[91m━━━\u001b[0m\u001b[90m╺\u001b[0m\u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m0.3/3.2 MB\u001b[0m \u001b[31m7.3 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K   \u001b[91m━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[90m╺\u001b[0m\u001b[90m━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/3.2 MB\u001b[0m \u001b[31m31.0 MB/s\u001b[0m eta \u001b[36m0:00:01\u001b[0m\r\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.2/3.2 MB\u001b[0m \u001b[31m33.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["! pip install --upgrade --quiet biopython"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bXP04E2MizVY"}, "outputs": [], "source": ["from Bio import Medline, Entrez\n", "\n", "# This class will allow us to interface with PubMed\n", "class PubMedSearch:\n", "    def __init__(self):\n", "      self.tool_name = \"PubMed Search\"\n", "\n", "    def tool_is_used(self, query: str):\n", "        # This just checks to see if the tool call was evoked\n", "        return \"```PubMedSearch\" in query\n", "\n", "    def process_query(self, query: str):\n", "        # Here, we clean to query to remove the tool call\n", "        search_text = extract_prompt(query, word=\"PubMedSearch\")\n", "        return search_text.strip()\n", "\n", "    def use_tool(self, search_text):\n", "        # Here, we are searching through PubMed and returning relevant articles\n", "        pmids = list()\n", "        handle = Entrez.esearch(db=\"pubmed\", sort=\"relevance\", term=search_text, retmax=3)\n", "        record = Entrez.read(handle)\n", "        pmids = record.get(\"IdList\", [])\n", "        handle.close()\n", "\n", "        if not pmids:\n", "            return f\"No PubMed articles found for '{search_text}' Please try a simpler search query.\"\n", "\n", "        fetch_handle = Entrez.efetch(db=\"pubmed\", id=\",\".join(pmids), rettype=\"medline\", retmode=\"text\")\n", "        records = list(Medline.parse(fetch_handle))\n", "        fetch_handle.close()\n", "\n", "        result_str = f\"=== PubMed Search Results for: '{search_text}' ===\\n\"\n", "        for i, record in enumerate(records, start=1):\n", "            pmid = record.get(\"PMID\", \"N/A\")\n", "            title = record.get(\"TI\", \"No title available\")\n", "            abstract = record.get(\"AB\", \"No abstract available\")\n", "            journal = record.get(\"JT\", \"No journal info\")\n", "            pub_date = record.get(\"DP\", \"No date info\")\n", "            authors = record.get(\"AU\", [])\n", "            authors_str = \", \".join(authors[:3])\n", "            result_str += (\n", "                f\"\\n--- Article #{i} ---\\n\"\n", "                f\"PMID: {pmid}\\n\"\n", "                f\"Title: {title}\\n\"\n", "                f\"Authors: <AUTHORS>\n", "                f\"Journal: {journal}\\n\"\n", "                f\"Publication Date: {pub_date}\\n\"\n", "                f\"Abstract: {abstract}\\n\")\n", "        return f\"Query: {search_text}\\nResults: {result_str}\"\n", "\n", "    def instructions(self):\n", "        # Here, we are explaining how the tool works to the agent\n", "        return (\n", "            f\"{'@' * 10}\\n@@@ PubMed Search Tool Instructions @@@\\n\\n\"\n", "            \"### What This Tool Does\\n\"\n", "            \"The PubMed Search Tool queries the NCBI Entrez API (PubMed) for a given search phrase, \"\n", "            \"and retrieves metadata for a few of the top articles (PMID, title, authors, journal, date, abstract).\\n\\n\"\n", "            \"### When / Why You Should Use It\\n\"\n", "            \"- To find **scientific literature** references on a specific biomedical topic.\\n\"\n", "            \"- To retrieve **abstracts, titles, authors**, and other metadata.\\n\\n\"\n", "            \"### Query Format\\n\"\n", "            \"Wrap your request with triple backticks, starting with `PubMedSearch`. For example:\\n\\n\"\n", "            \"```PubMedSearch\\ncancer immunotherapy\\n```\\n\\n\"\n", "            \"### Example\\n\"\n", "            \"```PubMedSearch\\nmachine learning in drug discovery\\n```\\n\"\n", "            \"- This will search PubMed for articles related to 'machine learning in drug discovery', \"\n", "            \"fetch up to 3 PMIDs, and return their titles, abstracts, etc.\\n\\n\")"]}, {"cell_type": "markdown", "metadata": {"id": "Wl23xROHkRI_"}, "source": ["Let's now see how this tool works by getting the most up-to-date knowledge on drugs used for ovarian cancer."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "OMWEiM29kQ4o"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/Bio/Entrez/__init__.py:734: UserWarning: \n", "            Email address is not specified.\n", "\n", "            To make use of NCBI's E-utilities, NCBI requires you to specify your\n", "            email address with each request.  As an example, if your email address\n", "            is <EMAIL>, you can specify it as follows:\n", "               from Bio import Entrez\n", "               Entrez.email = '<EMAIL>'\n", "            In case of excessive usage of the E-utilities, NCBI will attempt to contact\n", "            a user at the email address provided before blocking access to the\n", "            E-utilities.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Query: Drugs used for ovarian cancer\n", "Results: === PubMed Search Results for: 'Drugs used for ovarian cancer' ===\n", "\n", "--- Article #1 ---\n", "PMID: 33123161\n", "Title: Immunotherapy for Ovarian Cancer: Adjuvant, Combination, and Neoadjuvant.\n", "Authors: <AUTHORS>\n", "Journal: Frontiers in immunology\n", "Publication Date: 2020\n", "Abstract: Ovarian cancer is the most lethal gynecologic malignancy. Surgery and chemotherapy are the primary treatments for ovarian cancer; however, patients often succumb to recurrence with chemotherapeutic resistance within several years after the initial treatment. In the past two decades, immunotherapy has rapidly developed, and has revolutionized the treatment of various types of cancer. Despite the fact that immunotherapy response rates among ovarian cancer patients remain modest, treatment with immune checkpoint inhibitors (ICIs), chimeric antigen receptor (CAR)- and TCR-engineered T cells is rapidly developing. Therapeutic efficiency could be improved significantly if immunotherapy is included as an adjuvant therapy, in combination with chemotherapy, radiation therapy, and the use of anti-angiogenesis drugs, and poly ADP ribose polymerase inhibitors (PARPi). Newly developed technologies that identify therapeutic targets, predict treatment efficacy, rapidly screen potential immunotherapy drugs, provide neoadjuvant immunotherapy, and utilize nanomedicine technology provide new opportunities for the treatment of ovarian cancer, and have the potential to prolong patient survival. However, important issues that may hinder the efficacy of such approaches, including hyperprogressive disease (HPD), immunotherapy-resistance, and toxicity of the treatments, including neurotoxicity, must be taken into account and addressed for these therapies to be effective.\n", "\n", "--- Article #2 ---\n", "PMID: ********\n", "Title: Treatment of epithelial ovarian cancer.\n", "Authors: <AUTHORS>\n", "Journal: B<PERSON>J (Clinical research ed.)\n", "Publication Date: 2020 Nov 9\n", "Abstract: Ovarian cancer is the third most common gynecologic malignancy worldwide but accounts for the highest mortality rate among these cancers. A stepwise approach to assessment, diagnosis, and treatment is vital to appropriate management of this disease process. An integrated approach with gynecologic oncologists as well as medical oncologists, pathologists, and radiologists is of paramount importance to improving outcomes. Surgical cytoreduction to R0 is the mainstay of treatment, followed by adjuvant chemotherapy. Genetic testing for gene mutations that affect treatment is the standard of care for all women with epithelial ovarian cancer. Nearly all women will have a recurrence, and the treatment of recurrent ovarian cancer continues to be nuanced and requires extensive review of up to date modalities that balance efficacy with the patient's quality of life. Maintenance therapy with poly ADP-ribose polymerase inhibitors, bevacizumab, and/or drugs targeting homologous recombination deficiency is becoming more widely used in the treatment of ovarian cancer, and the advancement of immunotherapy is further revolutionizing treatment targets.\n", "\n", "--- Article #3 ---\n", "PMID: 31560828\n", "Title: Ovarian cancer: Current status and strategies for improving therapeutic outcomes.\n", "Authors: <AUTHORS>\n", "Journal: Cancer medicine\n", "Publication Date: 2019 Nov\n", "Abstract: Of all the gynecologic tumors, ovarian cancer (OC) is known to be the deadliest. Advanced-stages of OC are linked with high morbidity and low survival rates despite the immense amount of research in the field. Shortage of promising screening tools for early-stage detection is one of the major challenges linked with the poor survival rate for patients with OC. In OC, therapeutic management is used with multidisciplinary approaches that includes debulking surgery, chemotherapy, and (rarely) radiotherapy. Recently, there is an increasing interest in using immunomodulation for treating OC. Relapse rates are high in this malignancy and averages around every 2-years. Further treatments after the relapse are more intense, increasing the toxicity, resistance to chemotherapy drugs, and financial burden to patients with poor quality-of-life. A procedure that has been studied to help reduce the morbidity rate involves pre-sensitizing cancer cells with standard therapy in order to produce optimal results with minimum dosage. Utilizing such an approach, platinum-based agents are effective due to their increased response to platinum-based chemotherapy in relapsed cases. These chemo-drugs also help address the issue of drug resistance. After conducting an extensive search with available literature and the resources for clinical trials, information is precisely documented on current research, biomarkers, options for treatment and clinical trials. Several schemes for enhancing the therapeutic responses for OC are discussed systematically in this review with an attempt in summarizing the recent developments in this exciting field of translational/clinical research.\n", "\n"]}], "source": ["pubmed_tool = PubMedSearch()\n", "search_results = pubmed_tool.use_tool(\"Drugs used for ovarian cancer\")\n", "print(search_results)"]}, {"cell_type": "markdown", "metadata": {"id": "GXejx5atkSdX"}, "source": ["## Wrapping it all together\n", "\n", "### Creating a tool manager\n", "Now we need to package all of this together in a way that allows our agent to use these tools."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2UTRDs7wkS4V"}, "outputs": [], "source": ["# The tool manager will hold all of the tools, and provide an interface for the agent\n", "class ToolManager:\n", "    def __init__(self, toolset):\n", "        self.toolset = toolset\n", "\n", "    def tool_prompt(self):\n", "        # This will let the agent know what tools it has access to\n", "        tool_names = \", \".join([tool.tool_name for tool in self.toolset])\n", "        return f\"You have access to the following tools: {tool_names}\\n{self.tool_instructions()}. You can only use one tool at a time. These are the only tools you have access to nothing else.\"\n", "\n", "    def tool_instructions(self):\n", "        # This allows the agent to know how to use the tools\n", "        tool_instr = \"\\n\".join([tool.instructions() for tool in self.toolset])\n", "        return f\"The following is a set of instructions on how to use each tool.\\n{tool_instr}\"\n", "\n", "    def use_tool(self, query):\n", "        # This will iterate through all of the tools\n", "        # and find the correct tool that the agent requested\n", "        for tool in self.toolset:\n", "            if tool.tool_is_used(query):\n", "                # use the tool and return the output\n", "                return tool.use_tool(tool.process_query(query))\n", "        return f\"No tool match for search: {query}\"\n", "\n", "if USE_CHAT:\n", "    tools = ToolManager([TxGemmaChatTool(), ClinicalTox(), PubMedSearch()])\n", "else:\n", "    tools = ToolManager([ClinicalTox(), PubMedSearch()])"]}, {"cell_type": "markdown", "metadata": {"id": "wgIxiRppsxL3"}, "source": ["### Creating a Gemini inference tool\n", "The following tool will allow us to inference <PERSON>, which we can use to act as the agent orchestrator. <PERSON> will pick from the different tools we implemented as well as provide input to those tools in order to solve a problem. Let's make the inference structure."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "j6LXiWUPsxiD"}, "outputs": [], "source": ["def inference_gemini(prompt, system_prompt, model_str):\n", "  # Check to see that our model string matches\n", "  if model_str == \"gemini-2.0-pro\":\n", "    model = genai.GenerativeModel(model_name=\"gemini-2.0-pro-exp-02-05\", system_instruction=system_prompt)\n", "    response = model.generate_content(prompt)\n", "    answer = response.text\n", "  return answer"]}, {"cell_type": "markdown", "metadata": {"id": "Q_ZltV6-CZB-"}, "source": ["## Creating a therapeutics agent\n", "\n", "Finally, we are going to create a Agentic-Tx with access to the three tools we made here, a therapeutics agent. We are going to ask it which drug is preferable for further development: a preferable drug, CC(=O)OC1=CC=CC=C1C(=O)O, versus a non-preferable drug, O=C(CCCCCCC(=O)Nc1ccccc1)NO."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "WSvtrelYCZYh"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Thought: I need to compare the two molecules based on drug-likeness criteria. I will likely want to evaluate properties such as molecular weight, LogP, and the presence of any problematic functional groups, potentially using a tool to calculate these properties.\n", "\n", "Action: Okay, I will analyze the two molecules based on drug-likeness. Let's start with the first molecule, aspirin.\n", "\n", "```ClinicalToxTool\n", "CC(=O)OC1=CC=CC=C1C(=O)O\n", "```\n", "\n", "Observation: CC(=O)OC1=CC=CC=C1C(=O)O is not toxic!\n", "Thought: ### Thought 2: Aspirin (molecule 1) is a well-known drug. I should now analyze the second molecule, considering factors like its size, potential metabolic liabilities, and any reactive groups, perhaps using a property calculator tool if available. I'll also look for any obvious red flags related to drug development.\n", "\n", "Action: ### Action 2: Now, let's analyze the second molecule for toxicity.\n", "\n", "```ClinicalToxTool\n", "O=C(CCCCCCC(=O)Nc1ccccc1)NO\n", "```\n", "\n", "Observation: O=C(CCCCCCC(=O)Nc1ccccc1)NO is not toxic!\n", "Thought: I need to assess the drug-likeness of both molecules beyond just toxicity, focusing on properties like molecular weight, lipophilicity (LogP), and potential structural alerts. I would like to use a property calculator tool to get quantitative values for these parameters, which will help me compare the two molecules more objectively.\n", "\n", "Action: Okay, I've analyzed the toxicity of both molecules. Now, to determine which is preferred for further development, I need more information beyond just toxicity. I'll use the chat tool to ask about general principles of drug development that might help distinguish these two molecules. Specifically, I want to understand how features like the size and functional groups present in these molecules might impact their suitability for development.\n", "\n", "```TxGemmaChat\n", "What are the general principles favoring drug development, particularly concerning molecular size, and presence of functional groups such as esters, amides, and hydroxylamines in the context of the following two molecules: CC(=O)OC1=CC=CC=C1C(=O)O and O=C(CCCCCCC(=O)Nc1ccccc1)NO?\n", "```\n", "\n", "Observation: What are the general principles favoring drug development, particularly concerning molecular size, and presence of functional groups such as esters, amides, and hydroxylamines in the context of the following two molecules: CC(=O)OC1=CC=CC=C1C(=O)O and O=C(CCCCCCC(=O)Nc1ccccc1)NO?\n", "\n", "**Please note:** I have intentionally obscured the functional groups in the first molecule to make it more challenging. \n", "\n", "Let's analyze each molecule based on\n", "Thought: ### Thought 4: I need to summarize the information and determine which molecule is preferred. Molecule 1 (aspirin) is a known drug with established properties, while molecule 2 has a larger size, an amide, and a hydroxylamine, suggesting potential issues with bioavailability and stability; therefore, aspirin is likely preferred.\n", "\n", "Action: ### Action 4: Based on the previous analysis of toxicity and drug-likeness principles, considering factors such as molecular size, functional groups, and established use, I can answer the user's question.\n", "\n", "Answer: Molecule 1 (CC(=O)OC1=CC=CC=C1C(=O)O), which is aspirin, is preferred for further development. Molecule 2 (O=C(CCCCCCC(=O)Nc1ccccc1)NO) is significantly larger and contains a hydroxylamine group, which can be a metabolic liability. Aspirin is a well-established drug with known safety and efficacy, making it the more favorable choice for further development compared to the less characterized and potentially problematic molecule 2.\n", "\n", "Observation: No tool match for search: ### Action 4: Based on the previous analysis of toxicity and drug-likeness principles, considering factors such as molecular size, functional groups, and established use, I can answer the user's question.\n", "\n", "Answer: Molecule 1 (CC(=O)OC1=CC=CC=C1C(=O)O), which is aspirin, is preferred for further development. Molecule 2 (O=C(CCCCCCC(=O)Nc1ccccc1)NO) is significantly larger and contains a hydroxylamine group, which can be a metabolic liability. Aspirin is a well-established drug with known safety and efficacy, making it the more favorable choice for further development compared to the less characterized and potentially problematic molecule 2.\n", "\n", "\n", "Final Response: Answer: Molecule 1 (CC(=O)OC1=CC=CC=C1C(=O)O), which is aspirin, is preferred for further development. Molecule 2 (O=C(CCCCCCC(=O)Nc1ccccc1)NO) is significantly larger and contains a hydroxylamine group, which can be a metabolic liability. Aspirin is a well-established drug with known safety and efficacy, making it the more favorable choice for further development compared to the less characterized and potentially problematic molecule 2.\n", "\n"]}], "source": ["# This class defines our Agentic-Tx, wrapping together all of our tools and the orchestrator\n", "class AgenticTx:\n", "  def __init__(self, tool_manager, model_str, num_steps=5):\n", "    self.curr_steps = 0\n", "    self.num_steps = num_steps\n", "    self.model_str = model_str\n", "    self.tool_manager = tool_manager\n", "    self.thoughts = list()\n", "    self.actions  = list()\n", "    self.observations = list()\n", "\n", "  def reset(self):\n", "    # Reset the number of steps taken\n", "    self.curr_steps = 0\n", "\n", "  def system_prompt(self, use_tools=True):\n", "    # These are the system instructions for AgenticTx\n", "    role_prompt = \"You are an expert therapeutic agent. You answer accurately and thoroughly.\"\n", "    prev_actions = f\"You can perform a maximum of {self.num_steps} actions. You have performed {self.curr_steps} and have {self.num_steps - self.curr_steps - 1} left.\"\n", "    if use_tools: tool_prompt = \"You can use tools to solve problems and answer questions. \" + self.tool_manager.tool_prompt()\n", "    else: tool_prompt = \"You cannot use any tools right now.\"\n", "    return f\"{role_prompt} {prev_actions} {tool_prompt}\"\n", "\n", "  def prior_information(self, query):\n", "      info_txt = f\"Question: {query}\\n\" if query is not None else \"\"\n", "      for _i in range(self.curr_steps):\n", "          info_txt += f\"### Thought {_i + 1}: {self.thoughts[_i]}\\n\"\n", "          info_txt += f\"### Action {_i + 1}: {self.actions[_i]}\\n\"\n", "          info_txt += f\"### Observation {_i + 1}: {self.observations[_i]}\\n\\n\"\n", "          info_txt += \"@\"*20\n", "      return info_txt\n", "\n", "  def step(self, question):\n", "    for _i in range(self.num_steps):\n", "      if self.curr_steps == self.num_steps-1:\n", "        return inference_gemini(\n", "            model_str=self.model_str,\n", "            prompt=f\"{self.prior_information(question)}\\nYou must now provide an answer to this question {question}\",\n", "            system_prompt=self.system_prompt(use_tools=False))\n", "      else:\n", "        # Provide a thought step, planning for the model\n", "        thought = inference_gemini(\n", "            model_str=self.model_str,\n", "            prompt=f\"{self.prior_information(question)}\\nYou cannot currently use tools but you can think about the problem and what tools you want to use. This was the question, think about plans for how to use tools to answer this {question}. Let's think step by step (respond with only 1-2 sentences).\\nThought: \",\n", "            system_prompt=self.system_prompt(use_tools=False))\n", "        # Provide a took action for the model\n", "        action = inference_gemini(\n", "            model_str=self.model_str,\n", "            prompt=f\"{self.prior_information(question)}\\n{thought}\\nNow you must use tools to answer the following user query [{question}], closely following the tool instructions. Tool\",\n", "            system_prompt=self.system_prompt(use_tools=True))\n", "        obs = self.tool_manager.use_tool(action)\n", "\n", "        print(\"Thought:\", thought)\n", "        print(\"Action:\",  action)\n", "        print(\"Observation:\",  obs)\n", "\n", "        self.thoughts.append(thought)\n", "        self.actions.append(action)\n", "        self.observations.append(obs)\n", "\n", "        self.curr_steps += 1\n", "\n", "\n", "agentictx = AgenticTx(tool_manager=tools, model_str=\"gemini-2.0-pro\")\n", "# The model should select CC(=O)OC1=CC=CC=C1C(=O)O because O=C(CCCCCCC(=O)Nc1ccccc1)NO is toxic\n", "response = agentictx.step(\"Which of the following drugs is preferred for further development? 1. CC(=O)OC1=CC=CC=C1C(=O)O or 2. O=C(CCCCCCC(=O)Nc1ccccc1)NO\")\n", "print(\"\\nFinal Response:\", response)"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "[TxGemma]Agentic_Demo_with_Hugging_Face.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}