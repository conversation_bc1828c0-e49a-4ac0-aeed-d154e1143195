{"comment:0": "NOTE: THIS FILE IS GENERATED. DO NOT EDIT.", "comment:1": "Instead modify 'flutter/web_sdk/libraries.yaml' and follow the instructions therein.", "dartdevc": {"include": [{"path": "../dart-sdk/lib/libraries.json", "target": "dartdevc"}], "libraries": {"ui": {"uri": "lib/ui/ui.dart"}, "ui_web": {"uri": "lib/ui_web/ui_web.dart"}, "_engine": {"uri": "lib/_engine/engine.dart"}, "_skwasm_stub": {"uri": "lib/_skwasm_stub/skwasm_stub.dart"}, "_web_unicode": {"uri": "lib/_web_unicode/web_unicode.dart"}, "_web_locale_keymap": {"uri": "lib/_web_locale_keymap/web_locale_keymap.dart"}, "_web_test_fonts": {"uri": "lib/_web_test_fonts/web_test_fonts.dart"}}}, "dart2js": {"include": [{"path": "../dart-sdk/lib/libraries.json", "target": "dart2js"}], "libraries": {"ui": {"uri": "lib/ui/ui.dart"}, "ui_web": {"uri": "lib/ui_web/ui_web.dart"}, "_engine": {"uri": "lib/_engine/engine.dart"}, "_skwasm_stub": {"uri": "lib/_skwasm_stub/skwasm_stub.dart"}, "_web_unicode": {"uri": "lib/_web_unicode/web_unicode.dart"}, "_web_locale_keymap": {"uri": "lib/_web_locale_keymap/web_locale_keymap.dart"}, "_web_test_fonts": {"uri": "lib/_web_test_fonts/web_test_fonts.dart"}}}, "wasm": {"include": [{"path": "../dart-sdk/lib/libraries.json", "target": "wasm"}], "libraries": {"ui": {"uri": "lib/ui/ui.dart"}, "ui_web": {"uri": "lib/ui_web/ui_web.dart"}, "_engine": {"uri": "lib/_engine/engine.dart"}, "_skwasm_impl": {"uri": "lib/_skwasm_impl/skwasm_impl.dart"}, "_web_unicode": {"uri": "lib/_web_unicode/web_unicode.dart"}, "_web_locale_keymap": {"uri": "lib/_web_locale_keymap/web_locale_keymap.dart"}, "_web_test_fonts": {"uri": "lib/_web_test_fonts/web_test_fonts.dart"}}}}