import time
import streamlit as st
from duckduckgo_search import DDGS
from datetime import datetime
from dotenv import load_dotenv
import os
from phi.assistant import Assistant
from phi.llm.base import LLM
from phi.tools.serpapi_tools import SerpApiTools
from phi.tools.newspaper4k import Newspaper4k as NewspaperToolkit
from groq import Groq
from pydantic import Field, PrivateAttr
from typing import List, Dict

load_dotenv()

# Modelo de chat do Groq
class GroqChat(LLM):
    model: str = Field(default="llama-3.1-8b-instant")
    api_key: str = Field(...)

    _client: Groq = PrivateAttr()

    def __init__(self, api_key: str, model: str = "llama-3.1-8b-instant"):
        super().__init__(model=model, api_key=api_key)
        self._client = Groq(api_key=api_key)

    def chat(self, messages: List[Dict[str, str]]) -> str:
        completion = self._client.chat.completions.create(
            messages=messages,
            model=self.model
        )
        return completion.choices[0].message.content

    def complete(self, prompt: str) -> str:
        return self.chat([{"role": "user", "content": prompt}])

    def response(self, messages: List[Dict[str, str]]) -> str:
        return self.chat(messages)

# Função de tratamento de erro para capturar exceções e mostrar mensagens
def safe_call(func):
    """Wrapper to catch errors and print debugging information."""
    try:
        return func()
    except Exception as e:
        st.error(f"Error occurred: {str(e)}")
        return None

# Inicializar cliente Groq
def init_groq_client():
    groq_api_key = st.sidebar.text_input("Enter Groq API Key", value=os.getenv("GROQ_API_KEY", ""), type="password")
    if groq_api_key:
        return GroqChat(api_key=groq_api_key)
    return None

# Funções de busca, síntese e resumo de notícias
def search_news(topic):
    """Search for news articles using DuckDuckGo"""
    with DDGS() as ddg:
        # Adicionando atraso para evitar a limitação de taxa
        time.sleep(1)  # Delay de 1 segundo entre requisições
        results = ddg.text(f"{topic} news {datetime.now().strftime('%Y-%m')}", max_results=5)
        if results:
            news_results = "\n\n".join([ 
                f"Title: {result['title']}\nURL: {result['href']}\nSummary: {result['body']}" 
                for result in results
            ])
            return news_results
        return f"No news found for {topic}."

def synthesize_news(raw_news):
    """Synthesize the raw news articles into a comprehensive summary."""
    # Placeholder for synthesis logic (could use Groq model or another tool)
    return f"Synthesized version of the news: {raw_news}"

def summarize_news(synthesized_news):
    """Summarize the synthesized news."""
    # Placeholder for summarization logic (could use Groq model or another tool)
    return f"Summary of the news: {synthesized_news}"

# Função principal para processar as notícias
def process_news(topic, groq_client):
    """Run the news processing workflow"""
    with st.status("Processing news...", expanded=True) as status:
        # Buscar notícias
        status.write("🔍 Searching for news...")
        raw_news = safe_call(lambda: search_news(topic))
        if not raw_news:
            return None, None, None
        
        # Sintetizar as notícias
        status.write("🔄 Synthesizing information...")
        synthesized_news = safe_call(lambda: synthesize_news(raw_news))
        if not synthesized_news:
            return None, None, None
        
        # Resumir as notícias
        status.write("📝 Creating summary...")
        final_summary = safe_call(lambda: summarize_news(synthesized_news))
        if not final_summary:
            return None, None, None
        
        return raw_news, synthesized_news, final_summary

# Interface do usuário com Streamlit
st.set_page_config(page_title="AI News Processor", page_icon="📰")
st.title("📰 News Inshorts Agent")

groq_client = init_groq_client()

if groq_client:
    topic = st.text_input("Enter news topic:", value="artificial intelligence")
    
    if st.button("Process News", type="primary"):
        if topic:
            try:
                raw_news, synthesized_news, final_summary = process_news(topic, groq_client)
                if raw_news and synthesized_news and final_summary:
                    st.header(f"📝 News Summary: {topic}")
                    st.markdown(final_summary)
                else:
                    st.error("There was an issue with processing the news.")
            except Exception as e:
                st.error(f"An error occurred: {str(e)}")
        else:
            st.error("Please enter a topic!")
else:
    st.sidebar.warning("Please enter valid Groq API key.")
