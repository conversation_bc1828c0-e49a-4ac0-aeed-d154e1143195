
# SQL Database App

This application allows you to connect to a SQL database and generate SQL queries from natural language. The application generates the SQL query. The application will display the results of the query in a table format.

![SQL Database App](../../img/apps/ai_sql_database_app.jpg)

## To run the app

- Clone the repository
- Install the `requirements.txt` file
- Run the `app.py` file

```bash
streamlit run app.py
``` 
