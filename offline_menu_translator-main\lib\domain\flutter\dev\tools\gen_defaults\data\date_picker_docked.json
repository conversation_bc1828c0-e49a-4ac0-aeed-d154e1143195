{"version": "6_1_0", "md.comp.date-picker.docked.container.color": "surfaceContainerHigh", "md.comp.date-picker.docked.container.elevation": "md.sys.elevation.level3", "md.comp.date-picker.docked.container.height": 456.0, "md.comp.date-picker.docked.container.shape": "md.sys.shape.corner.large", "md.comp.date-picker.docked.container.width": 360.0, "md.comp.date-picker.docked.date.container.height": 48.0, "md.comp.date-picker.docked.date.container.shape": "md.sys.shape.corner.full", "md.comp.date-picker.docked.date.container.width": 48.0, "md.comp.date-picker.docked.date.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.date-picker.docked.date.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.date-picker.docked.date.label-text.text-style": "bodyLarge", "md.comp.date-picker.docked.date.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.date-picker.docked.date.selected.container.color": "primary", "md.comp.date-picker.docked.date.selected.focus.state-layer.color": "onPrimary", "md.comp.date-picker.docked.date.selected.hover.state-layer.color": "onPrimary", "md.comp.date-picker.docked.date.selected.label-text.color": "onPrimary", "md.comp.date-picker.docked.date.selected.pressed.state-layer.color": "onPrimary", "md.comp.date-picker.docked.date.state-layer.height": 40.0, "md.comp.date-picker.docked.date.state-layer.shape": "md.sys.shape.corner.full", "md.comp.date-picker.docked.date.state-layer.width": 40.0, "md.comp.date-picker.docked.date.today.container.outline.color": "primary", "md.comp.date-picker.docked.date.today.container.outline.width": 1.0, "md.comp.date-picker.docked.date.today.focus.state-layer.color": "primary", "md.comp.date-picker.docked.date.today.hover.state-layer.color": "primary", "md.comp.date-picker.docked.date.today.label-text.color": "primary", "md.comp.date-picker.docked.date.today.pressed.state-layer.color": "primary", "md.comp.date-picker.docked.date.unselected.focus.state-layer.color": "onSurfaceVariant", "md.comp.date-picker.docked.date.unselected.hover.state-layer.color": "onSurfaceVariant", "md.comp.date-picker.docked.date.unselected.label-text.color": "onSurface", "md.comp.date-picker.docked.date.unselected.outside-month.label-text.color": "outlineVariant", "md.comp.date-picker.docked.date.unselected.pressed.state-layer.color": "onSurfaceVariant", "md.comp.date-picker.docked.header.height": 64.0, "md.comp.date-picker.docked.menu-button.container.height": 40.0, "md.comp.date-picker.docked.menu-button.container.shape": "md.sys.shape.corner.full", "md.comp.date-picker.docked.menu-button.disabled.icon.color": "onSurface", "md.comp.date-picker.docked.menu-button.disabled.icon.opacity": 0.38, "md.comp.date-picker.docked.menu-button.disabled.label-text.color": "onSurface", "md.comp.date-picker.docked.menu-button.disabled.label-text.opacity": 0.38, "md.comp.date-picker.docked.menu-button.focus.icon.color": "onSurfaceVariant", "md.comp.date-picker.docked.menu-button.focus.label-text.color": "onSurfaceVariant", "md.comp.date-picker.docked.menu-button.focus.state-layer.color": "onSurfaceVariant", "md.comp.date-picker.docked.menu-button.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.date-picker.docked.menu-button.hover.icon.color": "onSurfaceVariant", "md.comp.date-picker.docked.menu-button.hover.label-text.color": "onSurfaceVariant", "md.comp.date-picker.docked.menu-button.hover.state-layer.color": "onSurfaceVariant", "md.comp.date-picker.docked.menu-button.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.date-picker.docked.menu-button.icon.color": "onSurfaceVariant", "md.comp.date-picker.docked.menu-button.icon.size": 18.0, "md.comp.date-picker.docked.menu-button.label-text.color": "onSurfaceVariant", "md.comp.date-picker.docked.menu-button.label-text.text-style": "labelLarge", "md.comp.date-picker.docked.menu-button.pressed.icon.color": "onSurfaceVariant", "md.comp.date-picker.docked.menu-button.pressed.label-text.color": "onSurfaceVariant", "md.comp.date-picker.docked.menu-button.pressed.state-layer.color": "onSurfaceVariant", "md.comp.date-picker.docked.menu-button.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.date-picker.docked.menu.list-item.container.height": 48.0, "md.comp.date-picker.docked.menu.list-item.focus.label-text.color": "onSurface", "md.comp.date-picker.docked.menu.list-item.focus.state-layer.color": "onSurface", "md.comp.date-picker.docked.menu.list-item.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.date-picker.docked.menu.list-item.hover.label-text.color": "onSurface", "md.comp.date-picker.docked.menu.list-item.hover.state-layer.color": "onSurface", "md.comp.date-picker.docked.menu.list-item.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.date-picker.docked.menu.list-item.label-text.color": "onSurface", "md.comp.date-picker.docked.menu.list-item.label-text.text-style": "bodyLarge", "md.comp.date-picker.docked.menu.list-item.pressed.label-text.color": "onSurface", "md.comp.date-picker.docked.menu.list-item.pressed.state-layer.color": "onSurface", "md.comp.date-picker.docked.menu.list-item.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.date-picker.docked.menu.list-item.selected.container.color": "surfaceVariant", "md.comp.date-picker.docked.menu.list-item.selected.focus.leading-icon.color": "onSurfaceVariant", "md.comp.date-picker.docked.menu.list-item.selected.hover.leading-icon.color": "onSurfaceVariant", "md.comp.date-picker.docked.menu.list-item.selected.leading-icon.color": "onSurface", "md.comp.date-picker.docked.menu.list-item.selected.leading-icon.size": 24.0, "md.comp.date-picker.docked.menu.list-item.selected.pressed.leading-icon.color": "onSurfaceVariant", "md.comp.date-picker.docked.weekdays.label-text.color": "onSurface", "md.comp.date-picker.docked.weekdays.label-text.text-style": "bodyLarge"}