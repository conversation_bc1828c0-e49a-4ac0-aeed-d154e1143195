import streamlit as st
from phi.agent import Agent
from phi.model.groq import Groq
from phi.tools.yfinance import YF<PERSON>nceTools
from phi.tools.duckduckgo import DuckDuckGo

# Lista de modelos Groq disponíveis
GROQ_MODELS = {
    "Meta llama-3.2-3b": "llama-3.2-3b-preview",
    "Mixtral 8x7B": "mixtral-8x7b-32768",
    "LLaMA2 70B": "llama2-70b-4096",
    "Gemma 9B": "gemma-9b-it"
}

def initialize_agents(api_key, model_id):
    """Inicializa os agentes com as configurações necessárias"""
    try:
        websearchagent = Agent(
            name='webagent',
            role='search the web for the information',
            model=Groq(id=model_id, api_key=api_key),
            tools=[DuckDuckGo()],
            instructions=['Always include sources'],
            show_tools_calls=True,
            markdown=True,
        )
        
        finagent = Agent(
            name='finagent',
            model=Groq(id=model_id, api_key=api_key),
            tools=[YFinanceTools(
                stock_price=True,
                analyst_recommendations=True,
                stock_fundamentals=True,
                company_news=True
            )],
            instructions='use tables to display the data',
            shows_tools_calls=True,
            markdown=True,
        )
        
        multiagent = Agent(
            team=[websearchagent, finagent],
            model=Groq(id=model_id, api_key=api_key),
            instructions=['Always include sources', 'use tables to display the data'],
            show_tools_calls=True,
            markdown=True,
        )
        
        return multiagent
    except Exception as e:
        raise Exception(f"Erro ao inicializar agentes: {str(e)}\nVerifique se a API key e o modelo selecionado estão corretos.")

def main():
    st.set_page_config(page_title="Análise de Ações", page_icon="📈")
    
    st.title("🤖 Assistente de Análise de Ações")
    
    # Sidebar para configurações
    with st.sidebar:
        st.header("Configurações")
        
        # Seleção do modelo
        selected_model = st.selectbox(
            "Selecione o modelo:",
            options=list(GROQ_MODELS.keys()),
            index=0
        )
        model_id = GROQ_MODELS[selected_model]
        
        # API Key
        api_key = st.text_input("Digite sua Groq API Key:", type="password")
        
        if api_key:
            st.success("API Key configurada!")
        else:
            st.warning("API Key necessária")
        
        st.markdown("---")
        st.markdown("""
        ### Sobre
        Este aplicativo usa a API Groq para análise de ações.
        Para obter uma API key, visite: [Groq Cloud](https://console.groq.com)
        """)
    
    # Input do ticker da ação
    ticker = st.text_input("Digite o ticker da ação (ex: TESLA):", "TESLA")
    
    # Opções de análise
    analysis_type = st.multiselect(
        "Selecione o tipo de análise:",
        ["Recomendações de Analistas", "Últimas Notícias", "Dados Fundamentais"],
        default=["Recomendações de Analistas", "Últimas Notícias"]
    )
    
    if st.button("Analisar"):
        if not api_key:
            st.error("Por favor, insira sua API key para continuar.")
            return
            
        if not ticker:
            st.error("Por favor, insira um ticker válido.")
            return
            
        if not analysis_type:
            st.error("Por favor, selecione pelo menos um tipo de análise.")
            return
        
        with st.spinner("Processando análise..."):
            try:
                multiagent = initialize_agents(api_key, model_id)
                
                # Construindo a query baseada nas seleções
                query_parts = []
                if "Recomendações de Analistas" in analysis_type:
                    query_parts.append("analyst recommendations")
                if "Últimas Notícias" in analysis_type:
                    query_parts.append("latest news")
                if "Dados Fundamentais" in analysis_type:
                    query_parts.append("fundamental data")
                
                query = f"Summarize {', '.join(query_parts)} for {ticker} stock"
                
                # Executando a análise
                st.info("Conectando aos agentes e processando sua solicitação...")
                response = multiagent.print_response(query, stream=False)
                
                # Exibindo resultados
                st.markdown("### Resultados da Análise")
                st.markdown(response)
                
            except Exception as e:
                st.error(f"""
                Erro ao processar a análise: {str(e)}
                
                Possíveis soluções:
                1. Verifique se sua API key está correta
                2. Confirme se o ticker da ação é válido
                3. Tente selecionar um modelo diferente
                4. Verifique sua conexão com a internet
                """)

if __name__ == "__main__":
    main()