"""
Módulo de interface do usuário para geração de capítulos de artigos acadêmicos ABNT.
"""

import os
import json
import logging
import streamlit as st
from typing import List, Dict, Any, Optional
from datetime import datetime

from .chapter_generator import ChapterGenerator
from .chapter_manager import ChapterManager
from .utils import display_sources, format_abnt_reference
from .export import export_to_pdf, markdown_to_docx

logger = logging.getLogger(__name__)

def render_chapter_generator_ui():
    """
    Renderiza a interface do usuário para geração de capítulos.
    """
    st.title("📝 Gerador de Capítulos de Artigos Acadêmicos")
    
    # Inicializar o gerenciador de capítulos
    chapter_manager = ChapterManager()
    
    # Criar abas
    tab_new, tab_manage, tab_compile = st.tabs(["Criar Novo Capítulo", "Gerenciar Capítulos", "Compilar Artigo"])
    
    with tab_new:
        render_new_chapter_tab(chapter_manager)
    
    with tab_manage:
        render_manage_chapters_tab(chapter_manager)
    
    with tab_compile:
        render_compile_article_tab(chapter_manager)

def render_new_chapter_tab(chapter_manager):
    """
    Renderiza a aba para criação de novos capítulos.
    
    Args:
        chapter_manager (ChapterManager): Instância do gerenciador de capítulos.
    """
    st.header("Criar Novo Capítulo")
    
    # Formulário para criar novo capítulo
    with st.form(key="new_chapter_form"):
        # Selecionar artigo existente ou criar novo
        article_option = st.radio(
            "Selecione uma opção:",
            ["Adicionar a um artigo existente", "Criar novo artigo"],
            index=0
        )
        
        if article_option == "Adicionar a um artigo existente":
            # Listar diretórios de artigos existentes
            article_dirs = [d for d in os.listdir(chapter_manager.base_dir) 
                           if os.path.isdir(os.path.join(chapter_manager.base_dir, d))]
            
            if not article_dirs:
                st.warning("Nenhum artigo encontrado. Crie um novo artigo primeiro.")
                article_id = ""
            else:
                article_id = st.selectbox(
                    "Selecione o artigo:",
                    article_dirs,
                    format_func=lambda x: x.replace("_", " ").title()
                )
        else:
            # Campo para o título do novo artigo
            article_title = st.text_input("Título do novo artigo:")
            article_id = article_title.lower().replace(" ", "_") if article_title else ""
        
        # Tipo de capítulo
        chapter_types = [
            "Resumo", "Abstract", "Introdução", "Metodologia", 
            "Desenvolvimento", "Resultados", "Discussão", "Conclusão"
        ]
        
        chapter_type = st.selectbox("Tipo de capítulo:", chapter_types)
        
        # Tópico do artigo
        topic = st.text_input("Tópico do artigo:", 
                             help="Tema principal do artigo, usado para orientar a geração do capítulo.")
        
        # Contagem de palavras
        word_count = st.slider("Contagem de palavras:", 100, 2000, 800, 100,
                              help="Número aproximado de palavras para o capítulo.")
        
        # Contexto adicional
        context = st.text_area("Contexto adicional (opcional):", 
                              help="Informações adicionais para orientar a geração do capítulo.")
        
        # Opções avançadas
        with st.expander("Opções avançadas"):
            # Modelo a ser usado
            model = st.selectbox(
                "Modelo:",
                ["llama3-70b-8192", "llama3-8b-8192", "mixtral-8x7b-32768"],
                index=0
            )
            
            # Usar capítulos anteriores como contexto
            use_previous_chapters = st.checkbox(
                "Usar capítulos anteriores como contexto",
                value=True,
                help="Inclui resumos dos capítulos anteriores para manter coerência."
            )
            
            # Polir capítulo automaticamente
            auto_polish = st.checkbox(
                "Polir capítulo automaticamente",
                value=True,
                help="Aplica polimento automático ao capítulo gerado."
            )
        
        # Botão para gerar capítulo
        submit_button = st.form_submit_button("Gerar Capítulo")
    
    # Processar o formulário quando enviado
    if submit_button and topic and article_id:
        with st.spinner("Gerando capítulo..."):
            try:
                # Inicializar o gerador de capítulos
                api_key = st.secrets.get("GROQ_API_KEY", "")
                chapter_generator = ChapterGenerator(api_key=api_key, model=model)
                
                # Obter resultados de pesquisa se disponíveis
                research_results = []
                research_path = os.path.join(chapter_manager.base_dir, article_id, "research_results.json")
                if os.path.exists(research_path):
                    with open(research_path, "r", encoding="utf-8") as f:
                        research_results = json.load(f)
                
                # Obter capítulos anteriores se solicitado
                previous_chapters = []
                if use_previous_chapters:
                    chapters = chapter_manager.list_chapters(article_id)
                    for chapter in chapters:
                        chapter_data = chapter_manager.get_chapter(article_id, chapter["type"])
                        if chapter_data:
                            previous_chapters.append(chapter_data["content"])
                
                # Gerar o capítulo
                chapter_content = chapter_generator.generate_chapter(
                    topic=topic,
                    chapter_type=chapter_type,
                    research_results=research_results,
                    word_count=word_count,
                    context=context,
                    previous_chapters=previous_chapters if use_previous_chapters else None
                )
                
                # Polir o capítulo se solicitado
                if auto_polish:
                    chapter_content = chapter_generator.polish_chapter(
                        chapter_content=chapter_content,
                        chapter_type=chapter_type
                    )
                
                # Salvar o capítulo
                chapter_path = chapter_manager.save_chapter(
                    article_id=article_id,
                    chapter_type=chapter_type,
                    chapter_content=chapter_content,
                    metadata={
                        "topic": topic,
                        "word_count": word_count,
                        "model": model,
                        "context": context
                    }
                )
                
                # Atualizar o artigo compilado
                chapter_manager.save_compiled_article(article_id)
                
                # Exibir o capítulo gerado
                st.success(f"Capítulo '{chapter_type}' gerado com sucesso!")
                st.subheader("Capítulo Gerado:")
                st.markdown(chapter_content)
                
                # Botões para ações adicionais
                col1, col2 = st.columns(2)
                with col1:
                    if st.button("Editar Capítulo"):
                        st.session_state["edit_chapter"] = {
                            "article_id": article_id,
                            "chapter_type": chapter_type,
                            "content": chapter_content
                        }
                        st.experimental_rerun()
                
                with col2:
                    if st.button("Exportar Capítulo para PDF"):
                        pdf_path = export_to_pdf(
                            markdown_text=chapter_content,
                            output_path=os.path.join(chapter_manager.base_dir, article_id, f"{chapter_type.lower()}.pdf"),
                            title=f"{chapter_type} - {topic}"
                        )
                        st.success(f"Capítulo exportado para PDF: {pdf_path}")
            
            except Exception as e:
                st.error(f"Erro ao gerar capítulo: {str(e)}")
                logger.error(f"Erro ao gerar capítulo: {str(e)}", exc_info=True)

def render_manage_chapters_tab(chapter_manager):
    """
    Renderiza a aba para gerenciamento de capítulos.
    
    Args:
        chapter_manager (ChapterManager): Instância do gerenciador de capítulos.
    """
    st.header("Gerenciar Capítulos")
    
    # Listar diretórios de artigos existentes
    article_dirs = [d for d in os.listdir(chapter_manager.base_dir) 
                   if os.path.isdir(os.path.join(chapter_manager.base_dir, d))]
    
    if not article_dirs:
        st.warning("Nenhum artigo encontrado. Crie um novo artigo primeiro.")
        return
    
    # Selecionar artigo
    article_id = st.selectbox(
        "Selecione o artigo:",
        article_dirs,
        format_func=lambda x: x.replace("_", " ").title(),
        key="manage_article_select"
    )
    
    # Listar capítulos do artigo selecionado
    chapters = chapter_manager.list_chapters(article_id)
    
    if not chapters:
        st.info(f"Nenhum capítulo encontrado para o artigo '{article_id.replace('_', ' ').title()}'.")
        return
    
    # Exibir capítulos em uma tabela
    st.subheader("Capítulos Disponíveis")
    
    # Criar colunas para a tabela
    col1, col2, col3, col4 = st.columns([3, 2, 2, 3])
    with col1:
        st.write("**Tipo de Capítulo**")
    with col2:
        st.write("**Palavras**")
    with col3:
        st.write("**Atualizado**")
    with col4:
        st.write("**Ações**")
    
    # Exibir cada capítulo
    for chapter in chapters:
        col1, col2, col3, col4 = st.columns([3, 2, 2, 3])
        
        with col1:
            st.write(chapter["type"])
        
        with col2:
            st.write(f"{chapter['word_count']} palavras")
        
        with col3:
            # Formatar a data de atualização
            updated_at = chapter.get("updated_at", "")
            if updated_at:
                try:
                    dt = datetime.fromisoformat(updated_at)
                    st.write(dt.strftime("%d/%m/%Y"))
                except:
                    st.write("Data desconhecida")
            else:
                st.write("Data desconhecida")
        
        with col4:
            # Botões de ação para cada capítulo
            button_col1, button_col2 = st.columns(2)
            
            with button_col1:
                if st.button("Visualizar", key=f"view_{chapter['file_name']}"):
                    chapter_data = chapter_manager.get_chapter(article_id, chapter["type"])
                    if chapter_data:
                        st.session_state["view_chapter"] = {
                            "article_id": article_id,
                            "chapter_type": chapter["type"],
                            "content": chapter_data["content"]
                        }
            
            with button_col2:
                if st.button("Editar", key=f"edit_{chapter['file_name']}"):
                    chapter_data = chapter_manager.get_chapter(article_id, chapter["type"])
                    if chapter_data:
                        st.session_state["edit_chapter"] = {
                            "article_id": article_id,
                            "chapter_type": chapter["type"],
                            "content": chapter_data["content"]
                        }
    
    # Exibir capítulo para visualização
    if "view_chapter" in st.session_state:
        view_chapter = st.session_state["view_chapter"]
        
        st.subheader(f"Visualizar Capítulo: {view_chapter['chapter_type']}")
        st.markdown(view_chapter["content"])
        
        # Botões de ação
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("Fechar"):
                del st.session_state["view_chapter"]
                st.experimental_rerun()
        
        with col2:
            if st.button("Editar este capítulo"):
                st.session_state["edit_chapter"] = view_chapter
                del st.session_state["view_chapter"]
                st.experimental_rerun()
        
        with col3:
            if st.button("Exportar para PDF"):
                pdf_path = export_to_pdf(
                    markdown_text=view_chapter["content"],
                    output_path=os.path.join(chapter_manager.base_dir, view_chapter["article_id"], f"{view_chapter['chapter_type'].lower()}.pdf"),
                    title=f"{view_chapter['chapter_type']} - {view_chapter['article_id'].replace('_', ' ').title()}"
                )
                st.success(f"Capítulo exportado para PDF: {pdf_path}")
    
    # Exibir formulário de edição de capítulo
    if "edit_chapter" in st.session_state:
        edit_chapter = st.session_state["edit_chapter"]
        
        st.subheader(f"Editar Capítulo: {edit_chapter['chapter_type']}")
        
        # Formulário de edição
        with st.form(key="edit_chapter_form"):
            # Campo de texto para editar o conteúdo
            edited_content = st.text_area(
                "Conteúdo do capítulo:",
                value=edit_chapter["content"],
                height=400
            )
            
            # Botões de ação
            col1, col2 = st.columns(2)
            
            with col1:
                cancel_button = st.form_submit_button("Cancelar")
            
            with col2:
                save_button = st.form_submit_button("Salvar Alterações")
        
        # Processar ações do formulário
        if cancel_button:
            del st.session_state["edit_chapter"]
            st.experimental_rerun()
        
        if save_button:
            # Atualizar o capítulo
            success = chapter_manager.update_chapter(
                article_id=edit_chapter["article_id"],
                chapter_type=edit_chapter["chapter_type"],
                new_content=edited_content
            )
            
            if success:
                # Atualizar o artigo compilado
                chapter_manager.save_compiled_article(edit_chapter["article_id"])
                
                st.success("Capítulo atualizado com sucesso!")
                del st.session_state["edit_chapter"]
                st.experimental_rerun()
            else:
                st.error("Erro ao atualizar o capítulo.")

def render_compile_article_tab(chapter_manager):
    """
    Renderiza a aba para compilação de artigos.
    
    Args:
        chapter_manager (ChapterManager): Instância do gerenciador de capítulos.
    """
    st.header("Compilar Artigo")
    
    # Listar diretórios de artigos existentes
    article_dirs = [d for d in os.listdir(chapter_manager.base_dir) 
                   if os.path.isdir(os.path.join(chapter_manager.base_dir, d))]
    
    if not article_dirs:
        st.warning("Nenhum artigo encontrado. Crie um novo artigo primeiro.")
        return
    
    # Selecionar artigo
    article_id = st.selectbox(
        "Selecione o artigo:",
        article_dirs,
        format_func=lambda x: x.replace("_", " ").title(),
        key="compile_article_select"
    )
    
    # Listar capítulos do artigo selecionado
    chapters = chapter_manager.list_chapters(article_id)
    
    if not chapters:
        st.info(f"Nenhum capítulo encontrado para o artigo '{article_id.replace('_', ' ').title()}'.")
        return
    
    # Exibir capítulos disponíveis
    st.subheader("Capítulos Disponíveis")
    
    # Criar uma lista de capítulos com checkboxes para seleção
    selected_chapters = []
    chapter_order = []
    
    for chapter in chapters:
        col1, col2 = st.columns([1, 9])
        
        with col1:
            selected = st.checkbox("", value=True, key=f"select_{chapter['file_name']}")
        
        with col2:
            st.write(f"{chapter['type']} ({chapter['word_count']} palavras)")
        
        if selected:
            selected_chapters.append(chapter["file_name"])
            chapter_order.append(chapter["file_name"])
    
    # Opções de compilação
    st.subheader("Opções de Compilação")
    
    # Permitir reordenar capítulos
    st.write("Arraste para reordenar os capítulos:")
    
    # Implementar reordenação manual (simplificada)
    for i, chapter_name in enumerate(chapter_order):
        col1, col2, col3 = st.columns([1, 1, 8])
        
        with col1:
            if i > 0 and st.button("↑", key=f"up_{chapter_name}"):
                # Mover para cima
                chapter_order[i], chapter_order[i-1] = chapter_order[i-1], chapter_order[i]
                st.experimental_rerun()
        
        with col2:
            if i < len(chapter_order) - 1 and st.button("↓", key=f"down_{chapter_name}"):
                # Mover para baixo
                chapter_order[i], chapter_order[i+1] = chapter_order[i+1], chapter_order[i]
                st.experimental_rerun()
        
        with col3:
            # Encontrar o tipo de capítulo correspondente ao nome do arquivo
            chapter_type = next((c["type"] for c in chapters if c["file_name"] == chapter_name), chapter_name)
            st.write(chapter_type)
    
    # Botão para compilar o artigo
    if st.button("Compilar Artigo"):
        with st.spinner("Compilando artigo..."):
            # Compilar o artigo com a ordem especificada
            article_content = chapter_manager.compile_article(article_id, chapter_order)
            
            # Salvar o artigo compilado
            article_path = chapter_manager.save_compiled_article(article_id, chapter_order)
            
            st.success("Artigo compilado com sucesso!")
            
            # Exibir o artigo compilado
            st.subheader("Artigo Compilado")
            st.markdown(article_content)
            
            # Botões para exportação
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("Exportar para PDF"):
                    pdf_path = export_to_pdf(
                        markdown_text=article_content,
                        output_path=os.path.join(chapter_manager.base_dir, article_id, "article.pdf"),
                        title=article_id.replace("_", " ").title()
                    )
                    st.success(f"Artigo exportado para PDF: {pdf_path}")
            
            with col2:
                if st.button("Exportar para DOCX"):
                    docx_path = markdown_to_docx(
                        markdown_text=article_content,
                        output_path=os.path.join(chapter_manager.base_dir, article_id, "article.docx"),
                        title=article_id.replace("_", " ").title()
                    )
                    st.success(f"Artigo exportado para DOCX: {docx_path}")
