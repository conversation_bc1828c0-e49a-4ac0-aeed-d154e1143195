"""
Módulo de geração de capítulos para o aplicativo de artigos acadêmicos ABNT.
Implementa a geração de capítulos individuais usando a API Groq.
"""

import logging
import json
import os
from typing import List, Dict, Any, Optional
from .article_generator import ArticleGenerator

logger = logging.getLogger(__name__)

class ChapterGenerator:
    """Classe para geração de capítulos de artigos acadêmicos"""

    def __init__(self, api_key, model="llama3-70b-8192", max_retries=5, use_cache=True, cache=None):
        """
        Inicializa o gerador de capítulos.

        Args:
            api_key (str): Chave da API Groq.
            model (str): Modelo a ser usado.
            max_retries (int): Número máximo de tentativas em caso de erro.
            use_cache (bool): Se deve usar cache.
            cache (APICache, optional): Instância de cache a ser usada.
        """
        # Usar o ArticleGenerator como base
        self.article_generator = ArticleGenerator(
            api_key=api_key,
            model=model,
            max_retries=max_retries,
            use_cache=use_cache,
            cache=cache
        )

    def generate_chapter(self, topic: str, chapter_type: str, research_results: List[Dict], 
                        word_count: int = 800, context: str = None, previous_chapters: List[str] = None):
        """
        Gera um capítulo individual para um artigo acadêmico.

        Args:
            topic (str): Tópico principal do artigo.
            chapter_type (str): Tipo de capítulo (introdução, metodologia, desenvolvimento, etc.).
            research_results (list): Resultados da pesquisa.
            word_count (int): Contagem de palavras desejada para o capítulo.
            context (str, optional): Contexto adicional para a geração do capítulo.
            previous_chapters (list, optional): Capítulos anteriores para manter coerência.

        Returns:
            str: Capítulo gerado.
        """
        # Preparar o contexto com os resultados da pesquisa
        research_context = "Informações da pesquisa:\n\n"
        for i, result in enumerate(research_results, 1):
            research_context += f"{i}. {result['title']}\n"
            research_context += f"   Autores: {', '.join(result['authors'])}\n"
            research_context += f"   Publicado: {result['published']}\n"
            research_context += f"   ID: {result['url']}\n"
            research_context += f"   Descrição: {result['description']}\n\n"

        # Adicionar contexto dos capítulos anteriores se fornecido
        previous_context = ""
        if previous_chapters and len(previous_chapters) > 0:
            previous_context = "Capítulos anteriores (resumo):\n\n"
            for i, chapter in enumerate(previous_chapters, 1):
                # Limitar o tamanho do resumo para não sobrecarregar o prompt
                chapter_summary = chapter[:500] + "..." if len(chapter) > 500 else chapter
                previous_context += f"Capítulo {i}:\n{chapter_summary}\n\n"

        # Definir instruções específicas para cada tipo de capítulo
        chapter_instructions = self._get_chapter_instructions(chapter_type, word_count)
        
        # Criar o prompt para geração do capítulo
        prompt = f"""Você é um especialista em escrever capítulos de artigos acadêmicos seguindo as normas ABNT.
Com base nas informações fornecidas, crie um capítulo de "{chapter_type}" para um artigo sobre "{topic}".

{chapter_instructions}

{research_context}

{previous_context if previous_context else ""}

{context if context else ""}

O capítulo deve:
1. Usar um tom acadêmico, formal e objetivo
2. Incluir informações relevantes das fontes de pesquisa
3. Ter aproximadamente {word_count} palavras
4. Incluir citações no formato ABNT ao longo do texto
5. Manter coerência com os capítulos anteriores (se fornecidos)

Para citações no formato ABNT:
- Citação direta curta (até 3 linhas): use aspas e indique o autor, ano e página entre parênteses. Ex: "texto citado" (SOBRENOME, ano, p. XX).
- Citação indireta: apenas indique o autor e ano entre parênteses. Ex: (SOBRENOME, ano).
- Use o ID fornecido para identificar cada fonte.

Formate o capítulo usando Markdown para os títulos e subtítulos.
"""

        # Gerar o capítulo com retry em caso de erro 429
        try:
            response = self.article_generator._call_groq_api_with_retry(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=2000
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Erro ao gerar capítulo: {str(e)}")
            # Retornar um capítulo básico em caso de erro
            return f"## {chapter_type}\n\nNão foi possível gerar o capítulo completo devido a limitações da API. Por favor, tente novamente mais tarde."

    def _get_chapter_instructions(self, chapter_type: str, word_count: int) -> str:
        """
        Retorna instruções específicas para cada tipo de capítulo.

        Args:
            chapter_type (str): Tipo de capítulo.
            word_count (int): Contagem de palavras desejada.

        Returns:
            str: Instruções específicas para o tipo de capítulo.
        """
        instructions = {
            "Introdução": f"""
Este capítulo deve:
- Contextualizar o tema do artigo
- Apresentar a justificativa e relevância do estudo
- Definir claramente os objetivos do trabalho
- Apresentar a estrutura do artigo
- Ter aproximadamente {word_count} palavras
""",
            "Metodologia": f"""
Este capítulo deve:
- Descrever detalhadamente os métodos e procedimentos utilizados
- Explicar como os dados foram coletados e analisados
- Justificar a escolha dos métodos
- Descrever os materiais utilizados (se aplicável)
- Ter aproximadamente {word_count} palavras
""",
            "Desenvolvimento": f"""
Este capítulo deve:
- Apresentar a fundamentação teórica do tema
- Desenvolver os conceitos principais
- Analisar criticamente as informações das fontes
- Organizar o conteúdo em subtópicos lógicos
- Ter aproximadamente {word_count} palavras
""",
            "Resultados": f"""
Este capítulo deve:
- Apresentar os resultados obtidos de forma clara e objetiva
- Utilizar dados, estatísticas ou exemplos concretos
- Evitar interpretações ou discussões (deixar para o capítulo de discussão)
- Organizar os resultados em ordem lógica
- Ter aproximadamente {word_count} palavras
""",
            "Discussão": f"""
Este capítulo deve:
- Interpretar os resultados apresentados
- Comparar os resultados com a literatura existente
- Discutir as implicações dos resultados
- Abordar limitações do estudo
- Ter aproximadamente {word_count} palavras
""",
            "Conclusão": f"""
Este capítulo deve:
- Sintetizar os principais resultados e contribuições
- Responder aos objetivos apresentados na introdução
- Apresentar as conclusões do estudo
- Sugerir direções para pesquisas futuras
- Ter aproximadamente {word_count} palavras
""",
            "Resumo": f"""
Este capítulo deve:
- Resumir todo o conteúdo do artigo em um único parágrafo
- Incluir objetivo, metodologia, principais resultados e conclusões
- Ser conciso e direto
- Ter aproximadamente {word_count} palavras
""",
            "Abstract": f"""
Este capítulo deve:
- Ser uma tradução precisa do resumo para o inglês
- Manter a mesma estrutura e conteúdo do resumo
- Usar terminologia acadêmica em inglês
- Ter aproximadamente {word_count} palavras
"""
        }
        
        return instructions.get(chapter_type, f"Este capítulo deve ter aproximadamente {word_count} palavras e seguir as normas ABNT.")

    def add_chapter_to_article(self, article_path: str, new_chapter: str, chapter_type: str) -> str:
        """
        Adiciona um novo capítulo a um artigo existente.

        Args:
            article_path (str): Caminho para o arquivo do artigo.
            new_chapter (str): Conteúdo do novo capítulo.
            chapter_type (str): Tipo de capítulo.

        Returns:
            str: Artigo atualizado com o novo capítulo.
        """
        try:
            # Ler o artigo existente
            with open(article_path, 'r', encoding='utf-8') as f:
                article_content = f.read()
            
            # Determinar onde inserir o novo capítulo
            # Para artigos acadêmicos, inserir antes das referências
            if "Referências Bibliográficas" in article_content:
                parts = article_content.split("Referências Bibliográficas", 1)
                updated_article = parts[0] + new_chapter + "\n\n**Referências Bibliográficas" + parts[1]
            else:
                # Se não houver seção de referências, adicionar ao final
                updated_article = article_content + "\n\n" + new_chapter
            
            return updated_article
            
        except Exception as e:
            logger.error(f"Erro ao adicionar capítulo ao artigo: {str(e)}")
            return article_content + "\n\n" + new_chapter

    def reorganize_chapters(self, chapters: List[Dict[str, str]], order: List[str]) -> str:
        """
        Reorganiza os capítulos de um artigo.

        Args:
            chapters (list): Lista de dicionários com os capítulos (tipo e conteúdo).
            order (list): Lista com a ordem desejada dos tipos de capítulos.

        Returns:
            str: Artigo reorganizado.
        """
        # Criar um dicionário para mapear tipo de capítulo para conteúdo
        chapter_map = {chapter["type"]: chapter["content"] for chapter in chapters}
        
        # Reorganizar os capítulos na ordem especificada
        reorganized_article = ""
        for chapter_type in order:
            if chapter_type in chapter_map:
                reorganized_article += chapter_map[chapter_type] + "\n\n"
        
        return reorganized_article.strip()

    def extract_chapters(self, article_content: str) -> List[Dict[str, str]]:
        """
        Extrai os capítulos de um artigo.

        Args:
            article_content (str): Conteúdo do artigo.

        Returns:
            list: Lista de dicionários com os capítulos (tipo e conteúdo).
        """
        # Lista de possíveis títulos de capítulos em artigos acadêmicos
        chapter_titles = [
            "Resumo", "Abstract", "Introdução", "Metodologia", 
            "Desenvolvimento", "Resultados", "Discussão", "Conclusão",
            "Referências Bibliográficas"
        ]
        
        # Adicionar variações com números (ex: "1. Introdução")
        numbered_titles = []
        for i in range(1, 10):
            for title in chapter_titles:
                numbered_titles.append(f"{i}. {title}")
                numbered_titles.append(f"{i} {title}")
        
        chapter_titles.extend(numbered_titles)
        
        # Extrair os capítulos
        chapters = []
        lines = article_content.split('\n')
        current_chapter = None
        current_content = []
        
        for line in lines:
            # Verificar se a linha é um título de capítulo
            is_chapter_title = False
            chapter_type = None
            
            # Verificar diferentes formatos de títulos (## Título, **Título**, etc.)
            clean_line = line.strip().replace('#', '').replace('*', '').strip()
            
            for title in chapter_titles:
                if title.lower() in clean_line.lower():
                    is_chapter_title = True
                    chapter_type = title
                    break
            
            if is_chapter_title and current_chapter is not None:
                # Salvar o capítulo atual antes de começar um novo
                chapters.append({
                    "type": current_chapter,
                    "content": '\n'.join(current_content)
                })
                current_content = []
            
            if is_chapter_title:
                current_chapter = chapter_type
                current_content = [line]
            elif current_chapter is not None:
                current_content.append(line)
        
        # Adicionar o último capítulo
        if current_chapter is not None and current_content:
            chapters.append({
                "type": current_chapter,
                "content": '\n'.join(current_content)
            })
        
        return chapters

    def polish_chapter(self, chapter_content: str, chapter_type: str) -> str:
        """
        Realiza o polimento de um capítulo.

        Args:
            chapter_content (str): Conteúdo do capítulo.
            chapter_type (str): Tipo de capítulo.

        Returns:
            str: Capítulo polido.
        """
        # Criar o prompt para polimento do capítulo
        prompt = f"""Você é um editor profissional especializado em artigos acadêmicos seguindo as normas ABNT.
Revise e aprimore o seguinte capítulo de "{chapter_type}", mantendo seu conteúdo e citações ABNT, mas melhorando:
1. Clareza e fluidez
2. Gramática e pontuação
3. Consistência de estilo
4. Formatação Markdown
5. Consistência nas citações ABNT

Certifique-se de que todas as citações estão no formato ABNT correto:
- Citação direta curta (até 3 linhas): use aspas e indique o autor, ano e página entre parênteses. Ex: "texto citado" (SOBRENOME, ano, p. XX).
- Citação indireta: apenas indique o autor e ano entre parênteses. Ex: (SOBRENOME, ano).

Capítulo:
{chapter_content}

Retorne o capítulo polido completo.
"""

        # Polir o capítulo com retry em caso de erro 429
        try:
            response = self.article_generator._call_groq_api_with_retry(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=2000
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Erro ao polir capítulo: {str(e)}")
            # Retornar o capítulo original em caso de erro
            return chapter_content
