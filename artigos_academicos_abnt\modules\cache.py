"""
Módulo de cache para o aplicativo de artigos acadêmicos ABNT.
Implementa um sistema de cache para armazenar respostas da API Groq.
"""

import os
import json
import hashlib
import pickle
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class APICache:
    """Implementa um sistema de cache otimizado para respostas da API Groq"""
    
    def __init__(self, cache_dir=".cache", expiration_days=7):
        """Inicializa o cache com suporte a estatísticas"""
        self.cache_dir = cache_dir
        self.expiration_days = expiration_days
        self.stats = {
            "hits": 0,
            "misses": 0,
            "saves": 0,
            "errors": 0
        }
        os.makedirs(cache_dir, exist_ok=True)
        
        # Limpar caches expirados na inicialização
        self._cleanup_expired()
    
    def _get_cache_key(self, messages, model, temperature, max_tokens):
        """Gera uma chave única para o cache baseada nos parâmetros da requisição"""
        # Simplificar a estrutura de mensagens para reduzir o tamanho da chave
        simplified_messages = []
        for msg in messages:
            simplified_messages.append({
                "role": msg.get("role", ""),
                "content": msg.get("content", "")
            })
        
        # Criar uma string com todos os parâmetros essenciais
        cache_str = json.dumps({
            'messages': simplified_messages,
            'model': model,
            'temperature': round(temperature, 2),  # Arredondar para reduzir variações insignificantes
            'max_tokens': max_tokens
        }, sort_keys=True)
        
        # Gerar um hash SHA-256 da string (mais seguro que MD5)
        return hashlib.sha256(cache_str.encode()).hexdigest()
    
    def _get_cache_path(self, cache_key):
        """Retorna o caminho para o arquivo de cache"""
        # Usar os primeiros 2 caracteres como subdiretório para evitar muitos arquivos em um único diretório
        subdir = cache_key[:2]
        subdir_path = os.path.join(self.cache_dir, subdir)
        os.makedirs(subdir_path, exist_ok=True)
        return os.path.join(subdir_path, f"{cache_key[2:]}.pkl")
    
    def _cleanup_expired(self):
        """Remove todos os caches expirados"""
        count = 0
        for root, _, files in os.walk(self.cache_dir):
            for file in files:
                if file.endswith(".pkl"):
                    file_path = os.path.join(root, file)
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if datetime.now() - file_time > timedelta(days=self.expiration_days):
                        try:
                            os.remove(file_path)
                            count += 1
                        except Exception:
                            pass
        if count > 0:
            logger.info(f"Removidos {count} arquivos de cache expirados")
    
    def get(self, messages, model, temperature, max_tokens):
        """Tenta obter uma resposta do cache com estatísticas"""
        cache_key = self._get_cache_key(messages, model, temperature, max_tokens)
        cache_path = self._get_cache_path(cache_key)
        
        # Verificar se o cache existe
        if not os.path.exists(cache_path):
            self.stats["misses"] += 1
            return None
        
        # Verificar se o cache expirou
        file_time = datetime.fromtimestamp(os.path.getmtime(cache_path))
        if datetime.now() - file_time > timedelta(days=self.expiration_days):
            try:
                os.remove(cache_path)  # Remover cache expirado
            except Exception:
                pass
            self.stats["misses"] += 1
            return None
        
        # Carregar o cache
        try:
            with open(cache_path, 'rb') as f:
                result = pickle.load(f)
                self.stats["hits"] += 1
                return result
        except Exception as e:
            logger.error(f"Erro ao carregar cache: {e}")
            self.stats["errors"] += 1
            # Tentar remover o arquivo corrompido
            try:
                os.remove(cache_path)
            except Exception:
                pass
            return None
    
    def set(self, messages, model, temperature, max_tokens, response):
        """Salva uma resposta no cache com tratamento de concorrência"""
        cache_key = self._get_cache_key(messages, model, temperature, max_tokens)
        cache_path = self._get_cache_path(cache_key)
        
        # Criar um arquivo temporário primeiro para evitar problemas de concorrência
        temp_path = f"{cache_path}.tmp"
        try:
            with open(temp_path, 'wb') as f:
                pickle.dump(response, f)
            
            # Renomear o arquivo temporário para o arquivo final
            if os.path.exists(cache_path):
                os.remove(cache_path)
            os.rename(temp_path, cache_path)
            
            self.stats["saves"] += 1
            return True
        except Exception as e:
            logger.error(f"Erro ao salvar cache: {e}")
            self.stats["errors"] += 1
            # Limpar arquivo temporário em caso de erro
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except Exception:
                    pass
            return False
    
    def get_stats(self):
        """Retorna estatísticas do uso do cache"""
        total = self.stats["hits"] + self.stats["misses"]
        hit_rate = (self.stats["hits"] / total * 100) if total > 0 else 0
        return {
            **self.stats,
            "hit_rate": f"{hit_rate:.1f}%",
            "total_requests": total
        }
