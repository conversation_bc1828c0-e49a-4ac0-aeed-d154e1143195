This page lists incidents for which we have written retrospectives and postmortems.

## Postmortems

When adding a new incident, start from the [postmortem template](postmortem-template.md).

* [Postmortem: Build Breakage on 2016-11-08](Postmortem-Build-Breakage-on-2016-11-08.md)
* [Postmortem: Beta 1 Release](Postmortem-Beta-1-Release.md)
* [Postmortem: AndroidX plugin migration](Postmortem-AndroidX-plugin-migration.md)
* [Postmortem: Widespread Gradle failures on Flutter v1.12.13 hotfix.5](Postmortem-Widespread-Gradle-failures-on-Flutter-v1.12.13-hotfix.5.md)
* [Postmortem: Flutter/Dart Incorrect Version July 2020](https://docs.google.com/document/d/1TeCpj-T0HAp7DcY2ag-q5iX218TeOHNcW5fUusaum0c/edit)
* [Postmortem: Flutter Gold Summer 2020 Outages](https://docs.google.com/document/d/1cZi84iWFJa9l7TIhSZEnhZlg7bBBIoJLjzx-uLUSLzI/edit?usp=sharing)
* [Postmortem: Pub.dev outage 2021-03-24](https://docs.google.com/document/d/1hBVi6_1FXoY1hG9zgKFUkqreP7UsnaF4EGspIJh0E2g/edit?hl=en)
* [Postmortem: Windows Defender alert on 2023-03-26](Postmortem-Windows-Defender-alert-on-2023-03-26.md)
* [Postmortem: flutter.dev outage on August 26 2023](Postmortem-flutter.dev-outage-on-August-26-2023.md)

## Retrospectives

* [Retrospective: Flutter 2.8 Stable Release](Retrospective-Flutter-2.8-Stable-Release.md)  16 December 2021