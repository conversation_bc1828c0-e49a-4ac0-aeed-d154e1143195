# Awesome AI Apps [![Awesome](https://awesome.re/badge.svg)](https://awesome.re)

![Banner](/assets/banner.png)

Powered by [Nebius AI Studio](https://dub.sh/AIStudio) - Your one-stop platform for building and deploying AI applications.

A comprehensive collection of practical examples, tutorials and recipes showcasing how to build powerful LLM-powered applications using various frameworks and tools.

From simple chatbots and MCP examples to advance AI Agents, this repository serves as your guide to building some cool AI applications.

## 🚀 Featured AI Agent Frameworks

- [<img src="https://raw.githubusercontent.com/google/adk-python/main/assets/agent-development-kit.png" alt="Google ADK logo" width="20" height="20"> Google Agent Development Kit (ADK)](https://google.github.io/adk-docs/)
- [<img src="https://avatars.githubusercontent.com/u/14957082?s=200&v=4" alt="OpenAI Agents SDK logo" width="20" height="20"> OpenAI Agents SDK](https://openai.github.io/openai-agents-python/)
- [<img src="https://cdn.simpleicons.org/langchain" alt="LangChain logo" width="25" height="25"> LangChain ](https://python.langchain.com/)
- [<img src="https://avatars.githubusercontent.com/u/130722866?s=200&v=4" alt="Llamaindex logo" width="20" height="20"> LlamaIndex](https://www.llamaindex.ai/)
- [<img src="https://avatars.githubusercontent.com/u/104874993?s=48&v=4" alt="Agno logo" width="20" height="20"> Agno](https://www.agno.com/)
-  [<img src="https://cdn.prod.website-files.com/66cf2bfc3ed15b02da0ca770/66d07240057721394308addd_Logo%20(1).svg" alt="CrewAI logo" width="35" height="25"> CrewAI](https://www.crewai.com/)
- [<img src="https://avatars.githubusercontent.com/u/110818415?s=200&v=4" alt="Pydantic AI logo" width="20" height="20"> Pydantic AI](https://ai.pydantic.dev/)


## 🧩 Starter Agents

**Quick-start agents for learning and extending:**

* [Agno HackerNews Analysis](starter_ai_agents/agno_starter) - Agno-based agent for trend analysis on HackerNews.
* [OpenAI SDK Starter](starter_ai_agents/openai_agents_sdk) - OpenAI Agents SDK based email helper & haiku writer.
* [LlamaIndex Task Manager](starter_ai_agents/llamaindex_starter) - LlamaIndex-powered task assistant.
* [CrewAI Research Crew](starter_ai_agents/crewai_starter) - Multi-agent research team.
* [PydanticAI Weather Bot](starter_ai_agents/pydantic_starter) - Real-time weather info.
* [LangChain-LangGraph Starter](starter_ai_agents/langchain_langgraph_starter) - LangChain + LangGraph starter.

## 🪶 Simple Agents

**Straightforward, practical use-cases:**

* [Finance Agent](simple_ai_agents/finance_agent) - Tracks live stock & market data.
* [Human-in-the-Loop Agent](simple_ai_agents/human_in_the_loop_agent) - HITL actions for safe AI tasks.
* [Newsletter Generator](simple_ai_agents/newsletter_agent) - AI newsletter builder with Firecrawl.
* [Reasoning Agent](simple_ai_agents/reasoning_agent) - Financial reasoning step-by-step.
* [Agno UI Example](simple_ai_agents/agno_ui_agent) - UI for web & finance agents.
* [Mastra Weather Bot](simple_ai_agents/mastra_ai_weather_agent) - Weather updates with Mastra AI.
* [Calendar Assistant](simple_ai_agents/cal_scheduling_agent) - Calendar scheduling with Cal.com.

## 🗂️ MCP Agents

**Examples using Managed Compute Providers:**

* [Doc-MCP](mcp_ai_agents/doc_mcp) - Semantic RAG docs & Q\&A.
* [LangGraph MCP Agent](mcp_ai_agents/langchain_langgraph_mcp_agent) - LangChain ReAct agent with Couchbase.
* [GitHub MCP Agent](mcp_ai_agents/github_mcp_agent) - Repo insights via MCP.
* [MCP Starter](mcp_ai_agents/mcp_starter) - GitHub repo analyzer starter.

## 📚 RAG Applications

**Retrieve-augmented generation examples:**

* [Resume Optimizer](rag_apps/resume_optimizer) - Boost resumes with AI.
* [LlamaIndex RAG Starter](rag_apps/llamaIndex_starter) - LlamaIndex + Nebius RAG starter.
* [PDF RAG Analyzer](rag_apps/pdf_rag_analyser) - Chat with multiple PDFs.
* [Qwen3 RAG Chat](rag_apps/qwen3_rag) - PDF chatbot with Streamlit.
* [Chat with Code](rag_apps/chat_with_code) - Conversational code explorer.


## 🔬 Advanced Agents

**Complex pipelines for end-to-end workflows:**

* [Deep Researcher](advance_ai_agents/deep_researcher_agent) - Multi-stage research with Agno & Scrapegraph AI.
* [Candilyzer](advance_ai_agents/candidate_analyser) - Analyze GitHub/LinkedIn profiles.
* [Job Finder](advance_ai_agents/job_finder_agent) - LinkedIn job search with Bright Data.
* [AI Trend Analyzer](advance_ai_agents/trend_analyzer_agent) - AI trend mining with Google ADK.
* [Conference Talk Generator](advance_ai_agents/conference_talk_abstract_generator) - Draft talk abstracts with Google ADK & Couchbase.
* [Finance Service Agent](advance_ai_agents/finance_service_agent) - FastAPI server for stock data and predictions with Agno.
<!-- * [YouTube to Blog](advance_ai_agents/youtube_to_blog_agent) - Auto-blog from YouTube videos. -->

## 📺 Playlist of Demo Videos & Tutorials

- [Build with MCP](https://www.youtube.com/playlist?list=PLMZM1DAlf0Lolxax4L2HS54Me8gn1gkz4)
- [Build AI Agents](https://www.youtube.com/playlist?list=PLMZM1DAlf0LqixhAG9BDk4O_FjqnaogK8)
- [AI Agents, MCP and more...](https://www.youtube.com/playlist?list=PL2ambAOfYA6-LDz0KpVKu9vJKAqhv0KKI)

## Getting Started

### Prerequisites

- Python 3.10 or higher
- Git
- pip (Python package manager) or uv

### Installation Steps

1. **Clone the repository**

   ```bash
   git clone https://github.com/Arindam200/awesome-ai-apps.git
   ```

2. **Navigate to the desired project directory**

   ```bash
   cd awesome-ai-apps/starter_ai_agents/agno_starter
   ```

3. **Install the required dependencies**

   ```bash
   pip install -r requirements.txt
   ```

4. **Follow project-specific instructions**
   - Each project has its own README.md with detailed setup and usage instructions
   - Make sure to read the project-specific documentation before running the application

## 🤝 Contributing

We welcome contributions from the community! Whether you're a beginner or an expert, your examples and tutorials can help others learn and grow. Here's how you can contribute:

1. Submit a Pull Request with your LLM application example
2. Add detailed documentation and setup instructions
3. Include requirements.txt or environment.yml
4. Share your experience and best practices

## 📜 License

This repository is licensed under the [MIT License](./LICENSE). Feel free to use and modify the examples for your projects.

## Thank You for the Support! 🙏

[![Star History Chart](https://api.star-history.com/svg?repos=Arindam200/awesome-ai-apps&type=Date)](https://www.star-history.com/#Arindam200/awesome-ai-apps&Date)
