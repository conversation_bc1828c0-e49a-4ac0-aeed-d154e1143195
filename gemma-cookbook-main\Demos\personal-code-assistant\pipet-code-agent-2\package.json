{"name": "pipet-code-agent", "publisher": "Google AI Developer Relations", "displayName": "Pipet Code Agent 2", "description": "", "version": "0.0.2", "engines": {"vscode": "^1.78.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "pipet-code-agent.codeQuestion", "title": "Pipet: Ask a coding question."}, {"command": "pipet-code-agent.commentCode", "title": "Pipet: Add a comment to selected code."}, {"command": "pipet-code-agent.reviewCode", "title": "Pipet: Review the selected code."}], "configuration": [{"title": "Pipet Code Agent 2", "properties": {"gemma.service.host": {"type": ["string", "null"], "default": null, "markdownDescription": "Enter the hostname or IP address for the Gemma server."}, "google.gemini.apiKey": {"type": ["string", "null"], "default": null, "markdownDescription": "Enter your [API Key](https://aistudio.google.com/app/apikey) for Gemini."}, "google.gemini.textModel": {"type": ["string"], "default": "models/gemini-1.0-pro-latest", "markdownDescription": "Provide the name of the model you want to use. Choose from the [base models](https://ai.google.dev/models/gemini) or your own [tuned model](https://ai.google.dev/docs/model_tuning_guidance)."}}}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/mocha": "^10.0.1", "@types/node": "20.2.5", "@types/vscode": "^1.78.0", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "@vscode/test-electron": "^2.3.2", "eslint": "^8.41.0", "glob": "^8.1.0", "mocha": "^10.8.2", "typescript": "^5.1.3"}, "dependencies": {"@google/generative-ai": "^0.3.0", "dotenv": "^16.1.4"}}