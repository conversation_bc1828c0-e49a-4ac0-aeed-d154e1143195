:root {
  --gradient-top: #fda64f;
    --gradient-bottom: #fc9d3d;
    --colorized-text: #e87500;
  --black: #070600;
  --white: #f7f7ff;
  --button-bg: var(--colorized-text);
  --button-bg-hover: #b75c01;
  --button-text: white;
  --border-radius: 1rem;
  --elevation-1: 0px 1px 1px 0px rgba(0, 0, 0, 0.14),
                 0px 2px 1px -1px rgba(0, 0, 0, 0.12),
                 0px 1px 3px 0px rgba(0, 0, 0, 0.2);
  --margin-left: 5rem;
  --display-font: "Poppins", Roboto, sans-serif;
  --text-font: Roboto, sans-serif;
}

body {
  background: var(--white);
  color: var(--black);
  font-family: var(--text-font);
  margin: 0;
}

.container {
   display: grid;
   grid-template-areas: "left-col main-col";
   grid-template-columns: 15rem auto;
   height: 100vh;
   width: 99vw;
   overflow: hidden;
}

.left-column {
   background: linear-gradient(180deg, var(--gradient-top), var(--gradient-bottom));
   border-radius: 0 var(--border-radius) var(--border-radius) 0;
   grid-area: left-col;
   padding: 1.5rem;
}

.right-column {
  display: grid;
  grid-area: main-col;
  grid-template-areas: "topbar"
                       "form"
                       "output";
  grid-template-rows: 4rem .5fr 1fr;
}

.logo {
  position: relative;
  text-align: center;
  top: 3rem;
}

.topbar {
  padding: 1rem;
  text-align: right;
  color: var(--colorized-text);
}

.settings {
  content: 'settings';
}

.material-symbols-outlined {
  cursor: pointer;
  font-variation-settings: 'FILL' 1,
                           'wght' 400,
                           'GRAD' 0,
                           'opsz' 24;
  font-size: 2rem !important;
  user-select: none;
}

h1 {
  color: var(--white);
  font-family: var(--display-font);
  font-size: 4rem;
  margin: auto;
  width: 100%;
}

h2 {
  color: var(--colorized-text);
  font-family: var(--display-font);
}

.form-container {
  display: grid;
  grid-area: form;
  grid-template-areas: "formbox import";
  grid-template-columns: min-content;
  justify-content: left;
  margin-left: var(--margin-left);
}

.import-button-group {
  grid-area: import;
}

form {
  display: inline-grid;
  grid-area: formbox;
  grid-template-rows: min-content;
  width: fit-content;
}

textarea {
  width: 45vw;
  height: 20vh;
  padding: 1rem;
  font-family: var(--text-font);
  font-size: 1rem;
  border: 1px solid #efd6d6;
  border-radius: 1rem;
  resize: none;
  box-shadow: var(--elevation-1);
}

textarea:focus {
  outline-color: var(--gradient-bottom);
}

button {
  padding: .7rem 2.5rem;
  background-color: var(--button-bg);
  color: var(--button-text);
  border: none;
  border-radius: 5px;
  cursor: pointer;
  height: fit-content;
  width: fit-content;
  margin: 1rem auto;
  font-family: var(--display-font);
  font-weight: 600;
  transition: background .3s;
}

button:hover,
button:focus {
  background: var(--button-bg-hover);
}

.import,
.copy-to-clipboard {
  margin-left: 1rem;
  padding: .5rem;
}

.button-group {
  display: inline-flex;
}

.tooltip {
  background: var(--black);
  border-radius: .5em;
  color: var(--white);
  filter: opacity(.75);
  font-family: var(--display-font);
  font-size: .9rem;
  font-weight: 500;
  position: relative;
  top: 5rem;
  left: -3rem;
  height: fit-content;
  opacity: 0;
  padding: .5em 1em;
  transition: opacity .2s;
  visibility: hidden;
}

button:hover + .tooltip,
button:focus + .tooltip {
  opacity: 1;
  visibility: visible;
}

.output {
  grid-area: output;
  margin-left: var(--margin-left);
}

.form-container,
.export {
  grid-template-columns: min-content;
  justify-content: left;
}

.export {
  display: grid;
  grid-template-areas: "output copybutton";
  width: fit-content;
}

.copy-button-group {
  grid-area: copybutton;
}

.json-output {
  grid-area: output;
}
