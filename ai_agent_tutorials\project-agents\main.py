import streamlit as st
import time
import logging
import traceback
from typing import List, Optional, Dict, Any
from datetime import datetime
from pathlib import Path
from agents import create_searcher_agent, create_writer_agent
from team import create_editor_team
from utils import load_env, get_rate_limit_info, setup_logging
from database import ArticleDatabase

# Setup logging and database
logger = setup_logging()
db = ArticleDatabase()

# Rate Limit Configuration
RATE_LIMIT_CONFIG = {
    "llama3-8b-8192": {"rpm": 30, "rpd": 14400, "tpm": 6000},
    "llama-3.2-3b-preview": {"rpm": 30, "rpd": 7000, "tpm": 7000},
    "deepseek-r1-distill-qwen-32b": {"rpm": 30, "rpd": 1000, "tpm": 6000},
}

class RateLimitManager:
    def __init__(self, model: str):
        self.model = model
        self.last_request_time = 0
        self.min_interval = 60 / RATE_LIMIT_CONFIG.get(model, {}).get('rpm', 30)

    def wait_if_needed(self):
        current_time = time.time()
        elapsed_time = current_time - self.last_request_time
        
        if elapsed_time < self.min_interval:
            wait_time = self.min_interval - elapsed_time
            logger.info(f"Rate limit reached. Waiting {wait_time:.2f} seconds.")
            time.sleep(wait_time)
        
        self.last_request_time = time.time()

def safe_metric_calculation(metrics: Dict[str, Any]) -> Dict[str, Any]:
    """Safely calculate metrics with robust error handling."""
    try:
        logger.debug(f"Raw Metrics: {metrics}")

        def extract_token_value(token_data):
            if isinstance(token_data, list):
                return sum(token_data) if token_data else 0
            return token_data if isinstance(token_data, (int, float)) else 0

        input_tokens = extract_token_value(metrics.get('input_tokens', 0))
        output_tokens = extract_token_value(metrics.get('output_tokens', 0))
        total_tokens = input_tokens + output_tokens
        total_time = metrics.get('total_time', 0)
        
        if not isinstance(total_time, (int, float)):
            total_time = 0

        tokens_per_second = total_tokens / total_time if total_time > 0 else 0

        return {
            'input': input_tokens,
            'output': output_tokens,
            'total': total_tokens,
            'total_time': total_time,
            'tokens_per_second': tokens_per_second,
            'additional_metrics': metrics.get('additional_metrics', {})
        }

    except Exception as e:
        logger.error(f"Metric processing error: {e}\n{traceback.format_exc()}")
        return {
            'input': 0,
            'output': 0,
            'total': 0,
            'total_time': 0,
            'tokens_per_second': 0,
            'additional_metrics': {}
        }

def format_metrics(metrics: Dict[str, Any]) -> str:
    """Format performance metrics for display."""
    if not metrics:
        return "No metrics available"
    
    metrics_display = [
        "📊 Performance Metrics 📊",
        f"• Tokens:",
        f"  - Input: {metrics.get('input', 0):,}",
        f"  - Output: {metrics.get('output', 0):,}",
        f"  - Total: {metrics.get('total', 0):,}",
        f"• Generation Time: {metrics.get('total_time', 0):.2f}s",
        f"• Tokens per Second: {metrics.get('tokens_per_second', 0):.2f} tokens/s"
    ]
    
    additional_metrics = metrics.get('additional_metrics', {})
    if additional_metrics:
        metrics_display.append("• Additional Metrics:")
        
        if isinstance(additional_metrics, dict):
            for key, value in additional_metrics.items():
                metrics_display.append(f"  - {key.replace('_', ' ').title()}: {value}")
        elif isinstance(additional_metrics, list):
            for item in additional_metrics:
                if isinstance(item, dict):
                    for key, value in item.items():
                        metrics_display.append(f"  - {key.replace('_', ' ').title()}: {value}")
                else:
                    metrics_display.append(f"  - {item}")
    
    return "\n".join(metrics_display)

def validate_and_retry_generation(
    topic: str, 
    model: str, 
    max_retries: int = 3
) -> Optional[Dict[str, Any]]:
    """Generate article with rate limit, retry handling, and metrics tracking."""
    rate_limiter = RateLimitManager(model)
    
    for attempt in range(max_retries):
        try:
            rate_limiter.wait_if_needed()
            start_time = time.time()
            
            # Create agents and team
            searcher = create_searcher_agent()
            writer = create_writer_agent()
            editor = create_editor_team(searcher, writer, load_env())
            editor.model.id = model

            # Generate response
            full_response = editor.run(f"Write an article about {topic}")
            
            if full_response:
                end_time = time.time()
                total_time = end_time - start_time
                
                # Handle both direct content and ArticleResult object
                if hasattr(full_response, 'content'):
                    article_content = full_response.content
                else:
                    article_content = str(full_response)
                
                # Clean article text
                article_text = article_content.replace("By Editor", "").strip()
                
                # Extract metrics, with fallback to empty dict
                raw_metrics = getattr(full_response, 'metrics', {})
                raw_metrics['total_time'] = total_time
                
                processed_metrics = safe_metric_calculation(raw_metrics)
                
                # Extract additional data if available
                search_terms = getattr(full_response, 'search_terms', [])
                sources = getattr(full_response, 'sources', [])
                
                return {
                    'article': article_text,
                    'metrics': processed_metrics,
                    'search_terms': search_terms,
                    'sources': sources
                }
            
        except Exception as e:
            logger.error(f"Attempt {attempt + 1} failed: {e}\n{traceback.format_exc()}")
            time.sleep(2 ** attempt)
    
    logger.error("Failed to generate article after maximum retries.")
    return None

def display_article_history():
    """Display a sidebar with recent articles."""
    st.sidebar.title("Article History")
    
    # Show favorites first
    favorites = db.get_recent_articles(100)
    favorites = [art for art in favorites if art['is_favorite']]
    
    if favorites:
        st.sidebar.subheader("⭐ Favorites")
        for article in favorites:
            col1, col2 = st.sidebar.columns([4, 1])
            col1.write(f"**{article['topic']}**")
            col1.caption(article['created_at'].split('.')[0] if isinstance(article['created_at'], str) else article['created_at'])
            
            if col2.button("⭐", key=f"fav_{article['id']}"):
                db.toggle_favorite(article['id'])
                st.rerun()
            
            if st.sidebar.button("View", key=f"view_{article['id']}"):
                st.session_state['view_article'] = article['id']
    
    # Show recent articles
    st.sidebar.subheader("Recent Articles")
    recent_articles = db.get_recent_articles(10)
    
    for article in recent_articles:
        if article['is_favorite']:
            continue
            
        col1, col2 = st.sidebar.columns([4, 1])
        col1.write(article['topic'])
        col1.caption(article['created_at'].split('.')[0] if isinstance(article['created_at'], str) else article['created_at'])
        
        if col2.button("☆", key=f"star_{article['id']}"):
            db.toggle_favorite(article['id'])
            st.rerun()
        
        if st.sidebar.button("View", key=f"view_rec_{article['id']}"):
            st.session_state['view_article'] = article['id']

def display_article_view(article_id: int):
    """Display a specific article in view mode."""
    article = db.get_article(article_id)
    if not article:
        st.error("Article not found")
        return
    
    st.title(article['topic'])
    st.caption(f"Generated on {article['created_at']} with {article['model_used']}")
    
    col1, col2 = st.columns([4, 1])
    col1.markdown(article['content'])
    
    if col2.button("⭐ Favorite" if not article['is_favorite'] else "★ Unfavorite"):
        db.toggle_favorite(article_id)
        st.rerun()
    
    # Show metrics if available
    metrics = db.get_article_metrics(article_id)
    if metrics:
        with st.expander("View Generation Metrics"):
            st.code(format_metrics(metrics))
    
    if st.button("← Back to Generator"):
        del st.session_state['view_article']

def main():
    st.set_page_config(
        page_title="NYT Article Generator",
        page_icon="📰",
        layout="wide"
    )
    
    # Initialize session state
    if 'view_article' not in st.session_state:
        st.session_state['view_article'] = None
    
    # Display article view if requested
    if st.session_state['view_article']:
        display_article_view(st.session_state['view_article'])
        return
    
    # Main generator interface
    display_article_history()
    
    st.title("📰 NYT Article Generator")
    st.markdown("Generate New York Times-style articles on any topic using AI.")
    
    # Model selection with info
    col1, col2 = st.columns([3, 2])
    
    with col1:
        selected_model = st.selectbox(
            "Select AI Model:",
            options=list(RATE_LIMIT_CONFIG.keys()),
            index=1,  # Default to llama3-70b-8192
            help="More powerful models may have stricter rate limits."
        )
    
    with col2:
        model_limits = RATE_LIMIT_CONFIG[selected_model]
        st.info(f"""
            **Model Limits**:
            - Requests/min: {model_limits['rpm']}
            - Requests/day: {model_limits['rpd']}
            - Tokens/min: {model_limits['tpm']:,}
        """)
    
    # Topic input with examples
    topic = st.text_input(
        "Article Topic:",
        placeholder="e.g., Latest developments in quantum computing",
        help="Be specific for better results. Example: 'Impact of AI on journalism in 2024'"
    )
    
    # Advanced options
    with st.expander("Advanced Options"):
        st.checkbox("Include sources in article", value=True, key="include_sources")
        st.checkbox("Show detailed generation process", value=False, key="debug_mode")
        st.slider("Minimum paragraphs", 10, 30, 15, help="Target article length")
    
    # Generation button
    if st.button("Generate Article", type="primary"):
        if not topic.strip():
            st.error("Please enter a topic")
        else:
            with st.spinner(f"Generating NYT-worthy article about '{topic}'..."):
                result = validate_and_retry_generation(topic, selected_model)
                
                if result:
                    # Save to database
                    article_id = db.save_article(
                        topic=topic,
                        content=result['article'],
                        model_used=selected_model,
                        metrics=result['metrics'],
                        search_terms=result.get('search_terms'),
                        sources=result.get('sources')
                    )
                    
                    # Display results
                    st.success("Article generated successfully!")
                    st.markdown(result['article'])
                    
                    with st.expander("Generation Metrics"):
                        st.code(format_metrics(result['metrics']))
                    
                    # Show sources if available
                    if result.get('sources') and st.session_state.include_sources:
                        st.subheader("Article Sources")
                        for source in result['sources']:
                            st.markdown(f"- [{source.get('domain', 'Source')}]({source['url']})")
                else:
                    st.error("Failed to generate article. Please try again later.")

if __name__ == "__main__":
    main()