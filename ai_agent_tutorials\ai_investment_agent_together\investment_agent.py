import streamlit as st
from phi.agent import Agent
from together import Together
from phi.tools.yfinance import YFinanceTools
from phi.tools.duckduckgo import DuckDuckGo
import requests

# Lista de modelos Together disponíveis
TOGETHER_MODELS = {
    "Meta-Llama 3.1 405B": "meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo",
    "Meta-Llama 3.1-8B": "meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo-128K",
    "Meta-Llama 3.2-3B": "meta-llama/Llama-3.2-3B-Instruct-Turbo",
    "Meta-Llama 3.2-1B": "meta-llama/Llama-3.2-1B-Instruct",
    "DeepSeek V3": "deepseek-ai/DeepSeek-V3",
    "Qwen 2.5 7B": "Qwen/Qwen2.5-7B-Instruct-Turbo",
    "Meta-Llama 3.3 70B": "meta-llama/Llama-3.3-70B-Instruct-Turbo"
}

def fetch_alpha_vantage_news(api_key, ticker):
    """Consulta as últimas notícias do Alpha Vantage."""
    try:
        url = f"https://www.alphavantage.co/query?function=NEWS_SENTIMENT&tickers={ticker}&apikey={api_key}"
        response = requests.get(url)
        response.raise_for_status()
        news_data = response.json()
        return news_data.get("feed", [])
    except Exception as e:
        raise Exception(f"Erro ao consultar Alpha Vantage: {str(e)}")

def format_news(news_list):
    """Formata a lista de notícias para exibição no Streamlit."""
    formatted_news = ""
    for news in news_list:
        title = news.get("title", "Sem título")
        summary = news.get("summary", "Sem resumo")
        link = news.get("url", "")
        formatted_news += f"**{title}**\n\n{summary}\n[Leia mais]({link})\n\n---\n"
    return formatted_news

def initialize_agents(api_key, model_id):
    """Inicializa os agentes com as configurações necessárias"""
    try:
        client = Together(api_key=api_key)
        model_config = {"model": model_id}

        websearchagent = Agent(
            name='webagent',
            role='search the web for the information',
            model=model_config,
            tools=[DuckDuckGo()],
            instructions=['Always include sources'],
            show_tools_calls=True,
            markdown=True,
            client=client
        )

        finagent = Agent(
            name='finagent',
            model=model_config,
            tools=[YFinanceTools(
                stock_price=True,
                analyst_recommendations=True,
                stock_fundamentals=True,
                company_news=True
            )],
            instructions='use tables to display the data',
            show_tools_calls=True,
            markdown=True,
            client=client
        )

        multiagent = Agent(
            team=[websearchagent, finagent],
            model=model_config,
            instructions=['Always include sources', 'use tables to display the data'],
            show_tools_calls=True,
            markdown=True,
            client=client
        )

        return multiagent
    except Exception as e:
        raise Exception(f"Erro ao inicializar agentes: {str(e)}\nVerifique se a API key e o modelo selecionado estão corretos.")

def main():
    st.set_page_config(page_title="Análise de Ações", page_icon="📈")
    st.title("🤖 Assistente de Análise de Ações")

    with st.sidebar:
        st.header("Configurações")
        selected_model = st.selectbox("Selecione o modelo:", options=list(TOGETHER_MODELS.keys()), index=0)
        model_id = TOGETHER_MODELS[selected_model]
        api_key_together = st.text_input("Digite sua Together API Key:", type="password")
        api_key_alpha = st.text_input("Digite sua Alpha Vantage API Key:", type="password")

        if api_key_together and api_key_alpha:
            st.success("API Keys configuradas!")
        else:
            st.warning("Ambas as API Keys são necessárias.")

    ticker = st.text_input("Digite o ticker da ação (ex: TESLA):", "TESLA")

    analysis_type = st.multiselect(
        "Selecione o tipo de análise:",
        ["Recomendações de Analistas", "Últimas Notícias", "Dados Fundamentais", "Notícias Alpha Vantage"],
        default=["Recomendações de Analistas", "Últimas Notícias"]
    )

    if st.button("Analisar"):
        if not api_key_together or not api_key_alpha:
            st.error("Por favor, insira todas as API keys para continuar.")
            return

        if not ticker:
            st.error("Por favor, insira um ticker válido.")
            return

        if not analysis_type:
            st.error("Por favor, selecione pelo menos um tipo de análise.")
            return

        with st.spinner("Processando análise..."):
            try:
                multiagent = initialize_agents(api_key_together, model_id)
                query_parts = []
                if "Recomendações de Analistas" in analysis_type:
                    query_parts.append("recent analyst recommendations")
                if "Últimas Notícias" in analysis_type:
                    query_parts.append("latest news")
                if "Dados Fundamentais" in analysis_type:
                    query_parts.append("fundamental data")

                query = f"Summarize {', '.join(query_parts)} for {ticker} stock, with sources."

                client = Together(api_key=api_key_together)
                response = client.chat.completions.create(
                    model=model_id,
                    messages=[{"role": "user", "content": query}],
                    max_tokens=1000,
                    temperature=0.7,
                    top_p=0.7,
                    top_k=50,
                    repetition_penalty=1,
                    stop=["<\uff5cend｜of｜sentence\uff5c>"],
                    stream=True
                )

                st.markdown("### Resultados da Análise")
                result = ""
                sources = ""
                for token in response:
                    if hasattr(token, 'choices'):
                        result += token.choices[0].delta.content
                        if "sources:" in token.choices[0].delta.content:
                            sources = token.choices[0].delta.content.split("sources:")[-1].strip()

                st.markdown(result)
                if sources:
                    st.markdown("### Fontes:")
                    st.markdown(sources)

                if "Notícias Alpha Vantage" in analysis_type:
                    st.markdown("### Notícias do Alpha Vantage")
                    news = fetch_alpha_vantage_news(api_key_alpha, ticker)
                    formatted_news = format_news(news)
                    st.markdown(formatted_news or "Nenhuma notícia encontrada.")

            except Exception as e:
                st.error(f"Erro ao processar a análise: {str(e)}")

if __name__ == "__main__":
    main()
