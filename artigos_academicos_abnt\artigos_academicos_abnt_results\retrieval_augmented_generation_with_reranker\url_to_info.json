{"http://arxiv.org/abs/2502.20245v1": {"url": "http://arxiv.org/abs/2502.20245v1", "title": "From Retrieval to Generation: Comparing Different Approaches", "description": "Knowledge-intensive tasks, particularly open-domain question answering (ODQA), document reranking, and retrieval-augmented language modeling, require a balance between retrieval accuracy and generative flexibility. Traditional retrieval models such as BM25 and Dense Passage Retrieval (DPR), efficiently retrieve from large corpora but often lack semantic depth. Generative models like GPT-4-o provide richer contextual understanding but face challenges in maintaining factual consistency. In this work, we conduct a systematic evaluation of retrieval-based, generation-based, and hybrid models, with a primary focus on their performance in ODQA and related retrieval-augmented tasks. Our results show that dense retrievers, particularly DPR, achieve strong performance in ODQA with a top-1 accuracy of 50.17\\% on NQ, while hybrid models improve nDCG@10 scores on BEIR from 43.42 (BM25) to 52.59, demonstrating their strength in document reranking. Additionally, we analyze language modeling tasks...", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "published": "2025-02-27", "pdf_url": "http://arxiv.org/pdf/2502.20245v1", "categories": ["cs.CL"]}, "http://arxiv.org/abs/2504.02921v1": {"url": "http://arxiv.org/abs/2504.02921v1", "title": "HyperRAG: Enhancing Quality-Efficiency Tradeoffs in Retrieval-Augmented Generation with Reranker KV-Cache Reuse", "description": "Retrieval-Augmented Generation (RAG) has emerged as a powerful paradigm for enhancing the performance of large language models (LLMs) by integrating external knowledge into the generation process. A key component of RAG pipelines is the reranker, which selects the most relevant documents from a pool of retrieved candidates and significantly improves the quality of the generated responses. While rerankers refine the selection of retrieved documents in RAG pipelines, they introduce computational challenges that hinder high throughput and low latency. To address this problem, we propose HyperRAG, a system that optimizes the trade-off between quality and efficiency in RAG pipelines by leveraging KV-cache reuse for efficient reranker inference. By reusing document-side KV-cache, HyperRAG achieves both high-quality generation and system-level efficiency. To fully realize the benefits of KV-cache reuse, HyperRAG incorporates a range of system-level optimizations designed to enhance efficie...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON> Cheng", "<PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2025-04-03", "pdf_url": "http://arxiv.org/pdf/2504.02921v1", "categories": ["cs.CL"]}, "http://arxiv.org/abs/2411.00142v1": {"url": "http://arxiv.org/abs/2411.00142v1", "title": "JudgeRank: Leveraging Large Language Models for Reasoning-Intensive Reranking", "description": "Accurate document retrieval is crucial for the success of retrieval-augmented generation (RAG) applications, including open-domain question answering and code completion. While large language models (LLMs) have been employed as dense encoders or listwise rerankers in RAG systems, they often struggle with reasoning-intensive tasks because they lack nuanced analysis when judging document relevance. To address this limitation, we introduce <PERSON><PERSON><PERSON><PERSON>, a novel agentic reranker that emulates human cognitive processes when assessing document relevance. Our approach consists of three key steps: (1) query analysis to identify the core problem, (2) document analysis to extract a query-aware summary, and (3) relevance judgment to provide a concise assessment of document relevance. We evaluate JudgeRank on the reasoning-intensive BRIGHT benchmark, demonstrating substantial performance improvements over first-stage retrieval methods and outperforming other popular reranking approaches. In additi...", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2024-10-31", "pdf_url": "http://arxiv.org/pdf/2411.00142v1", "categories": ["cs.CL", "cs.AI"]}, "http://arxiv.org/abs/2402.14318v1": {"url": "http://arxiv.org/abs/2402.14318v1", "title": "Assessing generalization capability of text ranking models in Polish", "description": "Retrieval-augmented generation (RAG) is becoming an increasingly popular technique for integrating internal knowledge bases with large language models. In a typical RAG pipeline, three models are used, responsible for the retrieval, reranking, and generation stages. In this article, we focus on the reranking problem for the Polish language, examining the performance of rerankers and comparing their results with available retrieval models. We conduct a comprehensive evaluation of existing models and those trained by us, utilizing a benchmark of 41 diverse information retrieval tasks for the Polish language. The results of our experiments show that most models struggle with out-of-domain generalization. However, a combination of effective optimization method and a large training dataset allows for building rerankers that are both compact in size and capable of generalization. The best of our models establishes a new state-of-the-art for reranking in the Polish language, outperforming ...", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Małgorzata Grębowiec"], "published": "2024-02-22", "pdf_url": "http://arxiv.org/pdf/2402.14318v1", "categories": ["cs.CL"]}, "http://arxiv.org/abs/2504.06276v1": {"url": "http://arxiv.org/abs/2504.06276v1", "title": "Can we repurpose multiple-choice question-answering models to rerank retrieved documents?", "description": "Yes, repurposing multiple-choice question-answering (MCQA) models for document reranking is both feasible and valuable. This preliminary work is founded on mathematical parallels between MCQA decision-making and cross-encoder semantic relevance assessments, leading to the development of R*, a proof-of-concept model that harmonizes these approaches. Designed to assess document relevance with depth and precision, R* showcases how MCQA's principles can improve reranking in information retrieval (IR) and retrieval-augmented generation (RAG) systems -- ultimately enhancing search and dialogue in AI-powered systems. Through experimental validation, R* proves to improve retrieval accuracy and contribute to the field's advancement by demonstrating a practical prototype of MCQA for reranking by keeping it lightweight.", "authors": ["<PERSON>"], "published": "2025-03-06", "pdf_url": "http://arxiv.org/pdf/2504.06276v1", "categories": ["cs.IR"]}, "http://arxiv.org/abs/2504.07439v1": {"url": "http://arxiv.org/abs/2504.07439v1", "title": "LLM4Ranking: An Easy-to-use Framework of Utilizing Large Language Models for Document Reranking", "description": "Utilizing large language models (LLMs) for document reranking has been a popular and promising research direction in recent years, many studies are dedicated to improving the performance and efficiency of using LLMs for reranking. Besides, it can also be applied in many real-world applications, such as search engines or retrieval-augmented generation. In response to the growing demand for research and application in practice, we introduce a unified framework, \\textbf{LLM4Ranking}, which enables users to adopt different ranking methods using open-source or closed-source API-based LLMs. Our framework provides a simple and extensible interface for document reranking with LLMs, as well as easy-to-use evaluation and fine-tuning scripts for this task. We conducted experiments based on this framework and evaluated various models and methods on several widely used datasets, providing reproducibility results on utilizing LLMs for document reranking. Our code is publicly available at https://...", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Weiwei Sun", "<PERSON><PERSON><PERSON>"], "published": "2025-04-10", "pdf_url": "http://arxiv.org/pdf/2504.07439v1", "categories": ["cs.IR", "cs.CL"]}, "http://arxiv.org/abs/2412.08519v1": {"url": "http://arxiv.org/abs/2412.08519v1", "title": "Bridging Relevance and Reasoning: Rationale Distillation in Retrieval-Augmented Generation", "description": "The reranker and generator are two critical components in the Retrieval-Augmented Generation (i.e., RAG) pipeline, responsible for ranking relevant documents and generating responses. However, due to differences in pre-training data and objectives, there is an inevitable gap between the documents ranked as relevant by the reranker and those required by the generator to support answering the query. To address this gap, we propose RADIO, a novel and practical preference alignment framework with RAtionale DIstillatiOn. Specifically, We first propose a rationale extraction method that leverages the reasoning capabilities of Large Language Models (LLMs) to extract the rationales necessary for answering the query. Subsequently, a rationale-based alignment process is designed to rerank the documents based on the extracted rationales, and fine-tune the reranker to align the preferences. We conduct extensive experiments on two tasks across three datasets to demonstrate the effectiveness of o...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhaocheng Du", "Xiangyang Li", "Xiangyu Zhao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON> Guo", "<PERSON><PERSON><PERSON>"], "published": "2024-12-11", "pdf_url": "http://arxiv.org/pdf/2412.08519v1", "categories": ["cs.CL"]}, "http://arxiv.org/abs/2410.10315v2": {"url": "http://arxiv.org/abs/2410.10315v2", "title": "EasyRAG: Efficient Retrieval-Augmented Generation Framework for Automated Network Operations", "description": "This paper presents EasyRAG, a simple, lightweight, and efficient retrieval-augmented generation framework for automated network operations. Our framework has three advantages. The first is accurate question answering. We designed a straightforward RAG scheme based on (1) a specific data processing workflow (2) dual-route sparse retrieval for coarse ranking (3) LLM Reranker for reranking (4) LLM answer generation and optimization. This approach achieved first place in the GLM4 track in the preliminary round and second place in the GLM4 track in the semifinals. The second is simple deployment. Our method primarily consists of BM25 retrieval and BGE-reranker reranking, requiring no fine-tuning of any models, occupying minimal VRAM, easy to deploy, and highly scalable; we provide a flexible code library with various search and generation strategies, facilitating custom process implementation. The last one is efficient inference. We designed an efficient inference acceleration scheme fo...", "authors": ["<PERSON><PERSON>", "Dongdong Kuang", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2024-10-14", "pdf_url": "http://arxiv.org/pdf/2410.10315v2", "categories": ["cs.CL", "cs.AI"]}, "http://arxiv.org/abs/2502.02464v3": {"url": "http://arxiv.org/abs/2502.02464v3", "title": "Rankify: A Comprehensive Python Toolkit for Retrieval, Re-Ranking, and Retrieval-Augmented Generation", "description": "Retrieval, re-ranking, and retrieval-augmented generation (RAG) are critical components of modern applications in information retrieval, question answering, or knowledge-based text generation. However, existing solutions are often fragmented, lacking a unified framework that easily integrates these essential processes. The absence of a standardized implementation, coupled with the complexity of retrieval and re-ranking workflows, makes it challenging for researchers to compare and evaluate different approaches in a consistent environment. While existing toolkits such as Rerankers and RankLLM provide general-purpose reranking pipelines, they often lack the flexibility required for fine-grained experimentation and benchmarking. In response to these challenges, we introduce Rankify, a powerful and modular open-source toolkit designed to unify retrieval, re-ranking, and RAG within a cohesive framework. Rankify supports a wide range of retrieval techniques, including dense and sparse ret...", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "published": "2025-02-04", "pdf_url": "http://arxiv.org/pdf/2502.02464v3", "categories": ["cs.IR", "cs.CL"]}, "http://arxiv.org/abs/2411.08724v1": {"url": "http://arxiv.org/abs/2411.08724v1", "title": "QCG-Rerank: Chunks Graph Rerank with Query Expansion in Retrieval-Augmented LLMs for Tourism Domain", "description": "Retrieval-Augmented Generation (RAG) mitigates the issue of hallucination in Large Language Models (LLMs) by integrating information retrieval techniques. However, in the tourism domain, since the query is usually brief and the content in the database is diverse, existing RAG may contain a significant amount of irrelevant or contradictory information contents after retrieval. To address this challenge, we propose the QCG-Rerank model. This model first performs an initial retrieval to obtain candidate chunks and then enhances semantics by extracting critical information to expand the original query. Next, we utilize the expanded query and candidate chunks to calculate similarity scores as the initial transition probability and construct the chunks graph. Subsequently, We iteratively compute the transition probabilities based on an initial estimate until convergence. The chunks with the highest score are selected and input into the LLMs to generate responses. We evaluate the model on ...", "authors": ["<PERSON><PERSON> Wei", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "published": "2024-11-04", "pdf_url": "http://arxiv.org/pdf/2411.08724v1", "categories": ["cs.CL", "cs.AI"]}}