{"cells": [{"cell_type": "markdown", "metadata": {"id": "XJ8Zzvm-QOkp", "outputId": "5c5ec834-62d3-45bb-b4b5-0e8d72acf5ec"}, "source": ["## Fine-Tuning with <PERSON><PERSON>ce Transformers\n", "In this notebook, we will learn about `sentence_transformers` library and then we will fine-tune a `bert-base-uncased` model with 2 different type of datasets:\n", "- Triplets\n", "- Sentence pairs with labels (SNLI)\n", "\n", "Let's first install the required dependencies and modules"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Aj8CcSn3aFes", "outputId": "04fd17e1-8f12-4f7c-80fa-9bd83c37623b"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m166.4/166.4 kB\u001b[0m \u001b[31m2.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m3.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m24.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m21.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m16.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m2.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m11.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m6.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m87.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "gcsfs 2025.3.2 requires fsspec==2025.3.2, but you have fsspec 2023.10.0 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0m"]}], "source": ["!pip install sentence-transformers datasets scikit-learn fsspec==2023.10.0 huggingface_hub -q"]}, {"cell_type": "markdown", "metadata": {"id": "0GKouiZIaFet"}, "source": ["We will use `all-MiniLM-L6-v2` to play around with sentence transformer features"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 528, "referenced_widgets": ["e9451d680c1243409290f6666d88c109", "0ddaa7892f5046b1a7af5dfdeba92170", "5a9caab607944d73adefaca0c329d93e", "76b07dcb8c27407693b36e28b19a7b34", "e8b1d74a6fd04bc99233bbd13766eae0", "907790c5ea5e4ff387d2f5fee5db101f", "43e10feb51124cb698961dfce19f2d63", "45420a5e75194812b0d8daf0ea522f69", "7a9ec427573e444095fc0989117de618", "e4db00b31bed49f6ac4ff3d31ee70f73", "4a5167fe1af74d93b3c75509774bd069", "b24bc5dcf25a40258634354a330ab782", "c764daffea824d028e35c1e9548741ef", "5bf431aaf3494b54a807825cedcb1b3e", "92a9d9985db049a3b1a73ac61267d506", "5dd4971b1bec48129fc24e61f5ab8d4a", "8eff86a7d7354631877161ca1f6226a4", "d087f1ec5e684bc58c018c2bbd6b7c65", "8b6da954bf974f6984d5b2b2b21eed07", "5b652cc549f64eefb5582f2cfdb5c251", "d40f1445fd0a4be4a9267a9399058b78", "93b4d77d1fd34cc89b340f207e518298", "fa1c64667b7342458bf348887eded7fb", "ee69f373f85141409eb43d130ee6e2d4", "9a520df216454c3bb0bb3168f2732fac", "6447c76dd58b46bc9c1be1247e966436", "e2e053d7393944dd92e54ddab295b295", "3f36062dbff74dd699af1435d40601e3", "80529e1375014c26a80e76ade512af1e", "a835d63344d945e0a3b6c4dc1027079d", "422cefdddc0d44b28446edde8bfe6c74", "3cef91f8e9ce476e9821524f13141a7d", "ce73fc8ce7f74511a2e833e67956c001", "fe14b81c14cf4f388824115e32c965b6", "b155168753f542ffb9b4eb5377e5f847", "0159f659caed4314b656795a8cc04d5d", "c5aba18b5fb64460a7bde397776c0237", "1d868a1ed88446f09d11096f51dab947", "5788bdff7e224d6190d472f575f6d4ae", "9652028c49fd49faad4d559971cd8f9c", "d9578b2e67a34c8494176c405d098f7c", "41231992eecd47e6b5c3d2940fc76840", "b546097337694dda8b20e2a54a627500", "cced4ed0166742b0bf9ee5efca31ef80", "ebfdf2f2d1754d0a8f507bb5fcab5419", "0b6f7b1c6f9c4fe0a38d6a6df8ae2caa", "24202108248b4fa48973fb490b80fc3a", "252edbe474bf4634872cad72f4941d59", "693d9d79a4b64bcc9ca6a4f60ff212da", "185ea724dde24b9c8ef8557d6129903e", "e040ad56899d4e6e9a5747099f0c2324", "a5aea53d741d4774b5b53e635918e400", "244f77c77a7e4086ae6ea16d14b70459", "07cbd50e37664779b35744058ce6d43e", "60b8c305480849c4bfed0502dbcab9a1", "50395c70e2e0472bac70801c24187354", "960320a4521e45418aa9345835983678", "7fc05dd9967a48a0a65a240962e852ee", "cfde7e7f488b4526aeb598455848a39d", "2b9cef22034c4c4796016dbeb3d74e20", "b4269a44cf894b60904f40be5acc309d", "5ce7264b8bb940259097d627855f78ca", "1da59909abea42fda512ac0de9ed886b", "017722a85da9449cbd00883ac0e8b361", "7cfba6efb52c462b96288f282fffcd46", "858315cc3a054bc3b78c27ab43d098b3", "f8dbbef33cd4400696c3b313a02f8cc9", "a7521cfb4bdb4b4a90de1dd94dad4212", "b39df2eb5e49478d81d98b4f9275797d", "7bdf4cd578204d54966776289af909ca", "119a7f6784d8467caaa81909ee69b0ed", "6c9229f5b7ee41a5a9518b309b49b3c8", "3736deb780654082a1770734de624376", "c04106dd69284604836728cf18560dc3", "b3c4477a81d44c1f9aacbff038d15c22", "e6135039ead145a7aa877c1ad98ae415", "823094962afa4cb5b15325cea704d6c3", "cbcd8ceb74014435acd8d38b92523401", "64be7a4121844caa8a678b61486c04b6", "28ca95515be545e3b4d6ac6d38322e07", "855b13677c90419bbf4ccafd6d65fad0", "2db57da9a47d4b3fbb2ef2cd78e465eb", "ba8941b16628445dacf42bc67611dcc6", "209c6aea6fc44777ae575d3395c9d3ac", "ff672cfe38c24bd2bcbc000f19bc8dcb", "9caccaa9a5aa42be86e1ca4042b28d13", "374cf30f14454c23bd77699c0a453ab0", "d9b3d940f5924144b33670111e7480a4", "546dd878b972440196d946114b64b738", "3b7010ec884f4995b4ea42c42b89d135", "1f72e988d2724ac0919e3700cb55849a", "2f69c775bd34472caf395056367e7cd5", "611c4e0cf8fb4f6f8ad82462a8bf1a35", "588b91319fde4044835528a94bb6c791", "b66b87eef31442429e446feefec656ea", "182ca926974f4031b9b04ab809ecbfee", "96fbc3915d6f485c8868769c8ee31fc1", "7a5f9d671f0d47cea4aa5564dd526e24", "9ab53de6d503476fa68d6e9844998818", "a9987ab38da34b20aebb91608d7cd2db", "92f46d85a65e4d71b776ae5b2d89af55", "289e984f642d4b5bbf5d9280fa6a3d04", "114367b385804397a664688f464be1a4", "dacaddae349b40329881cb45bb51c89f", "24fe7e32edf14a0facb9da87e0ea9a75", "33fba48158584e0399894f78968702c4", "60f8d2a7971649a991b9332f47e6b5ed", "b61adee1c04448deb94e0717f39fadbf", "3cedea4b5dd84366af266f2f650facfa", "bbf172c075904224b9737a0c432da204", "3b0733de13234274860e098c232aaa80", "197dce071e214777b650e0e08b13d913", "d1311e527c904412ad78dfe815d88dd9", "6aeb2ad17dce480aac8c070e5ff53488", "d7ce8f8bd1894ef78c70870c83d8bdcc", "070fbb01cdc94656b2f63dc94cdfe556", "0d6a26f7796c4e0e8366d6184a179dd3", "5f19a6c417024b57bbd1538062d9a96f", "cbec3f9e41ab403f99ad955a9fa58237", "56edaec14f4f4289a13e9c5490ea5d75", "c38efa55d22047e5b206ce0d8ddbd8fd"]}, "id": "jvqGebWEQjIX", "outputId": "76d654bd-c8c0-46b0-8a09-cea9dd5d96fc"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/huggingface_hub/utils/_auth.py:94: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}, {"output_type": "display_data", "data": {"text/plain": ["modules.json:   0%|          | 0.00/349 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "e9451d680c1243409290f6666d88c109"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config_sentence_transformers.json:   0%|          | 0.00/116 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "b24bc5dcf25a40258634354a330ab782"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["README.md:   0%|          | 0.00/10.5k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "fa1c64667b7342458bf348887eded7fb"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["sentence_bert_config.json:   0%|          | 0.00/53.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "fe14b81c14cf4f388824115e32c965b6"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/612 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ebfdf2f2d1754d0a8f507bb5fcab5419"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`\n", "WARNING:huggingface_hub.file_download:Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`\n"]}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/90.9M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "50395c70e2e0472bac70801c24187354"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/350 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "f8dbbef33cd4400696c3b313a02f8cc9"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "cbcd8ceb74014435acd8d38b92523401"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/466k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "546dd878b972440196d946114b64b738"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/112 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "a9987ab38da34b20aebb91608d7cd2db"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/190 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "3b0733de13234274860e098c232aaa80"}}, "metadata": {}}], "source": ["# model\n", "from sentence_transformers import SentenceTransformer\n", "\n", "model = SentenceTransformer(\"all-MiniLM-L6-v2\")"]}, {"cell_type": "markdown", "metadata": {"id": "i34k0VGDaFeu"}, "source": ["### Converting text to embeddings\n", "Let’s first try converting a given text into embeddings. You might also have used openai embeddings to get the embeddings of a given text but it charges money to use their embedding model so as an alternative you can use models from huggingface or any other opensource embedding model with sentence transformers to generate vector embeddings for your sentence.\n", "\n", "Now we will define the sentences for which we want to generate the embeddings in an array and then we can use `encode` method from our model to generate embeddings."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "EnGdeMJksBng", "outputId": "9b45d14f-43c0-4b6c-cf19-cc56794a3dd4"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Sentence: This framework generates embeddings for each input sentence\n", "Embedding: [-1.37173440e-02 -4.28515524e-02 -1.56286098e-02  1.40537424e-02\n", "  3.95538136e-02  1.21796273e-01  2.94333436e-02 -3.17523964e-02\n", "  3.54959816e-02 -7.93140158e-02  1.75878443e-02 -4.04369906e-02\n", "  4.97259386e-02  2.54911911e-02 -7.18700886e-02  8.14969018e-02\n", "  1.47071364e-03  4.79626842e-02 -4.50336263e-02 -9.92174745e-02\n", " -2.81770118e-02  6.45046607e-02  4.44670692e-02 -4.76217195e-02\n", " -3.52952518e-02  4.38671783e-02 -5.28566279e-02  4.33054753e-04\n", "  1.01921476e-01  1.64072346e-02  3.26996781e-02 -3.45987007e-02\n", "  1.21339252e-02  7.94871002e-02  4.58342349e-03  1.57778263e-02\n", " -9.68209095e-03  2.87626106e-02 -5.05806133e-02 -1.55793801e-02\n", " -2.87906900e-02 -9.62282531e-03  3.15556824e-02  2.27349177e-02\n", "  8.71449634e-02 -3.85027453e-02 -8.84718448e-02 -8.75500590e-03\n", " -2.12342944e-02  2.08923575e-02 -9.02077258e-02 -5.25732450e-02\n", " -1.05638755e-02  2.88310610e-02 -1.61455031e-02  6.17836649e-03\n", " -1.23234708e-02 -1.07337050e-02  2.83353962e-02 -5.28567843e-02\n", " -3.58617865e-02 -5.97989410e-02 -1.09055256e-02  2.91566234e-02\n", "  7.97979385e-02 -3.27880494e-04  6.83500059e-03  1.32718273e-02\n", " -4.24620137e-02  1.87656935e-02 -9.89234596e-02  2.09049955e-02\n", " -8.69606137e-02 -1.50151886e-02 -4.86202240e-02  8.04414973e-02\n", " -3.67700402e-03 -6.65044412e-02  1.14556789e-01 -3.04228794e-02\n", "  2.96631828e-02 -2.80695353e-02  4.64989841e-02 -2.25513540e-02\n", "  8.54223073e-02  3.15446407e-02  7.34541938e-02 -2.21862085e-02\n", " -5.29678427e-02  1.27129974e-02 -5.27339689e-02 -1.06188729e-01\n", "  7.04731643e-02  2.76736394e-02 -8.05531591e-02  2.39649694e-02\n", " -2.65125595e-02 -2.17331033e-02  4.35275622e-02  4.84712049e-02\n", " -2.37067286e-02  2.85768490e-02  1.11846134e-01 -6.34936169e-02\n", " -1.58318300e-02 -2.26169694e-02 -1.31027680e-02 -1.62067194e-03\n", " -3.60929184e-02 -9.78297144e-02 -4.67729159e-02  1.76271722e-02\n", " -3.97492163e-02 -1.76379530e-04  3.39627825e-02 -2.09633671e-02\n", "  6.33660704e-03 -2.59410869e-02  8.10410604e-02  6.14393651e-02\n", " -5.44595532e-03  6.48276433e-02 -1.16844051e-01  2.36860868e-02\n", " -1.32058999e-02 -1.12476468e-01  1.90049335e-02 -1.74661551e-34\n", "  5.58949746e-02  1.94244441e-02  4.65438850e-02  5.18645793e-02\n", "  3.89390998e-02  3.40541191e-02 -4.32114303e-02  7.90637285e-02\n", " -9.79530066e-02 -1.27441343e-02 -2.91870758e-02  1.02052093e-02\n", "  1.88115835e-02  1.08942486e-01  6.63465187e-02 -5.35295084e-02\n", " -3.29228789e-02  4.69827056e-02  2.28882935e-02  2.74115037e-02\n", " -2.91982908e-02  3.12706642e-02 -2.22850554e-02 -1.02282189e-01\n", " -2.79116705e-02  1.13793109e-02  9.06308964e-02 -4.75414544e-02\n", " -1.00718945e-01 -1.23232147e-02 -7.96928927e-02 -1.44636258e-02\n", " -7.76400939e-02 -7.66921137e-03  9.73956194e-03  2.24204883e-02\n", "  7.77268335e-02 -3.17156594e-03  2.11538095e-02 -3.30393985e-02\n", "  9.55248438e-03 -3.73011567e-02  2.61360798e-02 -9.79086850e-03\n", " -6.31505027e-02  5.77435270e-03 -3.80031466e-02  1.29684685e-02\n", " -1.82499327e-02 -1.56282950e-02 -1.23362220e-03  5.55579104e-02\n", "  1.13088237e-04 -5.61256669e-02  7.40165561e-02  1.84452012e-02\n", " -2.66368520e-02  1.31951859e-02  7.50086531e-02 -2.46797018e-02\n", " -3.24005969e-02 -1.57674961e-02 -8.03516805e-03 -5.61318453e-03\n", "  1.05687948e-02  3.26163415e-03 -3.91990133e-02 -9.38676819e-02\n", "  1.14227213e-01  6.57304525e-02 -4.72633429e-02  1.45087345e-02\n", " -3.54490429e-02 -3.37761603e-02 -5.15506007e-02 -3.81005509e-03\n", " -5.15036359e-02 -5.93429171e-02 -1.69413816e-03  7.42107853e-02\n", " -4.20091413e-02 -7.19975084e-02  3.17250304e-02 -1.66304037e-02\n", "  3.96982254e-03 -6.52750805e-02  2.77391169e-02 -7.51648918e-02\n", "  2.27456037e-02 -3.91368158e-02  1.54315867e-02 -5.54908849e-02\n", "  1.23318359e-02 -2.59520616e-02  6.66423514e-02 -6.91258866e-34\n", "  3.31628770e-02  8.47928822e-02 -6.65583834e-02  3.33541296e-02\n", "  4.71610995e-03  1.35362437e-02 -5.38694337e-02  9.20694172e-02\n", " -2.96876840e-02  3.16219628e-02 -2.37497389e-02  1.98771041e-02\n", "  1.03446215e-01 -9.06947628e-02  6.30628085e-03  1.42886229e-02\n", "  1.19293975e-02  6.43726019e-03  4.20104638e-02  1.25344666e-02\n", "  3.93019356e-02  5.35691865e-02 -4.30749841e-02  6.10432364e-02\n", " -5.39716166e-05  6.91682771e-02  1.05520552e-02  1.22111877e-02\n", " -7.23185241e-02  2.50469185e-02 -5.18371277e-02 -4.36562300e-02\n", " -6.71818778e-02  1.34828547e-02 -7.25889131e-02  7.04167411e-03\n", "  6.58939332e-02  1.08994646e-02 -2.60010269e-03  5.49968928e-02\n", "  5.06966747e-02  3.27948816e-02 -6.68833181e-02  6.45557866e-02\n", " -2.52076052e-02 -2.92572118e-02 -1.16696730e-01  3.24064307e-02\n", "  5.85858636e-02 -3.51756513e-02 -7.15240166e-02  2.24936157e-02\n", " -1.00786723e-01 -4.74544913e-02 -7.61963055e-02 -5.87166548e-02\n", "  4.21138406e-02 -7.47213885e-02  1.98467933e-02 -3.36500932e-03\n", " -5.29736578e-02  2.74729691e-02  3.45737003e-02 -6.11846894e-02\n", "  1.06364816e-01 -9.64119881e-02 -4.55944650e-02  1.51489899e-02\n", " -5.13527263e-03 -6.64447621e-02  4.31721844e-02 -1.10405609e-02\n", " -9.80250444e-03  7.53783211e-02 -1.49571020e-02 -4.80208434e-02\n", "  5.80726750e-02 -2.43896712e-02 -2.23137867e-02 -4.36992608e-02\n", "  5.12054078e-02 -3.28625888e-02  1.08763300e-01  6.08926676e-02\n", "  3.30792763e-03  5.53819910e-02  8.43201205e-02  1.27087394e-02\n", "  3.84465531e-02  6.52325526e-02 -2.94684023e-02  5.08005172e-02\n", " -2.09348202e-02  1.46135703e-01  2.25561336e-02 -1.77227761e-08\n", " -5.02672270e-02 -2.79201166e-04 -1.00328647e-01  2.42811944e-02\n", " -7.54043385e-02 -3.79140005e-02  3.96049842e-02  3.10079604e-02\n", " -9.05702822e-03 -6.50412068e-02  4.05452736e-02  4.83390577e-02\n", " -4.56962325e-02  4.76007583e-03  2.64358288e-03  9.35614184e-02\n", " -4.02599499e-02  3.27401869e-02  1.18298503e-02  5.54344989e-02\n", "  1.48052230e-01  7.21189231e-02  2.76996929e-04  1.68651212e-02\n", "  8.34881980e-03 -8.76154006e-03 -1.33649549e-02  6.14237189e-02\n", "  1.57168284e-02  6.94960952e-02  1.08621893e-02  6.08018301e-02\n", " -5.33421934e-02 -3.47924344e-02 -3.36272232e-02  6.93907067e-02\n", "  1.22987544e-02 -1.45237431e-01 -2.06969539e-03 -4.61132862e-02\n", "  3.72745027e-03 -5.59356390e-03 -1.00659817e-01 -4.45953384e-02\n", "  5.40921465e-02  4.98890551e-03  1.49534149e-02 -8.26060027e-02\n", "  6.26630783e-02 -5.01908781e-03 -4.81857918e-02 -3.53991501e-02\n", "  9.03389417e-03 -2.42338106e-02  5.66267148e-02  2.51529068e-02\n", " -1.70709081e-02 -1.24780722e-02  3.19518484e-02  1.38421021e-02\n", " -1.55815035e-02  1.00178264e-01  1.23657219e-01 -4.22967151e-02]\n", "\n", "Sentence: Sentences are passed as a list of string.\n", "Embedding: [ 5.64524941e-02  5.50024323e-02  3.13796066e-02  3.39485221e-02\n", " -3.54247317e-02  8.34667981e-02  9.88801047e-02  7.27547612e-03\n", " -6.68655103e-03 -7.65808066e-03  7.93738589e-02  7.39673560e-04\n", "  1.49292145e-02 -1.51047269e-02  3.67674157e-02  4.78743501e-02\n", " -4.81969640e-02 -3.76052447e-02 -4.60278578e-02 -8.89815986e-02\n", "  1.20228112e-01  1.30663246e-01 -3.73935960e-02  2.47857021e-03\n", "  2.55826325e-03  7.25814700e-02 -6.80436715e-02 -5.24696000e-02\n", "  4.90234233e-02  2.99563054e-02 -5.84429316e-02 -2.02263203e-02\n", "  2.08822247e-02  9.76692066e-02  3.52390185e-02  3.91140617e-02\n", "  1.05667710e-02  1.56232750e-03 -1.30822835e-02  8.52901116e-03\n", " -4.84095700e-03 -2.03766599e-02 -2.71800570e-02  2.83307787e-02\n", "  3.66017707e-02  2.51276139e-02 -9.90862176e-02  1.15626790e-02\n", " -3.60380001e-02 -7.23783970e-02 -1.12670109e-01  1.12942262e-02\n", " -3.86397541e-02  4.67386544e-02 -2.88460553e-02  2.26703994e-02\n", " -8.52408260e-03  3.32815014e-02 -1.06580171e-03 -7.09745437e-02\n", " -6.31170049e-02 -5.72187006e-02 -6.16026297e-02  5.47146276e-02\n", "  1.18318116e-02 -4.66261171e-02  2.56959777e-02 -7.07413908e-03\n", " -5.73842973e-02  4.12839465e-02 -5.91503382e-02  5.89021817e-02\n", " -4.41697463e-02  4.65081409e-02 -3.15814428e-02  5.58312461e-02\n", "  5.54578453e-02 -5.96533604e-02  4.06407602e-02  4.83762147e-03\n", " -4.96768393e-02 -1.00944325e-01  3.40077989e-02  4.13274020e-03\n", " -2.93523306e-03  2.11837478e-02 -3.73962186e-02 -2.79066861e-02\n", " -4.61767912e-02  5.26138358e-02 -2.79734768e-02 -1.62379235e-01\n", "  6.61041960e-02  1.72274373e-02 -5.45111392e-03  4.74473760e-02\n", " -3.82237360e-02 -3.96896824e-02  1.34544764e-02  4.49654087e-02\n", "  4.53673117e-03  2.82978825e-02  8.36633146e-02 -1.00858212e-02\n", " -1.19353987e-01 -3.84624228e-02  4.82858382e-02 -9.46083814e-02\n", "  1.91853922e-02 -9.96518135e-02 -6.30596876e-02  3.02695949e-02\n", "  1.17402570e-02 -4.78371941e-02 -6.20267820e-03 -3.32850255e-02\n", " -4.04387154e-03  1.28306951e-02  4.05254290e-02  7.56476447e-02\n", "  2.92434841e-02  2.84270886e-02 -2.78938897e-02  1.66857820e-02\n", " -2.47961972e-02 -6.83650896e-02  2.89969090e-02 -5.39867674e-33\n", " -2.69015925e-03 -2.65069436e-02 -6.47954585e-04 -8.46192706e-03\n", " -7.35154748e-02  4.94084414e-03 -5.97842075e-02  1.03438292e-02\n", "  2.12902902e-03 -2.88216444e-03 -3.17076519e-02 -9.42363963e-02\n", "  3.03019937e-02  7.00226650e-02  4.50685993e-02  3.69439311e-02\n", "  1.13593377e-02  3.53026800e-02  5.50449174e-03  1.34416239e-03\n", "  3.46120563e-03  7.75047839e-02  5.45112826e-02 -7.92055875e-02\n", " -9.31696743e-02 -4.03398387e-02  3.10668852e-02 -3.83081399e-02\n", " -5.89443184e-02  1.93331931e-02 -2.67160013e-02 -7.91938156e-02\n", "  1.04181760e-04  7.70621076e-02  4.16603349e-02  8.90932083e-02\n", "  3.56843360e-02 -1.09153222e-02  3.71498540e-02 -2.07070429e-02\n", " -2.46101301e-02 -2.05025356e-02  2.62201577e-02  3.43590602e-02\n", "  4.39251252e-02 -8.20521079e-03 -8.40710104e-02  4.24171053e-02\n", "  4.87498939e-02  5.95385171e-02  2.87747644e-02  3.37638147e-02\n", " -4.07442749e-02 -1.66374084e-03  7.91927502e-02  3.41088772e-02\n", " -5.72817109e-04  1.87749825e-02 -1.36964023e-02  7.38332942e-02\n", "  5.74538135e-04  8.33505318e-02  5.60811087e-02 -1.13710966e-02\n", "  4.42611761e-02  2.69581769e-02 -4.80536036e-02 -3.15087251e-02\n", "  7.75226131e-02  1.81773212e-02 -8.83005038e-02 -7.85518810e-03\n", " -6.22243173e-02  7.19373077e-02 -2.33475007e-02  6.52483944e-03\n", " -9.49526206e-03 -9.88312960e-02  4.01306339e-02  3.07397135e-02\n", " -2.21607480e-02 -9.45911482e-02  1.02367904e-02  1.02187753e-01\n", " -4.12960537e-02 -3.15778330e-02  4.74752076e-02 -1.10209733e-01\n", "  1.69614907e-02 -3.71709205e-02 -1.03261862e-02 -4.72538173e-02\n", " -1.20214038e-02 -1.93255320e-02  5.79292439e-02  4.23864988e-34\n", "  3.92013006e-02  8.41361657e-02 -1.02946743e-01  6.92259744e-02\n", "  1.68821327e-02 -3.26760784e-02  9.65959765e-03  1.80899873e-02\n", "  2.17939783e-02  1.63189359e-02 -9.69292223e-02  3.74851399e-03\n", " -2.38456838e-02 -3.44055705e-02  7.11962953e-02  9.21934086e-04\n", " -6.23854529e-03  3.23754065e-02 -8.90380237e-04  5.01908781e-03\n", " -4.24538031e-02  9.89084095e-02 -4.60321121e-02  4.69704643e-02\n", " -1.75284501e-02 -7.02515943e-03  1.32743660e-02 -5.30152209e-02\n", "  2.66403449e-03  1.45818805e-02  7.43346568e-03 -3.07131782e-02\n", " -2.09416393e-02  8.24110284e-02 -5.15893921e-02 -2.71178316e-02\n", "  1.17582992e-01  7.72500131e-03 -1.89523362e-02  3.94558981e-02\n", "  7.17360303e-02  2.59116702e-02  2.75191627e-02  9.50544327e-03\n", " -3.02355383e-02 -4.07944806e-02 -1.04028486e-01 -7.97421765e-03\n", " -3.64456978e-03  3.29716243e-02 -2.35954784e-02 -7.50511885e-03\n", " -5.82234003e-02 -3.17905955e-02 -4.18049321e-02  2.17453744e-02\n", " -6.67291731e-02 -4.89104539e-02  4.58514597e-03 -2.66046580e-02\n", " -1.12597018e-01  5.11167534e-02  5.48534170e-02 -6.69857115e-02\n", "  1.26766294e-01 -8.59487653e-02 -5.94231524e-02 -2.92187789e-03\n", " -1.14876162e-02 -1.26025796e-01 -3.48281278e-03 -9.12001580e-02\n", " -1.22933045e-01  1.33777121e-02 -4.75775003e-02 -6.57932907e-02\n", " -3.39410082e-02 -3.07107288e-02 -5.22034019e-02 -2.35464200e-02\n", "  5.90035059e-02 -3.85757945e-02  3.19701284e-02  4.05118577e-02\n", "  1.67077668e-02 -3.58281061e-02  1.45687936e-02  3.20137739e-02\n", " -1.34843625e-02  6.07819520e-02 -8.31399579e-03 -1.08105848e-02\n", "  4.69410606e-02  7.66133666e-02 -4.23400216e-02 -2.11963318e-08\n", " -7.25292489e-02 -4.20227833e-02 -6.12374581e-02  5.24666719e-02\n", " -1.42363543e-02  1.18486676e-02 -1.40789123e-02 -3.67529877e-02\n", " -4.44977768e-02 -1.15140220e-02  5.23316674e-02  2.96652205e-02\n", " -4.62780409e-02 -3.70892547e-02  1.89129505e-02  2.04306711e-02\n", " -2.24006046e-02 -1.48562649e-02 -1.79504137e-02  4.20007259e-02\n", "  1.40942754e-02 -2.83492506e-02 -1.16863012e-01  1.48956543e-02\n", " -7.30568776e-04  5.66028021e-02 -2.68740058e-02  1.09106652e-01\n", "  2.94563174e-03  1.19267881e-01  1.14212461e-01  8.92973915e-02\n", " -1.70255657e-02 -4.99053970e-02 -2.11930759e-02  3.18421796e-02\n", "  7.03436211e-02 -1.02929428e-01  8.23816657e-02  2.81968080e-02\n", "  3.21146138e-02  3.79107930e-02 -1.09553099e-01  8.19620118e-02\n", "  8.73216689e-02 -5.73563911e-02 -2.01709215e-02 -5.69444522e-02\n", " -1.30338436e-02 -5.55684455e-02 -1.32966004e-02  8.64010397e-03\n", "  5.30012399e-02 -4.06847112e-02  2.71708947e-02 -2.55949376e-03\n", "  3.05775888e-02 -4.61865403e-02  4.68034577e-03 -3.64947133e-02\n", "  6.80802763e-02  6.65087551e-02  8.49151835e-02 -3.32849100e-02]\n", "\n", "Sentence: The quick brown fox jumps over the lazy dog.\n", "Embedding: [ 4.39335592e-02  5.89344129e-02  4.81783748e-02  7.75481015e-02\n", "  2.67444067e-02 -3.76295745e-02 -2.60509434e-03 -5.99430837e-02\n", " -2.49605766e-03  2.20728014e-02  4.80259471e-02  5.57553358e-02\n", " -3.89454216e-02 -2.66167857e-02  7.69341178e-03 -2.62376685e-02\n", " -3.64160948e-02 -3.78161371e-02  7.40781501e-02 -4.95050699e-02\n", " -5.85217252e-02 -6.36196584e-02  3.24349962e-02  2.20085271e-02\n", " -7.10636899e-02 -3.31577621e-02 -6.94104284e-02 -5.00374064e-02\n", "  7.46267959e-02 -1.11133851e-01 -1.23062804e-02  3.77456732e-02\n", " -2.80313473e-02  1.45353302e-02 -3.15585658e-02 -8.05836320e-02\n", "  5.83525561e-02  2.59005558e-03  3.92802171e-02  2.57695690e-02\n", "  4.98505831e-02 -1.75624003e-03 -4.55298238e-02  2.92607639e-02\n", " -1.02017239e-01  5.22287376e-02 -7.90899470e-02 -1.02857035e-02\n", "  9.20249149e-03  1.30732451e-02 -4.04777899e-02 -2.77925450e-02\n", "  1.24667073e-02  6.72833249e-02  6.81248084e-02 -7.57116126e-03\n", " -6.09937636e-03 -4.23777103e-02  5.17815836e-02 -1.56707484e-02\n", "  9.56360251e-03  4.12390307e-02  2.14959048e-02  1.04293320e-02\n", "  2.73349863e-02  1.87062342e-02 -2.69607641e-02 -7.00542107e-02\n", " -1.04700506e-01 -1.89873111e-03  1.77016538e-02 -5.74725345e-02\n", " -1.44223738e-02  4.70466010e-04  2.33232859e-03 -2.51919944e-02\n", "  4.93004136e-02 -5.09610139e-02  6.31983057e-02  1.49164777e-02\n", " -2.70766709e-02 -4.52875830e-02 -4.90594730e-02  3.74939963e-02\n", "  3.84579971e-02  1.56899495e-03  3.09922565e-02  2.01630890e-02\n", " -1.24363247e-02 -3.06720063e-02 -2.78819501e-02 -6.89181909e-02\n", " -5.13677597e-02  2.14795750e-02  1.15746846e-02  1.25414587e-03\n", "  1.88765787e-02 -4.42318507e-02 -4.49817777e-02 -3.41872731e-03\n", "  1.31131392e-02  2.00098958e-02  1.21099755e-01  2.31074989e-02\n", " -2.20159609e-02 -3.28846648e-02 -3.15514230e-03  1.17860669e-04\n", "  9.91498977e-02  1.65239144e-02 -4.69672680e-03 -1.45366555e-02\n", " -3.71080288e-03  9.65135992e-02  2.85908189e-02  2.13481709e-02\n", " -7.17645288e-02 -2.41142008e-02 -4.40940596e-02 -1.07346900e-01\n", "  6.79946095e-02  1.30466774e-01 -7.97029957e-02  6.79507945e-03\n", " -2.37511843e-02 -4.61636297e-02 -2.99650710e-02 -3.69410083e-33\n", "  7.30969533e-02 -2.20171548e-02 -8.61464441e-02 -7.14379027e-02\n", " -6.36741668e-02 -7.21863136e-02 -5.93042094e-03 -2.33641006e-02\n", " -2.83657853e-02  4.77434583e-02 -8.06176364e-02 -1.56475790e-03\n", "  1.38443680e-02 -2.86235884e-02 -3.35386880e-02 -1.13777533e-01\n", " -9.17632226e-03 -1.08101116e-02  3.23195681e-02  5.88380657e-02\n", "  3.34209017e-02  1.07987978e-01 -3.72713357e-02 -2.96770614e-02\n", "  5.17190211e-02 -2.25338973e-02 -6.96090460e-02 -2.14474816e-02\n", " -2.33410746e-02  4.82199527e-02 -3.58766131e-02 -4.68990989e-02\n", " -3.97873446e-02  1.10813260e-01 -1.43006677e-02 -1.18464522e-01\n", "  5.82915209e-02 -6.25889450e-02 -2.94040926e-02  6.03238232e-02\n", " -2.44414923e-03  1.60115995e-02  2.67233886e-02  2.49530431e-02\n", " -6.49318844e-02 -1.06802071e-02  2.81464998e-02  1.03564011e-02\n", " -6.63574960e-04  1.98186096e-02 -3.04288827e-02  6.28422108e-03\n", "  5.15268072e-02 -4.75374833e-02 -6.44421130e-02  9.55031887e-02\n", "  7.55858198e-02 -2.81574670e-02 -3.49965841e-02  1.01816386e-01\n", "  1.98732316e-02 -3.68036404e-02  2.93523679e-03 -5.00745326e-02\n", "  1.50932133e-01 -6.16079345e-02 -8.58812705e-02  7.13988161e-03\n", " -1.33065498e-02  7.80404955e-02  1.75250787e-02  4.21279967e-02\n", "  3.57939452e-02 -1.32950410e-01  3.56970318e-02 -2.03116834e-02\n", "  1.24909664e-02 -3.80355380e-02  4.91543300e-02 -1.56540945e-02\n", "  1.21418200e-01 -8.08644965e-02 -4.68782149e-02  4.10843305e-02\n", " -1.84318107e-02  6.69690445e-02  4.33595059e-03  2.27315389e-02\n", " -1.36428494e-02 -4.53238375e-02 -3.92829925e-02 -6.29884889e-03\n", "  5.29610142e-02 -3.69064324e-02  7.11677149e-02  2.33343306e-33\n", "  1.05231345e-01 -4.81874198e-02  6.95919245e-02  6.56976402e-02\n", " -4.65148985e-02  5.14492355e-02 -1.24475174e-02  3.20872180e-02\n", " -9.23356563e-02  5.00932746e-02 -3.28876264e-02  1.39138829e-02\n", " -8.70194111e-04 -4.90909349e-03  1.03946343e-01  3.21593980e-04\n", "  5.28110377e-02 -1.17990635e-02  2.31565777e-02  1.31768631e-02\n", " -5.25962822e-02  3.26702856e-02  3.08663264e-04  6.41129017e-02\n", "  3.88501100e-02  5.88008165e-02  8.29793438e-02 -1.88149437e-02\n", " -2.26377267e-02 -1.00473635e-01 -3.83752622e-02 -5.88081330e-02\n", "  1.82419841e-03 -4.26995270e-02  2.50195544e-02  6.40059710e-02\n", " -3.77482884e-02 -6.83902297e-03 -2.54600798e-03 -9.76042449e-02\n", "  1.88475568e-02 -8.83147295e-04  1.73611827e-02  7.10790828e-02\n", "  3.30393203e-02  6.93420647e-03 -5.60523421e-02  5.14634810e-02\n", " -4.29542102e-02  4.60077263e-02 -8.78835004e-03  3.17289010e-02\n", "  4.93965596e-02  2.95190103e-02 -5.05192615e-02 -5.43187149e-02\n", "  1.49988336e-04 -2.76614502e-02  3.46878842e-02 -2.10890342e-02\n", "  1.38060367e-02  2.99887042e-02  1.39744347e-02 -4.26470814e-03\n", " -1.50336921e-02 -8.76095369e-02 -6.85053840e-02 -4.28141914e-02\n", "  7.76945502e-02 -7.10285231e-02 -7.37686455e-03  2.13727057e-02\n", "  1.35562001e-02 -7.90464953e-02  5.47666848e-03  8.30663443e-02\n", "  1.14148013e-01  1.80759979e-03  8.75490978e-02 -4.16045263e-02\n", "  1.55416531e-02 -1.01206088e-02 -7.32439617e-03  1.07966131e-02\n", " -6.62816837e-02  3.98413949e-02 -1.16711557e-01  6.42993897e-02\n", "  4.02919948e-02 -6.54741675e-02  1.95052642e-02  8.09995383e-02\n", "  5.36463559e-02  7.67969713e-02 -1.34852324e-02 -1.76919048e-08\n", " -4.43935432e-02  9.20645986e-03 -8.79590586e-02  4.26921360e-02\n", "  7.31365010e-02  1.68426763e-02 -4.03262787e-02  1.85131319e-02\n", "  8.44172686e-02 -3.74476984e-02  3.02996263e-02  2.90641822e-02\n", "  6.36878908e-02  2.89750323e-02 -1.47270346e-02  1.77542400e-02\n", " -3.36895660e-02  1.73161104e-02  3.37875262e-02  1.76826045e-01\n", " -1.75532904e-02 -6.03078045e-02 -1.43394899e-02 -2.38536298e-02\n", " -4.45531011e-02 -2.89850254e-02 -8.96776170e-02 -1.75938942e-03\n", " -2.61485949e-02  5.93997585e-03 -5.18355407e-02  8.57279599e-02\n", " -8.18398669e-02  8.35439563e-03  4.00789864e-02  4.17764597e-02\n", "  1.04573570e-01 -2.86562135e-03  1.96691155e-02  5.81047544e-03\n", "  1.33253373e-02  4.51000445e-02 -2.17588153e-02 -1.39493244e-02\n", " -6.86992407e-02 -2.94117862e-03 -3.10765207e-02 -1.05854444e-01\n", "  6.91624209e-02 -4.24115025e-02 -4.67681997e-02 -3.64750996e-02\n", "  4.50399853e-02  6.09816089e-02 -6.56561553e-02 -5.45642851e-03\n", " -1.86227206e-02 -6.31484315e-02 -3.87436971e-02  3.46733667e-02\n", "  5.55458590e-02  5.21627814e-02  5.61064892e-02  1.02063917e-01]\n", "\n"]}], "source": ["# Generating Embeddings\n", "\n", "# Our sentences we like to encode\n", "sentences = [\n", "    \"This framework generates embeddings for each input sentence\",\n", "    \"Sentences are passed as a list of string.\",\n", "    \"The quick brown fox jumps over the lazy dog.\",\n", "]\n", "\n", "# Sentences are encoded by calling model.encode()\n", "sentence_embeddings = model.encode(sentences)\n", "\n", "# Print the embeddings\n", "for sentence, embedding in zip(sentences, sentence_embeddings):\n", "    print(\"Sentence:\", sentence)\n", "    print(\"Embedding:\", embedding)\n", "    print(\"\")"]}, {"cell_type": "markdown", "metadata": {"id": "JI9bGfu_aFev"}, "source": ["### Cosine Similarity between Sentences\n", "\n", "You can use cosine similarity to find out the similarity between 2 sentences. Sentence transformers allow us to find the cosine similarity score between 2 sentences so let’s see it in action!\n", "\n", "First, we will import the required modules and convert our sentences into embeddings using the same model we used before"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "iWblprdHR_4Q"}, "outputs": [], "source": [" # Finding cosine similarity\n", "from sentence_transformers import SentenceTransformer, util\n", "\n", "# Sentences are encoded by calling model.encode()\n", "emb1 = model.encode(\"This is a red cat with a hat.\")\n", "emb2 = model.encode(\"Have you seen my red cat?\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "-4yDCPQ8aFew"}, "source": ["Now we can find the cosine similarity between these 2 embeddings using `util.cos_sim` method"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XZVQN0wUaFew", "outputId": "e975bd69-2e24-4f9f-f89b-0a2ae79513f6"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Cosine-Similarity: tensor([[0.6153]])\n"]}], "source": ["cos_sim = util.cos_sim(emb1, emb2)\n", "print(\"Cosine-Similarity:\", cos_sim)"]}, {"cell_type": "markdown", "metadata": {"id": "9AXCMYuAaFex"}, "source": ["### Semantic Search\n", "In semantic search, you have a query (it can be a sentence or an image) and you convert that query into embeddings and then you find the similar sentence embeddings for the given query embedding using semantic search by performing cosine similarity.\n", "\n", "Once we get all the similarity scores for different sentences, we then sort the sentences based on the scores in descending order meaning that the most similar sentence or a sentence with highest similarity score will be at the top and we can specify the number of similar sentences we want as “k”.\n", "\n", "Let’s see it in action!\n", "\n", "First we will define the existing sentences which works as a database meaning that we want to find the top k similar sentences from this list. We will have to convert these sentences into encodings so that we can perform cosine similarity on them."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "5O4shOJfSsDh"}, "outputs": [], "source": ["# Semantic Search\n", "from sentence_transformers import SentenceTransformer, util\n", "import torch\n", "\n", "# Corpus with example sentences\n", "corpus = [\n", "    \"A man is eating food.\",\n", "    \"A man is eating a piece of bread.\",\n", "    \"The girl is carrying a baby.\",\n", "    \"A man is riding a horse.\",\n", "    \"A woman is playing violin.\",\n", "    \"Two men pushed carts through the woods.\",\n", "    \"A man is riding a white horse on an enclosed ground.\",\n", "    \"A monkey is playing drums.\",\n", "    \"A cheetah is running behind its prey.\",\n", "]\n", "corpus_embeddings = model.encode(corpus, convert_to_tensor=True)"]}, {"cell_type": "markdown", "metadata": {"id": "ec5yURi3aFex"}, "source": ["Now we will define our queries and for each query we will find top 3 similar sentences from corpus"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "VJQyWH3WaFex", "outputId": "7ad3e8fd-81e0-45a6-c288-b7dbc3a39806"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "\n", "======================\n", "\n", "\n", "Query: A man is eating pasta.\n", "\n", "Top 5 most similar sentences in corpus:\n", "A man is eating food. (Score: 0.7035)\n", "A man is eating a piece of bread. (Score: 0.5272)\n", "A man is riding a horse. (Score: 0.1889)\n", "A man is riding a white horse on an enclosed ground. (Score: 0.1047)\n", "A cheetah is running behind its prey. (Score: 0.0980)\n", "\n", "\n", "======================\n", "\n", "\n", "Query: Someone in a gorilla costume is playing a set of drums.\n", "\n", "Top 5 most similar sentences in corpus:\n", "A monkey is playing drums. (Score: 0.6433)\n", "A woman is playing violin. (Score: 0.2564)\n", "A man is riding a horse. (Score: 0.1389)\n", "A man is riding a white horse on an enclosed ground. (Score: 0.1191)\n", "A cheetah is running behind its prey. (Score: 0.1080)\n", "\n", "\n", "======================\n", "\n", "\n", "Query: A cheetah chases prey on across a field.\n", "\n", "Top 5 most similar sentences in corpus:\n", "A cheetah is running behind its prey. (Score: 0.8253)\n", "A man is eating food. (Score: 0.1399)\n", "A monkey is playing drums. (Score: 0.1292)\n", "A man is riding a white horse on an enclosed ground. (Score: 0.1097)\n", "A man is riding a horse. (Score: 0.0650)\n"]}], "source": ["# Query sentences:\n", "queries = [\n", "    \"A man is eating pasta.\",\n", "    \"Someone in a gorilla costume is playing a set of drums.\",\n", "    \"A cheetah chases prey on across a field.\",\n", "]\n", "top_k = min(5, len(corpus))\n", "for query in queries:\n", "    query_embedding = model.encode(query, convert_to_tensor=True)\n", "\n", "    # We use cosine-similarity and torch.topk to find the highest 5 scores\n", "    cos_scores = util.cos_sim(query_embedding, corpus_embeddings)[0]\n", "    top_results = torch.topk(cos_scores, k=top_k)\n", "\n", "    print(\"\\n\\n======================\\n\\n\")\n", "    print(\"Query:\", query)\n", "    print(\"\\nTop 5 most similar sentences in corpus:\")\n", "    # print(top_results)\n", "    for score, idx in zip(top_results[0], top_results[1]):\n", "        print(corpus[idx], \"(Score: {:.4f})\".format(score))"]}, {"cell_type": "markdown", "metadata": {"id": "7KToHCIyaFex"}, "source": ["Additionaly, instead of using `util.cos_sim` and then getting the top k results, you can use `util.semantic_search` method to do the same thing easily."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZeW0W-nU_STg", "outputId": "96aaaa73-da89-400e-ee50-df12a0a1570f"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["===============\n", "\n", "Similar Sentences for 'A man is eating pasta.'\n", "A man is eating food. (score: 0.7035485506057739)\n", "A man is eating a piece of bread. (score: 0.5271987318992615)\n", "A man is riding a horse. (score: 0.1888955533504486)\n", "A man is riding a white horse on an enclosed ground. (score: 0.1046992838382721)\n", "A cheetah is running behind its prey. (score: 0.09803032130002975)\n", "===============\n", "\n", "Similar Sentences for 'Someone in a gorilla costume is playing a set of drums.'\n", "A monkey is playing drums. (score: 0.6432533264160156)\n", "A woman is playing violin. (score: 0.25641563534736633)\n", "A man is riding a horse. (score: 0.13887260854244232)\n", "A man is riding a white horse on an enclosed ground. (score: 0.11909148842096329)\n", "A cheetah is running behind its prey. (score: 0.10798671096563339)\n", "===============\n", "\n", "Similar Sentences for 'A cheetah chases prey on across a field.'\n", "A cheetah is running behind its prey. (score: 0.8253213167190552)\n", "A man is eating food. (score: 0.13989517092704773)\n", "A monkey is playing drums. (score: 0.12919360399246216)\n", "A man is riding a white horse on an enclosed ground. (score: 0.1097417101264)\n", "A man is riding a horse. (score: 0.06497810035943985)\n"]}], "source": ["# Using semantic_search utility from sentence transformers\n", "top_k = 5\n", "for query in queries:\n", "    query_embedding = model.encode(query, convert_to_tensor=True)\n", "    similar_results = util.semantic_search(query_embeddings=query_embedding,corpus_embeddings=corpus_embeddings,top_k=top_k)\n", "    print(\"===============\\n\")\n", "    print(f\"Similar Sentences for '{query}'\")\n", "    for result in similar_results[0]:\n", "      print(f\"{corpus[result['corpus_id']]} (score: {result['score']})\")"]}, {"cell_type": "markdown", "metadata": {"id": "xfgHJl9RaFey"}, "source": ["## Training a model using a triplets dataset\n", "Now we have enough knowledge about sentence transformers, so let's fine-tune a base model from scratch\n", "Please [read the blog](https://www.ionio.ai/blog/fine-tuning-embedding-models-using-sentence-transformers-code-included) to read about the fine-tuning process in more detail.\n", "\n", "Let’s first pull our base model and apply pooling on it so that we can get fixed 768 sized embedding array in output"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "UaJDWv2kEA4C", "colab": {"base_uri": "https://localhost:8080/", "height": 232, "referenced_widgets": ["1ba805cce304437f8672343551b8d6b6", "494975b04e48493d9bbb7ec17bf4e4c7", "20036ad9d8d34a3c8359ba28c4223bbd", "aed2314a879a4085871b1ea02538f403", "ed7207603d424b6fb6190ac4c80cb70b", "31869fe2f4004abdb0aa3dc2838bae1b", "54ce988c3f1b4ede91c29082cf4eff00", "fb1d14b0af0f4f65a637b9af616dfd5a", "4d3732e37b2c44deb12cb88ee24886e7", "9c62b09ac1054dec95cdd391fe952eb2", "e81296033f4842a6ab70dd96e38f4179", "6d7c7eeab9064c958dab97edebe344dc", "6ea2487f84bd47b398ec809c85be3574", "783a438233a44508bddece825527dffe", "0cd008957fdc4c17a5179a4bc05bcd29", "c0e2fccec2d045db8bc5361931c59755", "30909253f0b142eb8da5afd2cfee198c", "16e5ab743a4348a692648433dcf96284", "560e61f266ec4fc1a543e739243ad2e9", "c3ae5248af574ee881e908122d1387f6", "e01f1fbf9fdb4ee0b3624c4ffb75450a", "74235904c0b24cab8c932c193f446edd", "605663bf44914fc499bcd5984a8d99ff", "91f17675c036444f91e2291aedf247f6", "40d93d19cf9c49cf826e941199c4c029", "53f59550df2a44768d8d6d217b986c75", "6d95caffe75742ec95e0cd61a9c63a69", "609a5939ce184cd0b844dc6fead21820", "37d5f658adee4d0fb592221c037b0f1e", "ff1d7a236f4a464592cd5d3c3b87e57f", "bc3b210d47bd4c52a6f499a5ffdc1ca5", "aa49b2c5422345d2846583e8dbef242f", "0169057c940e43eda7d70dd0fba5a2f0", "491b0f3bcc9c4bef9903c4b33a1fd9f9", "4c18ee976bf34a3b9ae02a6759361016", "b1839f003c464f0eb910620b2c034198", "9534ade484774ab7a7f38427cdaf2939", "f8e32c094537485bad80f770b93559b2", "074fa95576964a52a96473328f000b8f", "62c8e8c130024a61a27415d00432412a", "7698ac94c994482fa70f73f50e01d99c", "f65e307c149b4a1a84e20a46162c4e95", "e764eda692af4f6db65ad2d393aed2a6", "8343e3a915b14df9a357074306ecea6b", "346099a0cc7f4deb892bed1d9c5272e2", "f89fbb9917aa4d12bb1ef222de98a6e1", "c69557b183f34668ab761e50c2778d5f", "1d6610c21fc64d49ad7445929c3e9f33", "8f6a86871a124b3c84a53d5396515ca5", "f650c75091364fbebfbc8666d44f0616", "cb500a66114742638c16bb4d8d562daa", "c8dc6021bd8c412280d232216756f771", "e6d7779904b0405da76e607169f155f4", "2587a58298484e9c8f31bd8a269a426f", "911d3568a4a441c88fa63b6b81507b84"]}, "outputId": "1e4fbd06-7d60-48dd-e5c1-dae865089f4c"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/570 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1ba805cce304437f8672343551b8d6b6"}}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`\n", "WARNING:huggingface_hub.file_download:Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`\n"]}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors:   0%|          | 0.00/440M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "6d7c7eeab9064c958dab97edebe344dc"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/48.0 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "605663bf44914fc499bcd5984a8d99ff"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["vocab.txt:   0%|          | 0.00/232k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "491b0f3bcc9c4bef9903c4b33a1fd9f9"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/466k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "346099a0cc7f4deb892bed1d9c5272e2"}}, "metadata": {}}], "source": ["# Training a bert model using sentence transformer\n", "from sentence_transformers import SentenceTransformer, models\n", "import torch\n", "\n", "word_embedding_model = models.Transformer(\"bert-base-uncased\", max_seq_length=256)\n", "pooling_model = models.Pooling(word_embedding_model.get_word_embedding_dimension())\n", "\n", "model = SentenceTransformer(modules=[word_embedding_model, pooling_model])"]}, {"cell_type": "markdown", "metadata": {"id": "O2X4EjS_aFez"}, "source": ["Now let’s pull our dataset, we are going to use `embedding-data/QQP_triplets` but you can use any other triplet dataset too if you want"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 499, "referenced_widgets": ["7fcb64bebd2348e197e88a9340e4e378", "5bf92edb53c54453bb5966ae5945e4e5", "4f4920af94f7479983096731a7d7c8b7", "735161b25207444fad64764bd81f0145", "6871cd30b2f24d378a051aac6350f72b", "d8cd1d5054b643ca9336868f6e30ac4e", "62eced6b279d45d9b69a3a37eb660a2d", "fb8ae4157155433dad6824d1c66081d9", "cd120a6c4fbd4df79c1fd195867331bb", "a1bfa1b543d448a1976f8f6db82fb89e", "84b48b03e9ef405e98e6fbca919f93bb", "171a1238d676470ab72cebbeef422fe8", "1e34b0c0921a4d7fb74cf5d6a6b3b2d8", "123b27a595a6495690d8b6dd4570bd62", "983f9e26676c465883ea326309f1b82e", "95dd2a4b78bf4c6dbd8c38d61725c8d9", "8777b9a7aaa54638a5e53663d03cd068", "069c39817db547d3b939d5254586480f", "efc96cae9da147efbce80ef186cee8bf", "67d5138af449451e8f08510a15b760c1", "c3330832181a4697b49ac6bb369b55e1", "0b5e3d75761b42c2979d778aa38af468", "6515c6e4dda145d790805e85633f5897", "512ba5eadae5475ba4ee3e85fec249c3", "a70a306b186d4f3582b5bcf1471fabe0", "8de8c60063d2482dbd9a3d4cd8a4646e", "03d02d74cded4bc88d01ca119fd81f11", "d9fe7ef5e07242a493a9051886929b56", "da756729693f44dc9f68aec0e420d616", "6c20cf22716244d7b96633dfa6df69c9", "5dc7bccd0c9143769557a06d5b088ce7", "3fa40891365e4f88b93717bffc7b9fe2", "5aa67343a7464ef8a97b3a8dab363aec", "67eb038c161242aca06907e42f0ef049", "9b913dc573fe4bab9e46eb0652e56ac4", "1bc1a943e37841a9ba7bf50f80fe6a8f", "ca9117dfc2cc483d8e579d2d31549ab8", "c6615d64c6ca4d28be799c89226bc783", "6f23d80329354dd9aa1362fd18ade1a3", "a5558de781874dc5b91464d1770f9e8e", "3663f56de560441da34e2bcc659792c2", "d6f4295082a9430f9dc91f29f6833baf", "ff7bc214520d4d089a7b03141f3f47ac", "7e90eb687d7b41eda6ad7078a5f1f45f", "ed94e034465b447f93caff492902b43f", "a755ee87ed64417fbf4118033a061e74", "7452921620864826ac4628408d6d619c", "2771761e002542f28f230e9659cec1b7", "d0f3410ac7bb42bd98b617830a43ba3f", "018c52af36a94996bd83db63f2c64e8b", "463ef8a5cc2a42d6aa24a43ca49bf607", "3f807f51af1b4490aaecf83e3beba6fa", "1573caf071044334a387869b38fb3150", "c96922bdf6cb460191e721f78a1741fc", "122f63abe846470483e17cb763909a56"]}, "id": "RuXF05UeHJFT", "outputId": "9fa4a2be-66f3-4e93-d98b-eb7f12b8bd70"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Downloading readme:   0%|          | 0.00/6.27k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "7fcb64bebd2348e197e88a9340e4e378"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading data files:   0%|          | 0/1 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "171a1238d676470ab72cebbeef422fe8"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Downloading data:   0%|          | 0.00/183M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "6515c6e4dda145d790805e85633f5897"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Extracting data files:   0%|          | 0/1 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "67eb038c161242aca06907e42f0ef049"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Generating train split: 0 examples [00:00, ? examples/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "ed94e034465b447f93caff492902b43f"}}, "metadata": {}}, {"output_type": "error", "ename": "NotImplementedError", "evalue": "Loading a dataset cached in a LocalFileSystem is not supported.", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNotImplementedError\u001b[0m                       Traceback (most recent call last)", "\u001b[0;32m<ipython-input-10-a4ef460f0cb7>\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      3\u001b[0m \u001b[0mdataset_id\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m\"embedding-data/QQP_triplets\"\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 4\u001b[0;31m \u001b[0mdataset\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mload_dataset\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdataset_id\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/datasets/load.py\u001b[0m in \u001b[0;36mload_dataset\u001b[0;34m(path, name, data_dir, data_files, split, cache_dir, features, download_config, download_mode, verification_mode, ignore_verifications, keep_in_memory, save_infos, revision, token, use_auth_token, task, streaming, num_proc, storage_options, **config_kwargs)\u001b[0m\n\u001b[1;32m   2147\u001b[0m         \u001b[0mkeep_in_memory\u001b[0m \u001b[0;32mif\u001b[0m \u001b[0mkeep_in_memory\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32mNone\u001b[0m \u001b[0;32melse\u001b[0m \u001b[0mis_small_dataset\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mbuilder_instance\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0minfo\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdataset_size\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2148\u001b[0m     )\n\u001b[0;32m-> 2149\u001b[0;31m     \u001b[0mds\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mbuilder_instance\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mas_dataset\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msplit\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0msplit\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mverification_mode\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mverification_mode\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0min_memory\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mkeep_in_memory\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   2150\u001b[0m     \u001b[0;31m# Rename and cast features to match task schema\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   2151\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mtask\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/datasets/builder.py\u001b[0m in \u001b[0;36mas_dataset\u001b[0;34m(self, split, run_post_process, verification_mode, ignore_verifications, in_memory)\u001b[0m\n\u001b[1;32m   1171\u001b[0m         \u001b[0mis_local\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mis_remote_filesystem\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_fs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1172\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mis_local\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1173\u001b[0;31m             \u001b[0;32mraise\u001b[0m \u001b[0mNotImplementedError\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34mf\"Loading a dataset cached in a {type(self._fs).__name__} is not supported.\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1174\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0;32mnot\u001b[0m \u001b[0mos\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mexists\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_output_dir\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1175\u001b[0m             raise FileNotFoundError(\n", "\u001b[0;31mNotImplementedError\u001b[0m: Loading a dataset cached in a LocalFileSystem is not supported."]}], "source": ["from datasets import load_dataset\n", "\n", "dataset_id = \"embedding-data/QQP_triplets\"\n", "dataset = load_dataset(dataset_id)"]}, {"cell_type": "markdown", "metadata": {"id": "gdqOJAUfaFez"}, "source": ["Let’s take a look at how each data looks like in dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Gr6hKYwKHXKu"}, "outputs": [], "source": ["dataset['train']['set'][0]"]}, {"cell_type": "markdown", "metadata": {"id": "VLKViZw4aFez"}, "source": ["As we can see, each example have a query, a positive sentence which is similar to that query and a list of negative sentences which are not similar to query.\n", "\n", "We can’t directly pass this dataset examples into our model because first we have to convert them to a specific format that sentence transformers and model can understand. Every training example must be in “InputExample” format in sentence transformers so we will convert our dataset data into this format.\n", "\n", "We will also take only first sentence from both `pos` and `neg` arrays to make it easy but in production scenario, you might need to pass the full array for better performance and accuracy"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "COnZ6FwVHr8V"}, "outputs": [], "source": ["from sentence_transformers import InputExample\n", "\n", "train_examples = []\n", "train_data = dataset['train']['set']\n", "# For agility we only 1/2 of our available data\n", "# n_examples = dataset['train'].num_rows // 4\n", "for i in range(0,1000):\n", "  example = train_data[i]\n", "  train_examples.append(InputExample(texts=[example['query'],example['pos'][0],example['neg'][0]]))"]}, {"cell_type": "markdown", "metadata": {"id": "F0X3jFP8aFe0"}, "source": ["Now let’s create our dataloader"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "On9cRvyDKWRu"}, "outputs": [], "source": ["from torch.utils.data import DataLoader\n", "\n", "train_dataloader = DataLoader(train_examples, shuffle=True, batch_size=16)"]}, {"cell_type": "markdown", "metadata": {"id": "8tGnjrL2aFe0"}, "source": ["Now let’s define our loss function. We can use “losses” class from sentence transformers which allows us to get different loss functions that we discussed above.\n", "\n", "We just have to attach the model to triplet loss function"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2Nw8qzlGKbGe"}, "outputs": [], "source": ["from sentence_transformers import losses\n", "train_loss = losses.TripletLoss(model)"]}, {"cell_type": "markdown", "metadata": {"id": "1ELHCPOFaFe0"}, "source": ["And now we are ready, let’s combine everything we prepared and fine-tune the model using `model.fit` method which takes dataloader and loss function as a train objectives."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "S9ydGxDLKwaD"}, "outputs": [], "source": ["model.fit(train_objectives=[(train_dataloader, train_loss)], epochs=4)"]}, {"cell_type": "markdown", "metadata": {"id": "p5iFcmw_aFe1"}, "source": ["Now let’s push this fine-tuned model on huggingface so that we can share it with other people and they can also see what we cooked!\n", "\n", "First login with <PERSON><PERSON> using your access token"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "nOo-D2iLaFe1"}, "outputs": [], "source": ["from huggingface_hub import notebook_login\n", "\n", "notebook_login()"]}, {"cell_type": "markdown", "metadata": {"id": "8N4jaF-YaFe1"}, "source": ["After that. call `save_to_hub` method to push your model on huggingface"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jz5XvtTVaFe1"}, "outputs": [], "source": ["model.save_to_hub(\n", "    \"distilroberta-base-sentence-transformer-triplets\", # Give a name to your model\n", "    organization=\"0xSH1V4M\" # Your Huggingface Username\n", "    train_datasets=[\"embedding-data/QQP_triplets\"],\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "HeXUOmADaFe1"}, "source": ["## Training a model using labeled sentences dataset\n", "\n", "Now let’s try to fine-tune a model using a different dataset. This time we will use a dataset in which each example contains a pair of sentences with a label score that defines the relationship between 2 sentences.\n", "\n", "Let’s first load our model and add pooling to it"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "A3WBRajMaFe1"}, "outputs": [], "source": ["from sentence_transformers import SentenceTransformer, models\n", "import torch\n", "\n", "word_embedding_model = models.Transformer(\"bert-base-uncased\", max_seq_length=256)\n", "pooling_model = models.Pooling(word_embedding_model.get_word_embedding_dimension())\n", "\n", "model = SentenceTransformer(modules=[word_embedding_model, pooling_model])"]}, {"cell_type": "markdown", "metadata": {"id": "A6iIy551aFe2"}, "source": ["We will use `snli` dataset to train this model which have the data in the format we discussed above"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "00clBYhUK5ki"}, "outputs": [], "source": ["from datasets import load_dataset\n", "\n", "# Using snli as a dataset\n", "snli = load_dataset('snli', split='train')\n", "# and remove bad rows\n", "snli = snli.filter(\n", "    lambda x: False if x['label'] == -1 else True\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "VIhM4o9caFe2"}, "source": ["Let’s take a look at how each example looks in dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zEg_gW9wsbrp"}, "outputs": [], "source": ["print(snli[0])"]}, {"cell_type": "markdown", "metadata": {"id": "dRVM9qjUaFe7"}, "source": ["Now let’s convert each data example in InputExample format"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "1mWB26_drAz6"}, "outputs": [], "source": ["from sentence_transformers import InputExample\n", "from tqdm.auto import tqdm  # so we see progress bar\n", "\n", "train_samples = []\n", "for row in tqdm(snli):\n", "    train_samples.append(InputExample(\n", "        texts=[row['premise'], row['hypothesis']],\n", "        label=row['label']\n", "    ))"]}, {"cell_type": "markdown", "metadata": {"id": "m6dL9PPKaFe7"}, "source": ["Now let’s define our dataloader and loss function. For this type of dataset, we will use sotfmax loss function"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jAcX88rqsgfj"}, "outputs": [], "source": ["from torch.utils.data import DataLoader\n", "\n", "train_dataloader = DataLoader(train_samples, shuffle=True, batch_size=16)\n", "train_loss = losses.SoftmaxLoss(model,sentence_embedding_dimension=model.get_sentence_embedding_dimension(),num_labels=3)"]}, {"cell_type": "markdown", "metadata": {"id": "e4aLKf3caFe8"}, "source": ["Now let’s train our model!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Ym-J6WBtt3MP"}, "outputs": [], "source": ["epochs = 1\n", "# Warmup for 10% of training as before (you can increase this count according to needs)\n", "warmup_steps = int(len(train_dataloader) * epochs * 0.1)\n", "# Train the model\n", "model.fit(train_objectives=[(train_dataloader, train_loss)], epochs=1, warmup_steps=warmup_steps)"]}, {"cell_type": "markdown", "metadata": {"id": "xZi5oBrgaFe8"}, "source": ["Let's save our fine-tuned model on huggingface"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MGzicva9aFe8"}, "outputs": [], "source": ["model.save_to_hub(\n", "    \"distilroberta-base-sentence-transformer-snli\", # Give a name to your model\n", "    organization=\"0xSH1V4M\", # Your Huggingface Username\n", "    train_datasets=[\"snli\"],\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "QzUxarYpaFe8"}, "source": ["## Evaluation\n", "Now it’s time to test our fine-tuned models with the base model and analyze the accuracy and performance of these models.\n", "\n", "We will first get the vector embeddings of some sentences using both models and then reduce the dimensions of these embeddings to 2 using “TSNE” technique then using metaploitlib, we will plot the embeddings on a 2D graph\n", "\n", "We will use these sentences for testing"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "aXmDsMMjt44X"}, "outputs": [], "source": ["from sentence_transformers import SentenceTransformer, models\n", "# Evaluation\n", "sentences = [\n", "    \"A man is eating food.\",\n", "    \"A man is eating a piece of bread.\",\n", "    \"The girl is carrying a baby.\",\n", "    \"A man is riding a horse.\",\n", "    \"A woman is playing violin.\",\n", "    \"Two men pushed carts through the woods.\",\n", "    \"A man is riding a white horse on an enclosed ground.\",\n", "    \"A monkey is playing drums.\",\n", "    \"A cheetah is running behind its prey.\",\n", "]"]}, {"cell_type": "markdown", "metadata": {"id": "10hMevLKaFe8"}, "source": ["Let’s first get the embeddings  of these sentences using “bert-base-uncased” model which is our base model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5WN7Ep_saFe9"}, "outputs": [], "source": ["model = SentenceTransformer(\"bert-base-uncased\")\n", "# Sentences are encoded by calling model.encode()\n", "sentence_embeddings = model.encode(sentences)"]}, {"cell_type": "markdown", "metadata": {"id": "IAoFYA6WaFe9"}, "source": ["Now let’s reduce the embedding dimensions using TSNE"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "EY3fqg0R4CoA"}, "outputs": [], "source": ["import numpy as np\n", "from sklearn.manifold import TSNE\n", "embeddings = np.array(sentence_embeddings)\n", "tsne = TSNE(n_components=2, random_state=42,perplexity=5)\n", "embeddings_2d = tsne.fit_transform(embeddings)"]}, {"cell_type": "markdown", "metadata": {"id": "fF5Z5L8_aFe9"}, "source": ["Now we have 2D embeddings, we will do clustering to classify all these embeddings into different classes so that it will be easy for us to visualize how these models are classifying different embeddings and the positions of embeddings in vector space"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "y62acma2FbHB"}, "outputs": [], "source": ["from sklearn.cluster import KMeans\n", "# Perform kmean clustering\n", "num_clusters = 3\n", "clustering_model = KMeans(n_clusters=num_clusters)\n", "clustering_model.fit(sentence_embeddings)\n", "cluster_assignment = clustering_model.labels_\n", "print(cluster_assignment)"]}, {"cell_type": "markdown", "metadata": {"id": "B5YSce59aFe-"}, "source": ["If you print the `cluster_assignment` array then you will see the class labels for every sentence which shows the class of each sentence\n", "\n", "Now let’s plot these embeddings in 2D vector space using metaplotlib"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "TA8FTFmT4hdZ"}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# Assuming your 2D embeddings are stored in 'embeddings_2d'\n", "\n", "# Create a scatter plot\n", "plt.figure(figsize=(6, 4))  # Adjust figure size as needed\n", "colors = [\"red\",\"green\",\"blue\"]\n", "\n", "for index,embedding in enumerate(embeddings_2d):\n", "  plt.scatter(embedding[0],embedding[1],color=colors[cluster_assignment[index]])\n", "# plt.scatter(embeddings_2d[:, 0], embeddings_2d[:, 1])  # Use first two columns for x and y\n", "\n", "# Optional: Add labels and title\n", "plt.xlabel(\"X\")\n", "plt.ylabel(\"Y\")\n", "plt.title(\"BERT Base Model\")\n", "\n", "# Optional: Add sentence labels (consider using for small datasets like yours)\n", "for i, sentence in enumerate(sentences):\n", "  plt.annotate(sentence, (embeddings_2d[i, 0], embeddings_2d[i, 1]))\n", "\n", "plt.grid(False)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "whbHPzCvaFe-"}, "source": ["### Plotting the results of fine-tuned model using triplets\n", "\n", "First pull the model from hugging<PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "keQeV7sN64r5"}, "outputs": [], "source": ["model = SentenceTransformer('0xSH1V4M/distilroberta-base-sentence-transformer-triplets')"]}, {"cell_type": "markdown", "metadata": {"id": "gmVhNZ_maFe-"}, "source": ["Prepare the sentence embeddings"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "HqiMWlsU7bIh"}, "outputs": [], "source": ["sentences = [\n", "    \"A man is eating food.\",\n", "    \"A man is eating a piece of bread.\",\n", "    \"The girl is carrying a baby.\",\n", "    \"A man is riding a horse.\",\n", "    \"A woman is playing violin.\",\n", "    \"Two men pushed carts through the woods.\",\n", "    \"A man is riding a white horse on an enclosed ground.\",\n", "    \"A monkey is playing drums.\",\n", "    \"A cheetah is running behind its prey.\",\n", "]\n", "sentence_embeddings = model.encode(sentences)"]}, {"cell_type": "markdown", "metadata": {"id": "XCzZnSvUaFe_"}, "source": ["Apply KMeans algorithm to perform clustering on embeddings"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zgofTmZZA9jz"}, "outputs": [], "source": ["from sklearn.cluster import KMeans\n", "# Perform kmean clustering\n", "num_clusters = 3\n", "clustering_model = KMeans(n_clusters=num_clusters)\n", "clustering_model.fit(sentence_embeddings)\n", "cluster_assignment = clustering_model.labels_\n", "print(cluster_assignment)"]}, {"cell_type": "markdown", "metadata": {"id": "1HdqMjcWaFe_"}, "source": ["Apply TSNE algorithm for dimension reduction"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hc-c9BTiBG7P"}, "outputs": [], "source": ["embeddings = np.array(sentence_embeddings)\n", "tsne = TSNE(n_components=2, random_state=42,perplexity=5)\n", "embeddings_2d = tsne.fit_transform(embeddings)\n", "# print(embeddings_2d)"]}, {"cell_type": "markdown", "metadata": {"id": "3hKyxDbhaFe_"}, "source": ["Plot the 2D embeddings on 2D graph"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "i460FuS57mvJ"}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "colors = [\"red\",\"green\",\"blue\"]\n", "\n", "# Create a scatter plot\n", "plt.figure(figsize=(6, 4))  # Adjust figure size as needed\n", "for index,embedding in enumerate(embeddings_2d):\n", "  plt.scatter(embedding[0],embedding[1],color=colors[cluster_assignment[index]])\n", "  # plt.scatter(embeddings_2d[:, 0], embeddings_2d[:, 1])  # Use first two columns for x and y\n", "\n", "# Optional: Add labels and title\n", "plt.xlabel(\"X\")\n", "plt.ylabel(\"Y\")\n", "plt.title(\"Fine-tuned with triplets\")\n", "\n", "# Optional: Add sentence labels (consider using for small datasets like yours)\n", "for i, sentence in enumerate(sentences):\n", "  plt.annotate(sentence, (embeddings_2d[i, 0], embeddings_2d[i, 1]))\n", "\n", "plt.grid(False)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "ry1i5XGnaFfA"}, "source": ["### Plotting the results of fine-tuned model using snli\n", "\n", "Pull the model from <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LfeiwVZv7o87"}, "outputs": [], "source": ["model = SentenceTransformer('0xSH1V4M/distilroberta-base-sentence-transformer-snli')"]}, {"cell_type": "markdown", "metadata": {"id": "jkqDqIQTaFfA"}, "source": ["Prepare the sentence embeddings"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pfJDtkL-n0Je"}, "outputs": [], "source": ["sentences = [\n", "    \"A man is eating food.\",\n", "    \"A man is eating a piece of bread.\",\n", "    \"The girl is carrying a baby.\",\n", "    \"A man is riding a horse.\",\n", "    \"A woman is playing violin.\",\n", "    \"Two men pushed carts through the woods.\",\n", "    \"A man is riding a white horse on an enclosed ground.\",\n", "    \"A monkey is playing drums.\",\n", "    \"A cheetah is running behind its prey.\",\n", "]\n", "sentence_embeddings = model.encode(sentences)"]}, {"cell_type": "markdown", "metadata": {"id": "DZ4FABbcaFfA"}, "source": ["Apply KMeans algorithm to perform clustering on embeddings"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "KzkSz14En2-R"}, "outputs": [], "source": ["from sklearn.cluster import KMeans\n", "# Perform kmean clustering\n", "num_clusters = 3\n", "clustering_model = KMeans(n_clusters=num_clusters)\n", "clustering_model.fit(sentence_embeddings)\n", "cluster_assignment = clustering_model.labels_\n", "print(cluster_assignment)"]}, {"cell_type": "markdown", "metadata": {"id": "PzOcoojyaFfB"}, "source": ["Apply TSNE Algorithm for dimension reduction"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ArNYt7H3n483"}, "outputs": [], "source": ["embeddings = np.array(sentence_embeddings)\n", "tsne = TSNE(n_components=2, random_state=42,perplexity=5)\n", "embeddings_2d = tsne.fit_transform(embeddings)"]}, {"cell_type": "markdown", "metadata": {"id": "Q19RwiGvaFfB"}, "source": ["Plot the 2D embeddings on 2D Graph using matplotlib"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "y_fKrt4mn6z5"}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "colors = [\"red\",\"green\",\"blue\"]\n", "\n", "# Create a scatter plot\n", "plt.figure(figsize=(6, 4))  # Adjust figure size as needed\n", "for index,embedding in enumerate(embeddings_2d):\n", "  plt.scatter(embedding[0],embedding[1],color=colors[cluster_assignment[index]])\n", "  # plt.scatter(embeddings_2d[:, 0], embeddings_2d[:, 1])  # Use first two columns for x and y\n", "\n", "# Optional: Add labels and title\n", "plt.xlabel(\"X\")\n", "plt.ylabel(\"Y\")\n", "plt.title(\"Fine-tuned with SNLI\")\n", "\n", "# Optional: Add sentence labels (consider using for small datasets like yours)\n", "for i, sentence in enumerate(sentences):\n", "  plt.annotate(sentence, (embeddings_2d[i, 0], embeddings_2d[i, 1]))\n", "\n", "plt.grid(False)\n", "plt.show()"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"e9451d680c1243409290f6666d88c109": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0ddaa7892f5046b1a7af5dfdeba92170", "IPY_MODEL_5a9caab607944d73adefaca0c329d93e", "IPY_MODEL_76b07dcb8c27407693b36e28b19a7b34"], "layout": "IPY_MODEL_e8b1d74a6fd04bc99233bbd13766eae0"}}, "0ddaa7892f5046b1a7af5dfdeba92170": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_907790c5ea5e4ff387d2f5fee5db101f", "placeholder": "​", "style": "IPY_MODEL_43e10feb51124cb698961dfce19f2d63", "value": "modules.json: 100%"}}, "5a9caab607944d73adefaca0c329d93e": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_45420a5e75194812b0d8daf0ea522f69", "max": 349, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7a9ec427573e444095fc0989117de618", "value": 349}}, "76b07dcb8c27407693b36e28b19a7b34": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e4db00b31bed49f6ac4ff3d31ee70f73", "placeholder": "​", "style": "IPY_MODEL_4a5167fe1af74d93b3c75509774bd069", "value": " 349/349 [00:00&lt;00:00, 18.1kB/s]"}}, "e8b1d74a6fd04bc99233bbd13766eae0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "907790c5ea5e4ff387d2f5fee5db101f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "43e10feb51124cb698961dfce19f2d63": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "45420a5e75194812b0d8daf0ea522f69": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7a9ec427573e444095fc0989117de618": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e4db00b31bed49f6ac4ff3d31ee70f73": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4a5167fe1af74d93b3c75509774bd069": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b24bc5dcf25a40258634354a330ab782": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c764daffea824d028e35c1e9548741ef", "IPY_MODEL_5bf431aaf3494b54a807825cedcb1b3e", "IPY_MODEL_92a9d9985db049a3b1a73ac61267d506"], "layout": "IPY_MODEL_5dd4971b1bec48129fc24e61f5ab8d4a"}}, "c764daffea824d028e35c1e9548741ef": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8eff86a7d7354631877161ca1f6226a4", "placeholder": "​", "style": "IPY_MODEL_d087f1ec5e684bc58c018c2bbd6b7c65", "value": "config_sentence_transformers.json: 100%"}}, "5bf431aaf3494b54a807825cedcb1b3e": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8b6da954bf974f6984d5b2b2b21eed07", "max": 116, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5b652cc549f64eefb5582f2cfdb5c251", "value": 116}}, "92a9d9985db049a3b1a73ac61267d506": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d40f1445fd0a4be4a9267a9399058b78", "placeholder": "​", "style": "IPY_MODEL_93b4d77d1fd34cc89b340f207e518298", "value": " 116/116 [00:00&lt;00:00, 9.66kB/s]"}}, "5dd4971b1bec48129fc24e61f5ab8d4a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8eff86a7d7354631877161ca1f6226a4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d087f1ec5e684bc58c018c2bbd6b7c65": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8b6da954bf974f6984d5b2b2b21eed07": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5b652cc549f64eefb5582f2cfdb5c251": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d40f1445fd0a4be4a9267a9399058b78": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "93b4d77d1fd34cc89b340f207e518298": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fa1c64667b7342458bf348887eded7fb": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ee69f373f85141409eb43d130ee6e2d4", "IPY_MODEL_9a520df216454c3bb0bb3168f2732fac", "IPY_MODEL_6447c76dd58b46bc9c1be1247e966436"], "layout": "IPY_MODEL_e2e053d7393944dd92e54ddab295b295"}}, "ee69f373f85141409eb43d130ee6e2d4": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3f36062dbff74dd699af1435d40601e3", "placeholder": "​", "style": "IPY_MODEL_80529e1375014c26a80e76ade512af1e", "value": "README.md: 100%"}}, "9a520df216454c3bb0bb3168f2732fac": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a835d63344d945e0a3b6c4dc1027079d", "max": 10454, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_422cefdddc0d44b28446edde8bfe6c74", "value": 10454}}, "6447c76dd58b46bc9c1be1247e966436": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3cef91f8e9ce476e9821524f13141a7d", "placeholder": "​", "style": "IPY_MODEL_ce73fc8ce7f74511a2e833e67956c001", "value": " 10.5k/10.5k [00:00&lt;00:00, 926kB/s]"}}, "e2e053d7393944dd92e54ddab295b295": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3f36062dbff74dd699af1435d40601e3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "80529e1375014c26a80e76ade512af1e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a835d63344d945e0a3b6c4dc1027079d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "422cefdddc0d44b28446edde8bfe6c74": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3cef91f8e9ce476e9821524f13141a7d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ce73fc8ce7f74511a2e833e67956c001": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fe14b81c14cf4f388824115e32c965b6": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b155168753f542ffb9b4eb5377e5f847", "IPY_MODEL_0159f659caed4314b656795a8cc04d5d", "IPY_MODEL_c5aba18b5fb64460a7bde397776c0237"], "layout": "IPY_MODEL_1d868a1ed88446f09d11096f51dab947"}}, "b155168753f542ffb9b4eb5377e5f847": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5788bdff7e224d6190d472f575f6d4ae", "placeholder": "​", "style": "IPY_MODEL_9652028c49fd49faad4d559971cd8f9c", "value": "sentence_bert_config.json: 100%"}}, "0159f659caed4314b656795a8cc04d5d": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d9578b2e67a34c8494176c405d098f7c", "max": 53, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_41231992eecd47e6b5c3d2940fc76840", "value": 53}}, "c5aba18b5fb64460a7bde397776c0237": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b546097337694dda8b20e2a54a627500", "placeholder": "​", "style": "IPY_MODEL_cced4ed0166742b0bf9ee5efca31ef80", "value": " 53.0/53.0 [00:00&lt;00:00, 4.54kB/s]"}}, "1d868a1ed88446f09d11096f51dab947": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5788bdff7e224d6190d472f575f6d4ae": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9652028c49fd49faad4d559971cd8f9c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d9578b2e67a34c8494176c405d098f7c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "41231992eecd47e6b5c3d2940fc76840": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b546097337694dda8b20e2a54a627500": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cced4ed0166742b0bf9ee5efca31ef80": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ebfdf2f2d1754d0a8f507bb5fcab5419": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0b6f7b1c6f9c4fe0a38d6a6df8ae2caa", "IPY_MODEL_24202108248b4fa48973fb490b80fc3a", "IPY_MODEL_252edbe474bf4634872cad72f4941d59"], "layout": "IPY_MODEL_693d9d79a4b64bcc9ca6a4f60ff212da"}}, "0b6f7b1c6f9c4fe0a38d6a6df8ae2caa": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_185ea724dde24b9c8ef8557d6129903e", "placeholder": "​", "style": "IPY_MODEL_e040ad56899d4e6e9a5747099f0c2324", "value": "config.json: 100%"}}, "24202108248b4fa48973fb490b80fc3a": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a5aea53d741d4774b5b53e635918e400", "max": 612, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_244f77c77a7e4086ae6ea16d14b70459", "value": 612}}, "252edbe474bf4634872cad72f4941d59": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_07cbd50e37664779b35744058ce6d43e", "placeholder": "​", "style": "IPY_MODEL_60b8c305480849c4bfed0502dbcab9a1", "value": " 612/612 [00:00&lt;00:00, 48.9kB/s]"}}, "693d9d79a4b64bcc9ca6a4f60ff212da": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "185ea724dde24b9c8ef8557d6129903e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e040ad56899d4e6e9a5747099f0c2324": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a5aea53d741d4774b5b53e635918e400": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "244f77c77a7e4086ae6ea16d14b70459": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "07cbd50e37664779b35744058ce6d43e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "60b8c305480849c4bfed0502dbcab9a1": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "50395c70e2e0472bac70801c24187354": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_960320a4521e45418aa9345835983678", "IPY_MODEL_7fc05dd9967a48a0a65a240962e852ee", "IPY_MODEL_cfde7e7f488b4526aeb598455848a39d"], "layout": "IPY_MODEL_2b9cef22034c4c4796016dbeb3d74e20"}}, "960320a4521e45418aa9345835983678": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b4269a44cf894b60904f40be5acc309d", "placeholder": "​", "style": "IPY_MODEL_5ce7264b8bb940259097d627855f78ca", "value": "model.safetensors: 100%"}}, "7fc05dd9967a48a0a65a240962e852ee": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1da59909abea42fda512ac0de9ed886b", "max": 90868376, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_017722a85da9449cbd00883ac0e8b361", "value": 90868376}}, "cfde7e7f488b4526aeb598455848a39d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7cfba6efb52c462b96288f282fffcd46", "placeholder": "​", "style": "IPY_MODEL_858315cc3a054bc3b78c27ab43d098b3", "value": " 90.9M/90.9M [00:00&lt;00:00, 129MB/s]"}}, "2b9cef22034c4c4796016dbeb3d74e20": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b4269a44cf894b60904f40be5acc309d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5ce7264b8bb940259097d627855f78ca": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1da59909abea42fda512ac0de9ed886b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "017722a85da9449cbd00883ac0e8b361": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7cfba6efb52c462b96288f282fffcd46": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "858315cc3a054bc3b78c27ab43d098b3": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f8dbbef33cd4400696c3b313a02f8cc9": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a7521cfb4bdb4b4a90de1dd94dad4212", "IPY_MODEL_b39df2eb5e49478d81d98b4f9275797d", "IPY_MODEL_7bdf4cd578204d54966776289af909ca"], "layout": "IPY_MODEL_119a7f6784d8467caaa81909ee69b0ed"}}, "a7521cfb4bdb4b4a90de1dd94dad4212": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6c9229f5b7ee41a5a9518b309b49b3c8", "placeholder": "​", "style": "IPY_MODEL_3736deb780654082a1770734de624376", "value": "tokenizer_config.json: 100%"}}, "b39df2eb5e49478d81d98b4f9275797d": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c04106dd69284604836728cf18560dc3", "max": 350, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b3c4477a81d44c1f9aacbff038d15c22", "value": 350}}, "7bdf4cd578204d54966776289af909ca": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e6135039ead145a7aa877c1ad98ae415", "placeholder": "​", "style": "IPY_MODEL_823094962afa4cb5b15325cea704d6c3", "value": " 350/350 [00:00&lt;00:00, 18.9kB/s]"}}, "119a7f6784d8467caaa81909ee69b0ed": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6c9229f5b7ee41a5a9518b309b49b3c8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3736deb780654082a1770734de624376": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c04106dd69284604836728cf18560dc3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b3c4477a81d44c1f9aacbff038d15c22": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e6135039ead145a7aa877c1ad98ae415": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "823094962afa4cb5b15325cea704d6c3": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cbcd8ceb74014435acd8d38b92523401": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_64be7a4121844caa8a678b61486c04b6", "IPY_MODEL_28ca95515be545e3b4d6ac6d38322e07", "IPY_MODEL_855b13677c90419bbf4ccafd6d65fad0"], "layout": "IPY_MODEL_2db57da9a47d4b3fbb2ef2cd78e465eb"}}, "64be7a4121844caa8a678b61486c04b6": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ba8941b16628445dacf42bc67611dcc6", "placeholder": "​", "style": "IPY_MODEL_209c6aea6fc44777ae575d3395c9d3ac", "value": "vocab.txt: 100%"}}, "28ca95515be545e3b4d6ac6d38322e07": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ff672cfe38c24bd2bcbc000f19bc8dcb", "max": 231508, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9caccaa9a5aa42be86e1ca4042b28d13", "value": 231508}}, "855b13677c90419bbf4ccafd6d65fad0": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_374cf30f14454c23bd77699c0a453ab0", "placeholder": "​", "style": "IPY_MODEL_d9b3d940f5924144b33670111e7480a4", "value": " 232k/232k [00:00&lt;00:00, 4.16MB/s]"}}, "2db57da9a47d4b3fbb2ef2cd78e465eb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ba8941b16628445dacf42bc67611dcc6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "209c6aea6fc44777ae575d3395c9d3ac": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ff672cfe38c24bd2bcbc000f19bc8dcb": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9caccaa9a5aa42be86e1ca4042b28d13": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "374cf30f14454c23bd77699c0a453ab0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d9b3d940f5924144b33670111e7480a4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "546dd878b972440196d946114b64b738": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3b7010ec884f4995b4ea42c42b89d135", "IPY_MODEL_1f72e988d2724ac0919e3700cb55849a", "IPY_MODEL_2f69c775bd34472caf395056367e7cd5"], "layout": "IPY_MODEL_611c4e0cf8fb4f6f8ad82462a8bf1a35"}}, "3b7010ec884f4995b4ea42c42b89d135": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_588b91319fde4044835528a94bb6c791", "placeholder": "​", "style": "IPY_MODEL_b66b87eef31442429e446feefec656ea", "value": "tokenizer.json: 100%"}}, "1f72e988d2724ac0919e3700cb55849a": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_182ca926974f4031b9b04ab809ecbfee", "max": 466247, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_96fbc3915d6f485c8868769c8ee31fc1", "value": 466247}}, "2f69c775bd34472caf395056367e7cd5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7a5f9d671f0d47cea4aa5564dd526e24", "placeholder": "​", "style": "IPY_MODEL_9ab53de6d503476fa68d6e9844998818", "value": " 466k/466k [00:00&lt;00:00, 16.8MB/s]"}}, "611c4e0cf8fb4f6f8ad82462a8bf1a35": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "588b91319fde4044835528a94bb6c791": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b66b87eef31442429e446feefec656ea": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "182ca926974f4031b9b04ab809ecbfee": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "96fbc3915d6f485c8868769c8ee31fc1": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7a5f9d671f0d47cea4aa5564dd526e24": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9ab53de6d503476fa68d6e9844998818": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a9987ab38da34b20aebb91608d7cd2db": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_92f46d85a65e4d71b776ae5b2d89af55", "IPY_MODEL_289e984f642d4b5bbf5d9280fa6a3d04", "IPY_MODEL_114367b385804397a664688f464be1a4"], "layout": "IPY_MODEL_dacaddae349b40329881cb45bb51c89f"}}, "92f46d85a65e4d71b776ae5b2d89af55": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_24fe7e32edf14a0facb9da87e0ea9a75", "placeholder": "​", "style": "IPY_MODEL_33fba48158584e0399894f78968702c4", "value": "special_tokens_map.json: 100%"}}, "289e984f642d4b5bbf5d9280fa6a3d04": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_60f8d2a7971649a991b9332f47e6b5ed", "max": 112, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b61adee1c04448deb94e0717f39fadbf", "value": 112}}, "114367b385804397a664688f464be1a4": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3cedea4b5dd84366af266f2f650facfa", "placeholder": "​", "style": "IPY_MODEL_bbf172c075904224b9737a0c432da204", "value": " 112/112 [00:00&lt;00:00, 6.74kB/s]"}}, "dacaddae349b40329881cb45bb51c89f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "24fe7e32edf14a0facb9da87e0ea9a75": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "33fba48158584e0399894f78968702c4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "60f8d2a7971649a991b9332f47e6b5ed": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b61adee1c04448deb94e0717f39fadbf": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3cedea4b5dd84366af266f2f650facfa": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bbf172c075904224b9737a0c432da204": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3b0733de13234274860e098c232aaa80": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_197dce071e214777b650e0e08b13d913", "IPY_MODEL_d1311e527c904412ad78dfe815d88dd9", "IPY_MODEL_6aeb2ad17dce480aac8c070e5ff53488"], "layout": "IPY_MODEL_d7ce8f8bd1894ef78c70870c83d8bdcc"}}, "197dce071e214777b650e0e08b13d913": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_070fbb01cdc94656b2f63dc94cdfe556", "placeholder": "​", "style": "IPY_MODEL_0d6a26f7796c4e0e8366d6184a179dd3", "value": "config.json: 100%"}}, "d1311e527c904412ad78dfe815d88dd9": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5f19a6c417024b57bbd1538062d9a96f", "max": 190, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_cbec3f9e41ab403f99ad955a9fa58237", "value": 190}}, "6aeb2ad17dce480aac8c070e5ff53488": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_56edaec14f4f4289a13e9c5490ea5d75", "placeholder": "​", "style": "IPY_MODEL_c38efa55d22047e5b206ce0d8ddbd8fd", "value": " 190/190 [00:00&lt;00:00, 15.3kB/s]"}}, "d7ce8f8bd1894ef78c70870c83d8bdcc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "070fbb01cdc94656b2f63dc94cdfe556": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0d6a26f7796c4e0e8366d6184a179dd3": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5f19a6c417024b57bbd1538062d9a96f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cbec3f9e41ab403f99ad955a9fa58237": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "56edaec14f4f4289a13e9c5490ea5d75": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c38efa55d22047e5b206ce0d8ddbd8fd": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1ba805cce304437f8672343551b8d6b6": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_494975b04e48493d9bbb7ec17bf4e4c7", "IPY_MODEL_20036ad9d8d34a3c8359ba28c4223bbd", "IPY_MODEL_aed2314a879a4085871b1ea02538f403"], "layout": "IPY_MODEL_ed7207603d424b6fb6190ac4c80cb70b"}}, "494975b04e48493d9bbb7ec17bf4e4c7": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_31869fe2f4004abdb0aa3dc2838bae1b", "placeholder": "​", "style": "IPY_MODEL_54ce988c3f1b4ede91c29082cf4eff00", "value": "config.json: 100%"}}, "20036ad9d8d34a3c8359ba28c4223bbd": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fb1d14b0af0f4f65a637b9af616dfd5a", "max": 570, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4d3732e37b2c44deb12cb88ee24886e7", "value": 570}}, "aed2314a879a4085871b1ea02538f403": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9c62b09ac1054dec95cdd391fe952eb2", "placeholder": "​", "style": "IPY_MODEL_e81296033f4842a6ab70dd96e38f4179", "value": " 570/570 [00:00&lt;00:00, 29.9kB/s]"}}, "ed7207603d424b6fb6190ac4c80cb70b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "31869fe2f4004abdb0aa3dc2838bae1b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "54ce988c3f1b4ede91c29082cf4eff00": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fb1d14b0af0f4f65a637b9af616dfd5a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4d3732e37b2c44deb12cb88ee24886e7": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9c62b09ac1054dec95cdd391fe952eb2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e81296033f4842a6ab70dd96e38f4179": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6d7c7eeab9064c958dab97edebe344dc": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6ea2487f84bd47b398ec809c85be3574", "IPY_MODEL_783a438233a44508bddece825527dffe", "IPY_MODEL_0cd008957fdc4c17a5179a4bc05bcd29"], "layout": "IPY_MODEL_c0e2fccec2d045db8bc5361931c59755"}}, "6ea2487f84bd47b398ec809c85be3574": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_30909253f0b142eb8da5afd2cfee198c", "placeholder": "​", "style": "IPY_MODEL_16e5ab743a4348a692648433dcf96284", "value": "model.safetensors: 100%"}}, "783a438233a44508bddece825527dffe": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_560e61f266ec4fc1a543e739243ad2e9", "max": 440449768, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c3ae5248af574ee881e908122d1387f6", "value": 440449768}}, "0cd008957fdc4c17a5179a4bc05bcd29": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e01f1fbf9fdb4ee0b3624c4ffb75450a", "placeholder": "​", "style": "IPY_MODEL_74235904c0b24cab8c932c193f446edd", "value": " 440M/440M [00:02&lt;00:00, 173MB/s]"}}, "c0e2fccec2d045db8bc5361931c59755": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "30909253f0b142eb8da5afd2cfee198c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "16e5ab743a4348a692648433dcf96284": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "560e61f266ec4fc1a543e739243ad2e9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c3ae5248af574ee881e908122d1387f6": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e01f1fbf9fdb4ee0b3624c4ffb75450a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "74235904c0b24cab8c932c193f446edd": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "605663bf44914fc499bcd5984a8d99ff": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_91f17675c036444f91e2291aedf247f6", "IPY_MODEL_40d93d19cf9c49cf826e941199c4c029", "IPY_MODEL_53f59550df2a44768d8d6d217b986c75"], "layout": "IPY_MODEL_6d95caffe75742ec95e0cd61a9c63a69"}}, "91f17675c036444f91e2291aedf247f6": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_609a5939ce184cd0b844dc6fead21820", "placeholder": "​", "style": "IPY_MODEL_37d5f658adee4d0fb592221c037b0f1e", "value": "tokenizer_config.json: 100%"}}, "40d93d19cf9c49cf826e941199c4c029": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ff1d7a236f4a464592cd5d3c3b87e57f", "max": 48, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bc3b210d47bd4c52a6f499a5ffdc1ca5", "value": 48}}, "53f59550df2a44768d8d6d217b986c75": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aa49b2c5422345d2846583e8dbef242f", "placeholder": "​", "style": "IPY_MODEL_0169057c940e43eda7d70dd0fba5a2f0", "value": " 48.0/48.0 [00:00&lt;00:00, 2.76kB/s]"}}, "6d95caffe75742ec95e0cd61a9c63a69": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "609a5939ce184cd0b844dc6fead21820": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "37d5f658adee4d0fb592221c037b0f1e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ff1d7a236f4a464592cd5d3c3b87e57f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bc3b210d47bd4c52a6f499a5ffdc1ca5": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "aa49b2c5422345d2846583e8dbef242f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0169057c940e43eda7d70dd0fba5a2f0": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "491b0f3bcc9c4bef9903c4b33a1fd9f9": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4c18ee976bf34a3b9ae02a6759361016", "IPY_MODEL_b1839f003c464f0eb910620b2c034198", "IPY_MODEL_9534ade484774ab7a7f38427cdaf2939"], "layout": "IPY_MODEL_f8e32c094537485bad80f770b93559b2"}}, "4c18ee976bf34a3b9ae02a6759361016": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_074fa95576964a52a96473328f000b8f", "placeholder": "​", "style": "IPY_MODEL_62c8e8c130024a61a27415d00432412a", "value": "vocab.txt: 100%"}}, "b1839f003c464f0eb910620b2c034198": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7698ac94c994482fa70f73f50e01d99c", "max": 231508, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f65e307c149b4a1a84e20a46162c4e95", "value": 231508}}, "9534ade484774ab7a7f38427cdaf2939": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e764eda692af4f6db65ad2d393aed2a6", "placeholder": "​", "style": "IPY_MODEL_8343e3a915b14df9a357074306ecea6b", "value": " 232k/232k [00:00&lt;00:00, 14.9MB/s]"}}, "f8e32c094537485bad80f770b93559b2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "074fa95576964a52a96473328f000b8f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "62c8e8c130024a61a27415d00432412a": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7698ac94c994482fa70f73f50e01d99c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f65e307c149b4a1a84e20a46162c4e95": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e764eda692af4f6db65ad2d393aed2a6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8343e3a915b14df9a357074306ecea6b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "346099a0cc7f4deb892bed1d9c5272e2": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f89fbb9917aa4d12bb1ef222de98a6e1", "IPY_MODEL_c69557b183f34668ab761e50c2778d5f", "IPY_MODEL_1d6610c21fc64d49ad7445929c3e9f33"], "layout": "IPY_MODEL_8f6a86871a124b3c84a53d5396515ca5"}}, "f89fbb9917aa4d12bb1ef222de98a6e1": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f650c75091364fbebfbc8666d44f0616", "placeholder": "​", "style": "IPY_MODEL_cb500a66114742638c16bb4d8d562daa", "value": "tokenizer.json: 100%"}}, "c69557b183f34668ab761e50c2778d5f": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c8dc6021bd8c412280d232216756f771", "max": 466062, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e6d7779904b0405da76e607169f155f4", "value": 466062}}, "1d6610c21fc64d49ad7445929c3e9f33": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2587a58298484e9c8f31bd8a269a426f", "placeholder": "​", "style": "IPY_MODEL_911d3568a4a441c88fa63b6b81507b84", "value": " 466k/466k [00:00&lt;00:00, 20.3MB/s]"}}, "8f6a86871a124b3c84a53d5396515ca5": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f650c75091364fbebfbc8666d44f0616": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cb500a66114742638c16bb4d8d562daa": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c8dc6021bd8c412280d232216756f771": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e6d7779904b0405da76e607169f155f4": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2587a58298484e9c8f31bd8a269a426f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "911d3568a4a441c88fa63b6b81507b84": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7fcb64bebd2348e197e88a9340e4e378": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5bf92edb53c54453bb5966ae5945e4e5", "IPY_MODEL_4f4920af94f7479983096731a7d7c8b7", "IPY_MODEL_735161b25207444fad64764bd81f0145"], "layout": "IPY_MODEL_6871cd30b2f24d378a051aac6350f72b"}}, "5bf92edb53c54453bb5966ae5945e4e5": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d8cd1d5054b643ca9336868f6e30ac4e", "placeholder": "​", "style": "IPY_MODEL_62eced6b279d45d9b69a3a37eb660a2d", "value": "Downloading readme: 100%"}}, "4f4920af94f7479983096731a7d7c8b7": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fb8ae4157155433dad6824d1c66081d9", "max": 6266, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_cd120a6c4fbd4df79c1fd195867331bb", "value": 6266}}, "735161b25207444fad64764bd81f0145": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a1bfa1b543d448a1976f8f6db82fb89e", "placeholder": "​", "style": "IPY_MODEL_84b48b03e9ef405e98e6fbca919f93bb", "value": " 6.27k/6.27k [00:00&lt;00:00, 547kB/s]"}}, "6871cd30b2f24d378a051aac6350f72b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d8cd1d5054b643ca9336868f6e30ac4e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "62eced6b279d45d9b69a3a37eb660a2d": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fb8ae4157155433dad6824d1c66081d9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cd120a6c4fbd4df79c1fd195867331bb": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a1bfa1b543d448a1976f8f6db82fb89e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "84b48b03e9ef405e98e6fbca919f93bb": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "171a1238d676470ab72cebbeef422fe8": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1e34b0c0921a4d7fb74cf5d6a6b3b2d8", "IPY_MODEL_123b27a595a6495690d8b6dd4570bd62", "IPY_MODEL_983f9e26676c465883ea326309f1b82e"], "layout": "IPY_MODEL_95dd2a4b78bf4c6dbd8c38d61725c8d9"}}, "1e34b0c0921a4d7fb74cf5d6a6b3b2d8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8777b9a7aaa54638a5e53663d03cd068", "placeholder": "​", "style": "IPY_MODEL_069c39817db547d3b939d5254586480f", "value": "Downloading data files: 100%"}}, "123b27a595a6495690d8b6dd4570bd62": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_efc96cae9da147efbce80ef186cee8bf", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_67d5138af449451e8f08510a15b760c1", "value": 1}}, "983f9e26676c465883ea326309f1b82e": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c3330832181a4697b49ac6bb369b55e1", "placeholder": "​", "style": "IPY_MODEL_0b5e3d75761b42c2979d778aa38af468", "value": " 1/1 [00:01&lt;00:00,  1.94s/it]"}}, "95dd2a4b78bf4c6dbd8c38d61725c8d9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8777b9a7aaa54638a5e53663d03cd068": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "069c39817db547d3b939d5254586480f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "efc96cae9da147efbce80ef186cee8bf": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "67d5138af449451e8f08510a15b760c1": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c3330832181a4697b49ac6bb369b55e1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0b5e3d75761b42c2979d778aa38af468": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6515c6e4dda145d790805e85633f5897": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_512ba5eadae5475ba4ee3e85fec249c3", "IPY_MODEL_a70a306b186d4f3582b5bcf1471fabe0", "IPY_MODEL_8de8c60063d2482dbd9a3d4cd8a4646e"], "layout": "IPY_MODEL_03d02d74cded4bc88d01ca119fd81f11"}}, "512ba5eadae5475ba4ee3e85fec249c3": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d9fe7ef5e07242a493a9051886929b56", "placeholder": "​", "style": "IPY_MODEL_da756729693f44dc9f68aec0e420d616", "value": "Downloading data: 100%"}}, "a70a306b186d4f3582b5bcf1471fabe0": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6c20cf22716244d7b96633dfa6df69c9", "max": 183431436, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5dc7bccd0c9143769557a06d5b088ce7", "value": 183431436}}, "8de8c60063d2482dbd9a3d4cd8a4646e": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3fa40891365e4f88b93717bffc7b9fe2", "placeholder": "​", "style": "IPY_MODEL_5aa67343a7464ef8a97b3a8dab363aec", "value": " 183M/183M [00:01&lt;00:00, 116MB/s]"}}, "03d02d74cded4bc88d01ca119fd81f11": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d9fe7ef5e07242a493a9051886929b56": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "da756729693f44dc9f68aec0e420d616": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6c20cf22716244d7b96633dfa6df69c9": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5dc7bccd0c9143769557a06d5b088ce7": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3fa40891365e4f88b93717bffc7b9fe2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5aa67343a7464ef8a97b3a8dab363aec": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "67eb038c161242aca06907e42f0ef049": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9b913dc573fe4bab9e46eb0652e56ac4", "IPY_MODEL_1bc1a943e37841a9ba7bf50f80fe6a8f", "IPY_MODEL_ca9117dfc2cc483d8e579d2d31549ab8"], "layout": "IPY_MODEL_c6615d64c6ca4d28be799c89226bc783"}}, "9b913dc573fe4bab9e46eb0652e56ac4": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6f23d80329354dd9aa1362fd18ade1a3", "placeholder": "​", "style": "IPY_MODEL_a5558de781874dc5b91464d1770f9e8e", "value": "Extracting data files: 100%"}}, "1bc1a943e37841a9ba7bf50f80fe6a8f": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3663f56de560441da34e2bcc659792c2", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d6f4295082a9430f9dc91f29f6833baf", "value": 1}}, "ca9117dfc2cc483d8e579d2d31549ab8": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ff7bc214520d4d089a7b03141f3f47ac", "placeholder": "​", "style": "IPY_MODEL_7e90eb687d7b41eda6ad7078a5f1f45f", "value": " 1/1 [00:00&lt;00:00, 42.23it/s]"}}, "c6615d64c6ca4d28be799c89226bc783": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6f23d80329354dd9aa1362fd18ade1a3": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a5558de781874dc5b91464d1770f9e8e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3663f56de560441da34e2bcc659792c2": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d6f4295082a9430f9dc91f29f6833baf": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ff7bc214520d4d089a7b03141f3f47ac": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7e90eb687d7b41eda6ad7078a5f1f45f": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ed94e034465b447f93caff492902b43f": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a755ee87ed64417fbf4118033a061e74", "IPY_MODEL_7452921620864826ac4628408d6d619c", "IPY_MODEL_2771761e002542f28f230e9659cec1b7"], "layout": "IPY_MODEL_d0f3410ac7bb42bd98b617830a43ba3f"}}, "a755ee87ed64417fbf4118033a061e74": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_018c52af36a94996bd83db63f2c64e8b", "placeholder": "​", "style": "IPY_MODEL_463ef8a5cc2a42d6aa24a43ca49bf607", "value": "Generating train split: "}}, "7452921620864826ac4628408d6d619c": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3f807f51af1b4490aaecf83e3beba6fa", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1573caf071044334a387869b38fb3150", "value": 1}}, "2771761e002542f28f230e9659cec1b7": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c96922bdf6cb460191e721f78a1741fc", "placeholder": "​", "style": "IPY_MODEL_122f63abe846470483e17cb763909a56", "value": " 101762/0 [00:01&lt;00:00, 65929.83 examples/s]"}}, "d0f3410ac7bb42bd98b617830a43ba3f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "018c52af36a94996bd83db63f2c64e8b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "463ef8a5cc2a42d6aa24a43ca49bf607": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3f807f51af1b4490aaecf83e3beba6fa": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "1573caf071044334a387869b38fb3150": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c96922bdf6cb460191e721f78a1741fc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "122f63abe846470483e17cb763909a56": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "nbformat": 4, "nbformat_minor": 0}