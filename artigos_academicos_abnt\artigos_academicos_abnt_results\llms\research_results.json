[{"url": "http://arxiv.org/abs/2412.18022v1", "title": "Trustworthy and Efficient LLMs Meet Databases", "description": "In the rapidly evolving AI era with large language models (LLMs) at the core, making LLMs more trustworthy and efficient, especially in output generation (inference), has gained significant attention. This is to reduce plausible but faulty LLM outputs (a.k.a hallucinations) and meet the highly increased inference demands. This tutorial explores such efforts and makes them transparent to the database community. Understanding these efforts is essential in harnessing LLMs in database tasks and adapting database techniques to LLMs. Furthermore, we delve into the synergy between LLMs and databases, highlighting new opportunities and challenges in their intersection. This tutorial aims to share with database researchers and practitioners essential concepts and strategies around LLMs, reduce the unfamiliarity of LLMs, and inspire joining in the intersection between LLMs and databases.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "published": "2024-12-23", "pdf_url": "http://arxiv.org/pdf/2412.18022v1", "categories": ["cs.<PERSON>", "cs.AI"]}, {"url": "http://arxiv.org/abs/2406.10300v1", "title": "Large Language Models as Software Components: A Taxonomy for LLM-Integrated Applications", "description": "Large Language Models (LLMs) have become widely adopted recently. Research explores their use both as autonomous agents and as tools for software engineering. LLM-integrated applications, on the other hand, are software systems that leverage an LLM to perform tasks that would otherwise be impossible or require significant coding effort. While LLM-integrated application engineering is emerging as new discipline, its terminology, concepts and methods need to be established. This study provides a taxonomy for LLM-integrated applications, offering a framework for analyzing and describing these systems. It also demonstrates various ways to utilize LLMs in applications, as well as options for implementing such integrations.   Following established methods, we analyze a sample of recent LLM-integrated applications to identify relevant dimensions. We evaluate the taxonomy by applying it to additional cases. This review shows that applications integrate LLMs in numerous ways for various purp...", "authors": ["<PERSON>"], "published": "2024-06-13", "pdf_url": "http://arxiv.org/pdf/2406.10300v1", "categories": ["cs.SE", "cs.CL", "cs.LG", "A.1; I.2.7; D.2.11"]}, {"url": "http://arxiv.org/abs/2405.19888v1", "title": "Parrot: Efficient Serving of LLM-based Applications with Semantic Variable", "description": "The rise of large language models (LLMs) has enabled LLM-based applications (a.k.a. AI agents or co-pilots), a new software paradigm that combines the strength of LLM and conventional software. Diverse LLM applications from different tenants could design complex workflows using multiple LLM requests to accomplish one task. However, they have to use the over-simplified request-level API provided by today's public LLM services, losing essential application-level information. Public LLM services have to blindly optimize individual LLM requests, leading to sub-optimal end-to-end performance of LLM applications.   This paper introduces Parrot, an LLM service system that focuses on the end-to-end experience of LLM-based applications. <PERSON><PERSON><PERSON> proposes Semantic Variable, a unified abstraction to expose application-level knowledge to public LLM services. A Semantic Variable annotates an input/output variable in the prompt of a request, and creates the data pipeline when connecting multiple LL...", "authors": ["<PERSON><PERSON>", "Zhenhua Han", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Yang", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "published": "2024-05-30", "pdf_url": "http://arxiv.org/pdf/2405.19888v1", "categories": ["cs.LG", "cs.AI"]}, {"url": "http://arxiv.org/abs/2411.15764v1", "title": "LLM Online Spatial-temporal Signal Reconstruction Under Noise", "description": "This work introduces the LLM Online Spatial-temporal Reconstruction (LLM-OSR) framework, which integrates Graph Signal Processing (GSP) and Large Language Models (LLMs) for online spatial-temporal signal reconstruction. The LLM-OSR utilizes a GSP-based spatial-temporal signal handler to enhance graph signals and employs LLMs to predict missing values based on spatiotemporal patterns. The performance of LLM-OSR is evaluated on traffic and meteorological datasets under varying Gaussian noise levels. Experimental results demonstrate that utilizing GPT-4-o mini within the LLM-OSR is accurate and robust under Gaussian noise conditions. The limitations are discussed along with future research insights, emphasizing the potential of combining GSP techniques with LLMs for solving spatiotemporal prediction tasks.", "authors": ["Yi Yan", "<PERSON><PERSON>", "Ercan Engin <PERSON>"], "published": "2024-11-24", "pdf_url": "http://arxiv.org/pdf/2411.15764v1", "categories": ["cs.LG", "eess.SP"]}, {"url": "http://arxiv.org/abs/2501.08579v1", "title": "What Limits LLM-based Human Simulation: LLMs or Our Design?", "description": "We argue that advancing LLM-based human simulation requires addressing both LLM's inherent limitations and simulation framework design challenges. Recent studies have revealed significant gaps between LLM-based human simulations and real-world observations, highlighting these dual challenges. To address these gaps, we present a comprehensive analysis of LLM limitations and our design issues, proposing targeted solutions for both aspects. Furthermore, we explore future directions that address both challenges simultaneously, particularly in data collection, LLM generation, and evaluation. To support further research in this field, we provide a curated collection of LLM-based human simulation resources.\\footnote{https://github.com/Persdre/llm-human-simulation}", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "published": "2025-01-15", "pdf_url": "http://arxiv.org/pdf/2501.08579v1", "categories": ["cs.CL"]}, {"url": "http://arxiv.org/abs/2311.10372v2", "title": "A Survey of Large Language Models for Code: Evolution, Benchmarking, and Future Trends", "description": "General large language models (LLMs), represented by ChatGPT, have demonstrated significant potential in tasks such as code generation in software engineering. This has led to the development of specialized LLMs for software engineering, known as Code LLMs. A considerable portion of Code LLMs is derived from general LLMs through model fine-tuning. As a result, Code LLMs are often updated frequently and their performance can be influenced by the base LLMs. However, there is currently a lack of systematic investigation into Code LLMs and their performance. In this study, we conduct a comprehensive survey and analysis of the types of Code LLMs and their differences in performance compared to general LLMs. We aim to address three questions: (1) What LLMs are specifically designed for software engineering tasks, and what is the relationship between these Code LLMs? (2) Do Code LLMs really outperform general LLMs in software engineering tasks? (3) Which LLMs are more proficient in differe...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Ming<PERSON> Ye", "<PERSON><PERSON>"], "published": "2023-11-17", "pdf_url": "http://arxiv.org/pdf/2311.10372v2", "categories": ["cs.SE"]}, {"url": "http://arxiv.org/abs/2412.07017v1", "title": "Asynchronous LLM Function Calling", "description": "Large language models (LLMs) use function calls to interface with external tools and data source. However, the current approach to LLM function calling is inherently synchronous, where each call blocks LLM inference, limiting LLM operation and concurrent function execution. In this work, we propose AsyncLM, a system for asynchronous LLM function calling. AsyncLM improves LLM's operational efficiency by enabling LLMs to generate and execute function calls concurrently. Instead of waiting for each call's completion, AsyncLM introduces an interrupt mechanism to asynchronously notify the LLM in-flight when function calls return. We design an in-context protocol for function calls and interrupts, provide fine-tuning strategy to adapt LLMs to the interrupt semantics, and implement these mechanisms efficiently on LLM inference process. We demonstrate that AsyncLM can reduce end-to-end task completion latency from 1.6x-5.4x compared to synchronous function calling on a set of benchmark task...", "authors": ["In Gim", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "published": "2024-12-09", "pdf_url": "http://arxiv.org/pdf/2412.07017v1", "categories": ["cs.CL", "cs.AI"]}, {"url": "http://arxiv.org/abs/2412.15487v2", "title": "Multi-LLM Text Summarization", "description": "In this work, we propose a Multi-LLM summarization framework, and investigate two different multi-LLM strategies including centralized and decentralized. Our multi-LLM summarization framework has two fundamentally important steps at each round of conversation: generation and evaluation. These steps are different depending on whether our multi-LLM decentralized summarization is used or centralized. In both our multi-LLM decentralized and centralized strategies, we have k different LLMs that generate diverse summaries of the text. However, during evaluation, our multi-LLM centralized summarization approach leverages a single LLM to evaluate the summaries and select the best one whereas k LLMs are used for decentralized multi-LLM summarization. Overall, we find that our multi-LLM summarization approaches significantly outperform the baselines that leverage only a single LLM by up to 3x. These results indicate the effectiveness of multi-LLM approaches for summarization.", "authors": ["Jiang<PERSON> Fang", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "published": "2024-12-20", "pdf_url": "http://arxiv.org/pdf/2412.15487v2", "categories": ["cs.CL"]}, {"url": "http://arxiv.org/abs/2404.14809v1", "title": "A Survey of Large Language Models on Generative Graph Analytics: Query, Learning, and Applications", "description": "A graph is a fundamental data model to represent various entities and their complex relationships in society and nature, such as social networks, transportation networks, financial networks, and biomedical systems. Recently, large language models (LLMs) have showcased a strong generalization ability to handle various NLP and multi-mode tasks to answer users' arbitrary questions and specific-domain content generation. Compared with graph learning models, LLMs enjoy superior advantages in addressing the challenges of generalizing graph tasks by eliminating the need for training graph learning models and reducing the cost of manual annotation. In this survey, we conduct a comprehensive investigation of existing LLM studies on graph data, which summarizes the relevant graph analytics tasks solved by advanced LLM models and points out the existing remaining challenges and future directions. Specifically, we study the key problems of LLM-based generative graph analytics (LLM-GGA) with thr...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2024-04-23", "pdf_url": "http://arxiv.org/pdf/2404.14809v1", "categories": ["cs.CL", "cs.AI", "cs.<PERSON>"]}, {"url": "http://arxiv.org/abs/2408.13006v2", "title": "Systematic Evaluation of LLM-as-a-Judge in LLM Alignment Tasks: Explainable Metrics and Diverse Prompt Templates", "description": "LLM-as-a-Judge has been widely applied to evaluate and compare different LLM alignmnet approaches (e.g., RLHF and DPO). However, concerns regarding its reliability have emerged, due to LLM judges' biases and inconsistent decision-making. Previous research has developed evaluation frameworks to assess reliability of LLM judges and their alignment with human preferences. However, the employed evaluation metrics often lack adequate explainability and fail to address LLM internal inconsistency. Additionally, existing studies inadequately explore the impact of various prompt templates when applying LLM-as-a-Judge methods, leading to potentially inconsistent comparisons between different alignment algorithms. In this work, we systematically evaluate LLM-as-a-Judge on alignment tasks by defining more theoretically interpretable evaluation metrics and explicitly mitigating LLM internal inconsistency from reliability metrics. We develop an open-source framework to evaluate, compare, and visu...", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Lin", "<PERSON>"], "published": "2024-08-23", "pdf_url": "http://arxiv.org/pdf/2408.13006v2", "categories": ["cs.CL"]}, {"url": "http://arxiv.org/abs/2308.08241v2", "title": "TEST: Text Prototype Aligned Embedding to Activate LLM's Ability for Time Series", "description": "This work summarizes two ways to accomplish Time-Series (TS) tasks in today's Large Language Model (LLM) context: LLM-for-TS (model-centric) designs and trains a fundamental large model, or fine-tunes a pre-trained LLM for TS data; TS-for-LLM (data-centric) converts TS into a model-friendly representation to enable the pre-trained LLM to handle TS data. Given the lack of data, limited resources, semantic context requirements, and so on, this work focuses on TS-for-LLM, where we aim to activate LLM's ability for TS data by designing a TS embedding method suitable for LLM. The proposed method is named TEST. It first tokenizes TS, builds an encoder to embed TS via instance-wise, feature-wise, and text-prototype-aligned contrast, where the TS embedding space is aligned to LLM embedding layer space, then creates soft prompts to make LLM more open to that embeddings, and finally implements TS tasks using the frozen LLM. We also demonstrate the feasibility of TS-for-LLM through theory and ...", "authors": ["Chenxi Sun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shen<PERSON> Hong"], "published": "2023-08-16", "pdf_url": "http://arxiv.org/pdf/2308.08241v2", "categories": ["cs.CL", "cs.AI"]}, {"url": "http://arxiv.org/abs/2408.02479v1", "title": "From LLMs to LLM-based Agents for Software Engineering: A Survey of Current, Challenges and Future", "description": "With the rise of large language models (LLMs), researchers are increasingly exploring their applications in var ious vertical domains, such as software engineering. LLMs have achieved remarkable success in areas including code generation and vulnerability detection. However, they also exhibit numerous limitations and shortcomings. LLM-based agents, a novel tech nology with the potential for Artificial General Intelligence (AGI), combine LLMs as the core for decision-making and action-taking, addressing some of the inherent limitations of LLMs such as lack of autonomy and self-improvement. Despite numerous studies and surveys exploring the possibility of using LLMs in software engineering, it lacks a clear distinction between LLMs and LLM based agents. It is still in its early stage for a unified standard and benchmarking to qualify an LLM solution as an LLM-based agent in its domain. In this survey, we broadly investigate the current practice and solutions for LLMs and LLM-based age...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Haipeng <PERSON>ai", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "published": "2024-08-05", "pdf_url": "http://arxiv.org/pdf/2408.02479v1", "categories": ["cs.SE", "cs.AI", "cs.CL"]}, {"url": "http://arxiv.org/abs/2411.01604v1", "title": "Large Language Model Supply Chain: Open Problems From the Security Perspective", "description": "Large Language Model (LLM) is changing the software development paradigm and has gained huge attention from both academia and industry. Researchers and developers collaboratively explore how to leverage the powerful problem-solving ability of LLMs for specific domain tasks. Due to the wide usage of LLM-based applications, e.g., ChatGPT, multiple works have been proposed to ensure the security of LLM systems. However, a comprehensive understanding of the entire processes of LLM system construction (the LLM supply chain) is crucial but relevant works are limited. More importantly, the security issues hidden in the LLM SC which could highly impact the reliable usage of LLMs are lack of exploration. Existing works mainly focus on assuring the quality of LLM from the model level, security assurance for the entire LLM SC is ignored. In this work, we take the first step to discuss the potential security risks in each component as well as the integration between components of LLM SC. We sum...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "published": "2024-11-03", "pdf_url": "http://arxiv.org/pdf/2411.01604v1", "categories": ["cs.CR", "cs.AI", "cs.SE"]}, {"url": "http://arxiv.org/abs/2501.19361v1", "title": "We're Different, We're the Same: Creative Homogeneity Across LLMs", "description": "Numerous powerful large language models (LLMs) are now available for use as writing support tools, idea generators, and beyond. Although these LLMs are marketed as helpful creative assistants, several works have shown that using an LLM as a creative partner results in a narrower set of creative outputs. However, these studies only consider the effects of interacting with a single LLM, begging the question of whether such narrowed creativity stems from using a particular LLM -- which arguably has a limited range of outputs -- or from using LLMs in general as creative assistants. To study this question, we elicit creative responses from humans and a broad set of LLMs using standardized creativity tests and compare the population-level diversity of responses. We find that LLM responses are much more similar to other LLM responses than human responses are to each other, even after controlling for response structure and other key variables. This finding of significant homogeneity in crea...", "authors": ["<PERSON>", "<PERSON><PERSON>"], "published": "2025-01-31", "pdf_url": "http://arxiv.org/pdf/2501.19361v1", "categories": ["cs.CY", "cs.AI", "cs.CL", "cs.LG"]}, {"url": "http://arxiv.org/abs/2503.12340v1", "title": "SVD-LLM V2: Optimizing Singular Value Truncation for Large Language Model Compression", "description": "Despite significant advancements, the practical deployment of Large Language Models (LLMs) is often hampered by their immense sizes, highlighting the need for effective compression techniques. Singular Value Decomposition (SVD) is a promising LLM compression technique. However, existing SVD-based compression methods fall short in reducing truncation losses, leading to less competitive performance in compressed models. In this work, we introduce SVD-LLM V2, a SVD-based LLM compression method that optimizes singular value truncation in SVD compression with two techniques. First, SVD-LLM V2 proposes to use theoretical truncation loss of weight matrices to assign a unique compression ratio to each weight matrix at different layers to accommodate weight redundancy heterogeneity. Second, SVD-LLM V2 proposes loss-optimized weight truncation to ensure that the truncated singular values result in a lower and more stable truncation loss in practice. We evaluate SVD-LLM V2 on ten datasets and ...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Zhongwei Wan", "<PERSON>", "<PERSON>"], "published": "2025-03-16", "pdf_url": "http://arxiv.org/pdf/2503.12340v1", "categories": ["cs.CL"]}]