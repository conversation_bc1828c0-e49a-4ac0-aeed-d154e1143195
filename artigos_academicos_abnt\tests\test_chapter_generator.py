"""
Script de teste para o gerador de capítulos de artigos acadêmicos ABNT.
"""

import os
import sys
import json
import unittest
from unittest.mock import MagicMock, patch

# Adicionar o diretório pai ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.chapter_generator import ChapterGenerator
from modules.chapter_manager import ChapterManager

class TestChapterGenerator(unittest.TestCase):
    """Testes para o gerador de capítulos"""

    def setUp(self):
        """Configuração inicial para os testes"""
        # Mock da API Groq
        self.api_key = "test_api_key"
        self.patcher = patch('modules.article_generator.Groq')
        self.mock_groq = self.patcher.start()
        
        # Mock da resposta da API
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = "Conteúdo de teste do capítulo"
        
        # Configurar o mock para retornar a resposta
        mock_client = MagicMock()
        mock_client.chat.completions.create.return_value = mock_response
        self.mock_groq.return_value = mock_client
        
        # Inicializar o gerador de capítulos com o mock
        self.chapter_generator = ChapterGenerator(api_key=self.api_key)
        
        # Mock do método _call_groq_api_with_retry
        self.chapter_generator.article_generator._call_groq_api_with_retry = MagicMock(return_value=mock_response)

    def tearDown(self):
        """Limpeza após os testes"""
        self.patcher.stop()

    def test_generate_chapter(self):
        """Teste da geração de capítulo"""
        # Dados de teste
        topic = "Inteligência Artificial"
        chapter_type = "Introdução"
        research_results = [
            {
                "title": "Artigo de teste",
                "authors": ["Autor 1", "Autor 2"],
                "published": "2023",
                "url": "http://example.com",
                "description": "Descrição de teste"
            }
        ]
        
        # Gerar capítulo
        chapter = self.chapter_generator.generate_chapter(
            topic=topic,
            chapter_type=chapter_type,
            research_results=research_results,
            word_count=500
        )
        
        # Verificar se o capítulo foi gerado
        self.assertEqual(chapter, "Conteúdo de teste do capítulo")
        
        # Verificar se o método _call_groq_api_with_retry foi chamado
        self.chapter_generator.article_generator._call_groq_api_with_retry.assert_called_once()

    def test_get_chapter_instructions(self):
        """Teste das instruções específicas para cada tipo de capítulo"""
        # Testar instruções para diferentes tipos de capítulos
        chapter_types = ["Introdução", "Metodologia", "Desenvolvimento", "Resultados", "Discussão", "Conclusão"]
        
        for chapter_type in chapter_types:
            instructions = self.chapter_generator._get_chapter_instructions(chapter_type, 500)
            self.assertIsInstance(instructions, str)
            self.assertIn("500", instructions)  # Deve conter a contagem de palavras
            self.assertIn(chapter_type, instructions)  # Deve mencionar o tipo de capítulo

    def test_polish_chapter(self):
        """Teste do polimento de capítulo"""
        # Dados de teste
        chapter_content = "Conteúdo de teste para polimento"
        chapter_type = "Introdução"
        
        # Polir capítulo
        polished_chapter = self.chapter_generator.polish_chapter(
            chapter_content=chapter_content,
            chapter_type=chapter_type
        )
        
        # Verificar se o capítulo foi polido
        self.assertEqual(polished_chapter, "Conteúdo de teste do capítulo")
        
        # Verificar se o método _call_groq_api_with_retry foi chamado
        self.chapter_generator.article_generator._call_groq_api_with_retry.assert_called_once()

class TestChapterManager(unittest.TestCase):
    """Testes para o gerenciador de capítulos"""

    def setUp(self):
        """Configuração inicial para os testes"""
        # Criar diretório temporário para testes
        self.test_dir = "test_chapters"
        os.makedirs(self.test_dir, exist_ok=True)
        
        # Inicializar o gerenciador de capítulos
        self.chapter_manager = ChapterManager(base_dir=self.test_dir)
        
        # Dados de teste
        self.article_id = "test_article"
        self.chapter_type = "Introdução"
        self.chapter_content = "Conteúdo de teste do capítulo"
        self.metadata = {"topic": "Teste", "word_count": 100}

    def tearDown(self):
        """Limpeza após os testes"""
        # Remover diretório de teste
        import shutil
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def test_save_and_get_chapter(self):
        """Teste de salvar e obter capítulo"""
        # Salvar capítulo
        chapter_path = self.chapter_manager.save_chapter(
            article_id=self.article_id,
            chapter_type=self.chapter_type,
            chapter_content=self.chapter_content,
            metadata=self.metadata
        )
        
        # Verificar se o arquivo foi criado
        self.assertTrue(os.path.exists(chapter_path))
        
        # Obter capítulo
        chapter = self.chapter_manager.get_chapter(
            article_id=self.article_id,
            chapter_type=self.chapter_type
        )
        
        # Verificar se o capítulo foi obtido corretamente
        self.assertIsNotNone(chapter)
        self.assertEqual(chapter["content"], self.chapter_content)
        self.assertEqual(chapter["metadata"]["topic"], self.metadata["topic"])
        self.assertEqual(chapter["metadata"]["word_count"], self.metadata["word_count"])

    def test_list_chapters(self):
        """Teste de listar capítulos"""
        # Salvar vários capítulos
        chapter_types = ["Resumo", "Introdução", "Metodologia"]
        
        for chapter_type in chapter_types:
            self.chapter_manager.save_chapter(
                article_id=self.article_id,
                chapter_type=chapter_type,
                chapter_content=f"Conteúdo de teste do capítulo {chapter_type}",
                metadata={"topic": "Teste", "word_count": 100}
            )
        
        # Listar capítulos
        chapters = self.chapter_manager.list_chapters(self.article_id)
        
        # Verificar se todos os capítulos foram listados
        self.assertEqual(len(chapters), len(chapter_types))
        
        # Verificar se os capítulos estão na ordem correta
        self.assertEqual(chapters[0]["type"], "Resumo")
        self.assertEqual(chapters[1]["type"], "Introdução")
        self.assertEqual(chapters[2]["type"], "Metodologia")

    def test_delete_chapter(self):
        """Teste de excluir capítulo"""
        # Salvar capítulo
        self.chapter_manager.save_chapter(
            article_id=self.article_id,
            chapter_type=self.chapter_type,
            chapter_content=self.chapter_content,
            metadata=self.metadata
        )
        
        # Verificar se o capítulo existe
        chapter = self.chapter_manager.get_chapter(
            article_id=self.article_id,
            chapter_type=self.chapter_type
        )
        self.assertIsNotNone(chapter)
        
        # Excluir capítulo
        success = self.chapter_manager.delete_chapter(
            article_id=self.article_id,
            chapter_type=self.chapter_type
        )
        
        # Verificar se a exclusão foi bem-sucedida
        self.assertTrue(success)
        
        # Verificar se o capítulo foi excluído
        chapter = self.chapter_manager.get_chapter(
            article_id=self.article_id,
            chapter_type=self.chapter_type
        )
        self.assertIsNone(chapter)

    def test_compile_article(self):
        """Teste de compilar artigo"""
        # Salvar vários capítulos
        chapter_types = ["Resumo", "Introdução", "Metodologia", "Conclusão"]
        
        for chapter_type in chapter_types:
            self.chapter_manager.save_chapter(
                article_id=self.article_id,
                chapter_type=chapter_type,
                chapter_content=f"## {chapter_type}\n\nConteúdo de teste do capítulo {chapter_type}",
                metadata={"topic": "Teste", "word_count": 100}
            )
        
        # Compilar artigo
        article_content = self.chapter_manager.compile_article(self.article_id)
        
        # Verificar se o artigo foi compilado corretamente
        self.assertIn("# Test Article", article_content)
        for chapter_type in chapter_types:
            self.assertIn(f"## {chapter_type}", article_content)
            self.assertIn(f"Conteúdo de teste do capítulo {chapter_type}", article_content)

if __name__ == "__main__":
    unittest.main()
