"""
Módulo para gerenciamento de capítulos de artigos acadêmicos ABNT.
"""

import os
import json
import logging
import shutil
from typing import List, Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class ChapterManager:
    """Classe para gerenciamento de capítulos de artigos acadêmicos"""

    def __init__(self, base_dir="artigos_academicos_abnt_results"):
        """
        Inicializa o gerenciador de capítulos.

        Args:
            base_dir (str): Diretório base para armazenar os artigos e capítulos.
        """
        self.base_dir = base_dir
        os.makedirs(base_dir, exist_ok=True)

    def save_chapter(self, article_id: str, chapter_type: str, chapter_content: str, 
                    metadata: Dict[str, Any] = None) -> str:
        """
        Salva um capítulo em um artigo existente.

        Args:
            article_id (str): ID do artigo (nome do diretório).
            chapter_type (str): Tipo de capítulo (introdução, metodologia, etc.).
            chapter_content (str): Conteúdo do capítulo.
            metadata (dict, optional): Metadados adicionais do capítulo.

        Returns:
            str: Caminho para o arquivo do capítulo salvo.
        """
        # Criar diretório do artigo se não existir
        article_dir = os.path.join(self.base_dir, article_id)
        os.makedirs(article_dir, exist_ok=True)
        
        # Criar diretório de capítulos se não existir
        chapters_dir = os.path.join(article_dir, "chapters")
        os.makedirs(chapters_dir, exist_ok=True)
        
        # Normalizar o nome do tipo de capítulo para uso em nome de arquivo
        chapter_file_name = chapter_type.lower().replace(" ", "_").replace(".", "")
        
        # Caminho para o arquivo do capítulo
        chapter_path = os.path.join(chapters_dir, f"{chapter_file_name}.txt")
        
        # Salvar o conteúdo do capítulo
        with open(chapter_path, "w", encoding="utf-8") as f:
            f.write(chapter_content)
        
        # Salvar metadados do capítulo
        if metadata is None:
            metadata = {}
        
        metadata.update({
            "type": chapter_type,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "word_count": len(chapter_content.split())
        })
        
        metadata_path = os.path.join(chapters_dir, f"{chapter_file_name}_metadata.json")
        with open(metadata_path, "w", encoding="utf-8") as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        return chapter_path

    def get_chapter(self, article_id: str, chapter_type: str) -> Optional[Dict[str, Any]]:
        """
        Obtém um capítulo de um artigo.

        Args:
            article_id (str): ID do artigo (nome do diretório).
            chapter_type (str): Tipo de capítulo.

        Returns:
            dict: Dicionário com o conteúdo e metadados do capítulo, ou None se não existir.
        """
        # Normalizar o nome do tipo de capítulo
        chapter_file_name = chapter_type.lower().replace(" ", "_").replace(".", "")
        
        # Caminhos para os arquivos
        chapters_dir = os.path.join(self.base_dir, article_id, "chapters")
        chapter_path = os.path.join(chapters_dir, f"{chapter_file_name}.txt")
        metadata_path = os.path.join(chapters_dir, f"{chapter_file_name}_metadata.json")
        
        # Verificar se os arquivos existem
        if not os.path.exists(chapter_path) or not os.path.exists(metadata_path):
            return None
        
        # Ler o conteúdo do capítulo
        with open(chapter_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Ler os metadados do capítulo
        with open(metadata_path, "r", encoding="utf-8") as f:
            metadata = json.load(f)
        
        return {
            "content": content,
            "metadata": metadata
        }

    def list_chapters(self, article_id: str) -> List[Dict[str, Any]]:
        """
        Lista todos os capítulos de um artigo.

        Args:
            article_id (str): ID do artigo (nome do diretório).

        Returns:
            list: Lista de dicionários com informações sobre os capítulos.
        """
        chapters = []
        chapters_dir = os.path.join(self.base_dir, article_id, "chapters")
        
        # Verificar se o diretório existe
        if not os.path.exists(chapters_dir):
            return chapters
        
        # Listar todos os arquivos de metadados
        metadata_files = [f for f in os.listdir(chapters_dir) if f.endswith("_metadata.json")]
        
        for metadata_file in metadata_files:
            # Caminho para o arquivo de metadados
            metadata_path = os.path.join(chapters_dir, metadata_file)
            
            # Extrair o nome do capítulo do nome do arquivo de metadados
            chapter_name = metadata_file.replace("_metadata.json", "")
            chapter_path = os.path.join(chapters_dir, f"{chapter_name}.txt")
            
            # Verificar se o arquivo do capítulo existe
            if not os.path.exists(chapter_path):
                continue
            
            # Ler os metadados
            with open(metadata_path, "r", encoding="utf-8") as f:
                metadata = json.load(f)
            
            # Adicionar à lista de capítulos
            chapters.append({
                "type": metadata.get("type", chapter_name),
                "file_name": chapter_name,
                "created_at": metadata.get("created_at", ""),
                "updated_at": metadata.get("updated_at", ""),
                "word_count": metadata.get("word_count", 0)
            })
        
        # Ordenar os capítulos pela ordem padrão de um artigo acadêmico
        chapter_order = {
            "resumo": 1,
            "abstract": 2,
            "introducao": 3,
            "metodologia": 4,
            "desenvolvimento": 5,
            "resultados": 6,
            "discussao": 7,
            "conclusao": 8
        }
        
        chapters.sort(key=lambda x: chapter_order.get(x["file_name"], 999))
        
        return chapters

    def delete_chapter(self, article_id: str, chapter_type: str) -> bool:
        """
        Exclui um capítulo de um artigo.

        Args:
            article_id (str): ID do artigo (nome do diretório).
            chapter_type (str): Tipo de capítulo.

        Returns:
            bool: True se o capítulo foi excluído com sucesso, False caso contrário.
        """
        # Normalizar o nome do tipo de capítulo
        chapter_file_name = chapter_type.lower().replace(" ", "_").replace(".", "")
        
        # Caminhos para os arquivos
        chapters_dir = os.path.join(self.base_dir, article_id, "chapters")
        chapter_path = os.path.join(chapters_dir, f"{chapter_file_name}.txt")
        metadata_path = os.path.join(chapters_dir, f"{chapter_file_name}_metadata.json")
        
        # Verificar se os arquivos existem
        if not os.path.exists(chapter_path) and not os.path.exists(metadata_path):
            return False
        
        # Excluir os arquivos
        try:
            if os.path.exists(chapter_path):
                os.remove(chapter_path)
            
            if os.path.exists(metadata_path):
                os.remove(metadata_path)
            
            return True
        except Exception as e:
            logger.error(f"Erro ao excluir capítulo: {str(e)}")
            return False

    def compile_article(self, article_id: str, chapter_order: List[str] = None) -> str:
        """
        Compila todos os capítulos de um artigo em um único documento.

        Args:
            article_id (str): ID do artigo (nome do diretório).
            chapter_order (list, optional): Ordem dos capítulos. Se não fornecida, usa a ordem padrão.

        Returns:
            str: Conteúdo do artigo compilado.
        """
        # Obter a lista de capítulos
        chapters = self.list_chapters(article_id)
        
        # Se não houver capítulos, retornar string vazia
        if not chapters:
            return ""
        
        # Se a ordem não for fornecida, usar a ordem padrão
        if chapter_order is None:
            chapter_order = [
                "resumo", "abstract", "introducao", "metodologia", 
                "desenvolvimento", "resultados", "discussao", "conclusao"
            ]
        
        # Criar um dicionário para mapear nome de arquivo para tipo de capítulo
        chapter_map = {chapter["file_name"]: chapter["type"] for chapter in chapters}
        
        # Compilar o artigo
        article_content = f"# {article_id.replace('_', ' ').title()}\n\n"
        
        for chapter_name in chapter_order:
            if chapter_name in chapter_map:
                chapter = self.get_chapter(article_id, chapter_map[chapter_name])
                if chapter:
                    article_content += chapter["content"] + "\n\n"
        
        # Adicionar referências bibliográficas se existirem
        references_path = os.path.join(self.base_dir, article_id, "references.txt")
        if os.path.exists(references_path):
            with open(references_path, "r", encoding="utf-8") as f:
                references = f.read()
            
            article_content += "## Referências Bibliográficas\n\n" + references
        
        return article_content

    def save_compiled_article(self, article_id: str, chapter_order: List[str] = None) -> str:
        """
        Compila e salva todos os capítulos de um artigo em um único arquivo.

        Args:
            article_id (str): ID do artigo (nome do diretório).
            chapter_order (list, optional): Ordem dos capítulos. Se não fornecida, usa a ordem padrão.

        Returns:
            str: Caminho para o arquivo do artigo compilado.
        """
        # Compilar o artigo
        article_content = self.compile_article(article_id, chapter_order)
        
        # Caminho para o arquivo do artigo
        article_path = os.path.join(self.base_dir, article_id, "article.txt")
        
        # Salvar o artigo
        with open(article_path, "w", encoding="utf-8") as f:
            f.write(article_content)
        
        return article_path

    def create_article_from_chapters(self, article_id: str, title: str, chapters: List[Dict[str, str]]) -> str:
        """
        Cria um novo artigo a partir de capítulos individuais.

        Args:
            article_id (str): ID do artigo (nome do diretório).
            title (str): Título do artigo.
            chapters (list): Lista de dicionários com os capítulos (tipo e conteúdo).

        Returns:
            str: Caminho para o diretório do artigo criado.
        """
        # Criar diretório do artigo
        article_dir = os.path.join(self.base_dir, article_id)
        os.makedirs(article_dir, exist_ok=True)
        
        # Criar diretório de capítulos
        chapters_dir = os.path.join(article_dir, "chapters")
        os.makedirs(chapters_dir, exist_ok=True)
        
        # Salvar cada capítulo
        for chapter in chapters:
            self.save_chapter(
                article_id=article_id,
                chapter_type=chapter["type"],
                chapter_content=chapter["content"],
                metadata={"title": title}
            )
        
        # Compilar e salvar o artigo
        self.save_compiled_article(article_id)
        
        return article_dir

    def update_chapter(self, article_id: str, chapter_type: str, new_content: str) -> bool:
        """
        Atualiza o conteúdo de um capítulo existente.

        Args:
            article_id (str): ID do artigo (nome do diretório).
            chapter_type (str): Tipo de capítulo.
            new_content (str): Novo conteúdo do capítulo.

        Returns:
            bool: True se o capítulo foi atualizado com sucesso, False caso contrário.
        """
        # Obter o capítulo existente
        chapter = self.get_chapter(article_id, chapter_type)
        if not chapter:
            return False
        
        # Atualizar o conteúdo e os metadados
        metadata = chapter["metadata"]
        metadata["updated_at"] = datetime.now().isoformat()
        metadata["word_count"] = len(new_content.split())
        
        # Salvar o capítulo atualizado
        self.save_chapter(
            article_id=article_id,
            chapter_type=chapter_type,
            chapter_content=new_content,
            metadata=metadata
        )
        
        return True

    def clone_chapter(self, source_article_id: str, target_article_id: str, chapter_type: str) -> bool:
        """
        Clona um capítulo de um artigo para outro.

        Args:
            source_article_id (str): ID do artigo de origem.
            target_article_id (str): ID do artigo de destino.
            chapter_type (str): Tipo de capítulo a ser clonado.

        Returns:
            bool: True se o capítulo foi clonado com sucesso, False caso contrário.
        """
        # Obter o capítulo de origem
        chapter = self.get_chapter(source_article_id, chapter_type)
        if not chapter:
            return False
        
        # Salvar o capítulo no artigo de destino
        self.save_chapter(
            article_id=target_article_id,
            chapter_type=chapter_type,
            chapter_content=chapter["content"],
            metadata=chapter["metadata"]
        )
        
        return True
