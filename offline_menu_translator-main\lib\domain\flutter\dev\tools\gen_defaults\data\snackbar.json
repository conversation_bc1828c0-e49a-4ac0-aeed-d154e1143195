{"version": "6_1_0", "md.comp.snackbar.action.focus.label-text.color": "inversePrimary", "md.comp.snackbar.action.focus.state-layer.color": "inversePrimary", "md.comp.snackbar.action.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.snackbar.action.hover.label-text.color": "inversePrimary", "md.comp.snackbar.action.hover.state-layer.color": "inversePrimary", "md.comp.snackbar.action.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.snackbar.action.label-text.color": "inversePrimary", "md.comp.snackbar.action.label-text.text-style": "labelLarge", "md.comp.snackbar.action.pressed.label-text.color": "inversePrimary", "md.comp.snackbar.action.pressed.state-layer.color": "inversePrimary", "md.comp.snackbar.action.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.snackbar.container.color": "inverseSurface", "md.comp.snackbar.container.elevation": "md.sys.elevation.level3", "md.comp.snackbar.container.shadow-color": "shadow", "md.comp.snackbar.container.shape": "md.sys.shape.corner.extra-small", "md.comp.snackbar.icon.color": "onInverseSurface", "md.comp.snackbar.icon.focus.icon.color": "onInverseSurface", "md.comp.snackbar.icon.focus.state-layer.color": "onInverseSurface", "md.comp.snackbar.icon.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.snackbar.icon.hover.icon.color": "onInverseSurface", "md.comp.snackbar.icon.hover.state-layer.color": "onInverseSurface", "md.comp.snackbar.icon.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.snackbar.icon.pressed.icon.color": "onInverseSurface", "md.comp.snackbar.icon.pressed.state-layer.color": "onInverseSurface", "md.comp.snackbar.icon.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.snackbar.icon.size": 24.0, "md.comp.snackbar.supporting-text.color": "onInverseSurface", "md.comp.snackbar.supporting-text.text-style": "bodyMedium", "md.comp.snackbar.with-single-line.container.height": 48.0, "md.comp.snackbar.with-two-lines.container.height": 68.0}