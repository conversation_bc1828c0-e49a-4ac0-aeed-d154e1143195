#### Developed by [<PERSON><PERSON>](https://linkedin.com/in/tiwari-nitin), [<PERSON><PERSON>](https://linkedin.com/in/sagar0-0malhotra) and [<PERSON><PERSON>](https://x.com/sloathee).

# PaliGemma Android HF
This repository is an implementation of inferring the PaliGemma Vision Language Model on Android using Hugging Face-Gradio Client API for tasks such as zero-shot object detection, image captioning and visual question-answering.


## Pipeline:

![Logo](assets/paligemma_android_hf_pipeline.png)


## Demo Outputs:

**Visual question-answering, zero-shot object detection, image captioning**

![Logo](assets/paligemma-android-hf1.gif)


**Reference Expression Segmentation**

![Logo](assets/paligemma-android-segmentation.gif)



