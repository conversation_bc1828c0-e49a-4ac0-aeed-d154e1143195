/* Styles for handling custom code snippets */
.snippet-container {
  padding: 10px;
  overflow: auto;
}

.snippet-description {
  padding: 8px 40px 12px 8px;
}

.snippet-container pre {
  max-height: 500px;
  overflow: auto;
  padding: 10px;
  margin: 0px;
}

.snippet-container ::-webkit-scrollbar {
  width: 12px;
}

.snippet-container ::-webkit-scrollbar-thumb {
  width: 12px;
  border-radius: 6px;
}

.snippet {
  position: relative;
}

.snippet-buttons button {
  border-style: none;
  padding: 10px 24px;
  cursor: pointer;
  float: left;
}

.snippet-buttons:after {
  content: "";
  clear: both;
  display: table;
}

.snippet-buttons button:focus {
  outline: none;
}

.snippet-buttons button:hover {
  opacity: 1.0;
}

.snippet-buttons :not([selected]) {
  opacity: 0.65;
}

.snippet-buttons [selected] {
  opacity: 1.0;
}

.snippet-container [hidden] {
  display: none;
}

.snippet-create-command {
  text-align: end;
  font-size: smaller;
  font-style: normal;
  font-family: courier, lucidia;
}

.snippet-dartpad {
  width: 100%;
  height: 500px;
}

/* Styles for the deep-link anchor button */

.anchor-container {
  position: relative;
}

.anchor-button-overlay {
  position: absolute;
  top: 5px;
  right: 5px;
  height: 28px;
  width: 28px;
  transition: .3s ease;
}

.anchor-button {
  border-style: none;
  background: none;
  cursor: pointer;
}

.anchor-button :focus {
  outline: 0px;
}

.anchor-button :hover {
  transition: .3s ease;
  opacity: 0.65;
}

/* Styles for the copy-to-clipboard button */

.copyable-container {
  position: relative;
}

.copy-button-overlay {
  position: absolute;
  top: 10px;
  right: 14px;
  height: 28px;
  width: 28px;
  transition: .3s ease;
}

.copy-button {
  border-style: none;
  background: none;
  cursor: pointer;
}

.copy-button :focus {
  outline: 0px;
}

.copy-button :hover {
  transition: .3s ease;
  opacity: 1.0;
}

.copy-image {
  opacity: 0.65;
  font-size: 28px;
  padding-top: 4px;
  color: var(--main-hyperlinks-color);
}

/* Light/Dark theme modifications */

.dark-theme .snippet-buttons button {
  background-color: #133e59;
  color: rgb(149, 149, 149);
}

.light-theme .snippet-buttons button {
  background-color: #2372a3;
  color: white;
}

.light-theme .snippet-container {
  --main-hyperlinks-color: #005cc6;
}

.dark-theme .snippet-container {
  background-color: rgb(30, 40, 51);
}

.light-theme .snippet-container {
  background-color: rgb(215, 235, 252);
}