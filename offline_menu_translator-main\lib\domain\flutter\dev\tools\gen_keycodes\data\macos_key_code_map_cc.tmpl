// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#import <Cocoa/Cocoa.h>
#import <Foundation/Foundation.h>
#include "./KeyCodeMap_Internal.h"

// DO NOT EDIT -- DO NOT EDIT -- DO NOT EDIT
// This file is generated by
// flutter/flutter:dev/tools/gen_keycodes/bin/gen_keycodes.dart and should not
// be edited directly.
//
// Edit the template
// flutter/flutter:dev/tools/gen_keycodes/data/keyboard_map_macos_cc.tmpl
// instead.
//
// See flutter/flutter:dev/tools/gen_keycodes/README.md for more information.

namespace flutter {

@@@MASK_CONSTANTS@@@

const NSDictionary* keyCodeToPhysicalKey = @{
@@@MACOS_SCAN_CODE_MAP@@@
};

const NSDictionary* keyCodeToLogicalKey = @{
@@@MACOS_KEYCODE_LOGICAL_MAP@@@
};

const NSDictionary* keyCodeToModifierFlag = @{
@@@KEYCODE_TO_MODIFIER_FLAG_MAP@@@
};

const NSDictionary* modifierFlagToKeyCode = @{
@@@MODIFIER_FLAG_TO_KEYCODE_MAP@@@
};

@@@SPECIAL_KEY_CONSTANTS@@@

const std::vector<LayoutGoal> kLayoutGoals = {
@@@LAYOUT_GOALS@@@
};

}  // namespace flutter
