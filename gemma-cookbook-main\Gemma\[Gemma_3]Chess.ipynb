{"cells": [{"cell_type": "markdown", "metadata": {"id": "ELAK3D1hLdI3"}, "source": ["# Gemma Chess: Discover a New Dimension!\n", "\n", "<table align=\"left\">\n", "  <td>\n", "    <a target=\"_blank\" href=\"https://colab.research.google.com/github/google-gemini/gemma-cookbook/blob/main/Gemma/[Gemma_3]Chess.ipynb\"><img src=\"https://www.tensorflow.org/images/colab_logo_32px.png\" />Run in Google Colab</a>\n", "  </td>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "f6-oFLQoJ-_w"}, "outputs": [], "source": ["!pip install gemma\n", "!pip install python-chess cairosvg"]}, {"cell_type": "markdown", "metadata": {"id": "ZsVR0WwVLbeb"}, "source": ["## <PERSON><PERSON> 3"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "bWuOztoZLPDv"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/root/.cache/kagglehub/models/google/gemma-3/flax/gemma3-12b-it/1\n", "CKPT_PATH: /root/.cache/kagglehub/models/google/gemma-3/flax/gemma3-12b-it/1/gemma3-12b-it\n", "TOKENIZER_PATH: /root/.cache/kagglehub/models/google/gemma-3/flax/gemma3-12b-it/1/tokenizer.model\n"]}], "source": ["import os\n", "from google.colab import userdata\n", "\n", "# Note: `userdata.get` is a Colab API. If you're not using Colab, set the env\n", "# vars as appropriate for your system.\n", "os.environ[\"KAGGLE_USERNAME\"] = userdata.get(\"KAGGLE_USERNAME\")\n", "os.environ[\"KAGGLE_KEY\"] = userdata.get(\"KAGGLE_KEY\")\n", "\n", "# Avoid memory fragmentation on JAX\n", "os.environ[\"XLA_PYTHON_CLIENT_MEM_FRACTION\"] = \"1.00\"\n", "\n", "import kagglehub\n", "\n", "GEMMA_VARIANT = \"gemma3-12b-it\"\n", "GEMMA_PATH = kagglehub.model_download(f'google/gemma-3/flax/{GEMMA_VARIANT}')\n", "print(GEMMA_PATH)\n", "CKPT_PATH = os.path.join(GEMMA_PATH, GEMMA_VARIANT)\n", "TOKENIZER_PATH = os.path.join(GEMMA_PATH, 'tokenizer.model')\n", "print('CKPT_PATH:', CKPT_PATH)\n", "print('TOKE<PERSON>ZER_PATH:', TOKENIZER_PATH)\n", "\n", "# Load model parameters and tokenizer\n", "from gemma import gm\n", "\n", "model = gm.nn.Gemma3_12B()\n", "params = gm.ckpts.load_params(CKPT_PATH)\n", "tokenizer = gm.text.Gemma3Tokenizer(TOKENIZER_PATH)\n", "\n", "chatbot = gm.text.ChatSampler(\n", "    model=model,\n", "    params=params,\n", "    multi_turn=False,\n", "    tokenizer=tokenizer,\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "YlbELEIuRgg8"}, "source": ["## Helper functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "UIxaX6WXRf6X"}, "outputs": [{"data": {"image/jpeg": "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\n", "image/png": "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\n", "text/plain": ["<PIL.PngImagePlugin.PngImageFile image mode=RGB size=390x390>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import chess\n", "import chess.svg\n", "\n", "board = chess.Board()\n", "\n", "from cairosvg import svg2png\n", "from io import BytesIO\n", "from PIL import Image\n", "\n", "def get_board_img(board):\n", "    out = BytesIO()\n", "    svg2png(chess.svg.board(board=board), write_to=out)\n", "    return Image.open(out)\n", "\n", "import re\n", "\n", "def extract_code_blocks(text):\n", "  \"\"\"\n", "  Extracts code blocks enclosed in triple backticks\n", "  \"\"\"\n", "  code_block_regex = r\"`(.*?)`\"\n", "  blocks = re.findall(code_block_regex, text, re.DOTALL)\n", "\n", "  cleaned_blocks = []\n", "  for block in blocks:\n", "    if len(block) <= 0:\n", "      continue\n", "\n", "    # Remove optional language specifier\n", "    clean_block = block.strip()\n", "    if '\\n' in clean_block:\n", "      clean_block = clean_block.split('\\n', 1)[1]\n", "    else:\n", "      clean_block = clean_block\n", "    cleaned_blocks.append(clean_block.strip())\n", "\n", "  return cleaned_blocks\n", "\n", "def build_prompt():\n", "  prompt = f\"\"\"<start_of_image>\n", "If you decide to invoke any of the function(s), it should be wrapped with ```tool_code```.\n", "\n", "You have access to the following tools.\n", "\n", "* `board.push_san(move:str)`: Make a move in standard algebraic notation)\n", "\n", "{'BLACK' if board.turn == chess.BLACK else 'WHITE'} to move.\n", "Possible moves are {list(board.legal_moves)}.\n", "Make your move.\n", "\"\"\"\n", "  print(prompt)\n", "  return prompt\n", "\n", "def build_prompt_rev():\n", "  prompt = f\"\"\"If you decide to invoke any of the function(s), it should be wrapped with ```tool_code```.\n", "\n", "You have access to the following tools.\n", "\n", "* `get_best_move()`: Get the best move from the engine.\n", "\n", "{'BLACK' if board.turn == chess.BLACK else 'WHITE'} to move.\n", "Get the next best move and explain it easy enough for five year old.\n", "\"\"\"\n", "  return prompt\n", "\n", "get_board_img(board)"]}, {"cell_type": "markdown", "metadata": {"id": "n8c2AG57F2Dt"}, "source": ["# Explainer"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ESM5EfdoF5hz"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Okay, let's analyze this game between <PERSON><PERSON><PERSON><PERSON> and <PERSON> Blue and pinpoint the most interesting move.  Given the context of this match (<PERSON><PERSON><PERSON><PERSON> vs. a supercomputer), the moves that stand out are those that demonstrate either <PERSON><PERSON><PERSON><PERSON>'s strategic brilliance or Deep Blue's surprising (or flawed) calculations.\n", "\n", "After careful consideration, I believe **32. g6** is the most interesting move. Here's why:\n", "\n", "*   **Sacrifice and Initiative:** This move is a pawn sacrifice (g6). Pawn sacrifices are often interesting because they disrupt the opponent's plans and can create attacking opportunities. In this case, <PERSON><PERSON><PERSON><PERSON> sacrifices the pawn to open the g-file and create a direct attack on <PERSON> Blue's king.\n", "*   **Deep Blue's Calculation Error (Likely):** It's highly probable that Deep Blue miscalculated the consequences of this sacrifice. While the move isn't *immediately* winning, it creates a complex tactical situation that likely exceeded Deep Blue's ability to evaluate accurately at the time.  Deep Blue was known to be strong in brute-force calculation but weaker in long-term strategic assessment.  The g6 sacrifice forces Deep Blue to deal with multiple threats and potential lines of attack, potentially leading to errors.\n", "*   **<PERSON><PERSON><PERSON><PERSON>'s Psychological Impact:**  Even if Deep Blue *could* have calculated the position accurately, playing such a bold and unexpected sacrifice would have put pressure on the computer.  <PERSON><PERSON><PERSON><PERSON> was a master of psychological warfare, and this move likely aimed to unsettle Deep Blue's evaluation process.\n", "*   **Leads to a Decisive Attack:** The move g6 directly leads to a series of tactical blows that ultimately decide the game. The subsequent moves (Bf3, Qb5, Qxf1+, etc.) all stem from the initiative created by the pawn sacrifice.\n", "\n", "**Why other moves are less interesting (though still important):**\n", "\n", "*   **13. Nh4 g5:** A dynamic exchange, but fairly standard.\n", "*   **20. dxe4 Bc5:** A typical recapture.\n", "*   **24. f3 Nxe3:** A tactical exchange, but not particularly surprising.\n", "*   **42. Nxg4+ Nxg4+:** A forced sequence, a consequence of the earlier moves.\n", "\n", "**In conclusion, 32. g6 is the most interesting move because it's a bold, strategically significant pawn sacrifice that likely exploited a weakness in Deep Blue's evaluation capabilities and set the stage for <PERSON><PERSON><PERSON><PERSON>'s victory.**\n", "\n", "\n", "\n", "Do you want me to elaborate on any specific aspect of the game or the move 32. g6?\n"]}], "source": ["prompt = f\"\"\"What is the most interesting move from below?\n", "\n", "[Event \"IBM Man-Machine, New York USA\"]\n", "[Site \"01\"]\n", "[Date \"1997.??.??\"]\n", "[EventDate \"?\"]\n", "[Round \"?\"]\n", "[Result \"1-0\"]\n", "[White \"<PERSON>\"]\n", "[Black \"Deep Blue (Computer)\"]\n", "[ECO \"A06\"]\n", "[WhiteElo \"?\"]\n", "[BlackElo \"?\"]\n", "[PlyCount \"89\"]\n", "1.Nf3 d5 2.g3 Bg4 3.b3 Nd7 4.Bb2 e6 5.Bg2 <PERSON>f6 6.<PERSON><PERSON><PERSON> c6 7.d3 Bd6 8.Nbd2 O-O 9.h3 Bh5 10.e3 h6 11.Qe1 Qa5 12.a3 Bc7 13.Nh4 g5 14.Nhf3 e5 15.e4 Rfe8 16.Nh2 Qb6 17.Qc1 a5 18.Re1 Bd6 19.Ndf1 dxe4 20.dxe4 Bc5 21.Ne3 Rad8 22.Nhf1 g4 23.hxg4 Nxg4 24.f3 Nxe3 25.Nxe3 Be7 26.Kh1 Bg5 27.Re2 a4 28.b4 f5 29.exf5 e4 30.f4 Bxe2 31.fxg5 Ne5 32.g6 Bf3 33.Bc3 Qb5 34.Qf1 Qxf1+ 35.Rxf1 h5 36.Kg1 Kf8 37.Bh3 b5 38.Kf2 Kg7 39.g4 Kh6 40.Rg1 hxg4 41.Bxg4 Bxg4 42.Nxg4+ Nxg4+ 43.Rxg4 Rd5 44.f6 Rd1 45.g7 1-0\n", "\"\"\"\n", "\n", "response = chatbot.chat(prompt=prompt)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QLJpQ1NRMTq4"}, "outputs": [{"data": {"image/svg+xml": "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" viewBox=\"0 0 390 390\" width=\"390\" height=\"390\"><desc><pre>. . . r r . k .\n. p . . . . . .\n. q p . . . P p\n. . . . n P . .\np P . . p . . .\nP . . . N . P .\n. B P . b . B .\nR . Q . . . . K</pre></desc><defs><g id=\"white-pawn\" class=\"white pawn\"><path d=\"M22.5 9c-2.21 0-4 1.79-4 4 0 .89.29 1.71.78 2.38C17.33 16.5 16 18.59 16 21c0 2.03.94 3.84 2.41 5.03-3 1.06-7.41 5.55-7.41 13.47h23c0-7.92-4.41-12.41-7.41-13.47 1.47-1.19 2.41-3 2.41-5.03 0-2.41-1.33-4.5-3.28-5.62.49-.67.78-1.49.78-2.38 0-2.21-1.79-4-4-4z\" fill=\"#fff\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" /></g><g id=\"white-knight\" class=\"white knight\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M 22,10 C 32.5,11 38.5,18 38,39 L 15,39 C 15,30 25,32.5 23,18\" style=\"fill:#ffffff; stroke:#000000;\" /><path d=\"M 24,18 C 24.38,20.91 18.45,25.37 16,27 C 13,29 13.18,31.34 11,31 C 9.958,30.06 12.41,27.96 11,28 C 10,28 11.19,29.23 10,30 C 9,30 5.997,31 6,26 C 6,24 12,14 12,14 C 12,14 13.89,12.1 14,10.5 C 13.27,9.506 13.5,8.5 13.5,7.5 C 14.5,6.5 16.5,10 16.5,10 L 18.5,10 C 18.5,10 19.28,8.008 21,7 C 22,7 22,10 22,10\" style=\"fill:#ffffff; stroke:#000000;\" /><path d=\"M 9.5 25.5 A 0.5 0.5 0 1 1 8.5,25.5 A 0.5 0.5 0 1 1 9.5 25.5 z\" style=\"fill:#000000; stroke:#000000;\" /><path d=\"M 15 15.5 A 0.5 1.5 0 1 1 14,15.5 A 0.5 1.5 0 1 1 15 15.5 z\" transform=\"matrix(0.866,0.5,-0.5,0.866,9.693,-5.173)\" style=\"fill:#000000; stroke:#000000;\" /></g><g id=\"white-bishop\" class=\"white bishop\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g fill=\"#fff\" stroke-linecap=\"butt\"><path d=\"M9 36c3.39-.97 10.11.43 13.5-2 3.39 2.43 10.11 1.03 13.5 2 0 0 1.65.54 3 2-.68.97-1.65.99-3 .5-3.39-.97-10.11.46-13.5-1-3.39 1.46-10.11.03-13.5 1-1.354.49-2.323.47-3-.5 1.354-1.94 3-2 3-2zM15 32c2.5 2.5 12.5 2.5 15 0 .5-1.5 0-2 0-2 0-2.5-2.5-4-2.5-4 5.5-1.5 6-11.5-5-15.5-11 4-10.5 14-5 15.5 0 0-2.5 1.5-2.5 4 0 0-.5.5 0 2zM25 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 1 1 5 0z\" /></g><path d=\"M17.5 26h10M15 30h15m-7.5-14.5v5M20 18h5\" stroke-linejoin=\"miter\" /></g><g id=\"white-rook\" class=\"white rook\" fill=\"#fff\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M9 39h27v-3H9v3zM12 36v-4h21v4H12zM11 14V9h4v2h5V9h5v2h5V9h4v5\" stroke-linecap=\"butt\" /><path d=\"M34 14l-3 3H14l-3-3\" /><path d=\"M31 17v12.5H14V17\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" /><path d=\"M31 29.5l1.5 2.5h-20l1.5-2.5\" /><path d=\"M11 14h23\" fill=\"none\" stroke-linejoin=\"miter\" /></g><g id=\"white-queen\" class=\"white queen\" fill=\"#fff\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M8 12a2 2 0 1 1-4 0 2 2 0 1 1 4 0zM24.5 7.5a2 2 0 1 1-4 0 2 2 0 1 1 4 0zM41 12a2 2 0 1 1-4 0 2 2 0 1 1 4 0zM16 8.5a2 2 0 1 1-4 0 2 2 0 1 1 4 0zM33 9a2 2 0 1 1-4 0 2 2 0 1 1 4 0z\" /><path d=\"M9 26c8.5-1.5 21-1.5 27 0l2-12-7 11V11l-5.5 13.5-3-15-3 15-5.5-14V25L7 14l2 12zM9 26c0 2 1.5 2 2.5 4 1 1.5 1 1 .5 3.5-1.5 1-1.5 2.5-1.5 2.5-1.5 1.5.5 2.5.5 2.5 6.5 1 16.5 1 23 0 0 0 1.5-1 0-2.5 0 0 .5-1.5-1-2.5-.5-2.5-.5-2 .5-3.5 1-2 2.5-2 2.5-4-8.5-1.5-18.5-1.5-27 0z\" stroke-linecap=\"butt\" /><path d=\"M11.5 30c3.5-1 18.5-1 22 0M12 33.5c6-1 15-1 21 0\" fill=\"none\" /></g><g id=\"white-king\" class=\"white king\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M22.5 11.63V6M20 8h5\" stroke-linejoin=\"miter\" /><path d=\"M22.5 25s4.5-7.5 3-10.5c0 0-1-2.5-3-2.5s-3 2.5-3 2.5c-1.5 3 3 10.5 3 10.5\" fill=\"#fff\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" /><path d=\"M11.5 37c5.5 3.5 15.5 3.5 21 0v-7s9-4.5 6-10.5c-4-6.5-13.5-3.5-16 4V27v-3.5c-3.5-7.5-13-10.5-16-4-3 6 5 10 5 10V37z\" fill=\"#fff\" /><path d=\"M11.5 30c5.5-3 15.5-3 21 0m-21 3.5c5.5-3 15.5-3 21 0m-21 3.5c5.5-3 15.5-3 21 0\" /></g><g id=\"black-pawn\" class=\"black pawn\"><path d=\"M22.5 9c-2.21 0-4 1.79-4 4 0 .89.29 1.71.78 2.38C17.33 16.5 16 18.59 16 21c0 2.03.94 3.84 2.41 5.03-3 1.06-7.41 5.55-7.41 13.47h23c0-7.92-4.41-12.41-7.41-13.47 1.47-1.19 2.41-3 2.41-5.03 0-2.41-1.33-4.5-3.28-5.62.49-.67.78-1.49.78-2.38 0-2.21-1.79-4-4-4z\" fill=\"#000\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" /></g><g id=\"black-knight\" class=\"black knight\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M 22,10 C 32.5,11 38.5,18 38,39 L 15,39 C 15,30 25,32.5 23,18\" style=\"fill:#000000; stroke:#000000;\" /><path d=\"M 24,18 C 24.38,20.91 18.45,25.37 16,27 C 13,29 13.18,31.34 11,31 C 9.958,30.06 12.41,27.96 11,28 C 10,28 11.19,29.23 10,30 C 9,30 5.997,31 6,26 C 6,24 12,14 12,14 C 12,14 13.89,12.1 14,10.5 C 13.27,9.506 13.5,8.5 13.5,7.5 C 14.5,6.5 16.5,10 16.5,10 L 18.5,10 C 18.5,10 19.28,8.008 21,7 C 22,7 22,10 22,10\" style=\"fill:#000000; stroke:#000000;\" /><path d=\"M 9.5 25.5 A 0.5 0.5 0 1 1 8.5,25.5 A 0.5 0.5 0 1 1 9.5 25.5 z\" style=\"fill:#ececec; stroke:#ececec;\" /><path d=\"M 15 15.5 A 0.5 1.5 0 1 1 14,15.5 A 0.5 1.5 0 1 1 15 15.5 z\" transform=\"matrix(0.866,0.5,-0.5,0.866,9.693,-5.173)\" style=\"fill:#ececec; stroke:#ececec;\" /><path d=\"M 24.55,10.4 L 24.1,11.85 L 24.6,12 C 27.75,13 30.25,14.49 32.5,18.75 C 34.75,23.01 35.75,29.06 35.25,39 L 35.2,39.5 L 37.45,39.5 L 37.5,39 C 38,28.94 36.62,22.15 34.25,17.66 C 31.88,13.17 28.46,11.02 25.06,10.5 L 24.55,10.4 z \" style=\"fill:#ececec; stroke:none;\" /></g><g id=\"black-bishop\" class=\"black bishop\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M9 36c3.39-.97 10.11.43 13.5-2 3.39 2.43 10.11 1.03 13.5 2 0 0 1.65.54 3 2-.68.97-1.65.99-3 .5-3.39-.97-10.11.46-13.5-1-3.39 1.46-10.11.03-13.5 1-1.354.49-2.323.47-3-.5 1.354-1.94 3-2 3-2zm6-4c2.5 2.5 12.5 2.5 15 0 .5-1.5 0-2 0-2 0-2.5-2.5-4-2.5-4 5.5-1.5 6-11.5-5-15.5-11 4-10.5 14-5 15.5 0 0-2.5 1.5-2.5 4 0 0-.5.5 0 2zM25 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 1 1 5 0z\" fill=\"#000\" stroke-linecap=\"butt\" /><path d=\"M17.5 26h10M15 30h15m-7.5-14.5v5M20 18h5\" stroke=\"#fff\" stroke-linejoin=\"miter\" /></g><g id=\"black-rook\" class=\"black rook\" fill=\"#000\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M9 39h27v-3H9v3zM12.5 32l1.5-2.5h17l1.5 2.5h-20zM12 36v-4h21v4H12z\" stroke-linecap=\"butt\" /><path d=\"M14 29.5v-13h17v13H14z\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" /><path d=\"M14 16.5L11 14h23l-3 2.5H14zM11 14V9h4v2h5V9h5v2h5V9h4v5H11z\" stroke-linecap=\"butt\" /><path d=\"M12 35.5h21M13 31.5h19M14 29.5h17M14 16.5h17M11 14h23\" fill=\"none\" stroke=\"#fff\" stroke-width=\"1\" stroke-linejoin=\"miter\" /></g><g id=\"black-queen\" class=\"black queen\" fill=\"#000\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g fill=\"#000\" stroke=\"none\"><circle cx=\"6\" cy=\"12\" r=\"2.75\" /><circle cx=\"14\" cy=\"9\" r=\"2.75\" /><circle cx=\"22.5\" cy=\"8\" r=\"2.75\" /><circle cx=\"31\" cy=\"9\" r=\"2.75\" /><circle cx=\"39\" cy=\"12\" r=\"2.75\" /></g><path d=\"M9 26c8.5-1.5 21-1.5 27 0l2.5-12.5L31 25l-.3-14.1-5.2 13.6-3-14.5-3 14.5-5.2-13.6L14 25 6.5 13.5 9 26zM9 26c0 2 1.5 2 2.5 4 1 1.5 1 1 .5 3.5-1.5 1-1.5 2.5-1.5 2.5-1.5 1.5.5 2.5.5 2.5 6.5 1 16.5 1 23 0 0 0 1.5-1 0-2.5 0 0 .5-1.5-1-2.5-.5-2.5-.5-2 .5-3.5 1-2 2.5-2 2.5-4-8.5-1.5-18.5-1.5-27 0z\" stroke-linecap=\"butt\" /><path d=\"M11 38.5a35 35 1 0 0 23 0\" fill=\"none\" stroke-linecap=\"butt\" /><path d=\"M11 29a35 35 1 0 1 23 0M12.5 31.5h20M11.5 34.5a35 35 1 0 0 22 0M10.5 37.5a35 35 1 0 0 24 0\" fill=\"none\" stroke=\"#fff\" /></g><g id=\"black-king\" class=\"black king\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M22.5 11.63V6\" stroke-linejoin=\"miter\" /><path d=\"M22.5 25s4.5-7.5 3-10.5c0 0-1-2.5-3-2.5s-3 2.5-3 2.5c-1.5 3 3 10.5 3 10.5\" fill=\"#000\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" /><path d=\"M11.5 37c5.5 3.5 15.5 3.5 21 0v-7s9-4.5 6-10.5c-4-6.5-13.5-3.5-16 4V27v-3.5c-3.5-7.5-13-10.5-16-4-3 6 5 10 5 10V37z\" fill=\"#000\" /><path d=\"M20 8h5\" stroke-linejoin=\"miter\" /><path d=\"M32 29.5s8.5-4 6.03-9.65C34.15 14 25 18 22.5 24.5l.01 2.1-.01-2.1C20 18 9.906 14 6.997 19.85c-2.497 5.65 4.853 9 4.853 9M11.5 30c5.5-3 15.5-3 21 0m-21 3.5c5.5-3 15.5-3 21 0m-21 3.5c5.5-3 15.5-3 21 0\" stroke=\"#fff\" /></g></defs><rect x=\"7.5\" y=\"7.5\" width=\"375\" height=\"375\" fill=\"none\" stroke=\"#212121\" stroke-width=\"15\" /><g transform=\"translate(20, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M23.328 10.016q-1.742 0-2.414.398-.672.398-.672 1.36 0 .765.5 1.218.508.445 1.375.445 1.196 0 1.914-.843.727-.852.727-2.258v-.32zm2.867-.594v4.992h-1.437v-1.328q-.492.797-1.227 1.18-.734.375-1.797.375-1.343 0-2.14-.75-.79-.758-.79-2.024 0-1.476.985-2.226.992-.75 2.953-.75h2.016V8.75q0-.992-.656-1.531-.649-.547-1.829-.547-.75 0-1.46.18-.711.18-1.368.539V6.062q.79-.304 1.532-.453.742-.156 1.445-.156 1.898 0 2.836.984.937.985.937 2.985z\" /></g><g transform=\"translate(20, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M23.328 10.016q-1.742 0-2.414.398-.672.398-.672 1.36 0 .765.5 1.218.508.445 1.375.445 1.196 0 1.914-.843.727-.852.727-2.258v-.32zm2.867-.594v4.992h-1.437v-1.328q-.492.797-1.227 1.18-.734.375-1.797.375-1.343 0-2.14-.75-.79-.758-.79-2.024 0-1.476.985-2.226.992-.75 2.953-.75h2.016V8.75q0-.992-.656-1.531-.649-.547-1.829-.547-.75 0-1.46.18-.711.18-1.368.539V6.062q.79-.304 1.532-.453.742-.156 1.445-.156 1.898 0 2.836.984.937.985.937 2.985z\" /></g><g transform=\"translate(65, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.922 10.047q0-1.586-.656-2.485-.649-.906-1.79-.906-1.14 0-1.796.906-.649.899-.649 2.485 0 1.586.649 2.492.656.898 1.797.898 1.14 0 1.789-.898.656-.906.656-2.492zm-4.89-3.055q.452-.781 1.14-1.156.695-.383 1.656-.383 1.594 0 2.586 1.266 1 1.265 1 3.328 0 2.062-1 3.328-.992 1.266-2.586 1.266-.96 0-1.656-.375-.688-.383-1.14-1.164v1.312h-1.446V2.258h1.445z\" /></g><g transform=\"translate(65, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.922 10.047q0-1.586-.656-2.485-.649-.906-1.79-.906-1.14 0-1.796.906-.649.899-.649 2.485 0 1.586.649 2.492.656.898 1.797.898 1.14 0 1.789-.898.656-.906.656-2.492zm-4.89-3.055q.452-.781 1.14-1.156.695-.383 1.656-.383 1.594 0 2.586 1.266 1 1.265 1 3.328 0 2.062-1 3.328-.992 1.266-2.586 1.266-.96 0-1.656-.375-.688-.383-1.14-1.164v1.312h-1.446V2.258h1.445z\" /></g><g transform=\"translate(110, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M25.96 6v1.344q-.608-.336-1.226-.5-.609-.172-1.234-.172-1.398 0-2.172.89-.773.883-.773 2.485 0 1.601.773 2.492.774.883 2.172.883.625 0 1.234-.164.618-.172 1.227-.508v1.328q-.602.281-1.25.422-.64.14-1.367.14-1.977 0-3.14-1.242-1.165-1.242-1.165-3.351 0-2.14 1.172-3.367 1.18-1.227 3.227-1.227.664 0 1.296.14.633.134 1.227.407z\" /></g><g transform=\"translate(110, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M25.96 6v1.344q-.608-.336-1.226-.5-.609-.172-1.234-.172-1.398 0-2.172.89-.773.883-.773 2.485 0 1.601.773 2.492.774.883 2.172.883.625 0 1.234-.164.618-.172 1.227-.508v1.328q-.602.281-1.25.422-.64.14-1.367.14-1.977 0-3.14-1.242-1.165-1.242-1.165-3.351 0-2.14 1.172-3.367 1.18-1.227 3.227-1.227.664 0 1.296.14.633.134 1.227.407z\" /></g><g transform=\"translate(155, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.973 6.992V2.258h1.437v12.156h-1.437v-1.312q-.453.78-1.149 1.164-.687.375-1.656.375-1.586 0-2.586-1.266-.992-1.266-.992-3.328 0-2.063.992-3.328 1-1.266 2.586-1.266.969 0 1.656.383.696.375 1.149 1.156zm-4.899 3.055q0 1.586.649 2.492.656.898 1.797.898 1.14 0 1.796-.898.657-.906.657-2.492 0-1.586-.657-2.485-.656-.906-1.796-.906-1.141 0-1.797.906-.649.899-.649 2.485z\" /></g><g transform=\"translate(155, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.973 6.992V2.258h1.437v12.156h-1.437v-1.312q-.453.78-1.149 1.164-.687.375-1.656.375-1.586 0-2.586-1.266-.992-1.266-.992-3.328 0-2.063.992-3.328 1-1.266 2.586-1.266.969 0 1.656.383.696.375 1.149 1.156zm-4.899 3.055q0 1.586.649 2.492.656.898 1.797.898 1.14 0 1.796-.898.657-.906.657-2.492 0-1.586-.657-2.485-.656-.906-1.796-.906-1.141 0-1.797.906-.649.899-.649 2.485z\" /></g><g transform=\"translate(200, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M26.555 9.68v.703h-6.61q.094 1.484.89 2.265.806.774 2.235.774.828 0 1.602-.203.781-.203 1.547-.61v1.36q-.774.328-1.586.5-.813.172-1.649.172-2.093 0-3.32-1.22-1.219-1.218-1.219-3.296 0-2.148 1.157-3.406 1.164-1.266 3.132-1.266 1.766 0 2.79 1.14 1.03 1.134 1.03 3.087zm-1.438-.422q-.015-1.18-.664-1.883-.64-.703-1.703-.703-1.203 0-1.93.68-.718.68-.828 1.914z\" /></g><g transform=\"translate(200, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M26.555 9.68v.703h-6.61q.094 1.484.89 2.265.806.774 2.235.774.828 0 1.602-.203.781-.203 1.547-.61v1.36q-.774.328-1.586.5-.813.172-1.649.172-2.093 0-3.32-1.22-1.219-1.218-1.219-3.296 0-2.148 1.157-3.406 1.164-1.266 3.132-1.266 1.766 0 2.79 1.14 1.03 1.134 1.03 3.087zm-1.438-.422q-.015-1.18-.664-1.883-.64-.703-1.703-.703-1.203 0-1.93.68-.718.68-.828 1.914z\" /></g><g transform=\"translate(245, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M25.285 2.258v1.195H23.91q-.773 0-1.078.313-.297.312-.297 1.125v.773h2.367v1.117h-2.367v7.633H21.09V6.781h-1.375V5.664h1.375v-.61q0-1.46.68-2.124.68-.672 2.156-.672z\" /></g><g transform=\"translate(245, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M25.285 2.258v1.195H23.91q-.773 0-1.078.313-.297.312-.297 1.125v.773h2.367v1.117h-2.367v7.633H21.09V6.781h-1.375V5.664h1.375v-.61q0-1.46.68-2.124.68-.672 2.156-.672z\" /></g><g transform=\"translate(290, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.973 9.937q0-1.562-.649-2.421-.64-.86-1.804-.86-1.157 0-1.805.86-.64.859-.64 2.421 0 1.555.64 2.415.648.859 1.805.859 1.164 0 1.804-.86.649-.859.649-2.414zm1.437 3.391q0 2.234-.992 3.32-.992 1.094-3.04 1.094-.757 0-1.429-.117-.672-.11-1.304-.344v-1.398q.632.344 1.25.508.617.164 1.257.164 1.414 0 2.118-.743.703-.734.703-2.226v-.711q-.446.773-1.141 1.156-.695.383-1.664.383-1.61 0-2.594-1.227-.984-1.226-.984-3.25 0-2.03.984-3.257.985-1.227 2.594-1.227.969 0 1.664.383t1.14 1.156V5.664h1.438z\" /></g><g transform=\"translate(290, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.973 9.937q0-1.562-.649-2.421-.64-.86-1.804-.86-1.157 0-1.805.86-.64.859-.64 2.421 0 1.555.64 2.415.648.859 1.805.859 1.164 0 1.804-.86.649-.859.649-2.414zm1.437 3.391q0 2.234-.992 3.32-.992 1.094-3.04 1.094-.757 0-1.429-.117-.672-.11-1.304-.344v-1.398q.632.344 1.25.508.617.164 1.257.164 1.414 0 2.118-.743.703-.734.703-2.226v-.711q-.446.773-1.141 1.156-.695.383-1.664.383-1.61 0-2.594-1.227-.984-1.226-.984-3.25 0-2.03.984-3.257.985-1.227 2.594-1.227.969 0 1.664.383t1.14 1.156V5.664h1.438z\" /></g><g transform=\"translate(335, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M26.164 9.133v5.281h-1.437V9.18q0-1.243-.485-1.86-.484-.617-1.453-.617-1.164 0-1.836.742-.672.742-.672 2.024v4.945h-1.445V2.258h1.445v4.765q.516-.789 1.211-1.18.703-.39 1.617-.39 1.508 0 2.282.938.773.93.773 2.742z\" /></g><g transform=\"translate(335, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M26.164 9.133v5.281h-1.437V9.18q0-1.243-.485-1.86-.484-.617-1.453-.617-1.164 0-1.836.742-.672.742-.672 2.024v4.945h-1.445V2.258h1.445v4.765q.516-.789 1.211-1.18.703-.39 1.617-.39 1.508 0 2.282.938.773.93.773 2.742z\" /></g><g transform=\"translate(0, 335) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.754 26.996h2.578v-8.898l-2.805.562v-1.437l2.79-.563h1.578v10.336h2.578v1.328h-6.72z\" /></g><g transform=\"translate(375, 335) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.754 26.996h2.578v-8.898l-2.805.562v-1.437l2.79-.563h1.578v10.336h2.578v1.328h-6.72z\" /></g><g transform=\"translate(0, 290) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M8.195 26.996h5.508v1.328H6.297v-1.328q.898-.93 2.445-2.492 1.555-1.57 1.953-2.024.758-.851 1.055-1.437.305-.594.305-1.164 0-.93-.657-1.516-.648-.586-1.695-.586-.742 0-1.57.258-.82.258-1.758.781v-1.593q.953-.383 1.781-.578.828-.196 1.516-.196 1.812 0 2.89.906 1.079.907 1.079 2.422 0 .72-.274 1.368-.265.64-.976 1.515-.196.227-1.243 1.313-1.046 1.078-2.953 3.023z\" /></g><g transform=\"translate(375, 290) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M8.195 26.996h5.508v1.328H6.297v-1.328q.898-.93 2.445-2.492 1.555-1.57 1.953-2.024.758-.851 1.055-1.437.305-.594.305-1.164 0-.93-.657-1.516-.648-.586-1.695-.586-.742 0-1.57.258-.82.258-1.758.781v-1.593q.953-.383 1.781-.578.828-.196 1.516-.196 1.812 0 2.89.906 1.079.907 1.079 2.422 0 .72-.274 1.368-.265.64-.976 1.515-.196.227-1.243 1.313-1.046 1.078-2.953 3.023z\" /></g><g transform=\"translate(0, 245) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M11.434 22.035q1.132.242 1.765 1.008.64.766.64 1.89 0 1.727-1.187 2.672-1.187.946-3.375.946-.734 0-1.515-.149-.774-.14-1.602-.43V26.45q.656.383 1.438.578.78.196 1.632.196 1.485 0 2.258-.586.782-.586.782-1.703 0-1.032-.727-1.61-.719-.586-2.008-.586h-1.36v-1.297h1.423q1.164 0 1.78-.46.618-.47.618-1.344 0-.899-.64-1.375-.633-.485-1.82-.485-.65 0-1.391.141-.743.14-1.633.437V16.95q.898-.25 1.68-.375.788-.125 1.484-.125 1.797 0 2.844.82 1.046.813 1.046 2.204 0 .968-.554 1.64-.555.664-1.578.922z\" /></g><g transform=\"translate(375, 245) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M11.434 22.035q1.132.242 1.765 1.008.64.766.64 1.89 0 1.727-1.187 2.672-1.187.946-3.375.946-.734 0-1.515-.149-.774-.14-1.602-.43V26.45q.656.383 1.438.578.78.196 1.632.196 1.485 0 2.258-.586.782-.586.782-1.703 0-1.032-.727-1.61-.719-.586-2.008-.586h-1.36v-1.297h1.423q1.164 0 1.78-.46.618-.47.618-1.344 0-.899-.64-1.375-.633-.485-1.82-.485-.65 0-1.391.141-.743.14-1.633.437V16.95q.898-.25 1.68-.375.788-.125 1.484-.125 1.797 0 2.844.82 1.046.813 1.046 2.204 0 .968-.554 1.64-.555.664-1.578.922z\" /></g><g transform=\"translate(0, 200) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M11.016 18.035L7.03 24.262h3.985zm-.414-1.375h1.984v7.602h1.664v1.312h-1.664v2.75h-1.57v-2.75H5.75v-1.523z\" /></g><g transform=\"translate(375, 200) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M11.016 18.035L7.03 24.262h3.985zm-.414-1.375h1.984v7.602h1.664v1.312h-1.664v2.75h-1.57v-2.75H5.75v-1.523z\" /></g><g transform=\"translate(0, 155) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.719 16.66h6.195v1.328h-4.75v2.86q.344-.118.688-.172.343-.063.687-.063 1.953 0 3.094 1.07 1.14 1.07 1.14 2.899 0 1.883-1.171 2.93-1.172 1.039-3.305 1.039-.735 0-1.5-.125-.758-.125-1.57-.375v-1.586q.703.383 1.453.57.75.188 1.586.188 1.351 0 2.14-.711.79-.711.79-1.93 0-1.219-.79-1.93-.789-.71-2.14-.71-.633 0-1.266.14-.625.14-1.281.438z\" /></g><g transform=\"translate(375, 155) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.719 16.66h6.195v1.328h-4.75v2.86q.344-.118.688-.172.343-.063.687-.063 1.953 0 3.094 1.07 1.14 1.07 1.14 2.899 0 1.883-1.171 2.93-1.172 1.039-3.305 1.039-.735 0-1.5-.125-.758-.125-1.57-.375v-1.586q.703.383 1.453.57.75.188 1.586.188 1.351 0 2.14-.711.79-.711.79-1.93 0-1.219-.79-1.93-.789-.71-2.14-.71-.633 0-1.266.14-.625.14-1.281.438z\" /></g><g transform=\"translate(0, 110) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M10.137 21.863q-1.063 0-1.688.727-.617.726-.617 1.992 0 1.258.617 1.992.625.727 1.688.727 1.062 0 1.68-.727.624-.734.624-1.992 0-1.266-.625-1.992-.617-.727-1.68-.727zm3.133-4.945v1.437q-.594-.28-1.204-.43-.601-.148-1.195-.148-1.562 0-2.39 1.055-.82 1.055-.938 3.188.46-.68 1.156-1.04.696-.367 1.531-.367 1.758 0 2.774 1.07 1.023 1.063 1.023 2.899 0 1.797-1.062 2.883-1.063 1.086-2.828 1.086-2.024 0-3.094-1.547-1.07-1.555-1.07-4.5 0-2.766 1.312-4.406 1.313-1.649 3.524-1.649.593 0 1.195.117.61.118 1.266.352z\" /></g><g transform=\"translate(375, 110) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M10.137 21.863q-1.063 0-1.688.727-.617.726-.617 1.992 0 1.258.617 1.992.625.727 1.688.727 1.062 0 1.68-.727.624-.734.624-1.992 0-1.266-.625-1.992-.617-.727-1.68-.727zm3.133-4.945v1.437q-.594-.28-1.204-.43-.601-.148-1.195-.148-1.562 0-2.39 1.055-.82 1.055-.938 3.188.46-.68 1.156-1.04.696-.367 1.531-.367 1.758 0 2.774 1.07 1.023 1.063 1.023 2.899 0 1.797-1.062 2.883-1.063 1.086-2.828 1.086-2.024 0-3.094-1.547-1.07-1.555-1.07-4.5 0-2.766 1.312-4.406 1.313-1.649 3.524-1.649.593 0 1.195.117.61.118 1.266.352z\" /></g><g transform=\"translate(0, 65) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.25 16.66h7.5v.672L9.516 28.324H7.867l3.985-10.336H6.25z\" /></g><g transform=\"translate(375, 65) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.25 16.66h7.5v.672L9.516 28.324H7.867l3.985-10.336H6.25z\" /></g><g transform=\"translate(0, 20) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M10 22.785q-1.125 0-1.773.602-.641.601-.641 1.656t.64 1.656q.649.602 1.774.602t1.773-.602q.649-.61.649-1.656 0-1.055-.649-1.656-.64-.602-1.773-.602zm-1.578-.672q-1.016-.25-1.586-.945-.563-.695-.563-1.695 0-1.399.993-2.211 1-.813 2.734-.813 1.742 0 2.734.813.993.812.993 2.21 0 1-.57 1.696-.563.695-1.571.945 1.14.266 1.773 1.04.641.773.641 1.89 0 1.695-1.04 2.602-1.03.906-2.96.906t-2.969-.906Q6 26.738 6 25.043q0-1.117.64-1.89.641-.774 1.782-1.04zm-.578-2.492q0 .906.562 1.414.57.508 1.594.508 1.016 0 1.586-.508.578-.508.578-1.414 0-.906-.578-1.414-.57-.508-1.586-.508-1.023 0-1.594.508-.562.508-.562 1.414z\" /></g><g transform=\"translate(375, 20) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M10 22.785q-1.125 0-1.773.602-.641.601-.641 1.656t.64 1.656q.649.602 1.774.602t1.773-.602q.649-.61.649-1.656 0-1.055-.649-1.656-.64-.602-1.773-.602zm-1.578-.672q-1.016-.25-1.586-.945-.563-.695-.563-1.695 0-1.399.993-2.211 1-.813 2.734-.813 1.742 0 2.734.813.993.812.993 2.21 0 1-.57 1.696-.563.695-1.571.945 1.14.266 1.773 1.04.641.773.641 1.89 0 1.695-1.04 2.602-1.03.906-2.96.906t-2.969-.906Q6 26.738 6 25.043q0-1.117.64-1.89.641-.774 1.782-1.04zm-.578-2.492q0 .906.562 1.414.57.508 1.594.508 1.016 0 1.586-.508.578-.508.578-1.414 0-.906-.578-1.414-.57-.508-1.586-.508-1.023 0-1.594.508-.562.508-.562 1.414z\" /></g><rect x=\"15\" y=\"330\" width=\"45\" height=\"45\" class=\"square dark a1\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"60\" y=\"330\" width=\"45\" height=\"45\" class=\"square light b1\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"105\" y=\"330\" width=\"45\" height=\"45\" class=\"square dark c1\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"150\" y=\"330\" width=\"45\" height=\"45\" class=\"square light d1\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"195\" y=\"330\" width=\"45\" height=\"45\" class=\"square dark e1\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"240\" y=\"330\" width=\"45\" height=\"45\" class=\"square light f1\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"285\" y=\"330\" width=\"45\" height=\"45\" class=\"square dark g1\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"330\" y=\"330\" width=\"45\" height=\"45\" class=\"square light h1\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"15\" y=\"285\" width=\"45\" height=\"45\" class=\"square light a2\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"60\" y=\"285\" width=\"45\" height=\"45\" class=\"square dark b2\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"105\" y=\"285\" width=\"45\" height=\"45\" class=\"square light c2\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"150\" y=\"285\" width=\"45\" height=\"45\" class=\"square dark d2\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"195\" y=\"285\" width=\"45\" height=\"45\" class=\"square light e2\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"240\" y=\"285\" width=\"45\" height=\"45\" class=\"square dark f2\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"285\" y=\"285\" width=\"45\" height=\"45\" class=\"square light g2\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"330\" y=\"285\" width=\"45\" height=\"45\" class=\"square dark h2\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"15\" y=\"240\" width=\"45\" height=\"45\" class=\"square dark a3\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"60\" y=\"240\" width=\"45\" height=\"45\" class=\"square light b3\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"105\" y=\"240\" width=\"45\" height=\"45\" class=\"square dark c3\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"150\" y=\"240\" width=\"45\" height=\"45\" class=\"square light d3\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"195\" y=\"240\" width=\"45\" height=\"45\" class=\"square dark e3\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"240\" y=\"240\" width=\"45\" height=\"45\" class=\"square light f3\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"285\" y=\"240\" width=\"45\" height=\"45\" class=\"square dark g3\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"330\" y=\"240\" width=\"45\" height=\"45\" class=\"square light h3\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"15\" y=\"195\" width=\"45\" height=\"45\" class=\"square light a4\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"60\" y=\"195\" width=\"45\" height=\"45\" class=\"square dark b4\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"105\" y=\"195\" width=\"45\" height=\"45\" class=\"square light c4\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"150\" y=\"195\" width=\"45\" height=\"45\" class=\"square dark d4\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"195\" y=\"195\" width=\"45\" height=\"45\" class=\"square light e4\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"240\" y=\"195\" width=\"45\" height=\"45\" class=\"square dark f4\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"285\" y=\"195\" width=\"45\" height=\"45\" class=\"square light g4\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"330\" y=\"195\" width=\"45\" height=\"45\" class=\"square dark h4\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"15\" y=\"150\" width=\"45\" height=\"45\" class=\"square dark a5\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"60\" y=\"150\" width=\"45\" height=\"45\" class=\"square light b5\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"105\" y=\"150\" width=\"45\" height=\"45\" class=\"square dark c5\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"150\" y=\"150\" width=\"45\" height=\"45\" class=\"square light d5\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"195\" y=\"150\" width=\"45\" height=\"45\" class=\"square dark e5\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"240\" y=\"150\" width=\"45\" height=\"45\" class=\"square light f5\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"285\" y=\"150\" width=\"45\" height=\"45\" class=\"square dark lastmove g5\" stroke=\"none\" fill=\"#aaa23b\" /><rect x=\"330\" y=\"150\" width=\"45\" height=\"45\" class=\"square light h5\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"15\" y=\"105\" width=\"45\" height=\"45\" class=\"square light a6\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"60\" y=\"105\" width=\"45\" height=\"45\" class=\"square dark b6\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"105\" y=\"105\" width=\"45\" height=\"45\" class=\"square light c6\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"150\" y=\"105\" width=\"45\" height=\"45\" class=\"square dark d6\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"195\" y=\"105\" width=\"45\" height=\"45\" class=\"square light e6\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"240\" y=\"105\" width=\"45\" height=\"45\" class=\"square dark f6\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"285\" y=\"105\" width=\"45\" height=\"45\" class=\"square light lastmove g6\" stroke=\"none\" fill=\"#cdd16a\" /><rect x=\"330\" y=\"105\" width=\"45\" height=\"45\" class=\"square dark h6\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"15\" y=\"60\" width=\"45\" height=\"45\" class=\"square dark a7\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"60\" y=\"60\" width=\"45\" height=\"45\" class=\"square light b7\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"105\" y=\"60\" width=\"45\" height=\"45\" class=\"square dark c7\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"150\" y=\"60\" width=\"45\" height=\"45\" class=\"square light d7\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"195\" y=\"60\" width=\"45\" height=\"45\" class=\"square dark e7\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"240\" y=\"60\" width=\"45\" height=\"45\" class=\"square light f7\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"285\" y=\"60\" width=\"45\" height=\"45\" class=\"square dark g7\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"330\" y=\"60\" width=\"45\" height=\"45\" class=\"square light h7\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"15\" y=\"15\" width=\"45\" height=\"45\" class=\"square light a8\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"60\" y=\"15\" width=\"45\" height=\"45\" class=\"square dark b8\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"105\" y=\"15\" width=\"45\" height=\"45\" class=\"square light c8\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"150\" y=\"15\" width=\"45\" height=\"45\" class=\"square dark d8\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"195\" y=\"15\" width=\"45\" height=\"45\" class=\"square light e8\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"240\" y=\"15\" width=\"45\" height=\"45\" class=\"square dark f8\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"285\" y=\"15\" width=\"45\" height=\"45\" class=\"square light g8\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"330\" y=\"15\" width=\"45\" height=\"45\" class=\"square dark h8\" stroke=\"none\" fill=\"#d18b47\" /><use href=\"#white-rook\" xlink:href=\"#white-rook\" transform=\"translate(15, 330)\" /><use href=\"#white-queen\" xlink:href=\"#white-queen\" transform=\"translate(105, 330)\" /><use href=\"#white-king\" xlink:href=\"#white-king\" transform=\"translate(330, 330)\" /><use href=\"#white-bishop\" xlink:href=\"#white-bishop\" transform=\"translate(60, 285)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(105, 285)\" /><use href=\"#black-bishop\" xlink:href=\"#black-bishop\" transform=\"translate(195, 285)\" /><use href=\"#white-bishop\" xlink:href=\"#white-bishop\" transform=\"translate(285, 285)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(15, 240)\" /><use href=\"#white-knight\" xlink:href=\"#white-knight\" transform=\"translate(195, 240)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(285, 240)\" /><use href=\"#black-pawn\" xlink:href=\"#black-pawn\" transform=\"translate(15, 195)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(60, 195)\" /><use href=\"#black-pawn\" xlink:href=\"#black-pawn\" transform=\"translate(195, 195)\" /><use href=\"#black-knight\" xlink:href=\"#black-knight\" transform=\"translate(195, 150)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(240, 150)\" /><use href=\"#black-queen\" xlink:href=\"#black-queen\" transform=\"translate(60, 105)\" /><use href=\"#black-pawn\" xlink:href=\"#black-pawn\" transform=\"translate(105, 105)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(285, 105)\" /><use href=\"#black-pawn\" xlink:href=\"#black-pawn\" transform=\"translate(330, 105)\" /><use href=\"#black-pawn\" xlink:href=\"#black-pawn\" transform=\"translate(60, 60)\" /><use href=\"#black-rook\" xlink:href=\"#black-rook\" transform=\"translate(150, 15)\" /><use href=\"#black-rook\" xlink:href=\"#black-rook\" transform=\"translate(195, 15)\" /><use href=\"#black-king\" xlink:href=\"#black-king\" transform=\"translate(285, 15)\" /></svg>", "text/plain": ["Board('3rr1k1/1p6/1qp3Pp/4nP2/pP2p3/P3N1P1/1BP1b1B1/R1Q4K b - - 0 32')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["board = chess.Board(\"3rr1k1/1p6/1qp4p/4nPP1/pP2p3/P3N1P1/1BP1b1B1/R1Q4K w - - 1 32\")\n", "board.push_san(\"g6\")\n", "board"]}, {"cell_type": "markdown", "metadata": {"id": "gKVPkBa7UxgI"}, "source": ["# Storytellers"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "JVw-9UN-UxGI"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The air in the IBM auditorium crackled with a nervous energy. It was 1997, New York City, and the world was holding its breath. <PERSON>, the reigning World Chess Champion, sat across from a hulking machine – Deep Blue, IBM’s chess-playing supercomputer. This was the rematch, the chance for Deep Blue to avenge its previous defeat.\n", "\n", "<PERSON><PERSON><PERSON><PERSON>, a whirlwind of intensity, tapped his fingers on the table, his eyes fixed on the board. He’d studied Deep Blue’s previous games, analyzed its strengths and weaknesses. He knew this wasn't just a game; it was a symbolic battle – man versus machine, intuition versus calculation.\n", "\n", "The opening unfolded with a familiar, almost polite, dance. <PERSON><PERSON><PERSON><PERSON>, playing white, opted for a solid, positional approach with 1. Nf3. <PERSON> Blue responded predictably, but <PERSON><PERSON><PERSON><PERSON>, sensing an opportunity, began to subtly steer the game towards a more complex, tactical landscape. He sacrificed a pawn, a calculated risk to disrupt Deep Blue’s rigid, algorithmic thinking.\n", "\n", "\"It's trying to force me into a closed position,\" <PERSON><PERSON><PERSON><PERSON> muttered to his second, <PERSON>. \"But I won't let it.\"\n", "\n", "The middle game became a tense, intricate struggle. <PERSON><PERSON><PERSON><PERSON>, relying on his intuition and understanding of positional nuances, maneuvered his pieces with a deceptive fluidity. Deep Blue, in turn, churned through millions of calculations per second, evaluating every possible move with cold, unfeeling precision. \n", "\n", "At move 15, <PERSON><PERSON><PERSON><PERSON> saw his chance. A daring pawn push, e4, opened the position and created a series of tactical possibilities. Deep Blue, seemingly unfazed, responded with a move that, on the surface, appeared logical. But <PERSON><PERSON><PERSON><PERSON> saw a deeper flaw, a subtle vulnerability in Deep Blue’s defensive structure.\n", "\n", "\"There,\" he whispered, pointing to a square on the board. \"It's overextended. It's relying too much on brute force.\"\n", "\n", "The game spiraled into a chaotic endgame. <PERSON><PERSON><PERSON><PERSON>, with a series of brilliant sacrifices and precise attacks, relentlessly pressed his advantage. Deep Blue, despite its immense processing power, seemed to falter, its calculations failing to account for the human element – the ability to anticipate, to improvise, to *feel* the flow of the game.\n", "\n", "The pivotal moment came at move 32. <PERSON><PERSON><PERSON><PERSON> sacrificed his bishop, a seemingly reckless move that left his king exposed. But it was a trap. <PERSON> Blue, calculating the immediate material gain, took the bait. \n", "\n", "\"It fell for it,\" <PERSON><PERSON><PERSON><PERSON> breathed, a flicker of triumph in his eyes.\n", "\n", "The final moves were a relentless cascade of attacks. <PERSON><PERSON><PERSON><PERSON>, with a combination of tactical brilliance and positional mastery, forced Deep Blue into a position from which there was no escape. At move 45, with a final, decisive push, <PERSON><PERSON><PERSON><PERSON> delivered the checkmate.\n", "\n", "The auditorium erupted in applause. <PERSON><PERSON><PERSON><PERSON>, exhausted but exhilarated, rose from his chair. He looked across at the silent, unblinking machine. \n", "\n", "\"It's a formidable opponent,\" he said, his voice echoing in the hall. \"But today, intuition prevailed.\"\n", "\n", "The score flashed on the screen: 1-0. <PERSON> had won. The battle of man versus machine had been fought, and for now, at least, humanity had emerged victorious. But everyone knew, with a chilling certainty, that the war was far from over. Deep Blue would learn. It would adapt. And the next time, the outcome might be very different.\n", "\n", "\n", "\n", "\n"]}], "source": ["prompt = f\"\"\"Write a short story about the gameplay below.\n", "\n", "[Event \"IBM Man-Machine, New York USA\"]\n", "[Site \"01\"]\n", "[Date \"1997.??.??\"]\n", "[EventDate \"?\"]\n", "[Round \"?\"]\n", "[Result \"1-0\"]\n", "[White \"<PERSON>\"]\n", "[Black \"Deep Blue (Computer)\"]\n", "[ECO \"A06\"]\n", "[WhiteElo \"?\"]\n", "[BlackElo \"?\"]\n", "[PlyCount \"89\"]\n", "1.Nf3 d5 2.g3 Bg4 3.b3 Nd7 4.Bb2 e6 5.Bg2 <PERSON>f6 6.<PERSON><PERSON><PERSON> c6 7.d3 Bd6 8.Nbd2 O-O 9.h3 Bh5 10.e3 h6 11.Qe1 Qa5 12.a3 Bc7 13.Nh4 g5 14.Nhf3 e5 15.e4 Rfe8 16.Nh2 Qb6 17.Qc1 a5 18.Re1 Bd6 19.Ndf1 dxe4 20.dxe4 Bc5 21.Ne3 Rad8 22.Nhf1 g4 23.hxg4 Nxg4 24.f3 Nxe3 25.Nxe3 Be7 26.Kh1 Bg5 27.Re2 a4 28.b4 f5 29.exf5 e4 30.f4 Bxe2 31.fxg5 Ne5 32.g6 Bf3 33.Bc3 Qb5 34.Qf1 Qxf1+ 35.Rxf1 h5 36.Kg1 Kf8 37.Bh3 b5 38.Kf2 Kg7 39.g4 Kh6 40.Rg1 hxg4 41.Bxg4 Bxg4 42.Nxg4+ Nxg4+ 43.Rxg4 Rd5 44.f6 Rd1 45.g7 1-0\n", "\"\"\"\n", "\n", "response = chatbot.chat(prompt=prompt)\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {"id": "2sVuJsJbWw2l"}, "source": ["# Supporting Chess Learning"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "1z9BXjhUW3tm"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Okay, let's break down the Sicilian Defense in chess. It's a hugely popular and complex opening, considered the most popular response to <PERSON>'s opening move of 1. e4. Here's a comprehensive explanation, covering its basics, key ideas, variations, and why it's so important.\n", "\n", "**1. The Basic Idea: Challenging White's Center**\n", "\n", "* **<PERSON>'s Opening:** <PERSON> typically starts with 1. e4 (moving the King's pawn two squares forward). This aims to control the center of the board and open lines for development.\n", "* **Black's Response: 1... c5** This is the defining move of the Sicilian Defense. Instead of mirroring <PERSON>'s pawn move (like 1... e5, which leads to more symmetrical openings), <PERSON> plays 1... c5.\n", "* **What it Does:**\n", "    * **Challenges <PERSON>'s Central Control:**  The c5 pawn immediately attacks <PERSON>'s d4 square.  It prevents <PERSON> from easily establishing a strong pawn center with d4.\n", "    * **Asymmetrical Position:** The Sicilian creates an asymmetrical position from the very beginning. This leads to more dynamic, tactical, and often sharper games compared to more symmetrical openings.\n", "    * **Counterattacking Potential:** <PERSON> often aims to develop a strong attack on <PERSON>'s kingside.\n", "\n", "**2. Why is it so Popular?**\n", "\n", "* **Fighting for the Win:** <PERSON> isn't content to simply equalize. The Sicilian is a fighting opening, giving Black good chances to play for a win against 1. e4.\n", "* **Rich in Theory:** It's incredibly well-studied, with a vast amount of theory. This means there are many different variations to choose from, allowing players to tailor their approach to their style.\n", "* **Dynamic and Tactical:**  The Sicilian often leads to exciting, tactical battles.\n", "* **Historically Significant:**  Many world champions (<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>) have used the Sicilian extensively.\n", "\n", "**3. Key Variations (A Simplified Overview - There are *many*!)**\n", "\n", "The Sicilian Defense branches out into a *huge* number of variations. Here are some of the most important and common ones, grouped roughly by their character:\n", "\n", "* **Open Sicilian (2. Nf3 d6 3. d4):** This is the most common and theoretically rich branch. <PERSON> plays d4, opening the position.\n", "    * **<PERSON><PERSON><PERSON> Variation (3... cxd4 4. Nxd4 Nf6 5. Nc3 a6):**  The most popular and arguably most complex variation. <PERSON> prepares ...e5 and ...b5 to challenge <PERSON>'s control of the center and kingside.\n", "    * **Dragon Variation (3... g6):** Black fianchettoes (develops) their bishop to g7, creating a powerful diagonal aimed at <PERSON>'s center.  Known for sharp, attacking games.\n", "    * **Classical Variation (3... Nf6 4. Nc3 Nc6):** A more solid and positional approach.\n", "    * **Scheveningen Variation (3... e6 4. Nc3 Nf6 5. g3):** Black creates a solid pawn structure and prepares to develop their pieces.\n", "* **Closed Sicilian (2. Nc3):** White avoids the open game and aims for a more positional struggle.  Less common at the highest levels but can be effective.\n", "* **<PERSON><PERSON><PERSON> Variation (2. c3):** White supports their d4 pawn push and aims for a solid, positional game.\n", "* **<PERSON><PERSON><PERSON> (2. Nf3 Nc6 3. Bb5):** <PERSON> avoids the main lines of the Open Sicilian by developing the bishop to b5, pinning the knight.\n", "\n", "**4. <PERSON> for Black in the Sicilian**\n", "\n", "* **Control the d5 Square:** This is a crucial square in many Sicilian variations. Black often tries to control it, either directly with pawns or indirectly with pieces.\n", "* **Dev<PERSON>p Actively:** Black needs to develop their pieces quickly and efficiently, often focusing on the queenside.\n", "* **Kingside Attack:** Many Sicilian variations involve a later kingside attack by <PERSON>.\n", "* **Be Prepared for Sharp Tactics:** The Sicilian is a tactical opening, so <PERSON> needs to be sharp and calculating.\n", "\n", "**5. <PERSON> for White in the Sicilian**\n", "\n", "* **Establish a Strong Center:** <PERSON> often tries to establish a strong pawn center with d4 and e4.\n", "* **Control the Open Files:** <PERSON> needs to control the open files, especially the d-file and e-file.\n", "* **Kingside Defense:** White needs to be prepared to defend against <PERSON>'s kingside attack.\n", "* **Exploit Black's Queenside Weaknesses:**  The Sicilian can sometimes leave <PERSON>'s queenside slightly vulnerable.\n", "\n", "**Resources for Learning More:**\n", "\n", "* **Chess.com:** Has articles, lessons, and videos on the Sicilian Defense.\n", "* **Lichess.org:** Similar resources to Chess.com.\n", "* **YouTube:** Search for \"Sicilian Defense\" and you'll find countless videos explaining the opening.\n", "* **Chess Books:** There are many books dedicated to the Sicilian Defense, covering specific variations in detail.\n", "\n", "\n", "\n", "**To help me tailor my explanation further, could you tell me:**\n", "\n", "*   Are you a beginner, intermediate, or advanced chess player?\n", "*   Are you interested in learning a specific variation of the Sicilian?\n"]}], "source": ["response = chatbot.chat(prompt=\"What is the \\\"Sicilian Defense\\\" in chess?\")\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QJEho9TCikVE"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["자, 시실리안 디펜스는 체스에서 아주 유명하고 재미있는 수들로 이루어진 전략이야. 마치 숨바꼭질처럼, 상대방을 속이고 기회를 잡는 방법이라고 생각하면 돼!\n", "\n", "**체스 게임 시작:**\n", "\n", "체스 게임은 보통 이렇게 시작해.\n", "\n", "```\n", "   a b c d e f g h\n", "8 ♖ ♘ ♗ ♕ ♔ ♗ ♘ ♖ 8\n", "7 ♙ ♙ ♙ ♙ ♙ ♙ ♙ ♙ 7\n", "6 . . . . . . . . 6\n", "5 . . . . . . . . 5\n", "4 . . . . . . . . 4\n", "3 . . . . . . . . 3\n", "2 ♙ ♙ ♙ ♙ ♙ ♙ ♙ ♙ 2\n", "1 ♖ ♘ ♗ ♕ ♔ ♗ ♘ ♖ 1\n", "   a b c d e f g h\n", "```\n", "\n", "**시실리안 디펜스는 뭘까?**\n", "\n", "보통 백(흰색)이 먼저 움직이는데, 백이 폰을 앞으로 두 칸 움직이는 'e4' 수로 시작하는 경우가 많아. 그러면 검(검은색)은 그냥 폰을 앞으로 두 칸 움직이는 'e5' 대신, 다른 폰을 움직여서 백의 e4 폰을 공격하는 거야. 주로 'c5' 수로 움직여.\n", "\n", "**왜 시실리안 디펜스라고 부를까?**\n", "\n", "이 수법이 처음 시실리 섬에서 유행하기 시작했기 때문에 '시실리안'이라는 이름이 붙었대.\n", "\n", "**왜 이렇게 움직이는 걸까?**\n", "\n", "*   **상대방을 당황하게 만들기:** 백은 보통 e4에 폰을 움직였을 때, 검은색이 e5로 똑같이 움직일 거라고 예상하거든. 그런데 검은색이 다른 폰을 움직이면 백은 당황해서 어떤 수를 둘지 고민하게 돼.\n", "*   **더 재미있는 게임 만들기:** 시실리안 디펜스는 복잡하고 다양한 수들이 나오기 때문에, 체스 게임이 더 재미있어질 수 있어. 마치 미로처럼, 상대방을 헷갈리게 만들면서 이길 수 있는 기회를 노리는 거지!\n", "\n", "**쉽게 말하면:**\n", "\n", "시실리안 디펜스는 체스에서 상대방을 놀라게 하고, 더 복잡하고 재미있는 게임을 만들 수 있는 전략이야. 마치 숨바꼭질에서 상대방이 어디 있는지 몰라하도록 숨는 것과 비슷하다고 생각하면 돼!\n", "\n", "**더 궁금한 점이 있다면 언제든지 물어봐!**\n"]}], "source": ["response = chatbot.chat(prompt=\"시실리안 디펜스가 뭐야? 초등학생도 쉽게 이해할 수 있게 설명해줘.\")\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "YeSWH9kVXcpi"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Okay, let's break down the concept of a \"passed pawn\" in chess. It's a crucial idea for understanding pawn structure and endgame strategy.\n", "\n", "**What is a Passed Pawn?**\n", "\n", "A passed pawn is a pawn that has **no opposing pawns** on its file (the column it sits on) or on any adjacent files (the files immediately to its left and right).  Essentially, there's nothing blocking its path to promotion.\n", "\n", "**Let's illustrate with an example:**\n", "\n", "Imagine a chessboard.  Let's say <PERSON> has a pawn on e5.\n", "\n", "*   **If** there are no Black pawns on the e-file (e6, e7, e8) and no Black pawns on the d-file (d6, d7, d8) and no Black pawns on the f-file (f6, f7, f8), then the pawn on e5 is a **passed pawn**.\n", "\n", "*   **If** Black has a pawn on e6, then the pawn on e5 is *not* a passed pawn.  It's blocked.\n", "\n", "**Why are Passed Pawns Important?**\n", "\n", "Passed pawns are powerful because they represent a potential threat of promotion.  Here's why they're valuable:\n", "\n", "*   **Promotion Threat:** The ultimate goal of a pawn is to reach the opposite end of the board and promote to a <PERSON>, <PERSON>ook, <PERSON>, or Knight. A passed pawn has a clear, unobstructed path to do so.\n", "*   **Forcing Action:**  A passed pawn forces the opponent to deal with it. They must dedicate pieces to block or capture it, which can tie up their forces and create weaknesses elsewhere on the board.\n", "*   **Endgame Dominance:** Passed pawns are *especially* strong in the endgame (when there are fewer pieces on the board).  With fewer pieces to defend, a passed pawn can often be pushed to promotion, winning the game.\n", "*   **Creating Weaknesses:**  The need to stop a passed pawn can force the opponent to move their King to defend, which can expose other squares and create tactical opportunities.\n", "\n", "**Types of Passed Pawns:**\n", "\n", "*   **Protected Passed Pawn:** A passed pawn that is supported by another pawn. This makes it much harder to capture and even more dangerous.\n", "*   **Isolated Passed Pawn:** A passed pawn that has no friendly pawns on adjacent files to support it. While it can be a threat, it's also vulnerable to attack.\n", "*   **Connected Passed Pawn:** A passed pawn that is connected to other friendly pawns. This provides mutual support and makes it very difficult to stop.\n", "\n", "**Key Takeaways:**\n", "\n", "*   A passed pawn is a pawn with a clear path to promotion.\n", "*   They are a significant threat and force the opponent to react.\n", "*   They are particularly powerful in the endgame.\n", "*   The type of passed pawn (protected, isolated, connected) affects its strength and vulnerability.\n", "\n", "\n", "\n", "Do you want me to give you some examples of how passed pawns are used in actual chess games, or perhaps explain how to create one?\n"]}], "source": ["response = chatbot.chat(prompt=\"Give me a clear explanation of the concept of a \\\"passed pawn\\\" in chess.\")\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {"id": "torZC-ChNFWv"}, "source": ["# <PERSON> explain chess"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "EZdfZZDgSLFA"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--2025-04-17 10:26:35--  https://github.com/official-stockfish/Stockfish/releases/latest/download/stockfish-ubuntu-x86-64-avx2.tar\n", "Resolving github.com (github.com)... 140.82.112.3\n", "Connecting to github.com (github.com)|140.82.112.3|:443... connected.\n", "HTTP request sent, awaiting response... 302 Found\n", "Location: https://github.com/official-stockfish/Stockfish/releases/download/sf_17.1/stockfish-ubuntu-x86-64-avx2.tar [following]\n", "--2025-04-17 10:26:35--  https://github.com/official-stockfish/Stockfish/releases/download/sf_17.1/stockfish-ubuntu-x86-64-avx2.tar\n", "Reusing existing connection to github.com:443.\n", "HTTP request sent, awaiting response... 302 Found\n", "Location: https://objects.githubusercontent.com/github-production-release-asset-2e65be/20976138/85758419-9488-4267-84ea-dc1379a61eb1?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=releaseassetproduction%2F20250417%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250417T102636Z&X-Amz-Expires=300&X-Amz-Signature=a4ece38c7d3844f03978d51f858ffdc22118ec7be587ad8134302ffacb96b9a0&X-Amz-SignedHeaders=host&response-content-disposition=attachment%3B%20filename%3Dstockfish-ubuntu-x86-64-avx2.tar&response-content-type=application%2Foctet-stream [following]\n", "--2025-04-17 10:26:36--  https://objects.githubusercontent.com/github-production-release-asset-2e65be/20976138/85758419-9488-4267-84ea-dc1379a61eb1?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=releaseassetproduction%2F20250417%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250417T102636Z&X-Amz-Expires=300&X-Amz-Signature=a4ece38c7d3844f03978d51f858ffdc22118ec7be587ad8134302ffacb96b9a0&X-Amz-SignedHeaders=host&response-content-disposition=attachment%3B%20filename%3Dstockfish-ubuntu-x86-64-avx2.tar&response-content-type=application%2Foctet-stream\n", "Resolving objects.githubusercontent.com (objects.githubusercontent.com)... ***************, ***************, ***************, ...\n", "Connecting to objects.githubusercontent.com (objects.githubusercontent.com)|***************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 80087040 (76M) [application/octet-stream]\n", "Saving to: ‘stockfish-ubuntu-x86-64-avx2.tar.1’\n", "\n", "stockfish-ubuntu-x8 100%[===================>]  76.38M   240MB/s    in 0.3s    \n", "\n", "2025-04-17 10:26:36 (240 MB/s) - ‘stockfish-ubuntu-x86-64-avx2.tar.1’ saved [80087040/80087040]\n", "\n"]}], "source": ["!wget https://github.com/official-stockfish/Stockfish/releases/latest/download/stockfish-ubuntu-x86-64-avx2.tar\n", "!tar -xf stockfish-ubuntu-x86-64-avx2.tar"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LM4ecVq1P1aV"}, "outputs": [{"data": {"image/svg+xml": "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" viewBox=\"0 0 390 390\" width=\"390\" height=\"390\"><desc><pre>. . . r r . . .\n. . . . . . . .\n. . p . . . P k\n. p . . n P . .\np P . . p . B .\nP . B . N b . .\n. . P . . K . .\n. . . . . . R .</pre></desc><defs><g id=\"white-pawn\" class=\"white pawn\"><path d=\"M22.5 9c-2.21 0-4 1.79-4 4 0 .89.29 1.71.78 2.38C17.33 16.5 16 18.59 16 21c0 2.03.94 3.84 2.41 5.03-3 1.06-7.41 5.55-7.41 13.47h23c0-7.92-4.41-12.41-7.41-13.47 1.47-1.19 2.41-3 2.41-5.03 0-2.41-1.33-4.5-3.28-5.62.49-.67.78-1.49.78-2.38 0-2.21-1.79-4-4-4z\" fill=\"#fff\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" /></g><g id=\"white-knight\" class=\"white knight\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M 22,10 C 32.5,11 38.5,18 38,39 L 15,39 C 15,30 25,32.5 23,18\" style=\"fill:#ffffff; stroke:#000000;\" /><path d=\"M 24,18 C 24.38,20.91 18.45,25.37 16,27 C 13,29 13.18,31.34 11,31 C 9.958,30.06 12.41,27.96 11,28 C 10,28 11.19,29.23 10,30 C 9,30 5.997,31 6,26 C 6,24 12,14 12,14 C 12,14 13.89,12.1 14,10.5 C 13.27,9.506 13.5,8.5 13.5,7.5 C 14.5,6.5 16.5,10 16.5,10 L 18.5,10 C 18.5,10 19.28,8.008 21,7 C 22,7 22,10 22,10\" style=\"fill:#ffffff; stroke:#000000;\" /><path d=\"M 9.5 25.5 A 0.5 0.5 0 1 1 8.5,25.5 A 0.5 0.5 0 1 1 9.5 25.5 z\" style=\"fill:#000000; stroke:#000000;\" /><path d=\"M 15 15.5 A 0.5 1.5 0 1 1 14,15.5 A 0.5 1.5 0 1 1 15 15.5 z\" transform=\"matrix(0.866,0.5,-0.5,0.866,9.693,-5.173)\" style=\"fill:#000000; stroke:#000000;\" /></g><g id=\"white-bishop\" class=\"white bishop\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g fill=\"#fff\" stroke-linecap=\"butt\"><path d=\"M9 36c3.39-.97 10.11.43 13.5-2 3.39 2.43 10.11 1.03 13.5 2 0 0 1.65.54 3 2-.68.97-1.65.99-3 .5-3.39-.97-10.11.46-13.5-1-3.39 1.46-10.11.03-13.5 1-1.354.49-2.323.47-3-.5 1.354-1.94 3-2 3-2zM15 32c2.5 2.5 12.5 2.5 15 0 .5-1.5 0-2 0-2 0-2.5-2.5-4-2.5-4 5.5-1.5 6-11.5-5-15.5-11 4-10.5 14-5 15.5 0 0-2.5 1.5-2.5 4 0 0-.5.5 0 2zM25 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 1 1 5 0z\" /></g><path d=\"M17.5 26h10M15 30h15m-7.5-14.5v5M20 18h5\" stroke-linejoin=\"miter\" /></g><g id=\"white-rook\" class=\"white rook\" fill=\"#fff\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M9 39h27v-3H9v3zM12 36v-4h21v4H12zM11 14V9h4v2h5V9h5v2h5V9h4v5\" stroke-linecap=\"butt\" /><path d=\"M34 14l-3 3H14l-3-3\" /><path d=\"M31 17v12.5H14V17\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" /><path d=\"M31 29.5l1.5 2.5h-20l1.5-2.5\" /><path d=\"M11 14h23\" fill=\"none\" stroke-linejoin=\"miter\" /></g><g id=\"white-king\" class=\"white king\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M22.5 11.63V6M20 8h5\" stroke-linejoin=\"miter\" /><path d=\"M22.5 25s4.5-7.5 3-10.5c0 0-1-2.5-3-2.5s-3 2.5-3 2.5c-1.5 3 3 10.5 3 10.5\" fill=\"#fff\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" /><path d=\"M11.5 37c5.5 3.5 15.5 3.5 21 0v-7s9-4.5 6-10.5c-4-6.5-13.5-3.5-16 4V27v-3.5c-3.5-7.5-13-10.5-16-4-3 6 5 10 5 10V37z\" fill=\"#fff\" /><path d=\"M11.5 30c5.5-3 15.5-3 21 0m-21 3.5c5.5-3 15.5-3 21 0m-21 3.5c5.5-3 15.5-3 21 0\" /></g><g id=\"black-pawn\" class=\"black pawn\"><path d=\"M22.5 9c-2.21 0-4 1.79-4 4 0 .89.29 1.71.78 2.38C17.33 16.5 16 18.59 16 21c0 2.03.94 3.84 2.41 5.03-3 1.06-7.41 5.55-7.41 13.47h23c0-7.92-4.41-12.41-7.41-13.47 1.47-1.19 2.41-3 2.41-5.03 0-2.41-1.33-4.5-3.28-5.62.49-.67.78-1.49.78-2.38 0-2.21-1.79-4-4-4z\" fill=\"#000\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" /></g><g id=\"black-knight\" class=\"black knight\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M 22,10 C 32.5,11 38.5,18 38,39 L 15,39 C 15,30 25,32.5 23,18\" style=\"fill:#000000; stroke:#000000;\" /><path d=\"M 24,18 C 24.38,20.91 18.45,25.37 16,27 C 13,29 13.18,31.34 11,31 C 9.958,30.06 12.41,27.96 11,28 C 10,28 11.19,29.23 10,30 C 9,30 5.997,31 6,26 C 6,24 12,14 12,14 C 12,14 13.89,12.1 14,10.5 C 13.27,9.506 13.5,8.5 13.5,7.5 C 14.5,6.5 16.5,10 16.5,10 L 18.5,10 C 18.5,10 19.28,8.008 21,7 C 22,7 22,10 22,10\" style=\"fill:#000000; stroke:#000000;\" /><path d=\"M 9.5 25.5 A 0.5 0.5 0 1 1 8.5,25.5 A 0.5 0.5 0 1 1 9.5 25.5 z\" style=\"fill:#ececec; stroke:#ececec;\" /><path d=\"M 15 15.5 A 0.5 1.5 0 1 1 14,15.5 A 0.5 1.5 0 1 1 15 15.5 z\" transform=\"matrix(0.866,0.5,-0.5,0.866,9.693,-5.173)\" style=\"fill:#ececec; stroke:#ececec;\" /><path d=\"M 24.55,10.4 L 24.1,11.85 L 24.6,12 C 27.75,13 30.25,14.49 32.5,18.75 C 34.75,23.01 35.75,29.06 35.25,39 L 35.2,39.5 L 37.45,39.5 L 37.5,39 C 38,28.94 36.62,22.15 34.25,17.66 C 31.88,13.17 28.46,11.02 25.06,10.5 L 24.55,10.4 z \" style=\"fill:#ececec; stroke:none;\" /></g><g id=\"black-bishop\" class=\"black bishop\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M9 36c3.39-.97 10.11.43 13.5-2 3.39 2.43 10.11 1.03 13.5 2 0 0 1.65.54 3 2-.68.97-1.65.99-3 .5-3.39-.97-10.11.46-13.5-1-3.39 1.46-10.11.03-13.5 1-1.354.49-2.323.47-3-.5 1.354-1.94 3-2 3-2zm6-4c2.5 2.5 12.5 2.5 15 0 .5-1.5 0-2 0-2 0-2.5-2.5-4-2.5-4 5.5-1.5 6-11.5-5-15.5-11 4-10.5 14-5 15.5 0 0-2.5 1.5-2.5 4 0 0-.5.5 0 2zM25 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 1 1 5 0z\" fill=\"#000\" stroke-linecap=\"butt\" /><path d=\"M17.5 26h10M15 30h15m-7.5-14.5v5M20 18h5\" stroke=\"#fff\" stroke-linejoin=\"miter\" /></g><g id=\"black-rook\" class=\"black rook\" fill=\"#000\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M9 39h27v-3H9v3zM12.5 32l1.5-2.5h17l1.5 2.5h-20zM12 36v-4h21v4H12z\" stroke-linecap=\"butt\" /><path d=\"M14 29.5v-13h17v13H14z\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" /><path d=\"M14 16.5L11 14h23l-3 2.5H14zM11 14V9h4v2h5V9h5v2h5V9h4v5H11z\" stroke-linecap=\"butt\" /><path d=\"M12 35.5h21M13 31.5h19M14 29.5h17M14 16.5h17M11 14h23\" fill=\"none\" stroke=\"#fff\" stroke-width=\"1\" stroke-linejoin=\"miter\" /></g><g id=\"black-king\" class=\"black king\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M22.5 11.63V6\" stroke-linejoin=\"miter\" /><path d=\"M22.5 25s4.5-7.5 3-10.5c0 0-1-2.5-3-2.5s-3 2.5-3 2.5c-1.5 3 3 10.5 3 10.5\" fill=\"#000\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" /><path d=\"M11.5 37c5.5 3.5 15.5 3.5 21 0v-7s9-4.5 6-10.5c-4-6.5-13.5-3.5-16 4V27v-3.5c-3.5-7.5-13-10.5-16-4-3 6 5 10 5 10V37z\" fill=\"#000\" /><path d=\"M20 8h5\" stroke-linejoin=\"miter\" /><path d=\"M32 29.5s8.5-4 6.03-9.65C34.15 14 25 18 22.5 24.5l.01 2.1-.01-2.1C20 18 9.906 14 6.997 19.85c-2.497 5.65 4.853 9 4.853 9M11.5 30c5.5-3 15.5-3 21 0m-21 3.5c5.5-3 15.5-3 21 0m-21 3.5c5.5-3 15.5-3 21 0\" stroke=\"#fff\" /></g></defs><rect x=\"7.5\" y=\"7.5\" width=\"375\" height=\"375\" fill=\"none\" stroke=\"#212121\" stroke-width=\"15\" /><g transform=\"translate(20, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M23.328 10.016q-1.742 0-2.414.398-.672.398-.672 1.36 0 .765.5 1.218.508.445 1.375.445 1.196 0 1.914-.843.727-.852.727-2.258v-.32zm2.867-.594v4.992h-1.437v-1.328q-.492.797-1.227 1.18-.734.375-1.797.375-1.343 0-2.14-.75-.79-.758-.79-2.024 0-1.476.985-2.226.992-.75 2.953-.75h2.016V8.75q0-.992-.656-1.531-.649-.547-1.829-.547-.75 0-1.46.18-.711.18-1.368.539V6.062q.79-.304 1.532-.453.742-.156 1.445-.156 1.898 0 2.836.984.937.985.937 2.985z\" /></g><g transform=\"translate(20, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M23.328 10.016q-1.742 0-2.414.398-.672.398-.672 1.36 0 .765.5 1.218.508.445 1.375.445 1.196 0 1.914-.843.727-.852.727-2.258v-.32zm2.867-.594v4.992h-1.437v-1.328q-.492.797-1.227 1.18-.734.375-1.797.375-1.343 0-2.14-.75-.79-.758-.79-2.024 0-1.476.985-2.226.992-.75 2.953-.75h2.016V8.75q0-.992-.656-1.531-.649-.547-1.829-.547-.75 0-1.46.18-.711.18-1.368.539V6.062q.79-.304 1.532-.453.742-.156 1.445-.156 1.898 0 2.836.984.937.985.937 2.985z\" /></g><g transform=\"translate(65, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.922 10.047q0-1.586-.656-2.485-.649-.906-1.79-.906-1.14 0-1.796.906-.649.899-.649 2.485 0 1.586.649 2.492.656.898 1.797.898 1.14 0 1.789-.898.656-.906.656-2.492zm-4.89-3.055q.452-.781 1.14-1.156.695-.383 1.656-.383 1.594 0 2.586 1.266 1 1.265 1 3.328 0 2.062-1 3.328-.992 1.266-2.586 1.266-.96 0-1.656-.375-.688-.383-1.14-1.164v1.312h-1.446V2.258h1.445z\" /></g><g transform=\"translate(65, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.922 10.047q0-1.586-.656-2.485-.649-.906-1.79-.906-1.14 0-1.796.906-.649.899-.649 2.485 0 1.586.649 2.492.656.898 1.797.898 1.14 0 1.789-.898.656-.906.656-2.492zm-4.89-3.055q.452-.781 1.14-1.156.695-.383 1.656-.383 1.594 0 2.586 1.266 1 1.265 1 3.328 0 2.062-1 3.328-.992 1.266-2.586 1.266-.96 0-1.656-.375-.688-.383-1.14-1.164v1.312h-1.446V2.258h1.445z\" /></g><g transform=\"translate(110, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M25.96 6v1.344q-.608-.336-1.226-.5-.609-.172-1.234-.172-1.398 0-2.172.89-.773.883-.773 2.485 0 1.601.773 2.492.774.883 2.172.883.625 0 1.234-.164.618-.172 1.227-.508v1.328q-.602.281-1.25.422-.64.14-1.367.14-1.977 0-3.14-1.242-1.165-1.242-1.165-3.351 0-2.14 1.172-3.367 1.18-1.227 3.227-1.227.664 0 1.296.14.633.134 1.227.407z\" /></g><g transform=\"translate(110, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M25.96 6v1.344q-.608-.336-1.226-.5-.609-.172-1.234-.172-1.398 0-2.172.89-.773.883-.773 2.485 0 1.601.773 2.492.774.883 2.172.883.625 0 1.234-.164.618-.172 1.227-.508v1.328q-.602.281-1.25.422-.64.14-1.367.14-1.977 0-3.14-1.242-1.165-1.242-1.165-3.351 0-2.14 1.172-3.367 1.18-1.227 3.227-1.227.664 0 1.296.14.633.134 1.227.407z\" /></g><g transform=\"translate(155, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.973 6.992V2.258h1.437v12.156h-1.437v-1.312q-.453.78-1.149 1.164-.687.375-1.656.375-1.586 0-2.586-1.266-.992-1.266-.992-3.328 0-2.063.992-3.328 1-1.266 2.586-1.266.969 0 1.656.383.696.375 1.149 1.156zm-4.899 3.055q0 1.586.649 2.492.656.898 1.797.898 1.14 0 1.796-.898.657-.906.657-2.492 0-1.586-.657-2.485-.656-.906-1.796-.906-1.141 0-1.797.906-.649.899-.649 2.485z\" /></g><g transform=\"translate(155, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.973 6.992V2.258h1.437v12.156h-1.437v-1.312q-.453.78-1.149 1.164-.687.375-1.656.375-1.586 0-2.586-1.266-.992-1.266-.992-3.328 0-2.063.992-3.328 1-1.266 2.586-1.266.969 0 1.656.383.696.375 1.149 1.156zm-4.899 3.055q0 1.586.649 2.492.656.898 1.797.898 1.14 0 1.796-.898.657-.906.657-2.492 0-1.586-.657-2.485-.656-.906-1.796-.906-1.141 0-1.797.906-.649.899-.649 2.485z\" /></g><g transform=\"translate(200, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M26.555 9.68v.703h-6.61q.094 1.484.89 2.265.806.774 2.235.774.828 0 1.602-.203.781-.203 1.547-.61v1.36q-.774.328-1.586.5-.813.172-1.649.172-2.093 0-3.32-1.22-1.219-1.218-1.219-3.296 0-2.148 1.157-3.406 1.164-1.266 3.132-1.266 1.766 0 2.79 1.14 1.03 1.134 1.03 3.087zm-1.438-.422q-.015-1.18-.664-1.883-.64-.703-1.703-.703-1.203 0-1.93.68-.718.68-.828 1.914z\" /></g><g transform=\"translate(200, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M26.555 9.68v.703h-6.61q.094 1.484.89 2.265.806.774 2.235.774.828 0 1.602-.203.781-.203 1.547-.61v1.36q-.774.328-1.586.5-.813.172-1.649.172-2.093 0-3.32-1.22-1.219-1.218-1.219-3.296 0-2.148 1.157-3.406 1.164-1.266 3.132-1.266 1.766 0 2.79 1.14 1.03 1.134 1.03 3.087zm-1.438-.422q-.015-1.18-.664-1.883-.64-.703-1.703-.703-1.203 0-1.93.68-.718.68-.828 1.914z\" /></g><g transform=\"translate(245, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M25.285 2.258v1.195H23.91q-.773 0-1.078.313-.297.312-.297 1.125v.773h2.367v1.117h-2.367v7.633H21.09V6.781h-1.375V5.664h1.375v-.61q0-1.46.68-2.124.68-.672 2.156-.672z\" /></g><g transform=\"translate(245, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M25.285 2.258v1.195H23.91q-.773 0-1.078.313-.297.312-.297 1.125v.773h2.367v1.117h-2.367v7.633H21.09V6.781h-1.375V5.664h1.375v-.61q0-1.46.68-2.124.68-.672 2.156-.672z\" /></g><g transform=\"translate(290, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.973 9.937q0-1.562-.649-2.421-.64-.86-1.804-.86-1.157 0-1.805.86-.64.859-.64 2.421 0 1.555.64 2.415.648.859 1.805.859 1.164 0 1.804-.86.649-.859.649-2.414zm1.437 3.391q0 2.234-.992 3.32-.992 1.094-3.04 1.094-.757 0-1.429-.117-.672-.11-1.304-.344v-1.398q.632.344 1.25.508.617.164 1.257.164 1.414 0 2.118-.743.703-.734.703-2.226v-.711q-.446.773-1.141 1.156-.695.383-1.664.383-1.61 0-2.594-1.227-.984-1.226-.984-3.25 0-2.03.984-3.257.985-1.227 2.594-1.227.969 0 1.664.383t1.14 1.156V5.664h1.438z\" /></g><g transform=\"translate(290, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.973 9.937q0-1.562-.649-2.421-.64-.86-1.804-.86-1.157 0-1.805.86-.64.859-.64 2.421 0 1.555.64 2.415.648.859 1.805.859 1.164 0 1.804-.86.649-.859.649-2.414zm1.437 3.391q0 2.234-.992 3.32-.992 1.094-3.04 1.094-.757 0-1.429-.117-.672-.11-1.304-.344v-1.398q.632.344 1.25.508.617.164 1.257.164 1.414 0 2.118-.743.703-.734.703-2.226v-.711q-.446.773-1.141 1.156-.695.383-1.664.383-1.61 0-2.594-1.227-.984-1.226-.984-3.25 0-2.03.984-3.257.985-1.227 2.594-1.227.969 0 1.664.383t1.14 1.156V5.664h1.438z\" /></g><g transform=\"translate(335, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M26.164 9.133v5.281h-1.437V9.18q0-1.243-.485-1.86-.484-.617-1.453-.617-1.164 0-1.836.742-.672.742-.672 2.024v4.945h-1.445V2.258h1.445v4.765q.516-.789 1.211-1.18.703-.39 1.617-.39 1.508 0 2.282.938.773.93.773 2.742z\" /></g><g transform=\"translate(335, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M26.164 9.133v5.281h-1.437V9.18q0-1.243-.485-1.86-.484-.617-1.453-.617-1.164 0-1.836.742-.672.742-.672 2.024v4.945h-1.445V2.258h1.445v4.765q.516-.789 1.211-1.18.703-.39 1.617-.39 1.508 0 2.282.938.773.93.773 2.742z\" /></g><g transform=\"translate(0, 335) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.754 26.996h2.578v-8.898l-2.805.562v-1.437l2.79-.563h1.578v10.336h2.578v1.328h-6.72z\" /></g><g transform=\"translate(375, 335) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.754 26.996h2.578v-8.898l-2.805.562v-1.437l2.79-.563h1.578v10.336h2.578v1.328h-6.72z\" /></g><g transform=\"translate(0, 290) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M8.195 26.996h5.508v1.328H6.297v-1.328q.898-.93 2.445-2.492 1.555-1.57 1.953-2.024.758-.851 1.055-1.437.305-.594.305-1.164 0-.93-.657-1.516-.648-.586-1.695-.586-.742 0-1.57.258-.82.258-1.758.781v-1.593q.953-.383 1.781-.578.828-.196 1.516-.196 1.812 0 2.89.906 1.079.907 1.079 2.422 0 .72-.274 1.368-.265.64-.976 1.515-.196.227-1.243 1.313-1.046 1.078-2.953 3.023z\" /></g><g transform=\"translate(375, 290) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M8.195 26.996h5.508v1.328H6.297v-1.328q.898-.93 2.445-2.492 1.555-1.57 1.953-2.024.758-.851 1.055-1.437.305-.594.305-1.164 0-.93-.657-1.516-.648-.586-1.695-.586-.742 0-1.57.258-.82.258-1.758.781v-1.593q.953-.383 1.781-.578.828-.196 1.516-.196 1.812 0 2.89.906 1.079.907 1.079 2.422 0 .72-.274 1.368-.265.64-.976 1.515-.196.227-1.243 1.313-1.046 1.078-2.953 3.023z\" /></g><g transform=\"translate(0, 245) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M11.434 22.035q1.132.242 1.765 1.008.64.766.64 1.89 0 1.727-1.187 2.672-1.187.946-3.375.946-.734 0-1.515-.149-.774-.14-1.602-.43V26.45q.656.383 1.438.578.78.196 1.632.196 1.485 0 2.258-.586.782-.586.782-1.703 0-1.032-.727-1.61-.719-.586-2.008-.586h-1.36v-1.297h1.423q1.164 0 1.78-.46.618-.47.618-1.344 0-.899-.64-1.375-.633-.485-1.82-.485-.65 0-1.391.141-.743.14-1.633.437V16.95q.898-.25 1.68-.375.788-.125 1.484-.125 1.797 0 2.844.82 1.046.813 1.046 2.204 0 .968-.554 1.64-.555.664-1.578.922z\" /></g><g transform=\"translate(375, 245) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M11.434 22.035q1.132.242 1.765 1.008.64.766.64 1.89 0 1.727-1.187 2.672-1.187.946-3.375.946-.734 0-1.515-.149-.774-.14-1.602-.43V26.45q.656.383 1.438.578.78.196 1.632.196 1.485 0 2.258-.586.782-.586.782-1.703 0-1.032-.727-1.61-.719-.586-2.008-.586h-1.36v-1.297h1.423q1.164 0 1.78-.46.618-.47.618-1.344 0-.899-.64-1.375-.633-.485-1.82-.485-.65 0-1.391.141-.743.14-1.633.437V16.95q.898-.25 1.68-.375.788-.125 1.484-.125 1.797 0 2.844.82 1.046.813 1.046 2.204 0 .968-.554 1.64-.555.664-1.578.922z\" /></g><g transform=\"translate(0, 200) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M11.016 18.035L7.03 24.262h3.985zm-.414-1.375h1.984v7.602h1.664v1.312h-1.664v2.75h-1.57v-2.75H5.75v-1.523z\" /></g><g transform=\"translate(375, 200) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M11.016 18.035L7.03 24.262h3.985zm-.414-1.375h1.984v7.602h1.664v1.312h-1.664v2.75h-1.57v-2.75H5.75v-1.523z\" /></g><g transform=\"translate(0, 155) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.719 16.66h6.195v1.328h-4.75v2.86q.344-.118.688-.172.343-.063.687-.063 1.953 0 3.094 1.07 1.14 1.07 1.14 2.899 0 1.883-1.171 2.93-1.172 1.039-3.305 1.039-.735 0-1.5-.125-.758-.125-1.57-.375v-1.586q.703.383 1.453.57.75.188 1.586.188 1.351 0 2.14-.711.79-.711.79-1.93 0-1.219-.79-1.93-.789-.71-2.14-.71-.633 0-1.266.14-.625.14-1.281.438z\" /></g><g transform=\"translate(375, 155) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.719 16.66h6.195v1.328h-4.75v2.86q.344-.118.688-.172.343-.063.687-.063 1.953 0 3.094 1.07 1.14 1.07 1.14 2.899 0 1.883-1.171 2.93-1.172 1.039-3.305 1.039-.735 0-1.5-.125-.758-.125-1.57-.375v-1.586q.703.383 1.453.57.75.188 1.586.188 1.351 0 2.14-.711.79-.711.79-1.93 0-1.219-.79-1.93-.789-.71-2.14-.71-.633 0-1.266.14-.625.14-1.281.438z\" /></g><g transform=\"translate(0, 110) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M10.137 21.863q-1.063 0-1.688.727-.617.726-.617 1.992 0 1.258.617 1.992.625.727 1.688.727 1.062 0 1.68-.727.624-.734.624-1.992 0-1.266-.625-1.992-.617-.727-1.68-.727zm3.133-4.945v1.437q-.594-.28-1.204-.43-.601-.148-1.195-.148-1.562 0-2.39 1.055-.82 1.055-.938 3.188.46-.68 1.156-1.04.696-.367 1.531-.367 1.758 0 2.774 1.07 1.023 1.063 1.023 2.899 0 1.797-1.062 2.883-1.063 1.086-2.828 1.086-2.024 0-3.094-1.547-1.07-1.555-1.07-4.5 0-2.766 1.312-4.406 1.313-1.649 3.524-1.649.593 0 1.195.117.61.118 1.266.352z\" /></g><g transform=\"translate(375, 110) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M10.137 21.863q-1.063 0-1.688.727-.617.726-.617 1.992 0 1.258.617 1.992.625.727 1.688.727 1.062 0 1.68-.727.624-.734.624-1.992 0-1.266-.625-1.992-.617-.727-1.68-.727zm3.133-4.945v1.437q-.594-.28-1.204-.43-.601-.148-1.195-.148-1.562 0-2.39 1.055-.82 1.055-.938 3.188.46-.68 1.156-1.04.696-.367 1.531-.367 1.758 0 2.774 1.07 1.023 1.063 1.023 2.899 0 1.797-1.062 2.883-1.063 1.086-2.828 1.086-2.024 0-3.094-1.547-1.07-1.555-1.07-4.5 0-2.766 1.312-4.406 1.313-1.649 3.524-1.649.593 0 1.195.117.61.118 1.266.352z\" /></g><g transform=\"translate(0, 65) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.25 16.66h7.5v.672L9.516 28.324H7.867l3.985-10.336H6.25z\" /></g><g transform=\"translate(375, 65) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.25 16.66h7.5v.672L9.516 28.324H7.867l3.985-10.336H6.25z\" /></g><g transform=\"translate(0, 20) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M10 22.785q-1.125 0-1.773.602-.641.601-.641 1.656t.64 1.656q.649.602 1.774.602t1.773-.602q.649-.61.649-1.656 0-1.055-.649-1.656-.64-.602-1.773-.602zm-1.578-.672q-1.016-.25-1.586-.945-.563-.695-.563-1.695 0-1.399.993-2.211 1-.813 2.734-.813 1.742 0 2.734.813.993.812.993 2.21 0 1-.57 1.696-.563.695-1.571.945 1.14.266 1.773 1.04.641.773.641 1.89 0 1.695-1.04 2.602-1.03.906-2.96.906t-2.969-.906Q6 26.738 6 25.043q0-1.117.64-1.89.641-.774 1.782-1.04zm-.578-2.492q0 .906.562 1.414.57.508 1.594.508 1.016 0 1.586-.508.578-.508.578-1.414 0-.906-.578-1.414-.57-.508-1.586-.508-1.023 0-1.594.508-.562.508-.562 1.414z\" /></g><g transform=\"translate(375, 20) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M10 22.785q-1.125 0-1.773.602-.641.601-.641 1.656t.64 1.656q.649.602 1.774.602t1.773-.602q.649-.61.649-1.656 0-1.055-.649-1.656-.64-.602-1.773-.602zm-1.578-.672q-1.016-.25-1.586-.945-.563-.695-.563-1.695 0-1.399.993-2.211 1-.813 2.734-.813 1.742 0 2.734.813.993.812.993 2.21 0 1-.57 1.696-.563.695-1.571.945 1.14.266 1.773 1.04.641.773.641 1.89 0 1.695-1.04 2.602-1.03.906-2.96.906t-2.969-.906Q6 26.738 6 25.043q0-1.117.64-1.89.641-.774 1.782-1.04zm-.578-2.492q0 .906.562 1.414.57.508 1.594.508 1.016 0 1.586-.508.578-.508.578-1.414 0-.906-.578-1.414-.57-.508-1.586-.508-1.023 0-1.594.508-.562.508-.562 1.414z\" /></g><rect x=\"15\" y=\"330\" width=\"45\" height=\"45\" class=\"square dark a1\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"60\" y=\"330\" width=\"45\" height=\"45\" class=\"square light b1\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"105\" y=\"330\" width=\"45\" height=\"45\" class=\"square dark c1\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"150\" y=\"330\" width=\"45\" height=\"45\" class=\"square light d1\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"195\" y=\"330\" width=\"45\" height=\"45\" class=\"square dark e1\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"240\" y=\"330\" width=\"45\" height=\"45\" class=\"square light f1\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"285\" y=\"330\" width=\"45\" height=\"45\" class=\"square dark g1\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"330\" y=\"330\" width=\"45\" height=\"45\" class=\"square light h1\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"15\" y=\"285\" width=\"45\" height=\"45\" class=\"square light a2\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"60\" y=\"285\" width=\"45\" height=\"45\" class=\"square dark b2\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"105\" y=\"285\" width=\"45\" height=\"45\" class=\"square light c2\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"150\" y=\"285\" width=\"45\" height=\"45\" class=\"square dark d2\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"195\" y=\"285\" width=\"45\" height=\"45\" class=\"square light e2\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"240\" y=\"285\" width=\"45\" height=\"45\" class=\"square dark f2\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"285\" y=\"285\" width=\"45\" height=\"45\" class=\"square light g2\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"330\" y=\"285\" width=\"45\" height=\"45\" class=\"square dark h2\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"15\" y=\"240\" width=\"45\" height=\"45\" class=\"square dark a3\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"60\" y=\"240\" width=\"45\" height=\"45\" class=\"square light b3\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"105\" y=\"240\" width=\"45\" height=\"45\" class=\"square dark c3\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"150\" y=\"240\" width=\"45\" height=\"45\" class=\"square light d3\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"195\" y=\"240\" width=\"45\" height=\"45\" class=\"square dark e3\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"240\" y=\"240\" width=\"45\" height=\"45\" class=\"square light f3\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"285\" y=\"240\" width=\"45\" height=\"45\" class=\"square dark g3\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"330\" y=\"240\" width=\"45\" height=\"45\" class=\"square light h3\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"15\" y=\"195\" width=\"45\" height=\"45\" class=\"square light a4\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"60\" y=\"195\" width=\"45\" height=\"45\" class=\"square dark b4\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"105\" y=\"195\" width=\"45\" height=\"45\" class=\"square light c4\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"150\" y=\"195\" width=\"45\" height=\"45\" class=\"square dark d4\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"195\" y=\"195\" width=\"45\" height=\"45\" class=\"square light e4\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"240\" y=\"195\" width=\"45\" height=\"45\" class=\"square dark f4\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"285\" y=\"195\" width=\"45\" height=\"45\" class=\"square light g4\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"330\" y=\"195\" width=\"45\" height=\"45\" class=\"square dark h4\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"15\" y=\"150\" width=\"45\" height=\"45\" class=\"square dark a5\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"60\" y=\"150\" width=\"45\" height=\"45\" class=\"square light b5\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"105\" y=\"150\" width=\"45\" height=\"45\" class=\"square dark c5\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"150\" y=\"150\" width=\"45\" height=\"45\" class=\"square light d5\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"195\" y=\"150\" width=\"45\" height=\"45\" class=\"square dark e5\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"240\" y=\"150\" width=\"45\" height=\"45\" class=\"square light f5\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"285\" y=\"150\" width=\"45\" height=\"45\" class=\"square dark g5\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"330\" y=\"150\" width=\"45\" height=\"45\" class=\"square light h5\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"15\" y=\"105\" width=\"45\" height=\"45\" class=\"square light a6\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"60\" y=\"105\" width=\"45\" height=\"45\" class=\"square dark b6\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"105\" y=\"105\" width=\"45\" height=\"45\" class=\"square light c6\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"150\" y=\"105\" width=\"45\" height=\"45\" class=\"square dark d6\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"195\" y=\"105\" width=\"45\" height=\"45\" class=\"square light e6\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"240\" y=\"105\" width=\"45\" height=\"45\" class=\"square dark f6\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"285\" y=\"105\" width=\"45\" height=\"45\" class=\"square light g6\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"330\" y=\"105\" width=\"45\" height=\"45\" class=\"square dark h6\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"15\" y=\"60\" width=\"45\" height=\"45\" class=\"square dark a7\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"60\" y=\"60\" width=\"45\" height=\"45\" class=\"square light b7\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"105\" y=\"60\" width=\"45\" height=\"45\" class=\"square dark c7\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"150\" y=\"60\" width=\"45\" height=\"45\" class=\"square light d7\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"195\" y=\"60\" width=\"45\" height=\"45\" class=\"square dark e7\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"240\" y=\"60\" width=\"45\" height=\"45\" class=\"square light f7\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"285\" y=\"60\" width=\"45\" height=\"45\" class=\"square dark g7\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"330\" y=\"60\" width=\"45\" height=\"45\" class=\"square light h7\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"15\" y=\"15\" width=\"45\" height=\"45\" class=\"square light a8\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"60\" y=\"15\" width=\"45\" height=\"45\" class=\"square dark b8\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"105\" y=\"15\" width=\"45\" height=\"45\" class=\"square light c8\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"150\" y=\"15\" width=\"45\" height=\"45\" class=\"square dark d8\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"195\" y=\"15\" width=\"45\" height=\"45\" class=\"square light e8\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"240\" y=\"15\" width=\"45\" height=\"45\" class=\"square dark f8\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"285\" y=\"15\" width=\"45\" height=\"45\" class=\"square light g8\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"330\" y=\"15\" width=\"45\" height=\"45\" class=\"square dark h8\" stroke=\"none\" fill=\"#d18b47\" /><use href=\"#white-rook\" xlink:href=\"#white-rook\" transform=\"translate(285, 330)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(105, 285)\" /><use href=\"#white-king\" xlink:href=\"#white-king\" transform=\"translate(240, 285)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(15, 240)\" /><use href=\"#white-bishop\" xlink:href=\"#white-bishop\" transform=\"translate(105, 240)\" /><use href=\"#white-knight\" xlink:href=\"#white-knight\" transform=\"translate(195, 240)\" /><use href=\"#black-bishop\" xlink:href=\"#black-bishop\" transform=\"translate(240, 240)\" /><use href=\"#black-pawn\" xlink:href=\"#black-pawn\" transform=\"translate(15, 195)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(60, 195)\" /><use href=\"#black-pawn\" xlink:href=\"#black-pawn\" transform=\"translate(195, 195)\" /><use href=\"#white-bishop\" xlink:href=\"#white-bishop\" transform=\"translate(285, 195)\" /><use href=\"#black-pawn\" xlink:href=\"#black-pawn\" transform=\"translate(60, 150)\" /><use href=\"#black-knight\" xlink:href=\"#black-knight\" transform=\"translate(195, 150)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(240, 150)\" /><use href=\"#black-pawn\" xlink:href=\"#black-pawn\" transform=\"translate(105, 105)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(285, 105)\" /><use href=\"#black-king\" xlink:href=\"#black-king\" transform=\"translate(330, 105)\" /><use href=\"#black-rook\" xlink:href=\"#black-rook\" transform=\"translate(150, 15)\" /><use href=\"#black-rook\" xlink:href=\"#black-rook\" transform=\"translate(195, 15)\" /></svg>", "text/plain": ["Board('3rr3/8/2p3Pk/1p2nP2/pP2p1B1/P1B1Nb2/2P2K2/6R1 b - - 0 41')"]}, "execution_count": 134, "metadata": {}, "output_type": "execute_result"}], "source": ["import chess.engine\n", "chessEngine = chess.engine.SimpleEngine.popen_uci(\"stockfish/stockfish-ubuntu-x86-64-avx2\")\n", "\n", "def get_best_move():\n", "  return chessEngine.play(board, chess.engine.Limit(time=0.1)).move\n", "\n", "sampler = gm.text.Sampler(\n", "    model=model,\n", "    params=params,\n", "    tokenizer=tokenizer,\n", ")\n", "chat_prompt = \"<start_of_turn>user\\n{prompt}<end_of_turn>\\n<start_of_turn>model\\n\"\n", "\n", "position = \"3rr3/8/2p3Pk/1p2nP2/pP2p1B1/P1B1Nb2/2P2K2/6R1 b - - 0 41\"\n", "board = chess.Board(position)\n", "board"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "oefhQNixP46B"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<start_of_turn>user\n", "If you decide to invoke any of the function(s), it should be wrapped with ```tool_code```.\n", "\n", "You have access to the following tools.\n", "\n", "* `get_best_move()`: Get the best move from the engine.\n", "\n", "BLACK to move.\n", "Get the next best move and explain it easy enough for five year old.\n", "<end_of_turn>\n", "<start_of_turn>model\n", "Okay, let's figure out the best move for <PERSON>!\n", "\n", "```tool_code\n", "print(get_best_move())\n", "```<end_of_turn>\n"]}], "source": ["prompt1 = chat_prompt.format(prompt=build_prompt_rev())\n", "response1 = sampler.sample(prompt1)\n", "chat_history = prompt1 + response1\n", "print(chat_history)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "P4rU6XjRU6xK"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<start_of_turn>user\n", "If you decide to invoke any of the function(s), it should be wrapped with ```tool_code```.\n", "\n", "You have access to the following tools.\n", "\n", "* `get_best_move()`: Get the best move from the engine.\n", "\n", "BLACK to move.\n", "Get the next best move and explain it easy enough for five year old.\n", "<end_of_turn>\n", "<start_of_turn>model\n", "Okay, let's figure out the best move for <PERSON>!\n", "\n", "```tool_code\n", "print(get_best_move())\n", "```<end_of_turn><start_of_turn>user\n", "<start_of_image>\n", "```tool_output\n", "e5g4\n", "```\n", "<end_of_turn>\n", "<start_of_turn>model\n", "Okay! The best move for <PERSON> is to move their knight from E5 to G4.\n", "\n", "Imagine the knight is a little horse jumping over the board. It can jump over other pieces! This move puts the horse in a good spot to attack <PERSON>'s king and maybe even take some of their pieces. It's like a surprise attack!<end_of_turn>\n"]}, {"data": {"image/svg+xml": "<svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" viewBox=\"0 0 390 390\" width=\"390\" height=\"390\"><desc><pre>. . . r r . . .\n. . . . . . . .\n. . p . . . P k\n. p . . . P . .\np P . . p . n .\nP . B . N b . .\n. . P . . K . .\n. . . . . . R .</pre></desc><defs><g id=\"white-pawn\" class=\"white pawn\"><path d=\"M22.5 9c-2.21 0-4 1.79-4 4 0 .89.29 1.71.78 2.38C17.33 16.5 16 18.59 16 21c0 2.03.94 3.84 2.41 5.03-3 1.06-7.41 5.55-7.41 13.47h23c0-7.92-4.41-12.41-7.41-13.47 1.47-1.19 2.41-3 2.41-5.03 0-2.41-1.33-4.5-3.28-5.62.49-.67.78-1.49.78-2.38 0-2.21-1.79-4-4-4z\" fill=\"#fff\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" /></g><g id=\"white-knight\" class=\"white knight\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M 22,10 C 32.5,11 38.5,18 38,39 L 15,39 C 15,30 25,32.5 23,18\" style=\"fill:#ffffff; stroke:#000000;\" /><path d=\"M 24,18 C 24.38,20.91 18.45,25.37 16,27 C 13,29 13.18,31.34 11,31 C 9.958,30.06 12.41,27.96 11,28 C 10,28 11.19,29.23 10,30 C 9,30 5.997,31 6,26 C 6,24 12,14 12,14 C 12,14 13.89,12.1 14,10.5 C 13.27,9.506 13.5,8.5 13.5,7.5 C 14.5,6.5 16.5,10 16.5,10 L 18.5,10 C 18.5,10 19.28,8.008 21,7 C 22,7 22,10 22,10\" style=\"fill:#ffffff; stroke:#000000;\" /><path d=\"M 9.5 25.5 A 0.5 0.5 0 1 1 8.5,25.5 A 0.5 0.5 0 1 1 9.5 25.5 z\" style=\"fill:#000000; stroke:#000000;\" /><path d=\"M 15 15.5 A 0.5 1.5 0 1 1 14,15.5 A 0.5 1.5 0 1 1 15 15.5 z\" transform=\"matrix(0.866,0.5,-0.5,0.866,9.693,-5.173)\" style=\"fill:#000000; stroke:#000000;\" /></g><g id=\"white-bishop\" class=\"white bishop\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><g fill=\"#fff\" stroke-linecap=\"butt\"><path d=\"M9 36c3.39-.97 10.11.43 13.5-2 3.39 2.43 10.11 1.03 13.5 2 0 0 1.65.54 3 2-.68.97-1.65.99-3 .5-3.39-.97-10.11.46-13.5-1-3.39 1.46-10.11.03-13.5 1-1.354.49-2.323.47-3-.5 1.354-1.94 3-2 3-2zM15 32c2.5 2.5 12.5 2.5 15 0 .5-1.5 0-2 0-2 0-2.5-2.5-4-2.5-4 5.5-1.5 6-11.5-5-15.5-11 4-10.5 14-5 15.5 0 0-2.5 1.5-2.5 4 0 0-.5.5 0 2zM25 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 1 1 5 0z\" /></g><path d=\"M17.5 26h10M15 30h15m-7.5-14.5v5M20 18h5\" stroke-linejoin=\"miter\" /></g><g id=\"white-rook\" class=\"white rook\" fill=\"#fff\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M9 39h27v-3H9v3zM12 36v-4h21v4H12zM11 14V9h4v2h5V9h5v2h5V9h4v5\" stroke-linecap=\"butt\" /><path d=\"M34 14l-3 3H14l-3-3\" /><path d=\"M31 17v12.5H14V17\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" /><path d=\"M31 29.5l1.5 2.5h-20l1.5-2.5\" /><path d=\"M11 14h23\" fill=\"none\" stroke-linejoin=\"miter\" /></g><g id=\"white-king\" class=\"white king\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M22.5 11.63V6M20 8h5\" stroke-linejoin=\"miter\" /><path d=\"M22.5 25s4.5-7.5 3-10.5c0 0-1-2.5-3-2.5s-3 2.5-3 2.5c-1.5 3 3 10.5 3 10.5\" fill=\"#fff\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" /><path d=\"M11.5 37c5.5 3.5 15.5 3.5 21 0v-7s9-4.5 6-10.5c-4-6.5-13.5-3.5-16 4V27v-3.5c-3.5-7.5-13-10.5-16-4-3 6 5 10 5 10V37z\" fill=\"#fff\" /><path d=\"M11.5 30c5.5-3 15.5-3 21 0m-21 3.5c5.5-3 15.5-3 21 0m-21 3.5c5.5-3 15.5-3 21 0\" /></g><g id=\"black-pawn\" class=\"black pawn\"><path d=\"M22.5 9c-2.21 0-4 1.79-4 4 0 .89.29 1.71.78 2.38C17.33 16.5 16 18.59 16 21c0 2.03.94 3.84 2.41 5.03-3 1.06-7.41 5.55-7.41 13.47h23c0-7.92-4.41-12.41-7.41-13.47 1.47-1.19 2.41-3 2.41-5.03 0-2.41-1.33-4.5-3.28-5.62.49-.67.78-1.49.78-2.38 0-2.21-1.79-4-4-4z\" fill=\"#000\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" /></g><g id=\"black-knight\" class=\"black knight\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M 22,10 C 32.5,11 38.5,18 38,39 L 15,39 C 15,30 25,32.5 23,18\" style=\"fill:#000000; stroke:#000000;\" /><path d=\"M 24,18 C 24.38,20.91 18.45,25.37 16,27 C 13,29 13.18,31.34 11,31 C 9.958,30.06 12.41,27.96 11,28 C 10,28 11.19,29.23 10,30 C 9,30 5.997,31 6,26 C 6,24 12,14 12,14 C 12,14 13.89,12.1 14,10.5 C 13.27,9.506 13.5,8.5 13.5,7.5 C 14.5,6.5 16.5,10 16.5,10 L 18.5,10 C 18.5,10 19.28,8.008 21,7 C 22,7 22,10 22,10\" style=\"fill:#000000; stroke:#000000;\" /><path d=\"M 9.5 25.5 A 0.5 0.5 0 1 1 8.5,25.5 A 0.5 0.5 0 1 1 9.5 25.5 z\" style=\"fill:#ececec; stroke:#ececec;\" /><path d=\"M 15 15.5 A 0.5 1.5 0 1 1 14,15.5 A 0.5 1.5 0 1 1 15 15.5 z\" transform=\"matrix(0.866,0.5,-0.5,0.866,9.693,-5.173)\" style=\"fill:#ececec; stroke:#ececec;\" /><path d=\"M 24.55,10.4 L 24.1,11.85 L 24.6,12 C 27.75,13 30.25,14.49 32.5,18.75 C 34.75,23.01 35.75,29.06 35.25,39 L 35.2,39.5 L 37.45,39.5 L 37.5,39 C 38,28.94 36.62,22.15 34.25,17.66 C 31.88,13.17 28.46,11.02 25.06,10.5 L 24.55,10.4 z \" style=\"fill:#ececec; stroke:none;\" /></g><g id=\"black-bishop\" class=\"black bishop\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M9 36c3.39-.97 10.11.43 13.5-2 3.39 2.43 10.11 1.03 13.5 2 0 0 1.65.54 3 2-.68.97-1.65.99-3 .5-3.39-.97-10.11.46-13.5-1-3.39 1.46-10.11.03-13.5 1-1.354.49-2.323.47-3-.5 1.354-1.94 3-2 3-2zm6-4c2.5 2.5 12.5 2.5 15 0 .5-1.5 0-2 0-2 0-2.5-2.5-4-2.5-4 5.5-1.5 6-11.5-5-15.5-11 4-10.5 14-5 15.5 0 0-2.5 1.5-2.5 4 0 0-.5.5 0 2zM25 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 1 1 5 0z\" fill=\"#000\" stroke-linecap=\"butt\" /><path d=\"M17.5 26h10M15 30h15m-7.5-14.5v5M20 18h5\" stroke=\"#fff\" stroke-linejoin=\"miter\" /></g><g id=\"black-rook\" class=\"black rook\" fill=\"#000\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M9 39h27v-3H9v3zM12.5 32l1.5-2.5h17l1.5 2.5h-20zM12 36v-4h21v4H12z\" stroke-linecap=\"butt\" /><path d=\"M14 29.5v-13h17v13H14z\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" /><path d=\"M14 16.5L11 14h23l-3 2.5H14zM11 14V9h4v2h5V9h5v2h5V9h4v5H11z\" stroke-linecap=\"butt\" /><path d=\"M12 35.5h21M13 31.5h19M14 29.5h17M14 16.5h17M11 14h23\" fill=\"none\" stroke=\"#fff\" stroke-width=\"1\" stroke-linejoin=\"miter\" /></g><g id=\"black-king\" class=\"black king\" fill=\"none\" fill-rule=\"evenodd\" stroke=\"#000\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M22.5 11.63V6\" stroke-linejoin=\"miter\" /><path d=\"M22.5 25s4.5-7.5 3-10.5c0 0-1-2.5-3-2.5s-3 2.5-3 2.5c-1.5 3 3 10.5 3 10.5\" fill=\"#000\" stroke-linecap=\"butt\" stroke-linejoin=\"miter\" /><path d=\"M11.5 37c5.5 3.5 15.5 3.5 21 0v-7s9-4.5 6-10.5c-4-6.5-13.5-3.5-16 4V27v-3.5c-3.5-7.5-13-10.5-16-4-3 6 5 10 5 10V37z\" fill=\"#000\" /><path d=\"M20 8h5\" stroke-linejoin=\"miter\" /><path d=\"M32 29.5s8.5-4 6.03-9.65C34.15 14 25 18 22.5 24.5l.01 2.1-.01-2.1C20 18 9.906 14 6.997 19.85c-2.497 5.65 4.853 9 4.853 9M11.5 30c5.5-3 15.5-3 21 0m-21 3.5c5.5-3 15.5-3 21 0m-21 3.5c5.5-3 15.5-3 21 0\" stroke=\"#fff\" /></g><radialGradient id=\"check_gradient\" r=\"0.5\"><stop offset=\"0%\" stop-color=\"#ff0000\" stop-opacity=\"1.0\" /><stop offset=\"50%\" stop-color=\"#e70000\" stop-opacity=\"1.0\" /><stop offset=\"100%\" stop-color=\"#9e0000\" stop-opacity=\"0.0\" /></radialGradient></defs><rect x=\"7.5\" y=\"7.5\" width=\"375\" height=\"375\" fill=\"none\" stroke=\"#212121\" stroke-width=\"15\" /><g transform=\"translate(20, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M23.328 10.016q-1.742 0-2.414.398-.672.398-.672 1.36 0 .765.5 1.218.508.445 1.375.445 1.196 0 1.914-.843.727-.852.727-2.258v-.32zm2.867-.594v4.992h-1.437v-1.328q-.492.797-1.227 1.18-.734.375-1.797.375-1.343 0-2.14-.75-.79-.758-.79-2.024 0-1.476.985-2.226.992-.75 2.953-.75h2.016V8.75q0-.992-.656-1.531-.649-.547-1.829-.547-.75 0-1.46.18-.711.18-1.368.539V6.062q.79-.304 1.532-.453.742-.156 1.445-.156 1.898 0 2.836.984.937.985.937 2.985z\" /></g><g transform=\"translate(20, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M23.328 10.016q-1.742 0-2.414.398-.672.398-.672 1.36 0 .765.5 1.218.508.445 1.375.445 1.196 0 1.914-.843.727-.852.727-2.258v-.32zm2.867-.594v4.992h-1.437v-1.328q-.492.797-1.227 1.18-.734.375-1.797.375-1.343 0-2.14-.75-.79-.758-.79-2.024 0-1.476.985-2.226.992-.75 2.953-.75h2.016V8.75q0-.992-.656-1.531-.649-.547-1.829-.547-.75 0-1.46.18-.711.18-1.368.539V6.062q.79-.304 1.532-.453.742-.156 1.445-.156 1.898 0 2.836.984.937.985.937 2.985z\" /></g><g transform=\"translate(65, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.922 10.047q0-1.586-.656-2.485-.649-.906-1.79-.906-1.14 0-1.796.906-.649.899-.649 2.485 0 1.586.649 2.492.656.898 1.797.898 1.14 0 1.789-.898.656-.906.656-2.492zm-4.89-3.055q.452-.781 1.14-1.156.695-.383 1.656-.383 1.594 0 2.586 1.266 1 1.265 1 3.328 0 2.062-1 3.328-.992 1.266-2.586 1.266-.96 0-1.656-.375-.688-.383-1.14-1.164v1.312h-1.446V2.258h1.445z\" /></g><g transform=\"translate(65, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.922 10.047q0-1.586-.656-2.485-.649-.906-1.79-.906-1.14 0-1.796.906-.649.899-.649 2.485 0 1.586.649 2.492.656.898 1.797.898 1.14 0 1.789-.898.656-.906.656-2.492zm-4.89-3.055q.452-.781 1.14-1.156.695-.383 1.656-.383 1.594 0 2.586 1.266 1 1.265 1 3.328 0 2.062-1 3.328-.992 1.266-2.586 1.266-.96 0-1.656-.375-.688-.383-1.14-1.164v1.312h-1.446V2.258h1.445z\" /></g><g transform=\"translate(110, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M25.96 6v1.344q-.608-.336-1.226-.5-.609-.172-1.234-.172-1.398 0-2.172.89-.773.883-.773 2.485 0 1.601.773 2.492.774.883 2.172.883.625 0 1.234-.164.618-.172 1.227-.508v1.328q-.602.281-1.25.422-.64.14-1.367.14-1.977 0-3.14-1.242-1.165-1.242-1.165-3.351 0-2.14 1.172-3.367 1.18-1.227 3.227-1.227.664 0 1.296.14.633.134 1.227.407z\" /></g><g transform=\"translate(110, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M25.96 6v1.344q-.608-.336-1.226-.5-.609-.172-1.234-.172-1.398 0-2.172.89-.773.883-.773 2.485 0 1.601.773 2.492.774.883 2.172.883.625 0 1.234-.164.618-.172 1.227-.508v1.328q-.602.281-1.25.422-.64.14-1.367.14-1.977 0-3.14-1.242-1.165-1.242-1.165-3.351 0-2.14 1.172-3.367 1.18-1.227 3.227-1.227.664 0 1.296.14.633.134 1.227.407z\" /></g><g transform=\"translate(155, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.973 6.992V2.258h1.437v12.156h-1.437v-1.312q-.453.78-1.149 1.164-.687.375-1.656.375-1.586 0-2.586-1.266-.992-1.266-.992-3.328 0-2.063.992-3.328 1-1.266 2.586-1.266.969 0 1.656.383.696.375 1.149 1.156zm-4.899 3.055q0 1.586.649 2.492.656.898 1.797.898 1.14 0 1.796-.898.657-.906.657-2.492 0-1.586-.657-2.485-.656-.906-1.796-.906-1.141 0-1.797.906-.649.899-.649 2.485z\" /></g><g transform=\"translate(155, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.973 6.992V2.258h1.437v12.156h-1.437v-1.312q-.453.78-1.149 1.164-.687.375-1.656.375-1.586 0-2.586-1.266-.992-1.266-.992-3.328 0-2.063.992-3.328 1-1.266 2.586-1.266.969 0 1.656.383.696.375 1.149 1.156zm-4.899 3.055q0 1.586.649 2.492.656.898 1.797.898 1.14 0 1.796-.898.657-.906.657-2.492 0-1.586-.657-2.485-.656-.906-1.796-.906-1.141 0-1.797.906-.649.899-.649 2.485z\" /></g><g transform=\"translate(200, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M26.555 9.68v.703h-6.61q.094 1.484.89 2.265.806.774 2.235.774.828 0 1.602-.203.781-.203 1.547-.61v1.36q-.774.328-1.586.5-.813.172-1.649.172-2.093 0-3.32-1.22-1.219-1.218-1.219-3.296 0-2.148 1.157-3.406 1.164-1.266 3.132-1.266 1.766 0 2.79 1.14 1.03 1.134 1.03 3.087zm-1.438-.422q-.015-1.18-.664-1.883-.64-.703-1.703-.703-1.203 0-1.93.68-.718.68-.828 1.914z\" /></g><g transform=\"translate(200, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M26.555 9.68v.703h-6.61q.094 1.484.89 2.265.806.774 2.235.774.828 0 1.602-.203.781-.203 1.547-.61v1.36q-.774.328-1.586.5-.813.172-1.649.172-2.093 0-3.32-1.22-1.219-1.218-1.219-3.296 0-2.148 1.157-3.406 1.164-1.266 3.132-1.266 1.766 0 2.79 1.14 1.03 1.134 1.03 3.087zm-1.438-.422q-.015-1.18-.664-1.883-.64-.703-1.703-.703-1.203 0-1.93.68-.718.68-.828 1.914z\" /></g><g transform=\"translate(245, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M25.285 2.258v1.195H23.91q-.773 0-1.078.313-.297.312-.297 1.125v.773h2.367v1.117h-2.367v7.633H21.09V6.781h-1.375V5.664h1.375v-.61q0-1.46.68-2.124.68-.672 2.156-.672z\" /></g><g transform=\"translate(245, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M25.285 2.258v1.195H23.91q-.773 0-1.078.313-.297.312-.297 1.125v.773h2.367v1.117h-2.367v7.633H21.09V6.781h-1.375V5.664h1.375v-.61q0-1.46.68-2.124.68-.672 2.156-.672z\" /></g><g transform=\"translate(290, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.973 9.937q0-1.562-.649-2.421-.64-.86-1.804-.86-1.157 0-1.805.86-.64.859-.64 2.421 0 1.555.64 2.415.648.859 1.805.859 1.164 0 1.804-.86.649-.859.649-2.414zm1.437 3.391q0 2.234-.992 3.32-.992 1.094-3.04 1.094-.757 0-1.429-.117-.672-.11-1.304-.344v-1.398q.632.344 1.25.508.617.164 1.257.164 1.414 0 2.118-.743.703-.734.703-2.226v-.711q-.446.773-1.141 1.156-.695.383-1.664.383-1.61 0-2.594-1.227-.984-1.226-.984-3.25 0-2.03.984-3.257.985-1.227 2.594-1.227.969 0 1.664.383t1.14 1.156V5.664h1.438z\" /></g><g transform=\"translate(290, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M24.973 9.937q0-1.562-.649-2.421-.64-.86-1.804-.86-1.157 0-1.805.86-.64.859-.64 2.421 0 1.555.64 2.415.648.859 1.805.859 1.164 0 1.804-.86.649-.859.649-2.414zm1.437 3.391q0 2.234-.992 3.32-.992 1.094-3.04 1.094-.757 0-1.429-.117-.672-.11-1.304-.344v-1.398q.632.344 1.25.508.617.164 1.257.164 1.414 0 2.118-.743.703-.734.703-2.226v-.711q-.446.773-1.141 1.156-.695.383-1.664.383-1.61 0-2.594-1.227-.984-1.226-.984-3.25 0-2.03.984-3.257.985-1.227 2.594-1.227.969 0 1.664.383t1.14 1.156V5.664h1.438z\" /></g><g transform=\"translate(335, 1) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M26.164 9.133v5.281h-1.437V9.18q0-1.243-.485-1.86-.484-.617-1.453-.617-1.164 0-1.836.742-.672.742-.672 2.024v4.945h-1.445V2.258h1.445v4.765q.516-.789 1.211-1.18.703-.39 1.617-.39 1.508 0 2.282.938.773.93.773 2.742z\" /></g><g transform=\"translate(335, 375) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M26.164 9.133v5.281h-1.437V9.18q0-1.243-.485-1.86-.484-.617-1.453-.617-1.164 0-1.836.742-.672.742-.672 2.024v4.945h-1.445V2.258h1.445v4.765q.516-.789 1.211-1.18.703-.39 1.617-.39 1.508 0 2.282.938.773.93.773 2.742z\" /></g><g transform=\"translate(0, 335) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.754 26.996h2.578v-8.898l-2.805.562v-1.437l2.79-.563h1.578v10.336h2.578v1.328h-6.72z\" /></g><g transform=\"translate(375, 335) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.754 26.996h2.578v-8.898l-2.805.562v-1.437l2.79-.563h1.578v10.336h2.578v1.328h-6.72z\" /></g><g transform=\"translate(0, 290) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M8.195 26.996h5.508v1.328H6.297v-1.328q.898-.93 2.445-2.492 1.555-1.57 1.953-2.024.758-.851 1.055-1.437.305-.594.305-1.164 0-.93-.657-1.516-.648-.586-1.695-.586-.742 0-1.57.258-.82.258-1.758.781v-1.593q.953-.383 1.781-.578.828-.196 1.516-.196 1.812 0 2.89.906 1.079.907 1.079 2.422 0 .72-.274 1.368-.265.64-.976 1.515-.196.227-1.243 1.313-1.046 1.078-2.953 3.023z\" /></g><g transform=\"translate(375, 290) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M8.195 26.996h5.508v1.328H6.297v-1.328q.898-.93 2.445-2.492 1.555-1.57 1.953-2.024.758-.851 1.055-1.437.305-.594.305-1.164 0-.93-.657-1.516-.648-.586-1.695-.586-.742 0-1.57.258-.82.258-1.758.781v-1.593q.953-.383 1.781-.578.828-.196 1.516-.196 1.812 0 2.89.906 1.079.907 1.079 2.422 0 .72-.274 1.368-.265.64-.976 1.515-.196.227-1.243 1.313-1.046 1.078-2.953 3.023z\" /></g><g transform=\"translate(0, 245) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M11.434 22.035q1.132.242 1.765 1.008.64.766.64 1.89 0 1.727-1.187 2.672-1.187.946-3.375.946-.734 0-1.515-.149-.774-.14-1.602-.43V26.45q.656.383 1.438.578.78.196 1.632.196 1.485 0 2.258-.586.782-.586.782-1.703 0-1.032-.727-1.61-.719-.586-2.008-.586h-1.36v-1.297h1.423q1.164 0 1.78-.46.618-.47.618-1.344 0-.899-.64-1.375-.633-.485-1.82-.485-.65 0-1.391.141-.743.14-1.633.437V16.95q.898-.25 1.68-.375.788-.125 1.484-.125 1.797 0 2.844.82 1.046.813 1.046 2.204 0 .968-.554 1.64-.555.664-1.578.922z\" /></g><g transform=\"translate(375, 245) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M11.434 22.035q1.132.242 1.765 1.008.64.766.64 1.89 0 1.727-1.187 2.672-1.187.946-3.375.946-.734 0-1.515-.149-.774-.14-1.602-.43V26.45q.656.383 1.438.578.78.196 1.632.196 1.485 0 2.258-.586.782-.586.782-1.703 0-1.032-.727-1.61-.719-.586-2.008-.586h-1.36v-1.297h1.423q1.164 0 1.78-.46.618-.47.618-1.344 0-.899-.64-1.375-.633-.485-1.82-.485-.65 0-1.391.141-.743.14-1.633.437V16.95q.898-.25 1.68-.375.788-.125 1.484-.125 1.797 0 2.844.82 1.046.813 1.046 2.204 0 .968-.554 1.64-.555.664-1.578.922z\" /></g><g transform=\"translate(0, 200) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M11.016 18.035L7.03 24.262h3.985zm-.414-1.375h1.984v7.602h1.664v1.312h-1.664v2.75h-1.57v-2.75H5.75v-1.523z\" /></g><g transform=\"translate(375, 200) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M11.016 18.035L7.03 24.262h3.985zm-.414-1.375h1.984v7.602h1.664v1.312h-1.664v2.75h-1.57v-2.75H5.75v-1.523z\" /></g><g transform=\"translate(0, 155) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.719 16.66h6.195v1.328h-4.75v2.86q.344-.118.688-.172.343-.063.687-.063 1.953 0 3.094 1.07 1.14 1.07 1.14 2.899 0 1.883-1.171 2.93-1.172 1.039-3.305 1.039-.735 0-1.5-.125-.758-.125-1.57-.375v-1.586q.703.383 1.453.57.75.188 1.586.188 1.351 0 2.14-.711.79-.711.79-1.93 0-1.219-.79-1.93-.789-.71-2.14-.71-.633 0-1.266.14-.625.14-1.281.438z\" /></g><g transform=\"translate(375, 155) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.719 16.66h6.195v1.328h-4.75v2.86q.344-.118.688-.172.343-.063.687-.063 1.953 0 3.094 1.07 1.14 1.07 1.14 2.899 0 1.883-1.171 2.93-1.172 1.039-3.305 1.039-.735 0-1.5-.125-.758-.125-1.57-.375v-1.586q.703.383 1.453.57.75.188 1.586.188 1.351 0 2.14-.711.79-.711.79-1.93 0-1.219-.79-1.93-.789-.71-2.14-.71-.633 0-1.266.14-.625.14-1.281.438z\" /></g><g transform=\"translate(0, 110) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M10.137 21.863q-1.063 0-1.688.727-.617.726-.617 1.992 0 1.258.617 1.992.625.727 1.688.727 1.062 0 1.68-.727.624-.734.624-1.992 0-1.266-.625-1.992-.617-.727-1.68-.727zm3.133-4.945v1.437q-.594-.28-1.204-.43-.601-.148-1.195-.148-1.562 0-2.39 1.055-.82 1.055-.938 3.188.46-.68 1.156-1.04.696-.367 1.531-.367 1.758 0 2.774 1.07 1.023 1.063 1.023 2.899 0 1.797-1.062 2.883-1.063 1.086-2.828 1.086-2.024 0-3.094-1.547-1.07-1.555-1.07-4.5 0-2.766 1.312-4.406 1.313-1.649 3.524-1.649.593 0 1.195.117.61.118 1.266.352z\" /></g><g transform=\"translate(375, 110) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M10.137 21.863q-1.063 0-1.688.727-.617.726-.617 1.992 0 1.258.617 1.992.625.727 1.688.727 1.062 0 1.68-.727.624-.734.624-1.992 0-1.266-.625-1.992-.617-.727-1.68-.727zm3.133-4.945v1.437q-.594-.28-1.204-.43-.601-.148-1.195-.148-1.562 0-2.39 1.055-.82 1.055-.938 3.188.46-.68 1.156-1.04.696-.367 1.531-.367 1.758 0 2.774 1.07 1.023 1.063 1.023 2.899 0 1.797-1.062 2.883-1.063 1.086-2.828 1.086-2.024 0-3.094-1.547-1.07-1.555-1.07-4.5 0-2.766 1.312-4.406 1.313-1.649 3.524-1.649.593 0 1.195.117.61.118 1.266.352z\" /></g><g transform=\"translate(0, 65) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.25 16.66h7.5v.672L9.516 28.324H7.867l3.985-10.336H6.25z\" /></g><g transform=\"translate(375, 65) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M6.25 16.66h7.5v.672L9.516 28.324H7.867l3.985-10.336H6.25z\" /></g><g transform=\"translate(0, 20) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M10 22.785q-1.125 0-1.773.602-.641.601-.641 1.656t.64 1.656q.649.602 1.774.602t1.773-.602q.649-.61.649-1.656 0-1.055-.649-1.656-.64-.602-1.773-.602zm-1.578-.672q-1.016-.25-1.586-.945-.563-.695-.563-1.695 0-1.399.993-2.211 1-.813 2.734-.813 1.742 0 2.734.813.993.812.993 2.21 0 1-.57 1.696-.563.695-1.571.945 1.14.266 1.773 1.04.641.773.641 1.89 0 1.695-1.04 2.602-1.03.906-2.96.906t-2.969-.906Q6 26.738 6 25.043q0-1.117.64-1.89.641-.774 1.782-1.04zm-.578-2.492q0 .906.562 1.414.57.508 1.594.508 1.016 0 1.586-.508.578-.508.578-1.414 0-.906-.578-1.414-.57-.508-1.586-.508-1.023 0-1.594.508-.562.508-.562 1.414z\" /></g><g transform=\"translate(375, 20) scale(0.75, 0.75)\" fill=\"#e5e5e5\" stroke=\"#e5e5e5\"><path d=\"M10 22.785q-1.125 0-1.773.602-.641.601-.641 1.656t.64 1.656q.649.602 1.774.602t1.773-.602q.649-.61.649-1.656 0-1.055-.649-1.656-.64-.602-1.773-.602zm-1.578-.672q-1.016-.25-1.586-.945-.563-.695-.563-1.695 0-1.399.993-2.211 1-.813 2.734-.813 1.742 0 2.734.813.993.812.993 2.21 0 1-.57 1.696-.563.695-1.571.945 1.14.266 1.773 1.04.641.773.641 1.89 0 1.695-1.04 2.602-1.03.906-2.96.906t-2.969-.906Q6 26.738 6 25.043q0-1.117.64-1.89.641-.774 1.782-1.04zm-.578-2.492q0 .906.562 1.414.57.508 1.594.508 1.016 0 1.586-.508.578-.508.578-1.414 0-.906-.578-1.414-.57-.508-1.586-.508-1.023 0-1.594.508-.562.508-.562 1.414z\" /></g><rect x=\"15\" y=\"330\" width=\"45\" height=\"45\" class=\"square dark a1\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"60\" y=\"330\" width=\"45\" height=\"45\" class=\"square light b1\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"105\" y=\"330\" width=\"45\" height=\"45\" class=\"square dark c1\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"150\" y=\"330\" width=\"45\" height=\"45\" class=\"square light d1\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"195\" y=\"330\" width=\"45\" height=\"45\" class=\"square dark e1\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"240\" y=\"330\" width=\"45\" height=\"45\" class=\"square light f1\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"285\" y=\"330\" width=\"45\" height=\"45\" class=\"square dark g1\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"330\" y=\"330\" width=\"45\" height=\"45\" class=\"square light h1\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"15\" y=\"285\" width=\"45\" height=\"45\" class=\"square light a2\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"60\" y=\"285\" width=\"45\" height=\"45\" class=\"square dark b2\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"105\" y=\"285\" width=\"45\" height=\"45\" class=\"square light c2\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"150\" y=\"285\" width=\"45\" height=\"45\" class=\"square dark d2\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"195\" y=\"285\" width=\"45\" height=\"45\" class=\"square light e2\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"240\" y=\"285\" width=\"45\" height=\"45\" class=\"square dark f2\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"285\" y=\"285\" width=\"45\" height=\"45\" class=\"square light g2\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"330\" y=\"285\" width=\"45\" height=\"45\" class=\"square dark h2\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"15\" y=\"240\" width=\"45\" height=\"45\" class=\"square dark a3\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"60\" y=\"240\" width=\"45\" height=\"45\" class=\"square light b3\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"105\" y=\"240\" width=\"45\" height=\"45\" class=\"square dark c3\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"150\" y=\"240\" width=\"45\" height=\"45\" class=\"square light d3\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"195\" y=\"240\" width=\"45\" height=\"45\" class=\"square dark e3\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"240\" y=\"240\" width=\"45\" height=\"45\" class=\"square light f3\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"285\" y=\"240\" width=\"45\" height=\"45\" class=\"square dark g3\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"330\" y=\"240\" width=\"45\" height=\"45\" class=\"square light h3\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"15\" y=\"195\" width=\"45\" height=\"45\" class=\"square light a4\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"60\" y=\"195\" width=\"45\" height=\"45\" class=\"square dark b4\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"105\" y=\"195\" width=\"45\" height=\"45\" class=\"square light c4\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"150\" y=\"195\" width=\"45\" height=\"45\" class=\"square dark d4\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"195\" y=\"195\" width=\"45\" height=\"45\" class=\"square light e4\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"240\" y=\"195\" width=\"45\" height=\"45\" class=\"square dark f4\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"285\" y=\"195\" width=\"45\" height=\"45\" class=\"square light lastmove g4\" stroke=\"none\" fill=\"#cdd16a\" /><rect x=\"330\" y=\"195\" width=\"45\" height=\"45\" class=\"square dark h4\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"15\" y=\"150\" width=\"45\" height=\"45\" class=\"square dark a5\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"60\" y=\"150\" width=\"45\" height=\"45\" class=\"square light b5\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"105\" y=\"150\" width=\"45\" height=\"45\" class=\"square dark c5\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"150\" y=\"150\" width=\"45\" height=\"45\" class=\"square light d5\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"195\" y=\"150\" width=\"45\" height=\"45\" class=\"square dark lastmove e5\" stroke=\"none\" fill=\"#aaa23b\" /><rect x=\"240\" y=\"150\" width=\"45\" height=\"45\" class=\"square light f5\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"285\" y=\"150\" width=\"45\" height=\"45\" class=\"square dark g5\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"330\" y=\"150\" width=\"45\" height=\"45\" class=\"square light h5\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"15\" y=\"105\" width=\"45\" height=\"45\" class=\"square light a6\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"60\" y=\"105\" width=\"45\" height=\"45\" class=\"square dark b6\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"105\" y=\"105\" width=\"45\" height=\"45\" class=\"square light c6\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"150\" y=\"105\" width=\"45\" height=\"45\" class=\"square dark d6\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"195\" y=\"105\" width=\"45\" height=\"45\" class=\"square light e6\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"240\" y=\"105\" width=\"45\" height=\"45\" class=\"square dark f6\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"285\" y=\"105\" width=\"45\" height=\"45\" class=\"square light g6\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"330\" y=\"105\" width=\"45\" height=\"45\" class=\"square dark h6\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"15\" y=\"60\" width=\"45\" height=\"45\" class=\"square dark a7\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"60\" y=\"60\" width=\"45\" height=\"45\" class=\"square light b7\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"105\" y=\"60\" width=\"45\" height=\"45\" class=\"square dark c7\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"150\" y=\"60\" width=\"45\" height=\"45\" class=\"square light d7\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"195\" y=\"60\" width=\"45\" height=\"45\" class=\"square dark e7\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"240\" y=\"60\" width=\"45\" height=\"45\" class=\"square light f7\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"285\" y=\"60\" width=\"45\" height=\"45\" class=\"square dark g7\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"330\" y=\"60\" width=\"45\" height=\"45\" class=\"square light h7\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"15\" y=\"15\" width=\"45\" height=\"45\" class=\"square light a8\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"60\" y=\"15\" width=\"45\" height=\"45\" class=\"square dark b8\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"105\" y=\"15\" width=\"45\" height=\"45\" class=\"square light c8\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"150\" y=\"15\" width=\"45\" height=\"45\" class=\"square dark d8\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"195\" y=\"15\" width=\"45\" height=\"45\" class=\"square light e8\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"240\" y=\"15\" width=\"45\" height=\"45\" class=\"square dark f8\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"285\" y=\"15\" width=\"45\" height=\"45\" class=\"square light g8\" stroke=\"none\" fill=\"#ffce9e\" /><rect x=\"330\" y=\"15\" width=\"45\" height=\"45\" class=\"square dark h8\" stroke=\"none\" fill=\"#d18b47\" /><rect x=\"240\" y=\"285\" width=\"45\" height=\"45\" class=\"check\" fill=\"url(#check_gradient)\" /><use href=\"#white-rook\" xlink:href=\"#white-rook\" transform=\"translate(285, 330)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(105, 285)\" /><use href=\"#white-king\" xlink:href=\"#white-king\" transform=\"translate(240, 285)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(15, 240)\" /><use href=\"#white-bishop\" xlink:href=\"#white-bishop\" transform=\"translate(105, 240)\" /><use href=\"#white-knight\" xlink:href=\"#white-knight\" transform=\"translate(195, 240)\" /><use href=\"#black-bishop\" xlink:href=\"#black-bishop\" transform=\"translate(240, 240)\" /><use href=\"#black-pawn\" xlink:href=\"#black-pawn\" transform=\"translate(15, 195)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(60, 195)\" /><use href=\"#black-pawn\" xlink:href=\"#black-pawn\" transform=\"translate(195, 195)\" /><use href=\"#black-knight\" xlink:href=\"#black-knight\" transform=\"translate(285, 195)\" /><use href=\"#black-pawn\" xlink:href=\"#black-pawn\" transform=\"translate(60, 150)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(240, 150)\" /><use href=\"#black-pawn\" xlink:href=\"#black-pawn\" transform=\"translate(105, 105)\" /><use href=\"#white-pawn\" xlink:href=\"#white-pawn\" transform=\"translate(285, 105)\" /><use href=\"#black-king\" xlink:href=\"#black-king\" transform=\"translate(330, 105)\" /><use href=\"#black-rook\" xlink:href=\"#black-rook\" transform=\"translate(150, 15)\" /><use href=\"#black-rook\" xlink:href=\"#black-rook\" transform=\"translate(195, 15)\" /></svg>", "text/plain": ["Board('3rr3/8/2p3Pk/1p3P2/pP2p1n1/P1B1Nb2/2P2K2/6R1 w - - 0 42')"]}, "execution_count": 136, "metadata": {}, "output_type": "execute_result"}], "source": ["# take the best move\n", "import io\n", "from contextlib import redirect_stdout\n", "\n", "f = io.StringIO()\n", "with redirect_stdout(f):\n", "  result = eval(extract_code_blocks(response1)[0])\n", "output = f.getvalue().rstrip('\\n')\n", "move = result if output == '' else output\n", "board.push_san(move)\n", "\n", "# return tool result, <PERSON>  to explain\n", "prompt2 = chat_prompt.format(prompt=f\"\"\"<start_of_image>\n", "```tool_output\n", "{move}\n", "```\n", "\"\"\")\n", "response2 = sampler.sample(chat_history + prompt2, images=get_board_img(board))\n", "chat_history += prompt2 + response2\n", "print(chat_history)\n", "\n", "board"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "[Gemma_3]Chess.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}