// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 55;
	objects = {

/* Begin PBXBuildFile section */
		D6B6983327892632004B7B51 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = D6B6983127892632004B7B51 /* Main.storyboard */; };
		D6B6983527892633004B7B51 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = D6B6983427892633004B7B51 /* Assets.xcassets */; };
		D6B6983827892633004B7B51 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = D6B6983627892633004B7B51 /* LaunchScreen.storyboard */; };
		D6B6983B27892633004B7B51 /* main.mm in Sources */ = {isa = PBXBuildFile; fileRef = D6B6983A27892633004B7B51 /* main.mm */; };
		D6B69850278E4DC8004B7B51 /* libios_display_list_benchmarks.dylib in Embed Libraries */ = {isa = PBXBuildFile; fileRef = D6B6984F278E4DC8004B7B51 /* libios_display_list_benchmarks.dylib */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
		D6B69853278E5833004B7B51 /* libios_display_list_benchmarks.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = D6B6984F278E4DC8004B7B51 /* libios_display_list_benchmarks.dylib */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		D6B6984527892715004B7B51 /* Embed Libraries */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				D6B69850278E4DC8004B7B51 /* libios_display_list_benchmarks.dylib in Embed Libraries */,
			);
			name = "Embed Libraries";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		D6B6982527892632004B7B51 /* IosBenchmarks.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = IosBenchmarks.app; sourceTree = BUILT_PRODUCTS_DIR; };
		D6B6983227892632004B7B51 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		D6B6983427892633004B7B51 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		D6B6983727892633004B7B51 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		D6B6983927892633004B7B51 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		D6B6983A27892633004B7B51 /* main.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = main.mm; sourceTree = "<group>"; };
		D6B6984F278E4DC8004B7B51 /* libios_display_list_benchmarks.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libios_display_list_benchmarks.dylib; path = ../../../../out/ios_profile/libios_display_list_benchmarks.dylib; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		D6B6982227892632004B7B51 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D6B69853278E5833004B7B51 /* libios_display_list_benchmarks.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		D6B6981C27892632004B7B51 = {
			isa = PBXGroup;
			children = (
				D6B6984F278E4DC8004B7B51 /* libios_display_list_benchmarks.dylib */,
				D6B6982727892632004B7B51 /* IosBenchmarks */,
				D6B6982627892632004B7B51 /* Products */,
				D6B6984127892715004B7B51 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		D6B6982627892632004B7B51 /* Products */ = {
			isa = PBXGroup;
			children = (
				D6B6982527892632004B7B51 /* IosBenchmarks.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		D6B6982727892632004B7B51 /* IosBenchmarks */ = {
			isa = PBXGroup;
			children = (
				D6B6983127892632004B7B51 /* Main.storyboard */,
				D6B6983427892633004B7B51 /* Assets.xcassets */,
				D6B6983627892633004B7B51 /* LaunchScreen.storyboard */,
				D6B6983927892633004B7B51 /* Info.plist */,
				D6B6983A27892633004B7B51 /* main.mm */,
			);
			path = IosBenchmarks;
			sourceTree = "<group>";
		};
		D6B6984127892715004B7B51 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		D6B6982427892632004B7B51 /* IosBenchmarks */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = D6B6983E27892633004B7B51 /* Build configuration list for PBXNativeTarget "IosBenchmarks" */;
			buildPhases = (
				D6B6982127892632004B7B51 /* Sources */,
				D6B6982227892632004B7B51 /* Frameworks */,
				D6B6982327892632004B7B51 /* Resources */,
				D6B6984527892715004B7B51 /* Embed Libraries */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = IosBenchmarks;
			productName = IosBenchmarks;
			productReference = D6B6982527892632004B7B51 /* IosBenchmarks.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		D6B6981D27892632004B7B51 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1320;
				TargetAttributes = {
					D6B6982427892632004B7B51 = {
						CreatedOnToolsVersion = 13.2.1;
					};
				};
			};
			buildConfigurationList = D6B6982027892632004B7B51 /* Build configuration list for PBXProject "IosBenchmarks" */;
			compatibilityVersion = "Xcode 13.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = D6B6981C27892632004B7B51;
			productRefGroup = D6B6982627892632004B7B51 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				D6B6982427892632004B7B51 /* IosBenchmarks */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		D6B6982327892632004B7B51 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D6B6983827892633004B7B51 /* LaunchScreen.storyboard in Resources */,
				D6B6983527892633004B7B51 /* Assets.xcassets in Resources */,
				D6B6983327892632004B7B51 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		D6B6982127892632004B7B51 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D6B6983B27892633004B7B51 /* main.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		D6B6983127892632004B7B51 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				D6B6983227892632004B7B51 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		D6B6983627892633004B7B51 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				D6B6983727892633004B7B51 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		D6B6983C27892633004B7B51 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_BITCODE = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				FLUTTER_ENGINE = ios_profile;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					../../../../,
					../../../../third_party/benchmark/include,
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.2;
				LIBRARY_SEARCH_PATHS = "";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		D6B6983D27892633004B7B51 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_BITCODE = NO;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FLUTTER_ENGINE = ios_profile;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					../../../../,
					../../../../third_party/benchmark/include,
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.2;
				LIBRARY_SEARCH_PATHS = "";
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		D6B6983F27892633004B7B51 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = IosBenchmarks/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "../../../../out/$(FLUTTER_ENGINE)/";
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.google.flutter.IosBenchmarks;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		D6B6984027892633004B7B51 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = IosBenchmarks/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = "../../../../out/$(FLUTTER_ENGINE)/";
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.google.flutter.IosBenchmarks;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		D6B6982027892632004B7B51 /* Build configuration list for PBXProject "IosBenchmarks" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D6B6983C27892633004B7B51 /* Debug */,
				D6B6983D27892633004B7B51 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		D6B6983E27892633004B7B51 /* Build configuration list for PBXNativeTarget "IosBenchmarks" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D6B6983F27892633004B7B51 /* Debug */,
				D6B6984027892633004B7B51 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = D6B6981D27892632004B7B51 /* Project object */;
}
