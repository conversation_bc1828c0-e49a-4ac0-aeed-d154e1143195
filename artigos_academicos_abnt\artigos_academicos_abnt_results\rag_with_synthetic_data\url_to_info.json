{"http://arxiv.org/abs/2410.09584v1": {"url": "http://arxiv.org/abs/2410.09584v1", "title": "Toward General Instruction-Following Alignment for Retrieval-Augmented Generation", "description": "Following natural instructions is crucial for the effective application of Retrieval-Augmented Generation (RAG) systems. Despite recent advancements in Large Language Models (LLMs), research on assessing and improving instruction-following (IF) alignment within the RAG domain remains limited. To address this issue, we propose VIF-RAG, the first automated, scalable, and verifiable synthetic pipeline for instruction-following alignment in RAG systems. We start by manually crafting a minimal set of atomic instructions (<100) and developing combination rules to synthesize and verify complex instructions for a seed set. We then use supervised models for instruction rewriting while simultaneously generating code to automate the verification of instruction quality via a Python executor. Finally, we integrate these instructions with extensive RAG and general data samples, scaling up to a high-quality VIF-RAG-QA dataset (>100k) through automated processes. To further bridge the gap in instru...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "published": "2024-10-12", "pdf_url": "http://arxiv.org/pdf/2410.09584v1", "categories": ["cs.CL", "cs.AI", "cs.IR", "cs.LG"], "source": "arxiv"}, "https://aws.amazon.com/blogs/machine-learning/generate-synthetic-data-for-evaluating-rag-systems-using-amazon-bedrock/": {"title": "Generate synthetic data for evaluating RAG systems using Amazon Bedrock", "url": "https://aws.amazon.com/blogs/machine-learning/generate-synthetic-data-for-evaluating-rag-systems-using-amazon-bedrock/", "summary": "This post explains how to use Anthropic Claude on Amazon Bedrock to generate synthetic data for evaluating your RAG system. Amazon Bedrock is a fully managed service that offers a choice of high-performing foundation models (FMs) from leading AI companies like AI21 Labs, Anthropic, Cohere, Meta, Stability AI, and Amazon through a single API ...", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}, "http://arxiv.org/abs/2503.12663v1": {"url": "http://arxiv.org/abs/2503.12663v1", "title": "Logic-RAG: Augmenting Large Multimodal Models with Visual-Spatial Knowledge for Road Scene Understanding", "description": "Large multimodal models (LMMs) are increasingly integrated into autonomous driving systems for user interaction. However, their limitations in fine-grained spatial reasoning pose challenges for system interpretability and user trust. We introduce Logic-RAG, a novel Retrieval-Augmented Generation (RAG) framework that improves LMMs' spatial understanding in driving scenarios. Logic-RAG constructs a dynamic knowledge base (KB) about object-object relationships in first-order logic (FOL) using a perception module, a query-to-logic embedder, and a logical inference engine. We evaluated Logic-RAG on visual-spatial queries using both synthetic and real-world driving videos. When using popular LMMs (GPT-4V, Claude 3.5) as proxies for an autonomous driving system, these models achieved only 55% accuracy on synthetic driving scenes and under 75% on real-world driving scenes. Augmenting them with Logic-RAG increased their accuracies to over 80% and 90%, respectively. An ablation study showed t...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON>"], "published": "2025-03-16", "pdf_url": "http://arxiv.org/pdf/2503.12663v1", "categories": ["cs.CV", "cs.CL", "cs.LG", "cs.R<PERSON>"], "source": "arxiv"}, "https://github.com/mddunlap924/LangChain-SynData-RAG-Eval": {"title": "Synthetic Data Generation using LangChain for IR and RAG Evaluation", "url": "https://github.com/mddunlap924/Lang<PERSON>hain-SynData-RAG-Eval", "summary": "When building an IR or RAG system, a dataset of context, queries, and answers is vital for evaluating the system's performance. Human-annotated datasets offer excellent ground truths but can be expensive and challenging to obtain; therefore, synthetic datasets generated using LLMs is an attractive solution and supplement.", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}, "http://arxiv.org/abs/2501.03468v1": {"url": "http://arxiv.org/abs/2501.03468v1", "title": "MTRAG: A Multi-Turn Conversational Benchmark for Evaluating Retrieval-Augmented Generation Systems", "description": "Retrieval-augmented generation (RAG) has recently become a very popular task for Large Language Models (LLMs). Evaluating them on multi-turn RAG conversations, where the system is asked to generate a response to a question in the context of a preceding conversation is an important and often overlooked task with several additional challenges. We present MTRAG: an end-to-end human-generated multi-turn RAG benchmark that reflects several real-world properties across diverse dimensions for evaluating the full RAG pipeline. MTRAG contains 110 conversations averaging 7.7 turns each across four domains for a total of 842 tasks. We also explore automation paths via synthetic data and LLM-as-a-Judge evaluation. Our human and automatic evaluations show that even state-of-the-art LLM RAG systems struggle on MTRAG. We demonstrate the need for strong retrieval and generation systems that can handle later turns, unanswerable questions, non-standalone questions, and multiple domains. MTRAG is avai...", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Danish Contractor", "<PERSON>"], "published": "2025-01-07", "pdf_url": "http://arxiv.org/pdf/2501.03468v1", "categories": ["cs.CL", "cs.AI"], "source": "arxiv"}, "https://github.com/stanford-futuredata/ARES": {"title": "stanford-futuredata/ARES: Automated Evaluation of RAG Systems - GitHub", "url": "https://github.com/stanford-futuredata/ARES", "summary": "ARES is a groundbreaking framework for evaluating Retrieval-Augmented Generation (RAG) models. The automated process combines synthetic data generation with fine-tuned classifiers to efficiently assess context relevance, answer faithfulness, and answer relevance, minimizing the need for extensive human annotations.", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}, "http://arxiv.org/abs/2410.11395v1": {"url": "http://arxiv.org/abs/2410.11395v1", "title": "Synthetic Interlocutors. Experiments with Generative AI to Prolong Ethnographic Encounters", "description": "This paper introduces \"Synthetic Interlocutors\" for ethnographic research. Synthetic Interlocutors are chatbots ingested with ethnographic textual material (interviews and observations) by using Retrieval Augmented Generation (RAG). We integrated an open-source large language model with ethnographic data from three projects to explore two questions: Can RAG digest ethnographic material and act as ethnographic interlocutor? And, if so, can Synthetic Interlocutors prolong encounters with the field and extend our analysis? Through reflections on the process of building our Synthetic Interlocutors and an experimental collaborative workshop, we suggest that RAG can digest ethnographic materials, and it might lead to prolonged, yet uneasy ethnographic encounters that allowed us to partially recreate and re-visit fieldwork interactions while facilitating opportunities for novel analytic insights. Synthetic Interlocutors can produce collaborative, ambiguous and serendipitous moments.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "published": "2024-10-15", "pdf_url": "http://arxiv.org/pdf/2410.11395v1", "categories": ["cs.HC", "cs.AI"], "source": "arxiv"}, "https://developer.nvidia.com/blog/evaluating-and-enhancing-rag-pipeline-performance-using-synthetic-data/": {"title": "Evaluating and Enhancing RAG Pipeline Performance Using Synthetic Data", "url": "https://developer.nvidia.com/blog/evaluating-and-enhancing-rag-pipeline-performance-using-synthetic-data/", "summary": "He spearheads projects spanning text-based and multimodal retrieval-augmented generation (RAG), LLM customization, and synthetic data generation, along with building generative AI solutions for a variety of enterprise use cases in different domains. He has an academic background in Applied Math with a PhD from MIT in computational physics.", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}, "http://arxiv.org/abs/2501.00353v1": {"url": "http://arxiv.org/abs/2501.00353v1", "title": "RAG-Instruct: Boosting LLMs with Diverse Retrieval-Augmented Instructions", "description": "Retrieval-Augmented Generation (RAG) has emerged as a key paradigm for enhancing large language models (LLMs) by incorporating external knowledge. However, current RAG methods face two limitations: (1) they only cover limited RAG scenarios. (2) They suffer from limited task diversity due to the lack of a general RAG dataset. To address these limitations, we propose RAG-Instruct, a general method for synthesizing diverse and high-quality RAG instruction data based on any source corpus. Our approach leverages (1) five RAG paradigms, which encompass diverse query-document relationships, and (2) instruction simulation, which enhances instruction diversity and quality by utilizing the strengths of existing instruction datasets. Using this method, we construct a 40K instruction dataset from Wikipedia, comprehensively covering diverse RAG scenarios and tasks. Experiments demonstrate that RAG-Instruct effectively enhances LLMs' RAG capabilities, achieving strong zero-shot performance and si...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Ke Ji", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "published": "2024-12-31", "pdf_url": "http://arxiv.org/pdf/2501.00353v1", "categories": ["cs.CL", "cs.AI", "cs.LG"], "source": "arxiv"}, "https://futureagi.com/blogs/generating-synthetic-datasets-for-retrieval-augmented-generation-(rag)": {"title": "Synthetic Datasets for Retrieval-Augmented Generation (RAG)", "url": "https://futureagi.com/blogs/generating-synthetic-datasets-for-retrieval-augmented-generation-(rag)", "summary": "How FutureAGI Revolutionizes Synthetic Data Generation for RAG. Future AGI offers a cutting-edge platform for generating high-quality synthetic datasets, empowering organizations to build robust Retrieval-Augmented Generation (RAG) systems with unparalleled efficiency and precision. With its intuitive and customizable approach, Future AGI ...", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}}