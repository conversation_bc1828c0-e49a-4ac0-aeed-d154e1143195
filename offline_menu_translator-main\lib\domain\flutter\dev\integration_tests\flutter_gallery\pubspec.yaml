name: flutter_gallery

environment:
  sdk: ^3.7.0-0

dependencies:
  flutter:
    sdk: flutter
  collection: 1.19.1
  intl: 0.20.2
  string_scanner: 1.4.1
  url_launcher: 6.3.1
  cupertino_icons: 1.0.8
  video_player: 2.9.5
  scoped_model: 2.0.0
  shrine_images: 2.0.2

  # Also update dev/benchmarks/complex_layout/pubspec.yaml
  # and dev/benchmarks/macrobenchmarks/pubspec.yaml
  flutter_gallery_assets: 1.0.2

  characters: 1.4.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  clock: 1.1.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  csslib: 1.0.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  html: 0.15.5+1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  material_color_utilities: 0.11.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  meta: 1.16.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  path: 1.9.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  plugin_platform_interface: 2.1.8 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  source_span: 1.10.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  term_glyph: 1.2.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  url_launcher_android: 6.3.15 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  url_launcher_ios: 6.3.3 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  url_launcher_linux: 3.2.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  url_launcher_macos: 3.2.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  url_launcher_platform_interface: 2.3.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  url_launcher_web: 2.4.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  url_launcher_windows: 3.1.4 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  vector_math: 2.1.4 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  video_player_android: 2.8.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  video_player_avfoundation: 2.7.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  video_player_platform_interface: 6.3.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  video_player_web: 2.3.4 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  web: 1.1.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_driver:
    sdk: flutter
  flutter_goldens:
    sdk: flutter
  test: 1.25.15
  integration_test:
    sdk: flutter

  _fe_analyzer_shared: 80.0.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  analyzer: 7.3.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  args: 2.7.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  async: 2.13.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  boolean_selector: 2.1.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  convert: 3.1.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  coverage: 1.11.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  crypto: 3.0.6 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  fake_async: 1.3.3 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  file: 7.0.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  frontend_server_client: 4.0.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  glob: 2.1.3 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  http_multi_server: 3.2.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  http_parser: 4.1.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  io: 1.0.5 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  js: 0.7.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  leak_tracker: 10.0.9 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  leak_tracker_flutter_testing: 3.0.9 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  leak_tracker_testing: 3.0.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  logging: 1.3.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  matcher: 0.12.17 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  mime: 2.0.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  node_preamble: 2.0.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  package_config: 2.2.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  platform: 3.1.6 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  pool: 1.5.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  process: 5.0.3 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  pub_semver: 2.2.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  shelf: 1.4.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  shelf_packages_handler: 3.0.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  shelf_static: 1.1.3 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  shelf_web_socket: 2.0.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  source_map_stack_trace: 2.1.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  source_maps: 0.10.13 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  stack_trace: 1.12.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  stream_channel: 2.1.4 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  sync_http: 0.3.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  test_api: 0.7.4 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  test_core: 0.6.8 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  typed_data: 1.4.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  vm_service: 15.0.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  watcher: 1.1.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  web_socket: 0.1.6 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  web_socket_channel: 3.0.2 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  webdriver: 3.1.0 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  webkit_inspection_protocol: 1.2.1 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"
  yaml: 3.1.3 # THIS LINE IS AUTOGENERATED - TO UPDATE USE "flutter update-packages --force-upgrade"

flutter:
  uses-material-design: true
  assets:
    - lib/gallery/example_code.dart
    - packages/flutter_gallery_assets/people/ali_landscape.png
    - packages/flutter_gallery_assets/monochrome/red-square-1024x1024.png
    - packages/flutter_gallery_assets/logos/flutter_white/logo.png
    - packages/flutter_gallery_assets/logos/fortnightly/fortnightly_logo.png
    - packages/flutter_gallery_assets/videos/bee.mp4
    - packages/flutter_gallery_assets/videos/butterfly.mp4
    - packages/flutter_gallery_assets/animated_images/animated_flutter_lgtm.gif
    - packages/flutter_gallery_assets/animated_images/animated_flutter_stickers.webp
    - packages/flutter_gallery_assets/food/butternut_squash_soup.png
    - packages/flutter_gallery_assets/food/cherry_pie.png
    - packages/flutter_gallery_assets/food/chopped_beet_leaves.png
    - packages/flutter_gallery_assets/food/fruits.png
    - packages/flutter_gallery_assets/food/pesto_pasta.png
    - packages/flutter_gallery_assets/food/roasted_chicken.png
    - packages/flutter_gallery_assets/food/spanakopita.png
    - packages/flutter_gallery_assets/food/spinach_onion_salad.png
    - packages/flutter_gallery_assets/food/icons/fish.png
    - packages/flutter_gallery_assets/food/icons/healthy.png
    - packages/flutter_gallery_assets/food/icons/main.png
    - packages/flutter_gallery_assets/food/icons/meat.png
    - packages/flutter_gallery_assets/food/icons/quick.png
    - packages/flutter_gallery_assets/food/icons/spicy.png
    - packages/flutter_gallery_assets/food/icons/veggie.png
    - packages/flutter_gallery_assets/logos/pesto/logo_small.png
    - packages/flutter_gallery_assets/places/india_chennai_flower_market.png
    - packages/flutter_gallery_assets/places/india_thanjavur_market.png
    - packages/flutter_gallery_assets/places/india_tanjore_bronze_works.png
    - packages/flutter_gallery_assets/places/india_tanjore_market_merchant.png
    - packages/flutter_gallery_assets/places/india_tanjore_thanjavur_temple.png
    - packages/flutter_gallery_assets/places/india_pondicherry_salt_farm.png
    - packages/flutter_gallery_assets/places/india_chennai_highway.png
    - packages/flutter_gallery_assets/places/india_chettinad_silk_maker.png
    - packages/flutter_gallery_assets/places/india_tanjore_thanjavur_temple_carvings.png
    - packages/flutter_gallery_assets/places/india_chettinad_produce.png
    - packages/flutter_gallery_assets/places/india_tanjore_market_technology.png
    - packages/flutter_gallery_assets/places/india_pondicherry_beach.png
    - packages/flutter_gallery_assets/places/india_pondicherry_fisherman.png
    - packages/flutter_gallery_assets/products/backpack.png
    - packages/flutter_gallery_assets/products/belt.png
    - packages/flutter_gallery_assets/products/cup.png
    - packages/flutter_gallery_assets/products/deskset.png
    - packages/flutter_gallery_assets/products/dress.png
    - packages/flutter_gallery_assets/products/earrings.png
    - packages/flutter_gallery_assets/products/flatwear.png
    - packages/flutter_gallery_assets/products/hat.png
    - packages/flutter_gallery_assets/products/jacket.png
    - packages/flutter_gallery_assets/products/jumper.png
    - packages/flutter_gallery_assets/products/kitchen_quattro.png
    - packages/flutter_gallery_assets/products/napkins.png
    - packages/flutter_gallery_assets/products/planters.png
    - packages/flutter_gallery_assets/products/platter.png
    - packages/flutter_gallery_assets/products/scarf.png
    - packages/flutter_gallery_assets/products/shirt.png
    - packages/flutter_gallery_assets/products/sunnies.png
    - packages/flutter_gallery_assets/products/sweater.png
    - packages/flutter_gallery_assets/products/sweats.png
    - packages/flutter_gallery_assets/products/table.png
    - packages/flutter_gallery_assets/products/teaset.png
    - packages/flutter_gallery_assets/products/top.png
    - packages/flutter_gallery_assets/people/square/ali.png
    - packages/flutter_gallery_assets/people/square/peter.png
    - packages/flutter_gallery_assets/people/square/sandra.png
    - packages/flutter_gallery_assets/people/square/stella.png
    - packages/flutter_gallery_assets/people/square/trevor.png
    - packages/shrine_images/diamond.png
    - packages/shrine_images/slanted_menu.png
    - packages/shrine_images/0-0.jpg
    - packages/shrine_images/1-0.jpg
    - packages/shrine_images/2-0.jpg
    - packages/shrine_images/3-0.jpg
    - packages/shrine_images/4-0.jpg
    - packages/shrine_images/5-0.jpg
    - packages/shrine_images/6-0.jpg
    - packages/shrine_images/7-0.jpg
    - packages/shrine_images/8-0.jpg
    - packages/shrine_images/9-0.jpg
    - packages/shrine_images/10-0.jpg
    - packages/shrine_images/11-0.jpg
    - packages/shrine_images/12-0.jpg
    - packages/shrine_images/13-0.jpg
    - packages/shrine_images/14-0.jpg
    - packages/shrine_images/15-0.jpg
    - packages/shrine_images/16-0.jpg
    - packages/shrine_images/17-0.jpg
    - packages/shrine_images/18-0.jpg
    - packages/shrine_images/19-0.jpg
    - packages/shrine_images/20-0.jpg
    - packages/shrine_images/21-0.jpg
    - packages/shrine_images/22-0.jpg
    - packages/shrine_images/23-0.jpg
    - packages/shrine_images/24-0.jpg
    - packages/shrine_images/25-0.jpg
    - packages/shrine_images/26-0.jpg
    - packages/shrine_images/27-0.jpg
    - packages/shrine_images/28-0.jpg
    - packages/shrine_images/29-0.jpg
    - packages/shrine_images/30-0.jpg
    - packages/shrine_images/31-0.jpg
    - packages/shrine_images/32-0.jpg
    - packages/shrine_images/33-0.jpg
    - packages/shrine_images/34-0.jpg
    - packages/shrine_images/35-0.jpg
    - packages/shrine_images/36-0.jpg
    - packages/shrine_images/37-0.jpg

  fonts:
    - family: Raleway
      fonts:
        - asset: packages/flutter_gallery_assets/fonts/raleway/Raleway-Regular.ttf
        - asset: packages/flutter_gallery_assets/fonts/raleway/Raleway-Medium.ttf
          weight: 500
        - asset: packages/flutter_gallery_assets/fonts/raleway/Raleway-SemiBold.ttf
          weight: 600
    - family: AbrilFatface
      fonts:
        - asset: packages/flutter_gallery_assets/fonts/abrilfatface/AbrilFatface-Regular.ttf
    - family: GalleryIcons
      fonts:
        - asset: packages/flutter_gallery_assets/fonts/private/gallery_icons/GalleryIcons.ttf
    - family: GoogleSans
      fonts:
        - asset: packages/flutter_gallery_assets/fonts/private/googlesans/GoogleSans-BoldItalic.ttf
          weight: 700
          style: italic
        - asset: packages/flutter_gallery_assets/fonts/private/googlesans/GoogleSans-Bold.ttf
          weight: 700
        - asset: packages/flutter_gallery_assets/fonts/private/googlesans/GoogleSans-Italic.ttf
          weight: 400
          style: italic
        - asset: packages/flutter_gallery_assets/fonts/private/googlesans/GoogleSans-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: packages/flutter_gallery_assets/fonts/private/googlesans/GoogleSans-Medium.ttf
          weight: 500
        - asset: packages/flutter_gallery_assets/fonts/private/googlesans/GoogleSans-Regular.ttf
          weight: 400
    - family: GoogleSansDisplay
      fonts:
        - asset: packages/flutter_gallery_assets/fonts/private/googlesans/GoogleSansDisplay-BoldItalic.ttf
          weight: 700
          style: italic
        - asset: packages/flutter_gallery_assets/fonts/private/googlesans/GoogleSansDisplay-Bold.ttf
          weight: 700
        - asset: packages/flutter_gallery_assets/fonts/private/googlesans/GoogleSansDisplay-Italic.ttf
          weight: 400
          style: italic
        - asset: packages/flutter_gallery_assets/fonts/private/googlesans/GoogleSansDisplay-MediumItalic.ttf
          style: italic
          weight: 500
        - asset: packages/flutter_gallery_assets/fonts/private/googlesans/GoogleSansDisplay-Medium.ttf
          weight: 500
        - asset: packages/flutter_gallery_assets/fonts/private/googlesans/GoogleSansDisplay-Regular.ttf
          weight: 400
    - family: LibreFranklin
      fonts:
      - asset: packages/flutter_gallery_assets/fonts/librefranklin/LibreFranklin-Bold.ttf
      - asset: packages/flutter_gallery_assets/fonts/librefranklin/LibreFranklin-Light.ttf
      - asset: packages/flutter_gallery_assets/fonts/librefranklin/LibreFranklin-Medium.ttf
      - asset: packages/flutter_gallery_assets/fonts/librefranklin/LibreFranklin-Regular.ttf
    - family: Merriweather
      fonts:
      - asset: packages/flutter_gallery_assets/fonts/merriweather/Merriweather-BlackItalic.ttf
      - asset: packages/flutter_gallery_assets/fonts/merriweather/Merriweather-Italic.ttf
      - asset: packages/flutter_gallery_assets/fonts/merriweather/Merriweather-Regular.ttf
      - asset: packages/flutter_gallery_assets/fonts/merriweather/Merriweather-Light.ttf

# PUBSPEC CHECKSUM: 054a
