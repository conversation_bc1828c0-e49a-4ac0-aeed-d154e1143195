# config.py

COLUMN_RENAMES = {
    'participant_id': 'participant_id',
    'sex': 'sex_feminino',
    'ethnicity_1': 'ethnicity_race',
    'birthplace': 'birthplace',
    'residence': 'residence',
    'family_income': 'family_income_category',
    'origin': 'hospital_origin',
    'origin_2': 'hospital_origin_details',
    'diseases': 'comorbidities',
    'historic': 'family_history',
    'drug': 'uses_regular_medication',
    'drugs_1': 'medication_details',
    'allergy': 'has_allergy',
    'allergy_2': 'allergy_details',
    'use_clopidogrel': 'uses_clopidogrel',
    'smoker': 'current_smoker',
    'types_cigarette_1': 'cigarette_type_industrialized',
    'types_cigarette_2': 'cigarette_type_straw',
    'types_cigarette_3': 'cigarette_type_pipe',
    'types_cigarette_4': 'cigarette_type_cigar',
    'types_cigarette_5': 'cigarette_type_rope_tobacco',
    'types_cigarette_6': 'cigarette_type_leaf_tobacco',
    'types_cigarette_7': 'cigarette_type_other',
    'other_smoke': 'cigarette_type_other_details',
    'how_many_smoker': 'cigarettes_per_day_current',
    'time_smoker': 'years_smoking_current',
    'ex_smoker': 'former_smoker',
    'types_cigarette_2_1': 'former_cigarette_type_industrialized',
    'types_cigarette_2_2': 'former_cigarette_type_straw',
    'types_cigarette_2_3': 'former_cigarette_type_pipe',
    'types_cigarette_2_4': 'former_cigarette_type_cigar',
    'types_cigarette_2_5': 'former_cigarette_type_rope_tobacco',
    'types_cigarette_2_6': 'former_cigarette_type_leaf_tobacco',
    'types_cigarette_2_7': 'former_cigarette_type_other',
    'other_smoke_2': 'former_cigarette_type_other_details',
    'how_many_ex_smoker': 'former_cigarettes_per_day',
    'smoker_2': 'former_smoker_status',
    'alcohol': 'alcohol_consumption',
    'illicit_drug': 'uses_illicit_drugs',
    'illicit_drug_3': 'illicit_drugs_frequency',
    'illicit_drug_4': 'illicit_drugs_duration',
    'illicit_drug_2': 'illicit_drugs_type',
    'anabolic_steroids': 'uses_anabolic_steroids',
    'caffeine': 'consumes_caffeine',
    'caffeine_cup': 'daily_coffee_cups',
    'exercise': 'exercise_frequency',
    'medical_record_v2': 'medical_record',
    'sex_v2': 'sex_feminino_duplicate',
    'age_v2': 'age_years',
    'weight_v2': 'weight_kg',
    'height_v2': 'height_cm',
    'ethnicity_1_v2': 'ethnicity_race_duplicate',
    'origin_v2': 'hospital_origin_v2',
    'symptoms_v2': 'symptoms',
    'diagnostic_v2': 'diagnosis',
    'angioplasty_classification_v2': 'angioplasty_urgency',
    'date_angioplasty_v2': 'angioplasty_date',
    'second_time_v2': 'angioplasty_repeat',
    'stent_0': 'stent_trunk',
    'stent_1': 'stent_da',
    'stent_2': 'stent_dg1',
    'stent_3': 'stent_dg2',
    'stent_4': 'stent_cx',
    'stent_5': 'stent_mg1',
    'stent_6': 'stent_mg2',
    'stent_7': 'stent_cd',
    'stent_8': 'stent_dp',
    'stent_9': 'stent_vp',
    'stent_10': 'stent_diagonal',
    'stent_11': 'stent_diagonalis',
    'stent_12': 'stent_saphenous_vein_graft',
    'stent_13': 'stent_mammary_artery',
    'stent_14': 'stent_other',
    'number_tronco': 'stent_count_trunk',
    'number_da': 'stent_count_da',
    'number_dg1': 'stent_count_dg1',
    'number_dg2': 'stent_count_dg2',
    'number_cx': 'stent_count_cx',
    'number_mg1': 'stent_count_mg1',
    'number_mg2': 'stent_count_mg2',
    'number_cd': 'stent_count_cd',
    'number_dp': 'stent_count_dp',
    'number_vp': 'stent_count_vp',
    'number_diagonal': 'stent_count_diagonal',
    'number_diagonalis': 'stent_count_diagonalis',
    'number_pontesafena': 'stent_count_saphenous_vein_graft',
    'number_pontemamaria': 'stent_count_mammary_artery',
    'number_others': 'stent_count_other',
    'complications': 'angioplasty_complications',
    'heart_rate_v2': 'heart_rate',
    'blood_pressure_2': 'systolic_bp',
    'blood_pressure_3': 'diastolic_bp',
    'mean_arterial_pressure_2': 'mean_arterial_pressure',
    'hospital_treatment': 'hospital_treatment_details',
    'evolution_coronary_unit': 'coronary_unit_evolution',
    'age_2': 'age_at_event',
    'timi_age': 'timi_age_category',
    'timi_comorbidity': 'timi_has_comorbidity',
    'timi_fc': 'timi_heart_rate',
    'timi_pas': 'timi_systolic_bp',
    'timi_killip': 'timi_killip_class',
    'timi_weight': 'timi_weight_category',
    'timi_suprast': 'timi_st_segment_elevation',
    'timi_time_treatment': 'timi_treatment_time',
    'timi_score': 'timi_risk_score',
    'arterial_hypertension_2': 'has_hypertension',
    'diabetes_2': 'diabetes_type'
}

DATA_TYPES = {
    'participant_id': 'str',
    'sex_feminino': 'category',
    'ethnicity_race': 'category',
    'birthplace': 'str',
    'residence': 'str',
    'family_income_category': 'category',
    'hospital_origin': 'category',
    'hospital_origin_details': 'str',
    'comorbidities': 'str',
    'family_history': 'str',
    'uses_regular_medication': 'bool',
    'medication_details': 'str',
    'has_allergy': 'bool',
    'allergy_details': 'str',
    'uses_clopidogrel': 'category',
    'current_smoker': 'bool',
    'cigarette_type_industrialized': 'bool',
    'cigarette_type_straw': 'bool',
    'cigarette_type_pipe': 'bool',
    'cigarette_type_cigar': 'bool',
    'cigarette_type_rope_tobacco': 'bool',
    'cigarette_type_leaf_tobacco': 'bool',
    'cigarette_type_other': 'bool',
    'cigarette_type_other_details': 'str',
    'cigarettes_per_day_current': 'float64',
    'years_smoking_current': 'float64',
    'former_smoker': 'bool',
    'former_cigarette_type_industrialized': 'bool',
    'former_cigarette_type_straw': 'bool',
    'former_cigarette_type_pipe': 'bool',
    'former_cigarette_type_cigar': 'bool',
    'former_cigarette_type_rope_tobacco': 'bool',
    'former_cigarette_type_leaf_tobacco': 'bool',
    'former_cigarette_type_other': 'bool',
    'former_cigarette_type_other_details': 'str',
    'former_cigarettes_per_day': 'float64',
    'former_smoker_status': 'str',
    'alcohol_consumption': 'category',
    'uses_illicit_drugs': 'bool',
    'illicit_drugs_frequency': 'str',
    'illicit_drugs_duration': 'str',
    'illicit_drugs_type': 'str',
    'uses_anabolic_steroids': 'bool',
    'consumes_caffeine': 'bool',
    'daily_coffee_cups': 'float64',
    'exercise_frequency': 'category',
    'medical_record': 'str',
    'sex_feminino_duplicate': 'category',
    'age_years': 'float64',
    'weight_kg': 'float64',
    'height_cm': 'float64',
    'ethnicity_race_duplicate': 'category',
    'hospital_origin_v2': 'category',
    'symptoms': 'str',
    'diagnosis': 'str',
    'angioplasty_urgency': 'category',
    'angioplasty_date': 'datetime64[ns]',
    'angioplasty_repeat': 'bool',
    'stent_trunk': 'bool',
    'stent_da': 'bool',
    'stent_dg1': 'bool',
    'stent_dg2': 'bool',
    'stent_cx': 'bool',
    'stent_mg1': 'bool',
    'stent_mg2': 'bool',
    'stent_cd': 'bool',
    'stent_dp': 'bool',
    'stent_vp': 'bool',
    'stent_diagonal': 'bool',
    'stent_diagonalis': 'bool',
    'stent_saphenous_vein_graft': 'bool',
    'stent_mammary_artery': 'bool',
    'stent_other': 'bool',
    'stent_count_trunk': 'float64',
    'stent_count_da': 'float64',
    'stent_count_dg1': 'float64',
    'stent_count_dg2': 'float64',
    'stent_count_cx': 'float64',
    'stent_count_mg1': 'float64',
    'stent_count_mg2': 'float64',
    'stent_count_cd': 'float64',
    'stent_count_dp': 'float64',
    'stent_count_vp': 'float64',
    'stent_count_diagonal': 'float64',
    'stent_count_diagonalis': 'float64',
    'stent_count_saphenous_vein_graft': 'float64',
    'stent_count_mammary_artery': 'float64',
    'stent_count_other': 'float64',
    'angioplasty_complications': 'str',
    'heart_rate': 'float64',
    'systolic_bp': 'float64',
    'diastolic_bp': 'float64',
    'mean_arterial_pressure': 'float64',
    'hospital_treatment_details': 'str',
    'coronary_unit_evolution': 'str',
    'age_at_event': 'float64',
    'timi_age_category': 'category',
    'timi_has_comorbidity': 'bool',
    'timi_heart_rate': 'bool',
    'timi_systolic_bp': 'bool',
    'timi_killip_class': 'bool',
    'timi_weight_category': 'bool',
    'timi_st_segment_elevation': 'bool',
    'timi_treatment_time': 'bool',
    'timi_risk_score': 'float64',
    'has_hypertension': 'bool',
    'diabetes_type': 'category'
}

CATEGORY_ENCODINGS = {
    'sex_feminino': {1: True, 2: False},
    'sex_feminino_duplicate': {1: True, 2: False},
    'ethnicity_race': {
        1: 'black',
        2: 'pardo',
        3: 'yellow',
        4: 'indigenous',
        5: 'white',
        6: 'not_informed'
    },
    'family_income_category': {
        0: 'less_than_1_salary',
        1: 'up_to_2_salaries',
        2: '2_to_5_salaries',
        3: 'more_than_5_salaries'
    },
    'hospital_origin': {
        0: 'emergency_room',
        1: 'upa',
        3: 'ambulatory',
        4: 'home',
        5: 'other'
    },
    'uses_regular_medication': {1: True, 0: False},
    'has_allergy': {1: True, 0: False},
    'uses_clopidogrel': {1: True, 0: False},
    'current_smoker': {1: True, 0: False},
    'cigarette_type_industrialized': {1: True, 0: False},
    'cigarette_type_straw': {1: True, 0: False},
    'cigarette_type_pipe': {1: True, 0: False},
    'cigarette_type_cigar': {1: True, 0: False},
    'cigarette_type_rope_tobacco': {1: True, 0: False},
    'cigarette_type_leaf_tobacco': {1: True, 0: False},
    'cigarette_type_other': {1: True, 0: False},
    'former_smoker': {1: True, 0: False},
    'former_cigarette_type_industrialized': {1: True, 0: False},
    'former_cigarette_type_straw': {1: True, 0: False},
    'former_cigarette_type_pipe': {1: True, 0: False},
    'former_cigarette_type_cigar': {1: True, 0: False},
    'former_cigarette_type_rope_tobacco': {1: True, 0: False},
    'former_cigarette_type_leaf_tobacco': {1: True, 0: False},
    'former_cigarette_type_other': {1: True, 0: False},
    'alcohol_consumption': {
        0: 'none',
        1: 'social',
        2: 'frequent'
    },
    'uses_illicit_drugs': {1: True, 0: False},
    'uses_anabolic_steroids': {1: True, 0: False},
    'consumes_caffeine': {1: True, 0: False},
    'exercise_frequency': {
        0: 'sedentary',
        1: 'twice_week',
        2: 'three_times_week',
        3: 'more_than_four_times_week'
    },
    'angioplasty_urgency': {
        0: 'elective',
        1: 'emergency'
    },
    'angioplasty_repeat': {1: True, 0: False},
    'stent_trunk': {1: True, 0: False},
    'stent_da': {1: True, 0: False},
    'stent_dg1': {1: True, 0: False},
    'stent_dg2': {1: True, 0: False},
    'stent_cx': {1: True, 0: False},
    'stent_mg1': {1: True, 0: False},
    'stent_mg2': {1: True, 0: False},
    'stent_cd': {1: True, 0: False},
    'stent_dp': {1: True, 0: False},
    'stent_vp': {1: True, 0: False},
    'stent_diagonal': {1: True, 0: False},
    'stent_diagonalis': {1: True, 0: False},
    'stent_saphenous_vein_graft': {1: True, 0: False},
    'stent_mammary_artery': {1: True, 0: False},
    'stent_other': {1: True, 0: False},
    'timi_age_category': {
        0: 'under_65',
        2: '65_to_74',
        3: 'over_75'
    },
    'timi_has_comorbidity': {1: True, 0: False},
    'timi_heart_rate': {2: True, 0: False},  # Corrigido de 2 para 1
    'timi_systolic_bp': {3: True, 0: False},  # Corrigido de 3 para 1
    'timi_killip_class': {2: True, 0: False},  # Corrigido de 2 para 1
    'timi_weight_category': {1: True, 0: False},
    'timi_st_segment_elevation': {1: True, 0: False},
    'timi_treatment_time': {1: True, 0: False},
    'has_hypertension': {1: True, 0: False},
    'diabetes_type': {
        0: 'no_diabetes',
        1: 'type_1',
        2: 'type_2'
    }
}

CONDITIONAL_COLUMNS = {
    'current_smoker': [
        'cigarette_type_industrialized',
        'cigarette_type_straw',
        'cigarette_type_pipe',
        'cigarette_type_cigar',
        'cigarette_type_rope_tobacco',
        'cigarette_type_leaf_tobacco',
        'cigarette_type_other',
        'cigarettes_per_day_current',
        'years_smoking_current'
    ],
    'former_smoker': [
        'former_cigarette_type_industrialized',
        'former_cigarette_type_straw',
        'former_cigarette_type_pipe',
        'former_cigarette_type_cigar',
        'former_cigarette_type_rope_tobacco',
        'former_cigarette_type_leaf_tobacco',
        'former_cigarette_type_other',
        'former_cigarettes_per_day',
        'former_smoker_status'
    ],
    'uses_regular_medication': ['medication_details'],
    'has_allergy': ['allergy_details'],
    'uses_illicit_drugs': [
        'illicit_drugs_frequency',
        'illicit_drugs_duration',
        'illicit_drugs_type'
    ],
    'cigarette_type_other': ['cigarette_type_other_details'],
    'former_cigarette_type_other': ['former_cigarette_type_other_details'],
    'stent_other': ['stent_count_other'],

    'consumes_caffeine': [
        'daily_coffee_cups'
    ],
    'hospital_origin_v2': [
        'hospital_origin_details'
    ],
    'stent_trunk': [
        'stent_count_trunk'
    ],
    'stent_da': [
        'stent_count_da'
    ],
    'stent_dg1': [
        'stent_count_dg1'
    ],
    'stent_dg2': [
        'stent_count_dg2'
    ],
    'stent_cx': [
        'stent_count_cx'
    ],
    'stent_mg1': [
        'stent_count_mg1'
    ],
    'stent_mg2': [
        'stent_count_mg2'
    ],
    'stent_cd': [
        'stent_count_cd'
    ],
    'stent_dp': [
        'stent_count_dp'
    ],
    'stent_vp': [
        'stent_count_vp'
    ],
    'stent_diagonal': [
        'stent_count_diagonal'
    ],
    'stent_diagonalis': [
        'stent_count_diagonalis'
    ],
    'stent_saphenous_vein_graft': [
        'stent_count_saphenous_vein_graft'
    ],
    'stent_mammary_artery': [
        'stent_count_mammary_artery'
    ]
}