// Autogenerated from <PERSON>eon (v24.1.0), do not edit directly.
// See also: https://pub.dev/packages/pigeon

import Foundation

#if os(iOS)
  import Flutter
#elseif os(macOS)
  import FlutterMacOS
#else
  #error("Unsupported platform.")
#endif

/// Error class for passing custom error details to Dart side.
final class PigeonError: Error {
  let code: String
  let message: String?
  let details: Sendable?

  init(code: String, message: String?, details: Sendable?) {
    self.code = code
    self.message = message
    self.details = details
  }

  var localizedDescription: String {
    return
      "PigeonError(code: \(code), message: \(message ?? "<nil>"), details: \(details ?? "<nil>")"
      }
}

private func wrapResult(_ result: Any?) -> [Any?] {
  return [result]
}

private func wrapError(_ error: Any) -> [Any?] {
  if let pigeonError = error as? PigeonError {
    return [
      pigeonError.code,
      pigeonError.message,
      pigeonError.details,
    ]
  }
  if let flutterError = error as? FlutterError {
    return [
      flutterError.code,
      flutterError.message,
      flutterError.details,
    ]
  }
  return [
    "\(error)",
    "\(type(of: error))",
    "Stacktrace: \(Thread.callStackSymbols)",
  ]
}

private func isNullish(_ value: Any?) -> Bool {
  return value is NSNull || value == nil
}

private func nilOrValue<T>(_ value: Any?) -> T? {
  if value is NSNull { return nil }
  return value as! T?
}

enum PreferredBackend: Int {
  case unknown = 0
  case cpu = 1
  case gpu = 2
  case gpuFloat16 = 3
  case gpuMixed = 4
  case gpuFull = 5
  case tpu = 6
}

private class PigeonInterfacePigeonCodecReader: FlutterStandardReader {
  override func readValue(ofType type: UInt8) -> Any? {
    switch type {
    case 129:
      let enumResultAsInt: Int? = nilOrValue(self.readValue() as! Int?)
      if let enumResultAsInt = enumResultAsInt {
        return PreferredBackend(rawValue: enumResultAsInt)
      }
      return nil
    default:
      return super.readValue(ofType: type)
    }
  }
}

private class PigeonInterfacePigeonCodecWriter: FlutterStandardWriter {
  override func writeValue(_ value: Any) {
    if let value = value as? PreferredBackend {
      super.writeByte(129)
      super.writeValue(value.rawValue)
    } else {
      super.writeValue(value)
    }
  }
}

private class PigeonInterfacePigeonCodecReaderWriter: FlutterStandardReaderWriter {
  override func reader(with data: Data) -> FlutterStandardReader {
    return PigeonInterfacePigeonCodecReader(data: data)
  }

  override func writer(with data: NSMutableData) -> FlutterStandardWriter {
    return PigeonInterfacePigeonCodecWriter(data: data)
  }
}

class PigeonInterfacePigeonCodec: FlutterStandardMessageCodec, @unchecked Sendable {
  static let shared = PigeonInterfacePigeonCodec(readerWriter: PigeonInterfacePigeonCodecReaderWriter())
}


/// Generated protocol from Pigeon that represents a handler of messages from Flutter.
protocol PlatformService {
  func createModel(maxTokens: Int64, modelPath: String, loraRanks: [Int64]?, preferredBackend: PreferredBackend?, maxNumImages: Int64?, completion: @escaping (Result<Void, Error>) -> Void)
  func closeModel(completion: @escaping (Result<Void, Error>) -> Void)
  func createSession(temperature: Double, randomSeed: Int64, topK: Int64, topP: Double?, loraPath: String?, enableVisionModality: Bool?, completion: @escaping (Result<Void, Error>) -> Void)
  func closeSession(completion: @escaping (Result<Void, Error>) -> Void)
  func sizeInTokens(prompt: String, completion: @escaping (Result<Int64, Error>) -> Void)
  func addQueryChunk(prompt: String, completion: @escaping (Result<Void, Error>) -> Void)
  func addImage(imageBytes: FlutterStandardTypedData, completion: @escaping (Result<Void, Error>) -> Void)
  func generateResponse(completion: @escaping (Result<String, Error>) -> Void)
  func generateResponseAsync(completion: @escaping (Result<Void, Error>) -> Void)
}

/// Generated setup class from Pigeon to handle messages through the `binaryMessenger`.
class PlatformServiceSetup {
  static var codec: FlutterStandardMessageCodec { PigeonInterfacePigeonCodec.shared }
  /// Sets up an instance of `PlatformService` to handle messages through the `binaryMessenger`.
  static func setUp(binaryMessenger: FlutterBinaryMessenger, api: PlatformService?, messageChannelSuffix: String = "") {
    let channelSuffix = messageChannelSuffix.count > 0 ? ".\(messageChannelSuffix)" : ""
    let createModelChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.flutter_gemma.PlatformService.createModel\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      createModelChannel.setMessageHandler { message, reply in
        let args = message as! [Any?]
        let maxTokensArg = args[0] as! Int64
        let modelPathArg = args[1] as! String
        let loraRanksArg: [Int64]? = nilOrValue(args[2])
        let preferredBackendArg: PreferredBackend? = nilOrValue(args[3])
        let maxNumImagesArg: Int64? = nilOrValue(args[4])
        api.createModel(maxTokens: maxTokensArg, modelPath: modelPathArg, loraRanks: loraRanksArg, preferredBackend: preferredBackendArg, maxNumImages: maxNumImagesArg) { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      createModelChannel.setMessageHandler(nil)
    }
    let closeModelChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.flutter_gemma.PlatformService.closeModel\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      closeModelChannel.setMessageHandler { _, reply in
        api.closeModel { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      closeModelChannel.setMessageHandler(nil)
    }
    let createSessionChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.flutter_gemma.PlatformService.createSession\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      createSessionChannel.setMessageHandler { message, reply in
        let args = message as! [Any?]
        let temperatureArg = args[0] as! Double
        let randomSeedArg = args[1] as! Int64
        let topKArg = args[2] as! Int64
        let topPArg: Double? = nilOrValue(args[3])
        let loraPathArg: String? = nilOrValue(args[4])
        let enableVisionModalityArg: Bool? = nilOrValue(args[5])
        api.createSession(temperature: temperatureArg, randomSeed: randomSeedArg, topK: topKArg, topP: topPArg, loraPath: loraPathArg, enableVisionModality: enableVisionModalityArg) { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      createSessionChannel.setMessageHandler(nil)
    }
    let closeSessionChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.flutter_gemma.PlatformService.closeSession\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      closeSessionChannel.setMessageHandler { _, reply in
        api.closeSession { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      closeSessionChannel.setMessageHandler(nil)
    }
    let sizeInTokensChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.flutter_gemma.PlatformService.sizeInTokens\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      sizeInTokensChannel.setMessageHandler { message, reply in
        let args = message as! [Any?]
        let promptArg = args[0] as! String
        api.sizeInTokens(prompt: promptArg) { result in
          switch result {
          case .success(let res):
            reply(wrapResult(res))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      sizeInTokensChannel.setMessageHandler(nil)
    }
    let addQueryChunkChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.flutter_gemma.PlatformService.addQueryChunk\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      addQueryChunkChannel.setMessageHandler { message, reply in
        let args = message as! [Any?]
        let promptArg = args[0] as! String
        api.addQueryChunk(prompt: promptArg) { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      addQueryChunkChannel.setMessageHandler(nil)
    }
    let addImageChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.flutter_gemma.PlatformService.addImage\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      addImageChannel.setMessageHandler { message, reply in
        let args = message as! [Any?]
        let imageBytesArg = args[0] as! FlutterStandardTypedData
        api.addImage(imageBytes: imageBytesArg) { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      addImageChannel.setMessageHandler(nil)
    }
    let generateResponseChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.flutter_gemma.PlatformService.generateResponse\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      generateResponseChannel.setMessageHandler { _, reply in
        api.generateResponse { result in
          switch result {
          case .success(let res):
            reply(wrapResult(res))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      generateResponseChannel.setMessageHandler(nil)
    }
    let generateResponseAsyncChannel = FlutterBasicMessageChannel(name: "dev.flutter.pigeon.flutter_gemma.PlatformService.generateResponseAsync\(channelSuffix)", binaryMessenger: binaryMessenger, codec: codec)
    if let api = api {
      generateResponseAsyncChannel.setMessageHandler { _, reply in
        api.generateResponseAsync { result in
          switch result {
          case .success:
            reply(wrapResult(nil))
          case .failure(let error):
            reply(wrapError(error))
          }
        }
      }
    } else {
      generateResponseAsyncChannel.setMessageHandler(nil)
    }
  }
}
