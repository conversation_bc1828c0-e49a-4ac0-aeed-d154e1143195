import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../core/services/local_gemma_translation_service.dart';
import '../../core/services/gemma_downloader_service.dart';
import 'model_download_page.dart';

/// Page that checks if the model is downloaded and redirects accordingly
class ModelCheckPage extends StatefulWidget {
  const ModelCheckPage({super.key});

  @override
  State<ModelCheckPage> createState() => _ModelCheckPageState();
}

class _ModelCheckPageState extends State<ModelCheckPage> {
  final LocalGemmaTranslationService _localGemma =
      LocalGemmaTranslationService.instance;
  final GemmaDownloaderService _downloader = GemmaDownloaderService.instance;

  String _statusMessage = 'Checking AI model...';
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _checkModelAndProceed();
  }

  Future<void> _checkModelAndProceed() async {
    try {
      setState(() {
        _statusMessage = 'Checking if Gemma-3N model is available...';
        _hasError = false;
      });

      if (kDebugMode) {
        print('🔍 Checking Gemma-3N model availability...');
      }

      // Simulate checking model existence (since path_provider doesn't work on web)
      await Future.delayed(const Duration(seconds: 2));

      // For demo purposes, assume model is not downloaded
      final isModelAvailable = false;

      if (isModelAvailable) {
        // Model is available, proceed to main app
        setState(() {
          _statusMessage = 'Model ready! Starting app...';
        });

        await Future.delayed(const Duration(seconds: 1));

        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/home');
        }
      } else {
        // Model not available, start download
        await _downloadModel();
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error checking model: $e';
        _hasError = true;
      });

      if (kDebugMode) {
        print('❌ Error checking model: $e');
      }
    }
  }

  Future<void> _downloadModel() async {
    try {
      setState(() {
        _statusMessage = 'Gemma-3N model not found. Starting download...';
        _isDownloading = true;
        _downloadProgress = 0.0;
        _hasError = false;
      });

      if (kDebugMode) {
        print('📥 Starting Gemma-3N model download...');
      }

      // Simulate download with progress
      for (int i = 0; i <= 100; i += 2) {
        await Future.delayed(const Duration(milliseconds: 100));

        if (mounted) {
          setState(() {
            _downloadProgress = i / 100.0;
            _statusMessage = 'Downloading Gemma-3N model... $i%';
          });
        }
      }

      // Download completed
      setState(() {
        _statusMessage = '✅ Model downloaded successfully! Initializing...';
        _isDownloading = false;
      });

      if (kDebugMode) {
        print('✅ Gemma-3N model download completed!');
      }

      // Initialize the model
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _statusMessage = 'Ready! Starting SimulTrans AI...';
      });

      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Download failed: $e';
        _isDownloading = false;
        _hasError = true;
      });

      if (kDebugMode) {
        print('❌ Model download failed: $e');
      }
    }
  }

  void _retryDownload() {
    _checkModelAndProceed();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.surface,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.translate,
                size: 80,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 24),
              Text(
                'SimulTrans AI',
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
              const SizedBox(height: 16),
              Text(
                '100% Offline Translation',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.secondary,
                    ),
              ),
              const SizedBox(height: 48),
              const CircularProgressIndicator(),
              const SizedBox(height: 24),
              Text(
                'Checking AI model...',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
