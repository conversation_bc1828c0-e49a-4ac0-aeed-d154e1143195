import 'package:flutter/material.dart';
import 'model_download_page.dart';

/// Page that checks if the model is downloaded and redirects accordingly
class ModelCheckPage extends StatefulWidget {
  const ModelCheckPage({super.key});

  @override
  State<ModelCheckPage> createState() => _ModelCheckPageState();
}

class _ModelCheckPageState extends State<ModelCheckPage> {
  @override
  void initState() {
    super.initState();
    _checkModelAndRedirect();
  }

  Future<void> _checkModelAndRedirect() async {
    try {
      // For web, we'll always show the download page first
      // since path_provider doesn't work reliably on web
      if (mounted) {
        // Always show download page for now
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => ModelDownloadPage(
              isRequired: true,
              onModelReady: () {
                // Model is now ready, this will be called when download completes
              },
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        // Error checking model, show download page
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => ModelDownloadPage(
              isRequired: true,
              onModelReady: () {
                // Model is now ready
              },
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.surface,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.translate,
                size: 80,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 24),
              Text(
                'SimulTrans AI',
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
              const SizedBox(height: 16),
              Text(
                '100% Offline Translation',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.secondary,
                    ),
              ),
              const SizedBox(height: 48),
              const CircularProgressIndicator(),
              const SizedBox(height: 24),
              Text(
                'Checking AI model...',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
