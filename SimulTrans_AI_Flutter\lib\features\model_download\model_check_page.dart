import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../core/services/local_gemma_translation_service.dart';
import '../../core/services/gemma_downloader_service.dart';
import 'model_download_page.dart';

/// Page that checks if the model is downloaded and redirects accordingly
class ModelCheckPage extends StatefulWidget {
  const ModelCheckPage({super.key});

  @override
  State<ModelCheckPage> createState() => _ModelCheckPageState();
}

class _ModelCheckPageState extends State<ModelCheckPage> {
  final LocalGemmaTranslationService _localGemma =
      LocalGemmaTranslationService.instance;
  final GemmaDownloaderService _downloader = GemmaDownloaderService.instance;

  String _statusMessage = 'Checking AI model...';
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _checkModelAndProceed();
  }

  Future<void> _checkModelAndProceed() async {
    try {
      setState(() {
        _statusMessage = 'Checking if Gemma-3N model is available...';
        _hasError = false;
      });

      if (kDebugMode) {
        print('🔍 Checking Gemma-3N model availability...');
      }

      // Simulate checking model existence (since path_provider doesn't work on web)
      await Future.delayed(const Duration(seconds: 2));

      // For demo purposes, assume model is not downloaded
      final isModelAvailable = false;

      if (isModelAvailable) {
        // Model is available, proceed to main app
        setState(() {
          _statusMessage = 'Model ready! Starting app...';
        });

        await Future.delayed(const Duration(seconds: 1));

        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/home');
        }
      } else {
        // Model not available, start download
        await _downloadModel();
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error checking model: $e';
        _hasError = true;
      });

      if (kDebugMode) {
        print('❌ Error checking model: $e');
      }
    }
  }

  Future<void> _downloadModel() async {
    try {
      setState(() {
        _statusMessage = 'Gemma-3N model not found. Starting download...';
        _isDownloading = true;
        _downloadProgress = 0.0;
        _hasError = false;
      });

      if (kDebugMode) {
        print('📥 Starting Gemma-3N model download...');
      }

      // Simulate download with progress
      for (int i = 0; i <= 100; i += 2) {
        await Future.delayed(const Duration(milliseconds: 100));

        if (mounted) {
          setState(() {
            _downloadProgress = i / 100.0;
            _statusMessage = 'Downloading Gemma-3N model... $i%';
          });
        }
      }

      // Download completed
      setState(() {
        _statusMessage = '✅ Model downloaded successfully! Initializing...';
        _isDownloading = false;
      });

      if (kDebugMode) {
        print('✅ Gemma-3N model download completed!');
      }

      // Initialize the model
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _statusMessage = 'Ready! Starting SimulTrans AI...';
      });

      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Download failed: $e';
        _isDownloading = false;
        _hasError = true;
      });

      if (kDebugMode) {
        print('❌ Model download failed: $e');
      }
    }
  }

  void _retryDownload() {
    _checkModelAndProceed();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.surface,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App Icon and Title
                Icon(
                  _hasError
                      ? Icons.error_outline
                      : _isDownloading
                          ? Icons.download
                          : Icons.translate,
                  size: 80,
                  color: _hasError
                      ? Theme.of(context).colorScheme.error
                      : Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 24),
                Text(
                  'SimulTrans AI',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                ),
                const SizedBox(height: 16),
                Text(
                  '100% Offline Translation',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                ),
                const SizedBox(height: 48),

                // Progress Section
                if (_isDownloading) ...[
                  // Download Progress Bar
                  Container(
                    width: double.infinity,
                    height: 8,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceVariant,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: _downloadProgress,
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Theme.of(context).colorScheme.primary,
                              Theme.of(context).colorScheme.secondary,
                            ],
                          ),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '${(_downloadProgress * 100).toInt()}%',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                  ),
                  const SizedBox(height: 8),
                ] else if (!_hasError) ...[
                  // Loading Spinner
                  CircularProgressIndicator(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 24),
                ],

                // Status Message
                Text(
                  _statusMessage,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: _hasError
                            ? Theme.of(context).colorScheme.error
                            : Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                  textAlign: TextAlign.center,
                ),

                // Error Actions
                if (_hasError) ...[
                  const SizedBox(height: 32),
                  ElevatedButton.icon(
                    onPressed: _retryDownload,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                    ),
                  ),
                ],

                // Download Info
                if (_isDownloading) ...[
                  const SizedBox(height: 32),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context)
                          .colorScheme
                          .surfaceVariant
                          .withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              size: 20,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Downloading Gemma-3N Model',
                              style: Theme.of(context)
                                  .textTheme
                                  .titleSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '• Size: ~1.8GB\n• One-time download\n• 100% offline after download\n• No internet required for translation',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurfaceVariant,
                                  ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
