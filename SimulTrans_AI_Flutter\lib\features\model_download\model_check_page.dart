import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../core/services/local_gemma_translation_service.dart';
import '../../data/gemma_downloader_datasource.dart';
import 'model_download_page.dart';

/// Page that checks if the model is downloaded and redirects accordingly
class ModelCheckPage extends StatefulWidget {
  const ModelCheckPage({super.key});

  @override
  State<ModelCheckPage> createState() => _ModelCheckPageState();
}

class _ModelCheckPageState extends State<ModelCheckPage> {
  final LocalGemmaTranslationService _localGemma =
      LocalGemmaTranslationService.instance;
  final GemmaDownloaderDatasource _downloader =
      GemmaDownloaderDatasource.instance;

  String _statusMessage = 'Checking AI model...';
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _checkModelAndProceed();
  }

  Future<void> _checkModelAndProceed() async {
    try {
      setState(() {
        _statusMessage = 'Checking platform compatibility...';
        _hasError = false;
      });

      if (kDebugMode) {
        print('🔍 Checking platform and model availability...');
      }

      // Check if we're on web
      if (kIsWeb) {
        setState(() {
          _statusMessage =
              'Web version: Limited functionality. Download mobile/desktop app for full AI features.';
          _hasError = true;
        });
        return;
      }

      setState(() {
        _statusMessage = 'Checking if Gemma-3N model is available...';
      });

      // Check if model is actually available
      await Future.delayed(const Duration(seconds: 1));

      final isModelAvailable = await _downloader.checkModelExistence();

      if (isModelAvailable) {
        // Model is available, proceed to main app
        setState(() {
          _statusMessage = 'Model ready! Starting app...';
        });

        await Future.delayed(const Duration(seconds: 1));

        if (mounted) {
          Navigator.of(context).pushReplacementNamed('/home');
        }
      } else {
        // Model not available, start download
        await _downloadModel();
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error: $e';
        _hasError = true;
      });

      if (kDebugMode) {
        print('❌ Error checking model: $e');
      }
    }
  }

  Future<void> _downloadModel() async {
    try {
      setState(() {
        _statusMessage = 'Gemma-3N model not found. Starting download...';
        _isDownloading = true;
        _downloadProgress = 0.0;
        _hasError = false;
      });

      if (kDebugMode) {
        print('📥 Starting Gemma-3N model download...');
      }

      // Use the actual downloader service
      final success = await _downloader.downloadModel(
        onProgress: (progress) {
          if (mounted) {
            setState(() {
              _downloadProgress = progress;
              _statusMessage =
                  'Downloading Gemma-3N model... ${(progress * 100).toInt()}%';
            });
          }
        },
      );

      if (success) {
        // Download completed
        setState(() {
          _statusMessage = '✅ Model downloaded successfully! Initializing...';
          _isDownloading = false;
        });
      } else {
        throw Exception('Download failed');
      }

      if (kDebugMode) {
        print('✅ Gemma-3N model download completed!');
      }

      // Initialize the model
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _statusMessage = 'Ready! Starting SimulTrans AI...';
      });

      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Download failed: $e';
        _isDownloading = false;
        _hasError = true;
      });

      if (kDebugMode) {
        print('❌ Model download failed: $e');
      }
    }
  }

  void _retryDownload() {
    _checkModelAndProceed();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.surface,
            ],
          ),
        ),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                // App Icon and Title
                Icon(
                  _hasError ? Icons.warning_amber : Icons.translate,
                  size: 80,
                  color: _hasError
                      ? Theme.of(context).colorScheme.error
                      : Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 24),
                Text(
                  'SimulTrans AI',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                ),
                const SizedBox(height: 16),
                Text(
                  '100% Offline Translation',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.secondary,
                      ),
                ),
                const SizedBox(height: 48),

                // Progress Section
                if (_isDownloading) ...[
                  CircularProgressIndicator(
                    value: _downloadProgress,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '${(_downloadProgress * 100).toInt()}%',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                  ),
                ] else if (!_hasError) ...[
                  CircularProgressIndicator(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ],

                const SizedBox(height: 24),

                // Status Message
                Text(
                  _statusMessage,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: _hasError
                            ? Theme.of(context).colorScheme.error
                            : Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                  textAlign: TextAlign.center,
                ),

                // Simple retry button for errors
                if (_hasError) ...[
                  const SizedBox(height: 32),
                  ElevatedButton.icon(
                    onPressed: _retryDownload,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
