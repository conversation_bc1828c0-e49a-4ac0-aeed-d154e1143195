import 'package:flutter/material.dart';
import '../../core/services/local_gemma_translation_service.dart';
import 'model_download_page.dart';

/// Page that checks if the model is downloaded and redirects accordingly
class ModelCheckPage extends StatefulWidget {
  const ModelCheckPage({super.key});

  @override
  State<ModelCheckPage> createState() => _ModelCheckPageState();
}

class _ModelCheckPageState extends State<ModelCheckPage> {
  final LocalGemmaTranslationService _localGemma =
      LocalGemmaTranslationService.instance;

  @override
  void initState() {
    super.initState();
    _checkModelAndRedirect();
  }

  Future<void> _checkModelAndRedirect() async {
    try {
      // Initialize the service
      await _localGemma.initialize();

      // Check if model is downloaded
      final isModelDownloaded = await _localGemma.isModelDownloaded;

      if (mounted) {
        if (isModelDownloaded) {
          // Model is ready, go to home
          Navigator.of(context).pushReplacementNamed('/home');
        } else {
          // Model not downloaded, show download page
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (context) => ModelDownloadPage(
                isRequired: true,
                onModelReady: () {
                  // Model is now ready, this will be called when download completes
                },
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        // Error checking model, show download page
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => ModelDownloadPage(
              isRequired: true,
              onModelReady: () {
                // Model is now ready
              },
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.surface,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.translate,
                size: 80,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 24),
              Text(
                'SimulTrans AI',
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
              const SizedBox(height: 16),
              Text(
                '100% Offline Translation',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.secondary,
                    ),
              ),
              const SizedBox(height: 48),
              const CircularProgressIndicator(),
              const SizedBox(height: 24),
              Text(
                'Checking AI model...',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
