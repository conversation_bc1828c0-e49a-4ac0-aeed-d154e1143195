// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'dart:ffi';

typedef RawRect = Pointer<Float>;
typedef RawIRect = Pointer<Int32>;
typedef RawRRect = Pointer<Float>;
typedef RawPointArray = Pointer<Float>;
typedef RawRSTransformArray = Pointer<Float>;
typedef RawMatrix33 = Pointer<Float>;
typedef RawMatrix44 = Pointer<Float>;
typedef RawColorArray = Pointer<Uint32>;
