# Disclaimer: This function was generated by AI. Please review before using.
# Agent Name: data_visualization_agent
# Time Created: 2025-01-31 15:08:29

def data_visualization(data_raw):
    import pandas as pd
    import numpy as np
    import json
    import plotly.graph_objects as go
    import plotly.io as pio





    # Create a boxplot
    fig = go.Figure()
    
    # Add the boxplot trace
    fig.add_trace(go.Box(
        x=data_raw['Churn'],
        y=data_raw['MonthlyCharges'],
        boxmean=True,
        jitter=0.5,
        whiskerwidth=0.2,
        marker=dict(
            color='#3381ff'
        ),
        line=dict(
            width=0.65
        )
    ))
    
    # Update the layout
    fig.update_layout(
        template="plotly_white",
        xaxis_title="Churn Status",
        yaxis_title="Monthly Charges Distribution",
        title="Distribution of Monthly Charges by Churn Status",
        font=dict(
            size=8.8
        ),
        title_font_size=13.2
    )
    
    # Update the hover template
    fig.update_traces(
        hovertemplate=[
            "<b>Churn Status</b>: %{x}<br>"
            "<b>Monthly Charges</b>: %{y}<br>"
            "<extra></extra>"
        ]
    )
    
    # Convert figure to JSON
    fig_json = pio.to_json(fig)
    fig_dict = json.loads(fig_json)
    
    return fig_dict