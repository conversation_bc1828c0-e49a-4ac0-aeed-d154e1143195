name: Third party deps scan
on:
  # Only the default branch is supported.
  branch_protection_rule:
  push:
    branches: [ main ]
  pull_request:
    types: [ labeled ]

# Declare default permissions as read only.
permissions: read-all

jobs:
  extract-deps:
    name: Extract Dependencies
    runs-on: ubuntu-20.04
    if: ${{ (github.repository == 'flutter/engine' && github.event_name == 'push') || github.event.label.name == 'vulnerability scan' }}
    permissions:
      # Needed to upload the SARIF results to code-scanning dashboard.
      security-events: write
      contents: read
    steps:
      - name: "Checkout code"
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          persist-credentials: false
      - name: "setup python"
        uses: actions/setup-python@0b93645e9fea7318ecaed2b359559ac225c90a2b
        with:
          python-version: '3.7.7' # install the python version needed
      - name: "extract deps, find commit hash, pass to osv-scanner"
        run: python ci/scan_deps.py --output osv-lockfile-${{github.sha}}.json
      - name: "upload osv-scanner deps"
        uses: actions/upload-artifact@v4
        with:
          # use github.ref in name to avoid duplicated artifacts
          name: osv-lockfile-${{github.sha}}
          path: osv-lockfile-${{github.sha}}.json
          retention-days: 2
  vuln-scan:
    name: Vulnerability scanning
    needs:
      extract-deps
    uses: "google/osv-scanner-action/.github/workflows/osv-scanner-reusable.yml@v1.9.0"
    with:
      # Download the artifact uploaded in extract-deps step
      download-artifact: osv-lockfile-${{github.sha}}
      scan-args: |-
        --lockfile=osv-scanner:osv-lockfile-${{github.sha}}.json
      fail-on-vuln: false
    # makes sure the osv-formatted vulns are uploaded
    permissions:
      # Needed to upload the SARIF results to code-scanning dashboard.
      security-events: write
      actions: read
      contents: read
