# Copyright 2014 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# This file contains Chrome-feature-related build flags (see ui.gni for
# UI-related ones). These should theoretically be moved to the build files of
# the features themselves.
#
# However, today we have many "bad" dependencies on some of these flags from,
# e.g. base, so they need to be global to match the GYP configuration. Also,
# anything that needs a grit define must be in either this file or ui.gni.
#
# PLEASE TRY TO AVOID ADDING FLAGS TO THIS FILE in cases where grit isn't
# required. See the declare_args block of BUILDCONFIG.gn for advice on how
# to set up feature flags.

if (is_android) {
  import("//build/config/android/config.gni")
}

declare_args() {
  use_blink = false
}
