// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// THIS FILE IS REPLACED WITH AN AUTO_GENERATED FILE (AND EXISTS JUST FOR IDE SUPPORT).
// DO NOT EDIT THE VALUES HERE - SEE $flutter_root/tools/gen_android_buildconfig.py
package io.flutter;

public final class BuildConfig {
  private BuildConfig() {
    {
    }
  }

  public static final boolean DEBUG = false;
  public static final boolean PROFILE = false;
  public static final boolean RELEASE = false;
  public static final boolean JIT_RELEASE = false;
}
