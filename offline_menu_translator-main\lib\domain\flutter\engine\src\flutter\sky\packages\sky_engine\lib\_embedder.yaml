# This file is suitable for use within the tree. A different _embedder.yaml
# is generated by the BUILD.gn in this directory. Changes here must be
# mirrored there.
embedded_libs:
  "dart:async": "../../../../third_party/dart/sdk/lib/async/async.dart"
  "dart:collection": "../../../../third_party/dart/sdk/lib/collection/collection.dart"
  "dart:convert": "../../../../third_party/dart/sdk/lib/convert/convert.dart"
  "dart:core": "../../../../third_party/dart/sdk/lib/core/core.dart"
  "dart:developer": "../../../../third_party/dart/sdk/lib/developer/developer.dart"
  "dart:ffi": "../../../../third_party/dart/sdk/lib/ffi/ffi.dart"
  "dart:html": "../../../../third_party/dart/sdk/lib/html/html_dart2js.dart"
  "dart:io": "../../../../third_party/dart/sdk/lib/io/io.dart"
  "dart:isolate": "../../../../third_party/dart/sdk/lib/isolate/isolate.dart"
  "dart:js": "../../../../third_party/dart/sdk/lib/js/js.dart"
  "dart:js_interop": "../../../../third_party/dart/sdk/lib/js_interop/js_interop.dart"
  "dart:js_interop_unsafe": "../../../../third_party/dart/sdk/lib/js_interop_unsafe/js_interop_unsafe.dart"
  "dart:js_util": "../../../../third_party/dart/sdk/lib/js_util/js_util.dart"
  "dart:math": "../../../../third_party/dart/sdk/lib/math/math.dart"
  "dart:typed_data": "../../../../third_party/dart/sdk/lib/typed_data/typed_data.dart"
  "dart:ui": "../../../../lib/ui/ui.dart"
  "dart:ui_web": "../../../../lib/web_ui/lib/ui_web/src/ui_web.dart"

  "dart:_http": "../../../../third_party/dart/sdk/lib/_http/http.dart"
  "dart:_interceptors": "../../../../third_party/dart/sdk/lib/_interceptors/interceptors.dart"
  # The _internal library is needed as some implementations bleed into the
  # public API, e.g. List being Iterable by virtue of implementing
  # EfficientLengthIterable. Not including this library yields analysis errors.
  "dart:_internal": "../../../../third_party/dart/sdk/lib/internal/internal.dart"
  # The _js_annotations library is also needed for the same reasons as _internal.
  "dart:_js_annotations": "../../../../third_party/dart/sdk/lib/js/_js_annotations.dart"
  # The _js_types library is also needed for the same reasons as _internal.
  "dart:_js_types": "../../../../third_party/dart/sdk/lib/_internal/js_shared/lib/js_types.dart"
  "dart:nativewrappers": "../../../../third_party/dart/sdk/lib/html/dartium/nativewrappers.dart"
