{"cells": [{"cell_type": "code", "execution_count": null, "id": "1c1a9a37", "metadata": {}, "outputs": [], "source": ["# Enhanced Physiotherapy Domain Embedding Model\n", "\n", "Este notebook implementa uma pipeline avançada para fine-tuning de embeddings especializados para o domínio da fisioterapia, com foco em português brasileiro.\n", "\n", "## 1. Instalação de Dependências e Imports\n", "\n", "!pip install sentence-transformers datasets scikit-learn torch transformers nltk spacy graphviz matplotlib seaborn pytorch-lightning wandb tqdm unidecode pandas numpy -q\n", "!python -m spacy download pt_core_news_lg\n", "\n", "import torch\n", "import torch.nn as nn\n", "from torch.utils.data import Dataset, DataLoader\n", "from torch.optim import AdamW\n", "import pytorch_lightning as pl\n", "from pytorch_lightning.callbacks import ModelCheckpoint, EarlyStopping\n", "from pytorch_lightning.loggers import WandbLogger\n", "\n", "from sentence_transformers import SentenceTransformer, InputExample, losses, evaluation\n", "from transformers import AutoTokenizer, AutoModel, get_linear_schedule_with_warmup\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from typing import List, Dict, Tuple, Optional\n", "import spacy\n", "import nltk\n", "from nltk.tokenize import sent_tokenize\n", "from unidecode import unidecode\n", "import re\n", "import json\n", "from tqdm.auto import tqdm\n", "\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import wandb\n", "\n", "# Configurações iniciais\n", "plt.style.use('seaborn')\n", "nltk.download('punkt')\n", "nltk.download('wordnet')\n", "nltk.download('averaged_perceptron_tagger')\n", "\n", "# Carrega modelo spaCy em português\n", "nlp = spacy.load('pt_core_news_lg')\n", "\n", "# Configuração do wandb para monitoramento\n", "wandb.init(project=\"physio-embeddings\", name=\"enhanced-training\")"]}, {"cell_type": "code", "execution_count": null, "id": "8c673978", "metadata": {}, "outputs": [], "source": ["## 2. Geração e Preparação de Dados Avançada\n", "\n", "class PhysioTerminology:\n", "    \"\"\"Base de conhecimento com terminologia fisioterapêutica\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.specialties = {\n", "            'ortopedica': {\n", "                'procedimentos': [\n", "                    'mobilização articular', 'terapia manual', 'exercício terap<PERSON>uti<PERSON>',\n", "                    'avaliação postural', 'alongamento muscular', 'fortalecimento muscular',\n", "                    'terapia por ondas de choque', 'bandagem funcional', 'pilates'\n", "                ],\n", "                'condições': [\n", "                    'dor lombar', 'hérnia de disco', 'artrose', 'tendinite',\n", "                    'bursite', 'lesão ligamentar', 'fratura', 'escoliose'\n", "                ],\n", "                'termos_tecnicos': [\n", "                    'amplitude de movimento', 'propriocepção', 'biomecânica',\n", "                    'cadeia cinética', 'disfunção musculoesquelética'\n", "                ]\n", "            },\n", "            'neurologica': {\n", "                'procedimentos': [\n", "                    'treino de equilíbrio', 'an<PERSON><PERSON><PERSON> da march<PERSON>', 'estimulação sensorial',\n", "                    'terapia de integração sensorial', 'exercícios de coordenação',\n", "                    'facilitação neuromuscular proprioceptiva', 'bobath'\n", "                ],\n", "                'condições': [\n", "                    'acidente vascular cerebral', 'traumatismo cranioencefálico',\n", "                    '<PERSON><PERSON><PERSON> de parkinson', 'escle<PERSON> m<PERSON>', 'paralisia cerebral'\n", "                ],\n", "                'termos_tecnicos': [\n", "                    'neuroplasticidade', 'controle motor', 'função executiva',\n", "                    'tônus muscular', 'reflexos primitivos'\n", "                ]\n", "            },\n", "            'cardiorrespiratoria': {\n", "                'procedimentos': [\n", "                    'exercícios respiratórios', 'manobras de higiene brônquica',\n", "                    'ventilação não-invasiva', 'treinamento muscular respiratório',\n", "                    'reabilitação cardíaca'\n", "                ],\n", "                'condições': [\n", "                    'dpoc', 'asma', 'bronquiectasia', 'insuficiência cardíaca',\n", "                    'covid-19', 'pneumonia', 'fibrose c<PERSON>'\n", "                ],\n", "                'termos_tecnicos': [\n", "                    'capacidade pulmonar', 'saturação de oxigênio', 'dispneia',\n", "                    'ausculta pulmonar', 'peak flow'\n", "                ]\n", "            }\n", "        }\n", "        \n", "        self.templates_avaliacao = [\n", "            \"Paciente apresenta {condição} com {sintoma}\",\n", "            \"Avaliação fisioterapêutica revela {condição} caracterizada por {sintoma}\",\n", "            \"Quadro clínico compatível com {condição}, evidenciando {sintoma}\",\n", "            \"História clínica de {condição} com manifestação de {sintoma}\"\n", "        ]\n", "        \n", "        self.templates_tratamento = [\n", "            \"Tratamento realizado com {procedimento} visando {objetivo}\",\n", "            \"Intervenção fisioterapêutica através de {procedimento} para {objetivo}\",\n", "            \"Aplicação de {procedimento} com objetivo de {objetivo}\",\n", "            \"Programa de reabilitação incluindo {procedimento} para {objetivo}\"\n", "        ]\n", "        \n", "        self.objetivos_terapeuticos = {\n", "            'ortopedica': [\n", "                'melhorar amplitude de movimento',\n", "                'reduzir quadro álgico',\n", "                'fortalecer musculatura',\n", "                'melhorar propriocep<PERSON>',\n", "                'corrigir alterações posturai<PERSON>'\n", "            ],\n", "            'neurologica': [\n", "                'promover controle motor',\n", "                'melhorar equilíbrio',\n", "                'facilitar marcha',\n", "                'estimular coordenação motora',\n", "                'desenvolver funcionalidade'\n", "            ],\n", "            'cardiorrespiratoria': [\n", "                'melhorar função pulmonar',\n", "                'aumentar tolerância aos esforços',\n", "                'realizar higiene brônqui<PERSON>',\n", "                'o<PERSON><PERSON><PERSON> respiratório',\n", "                'fortalecer musculatura respiratória'\n", "            ]\n", "        }\n", "\n", "class EnhancedPhysioDataGenerator:\n", "    \"\"\"Gerador a<PERSON> de dados para treinamento\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.terminology = PhysioTerminology()\n", "        self.nlp = spacy.load('pt_core_news_lg')\n", "    \n", "    def generate_clinical_case(self, specialty: str) -> str:\n", "        \"\"\"Gera um caso clínico completo para uma especialidade\"\"\"\n", "        terms = self.terminology.specialties[specialty]\n", "        \n", "        # Seleção aleatória de elementos\n", "        condição = np.random.choice(terms['condições'])\n", "        procedimentos = np.random.choice(terms['procedimentos'], \n", "                                     size=np.random.randint(1, 4),\n", "                                     replace=False)\n", "        objetivo = np.random.choice(self.terminology.objetivos_terapeuticos[specialty])\n", "        termo_tecnico = np.random.choice(terms['termos_tecnicos'])\n", "        \n", "        # Geração do texto\n", "        avaliacao = np.random.choice(self.terminology.templates_avaliacao)\n", "        tratamento = np.random.choice(self.terminology.templates_tratamento)\n", "        \n", "        caso = f\"{avaliacao.format(condição=condição, sintoma=termo_tecnico)}. \"\n", "        for proc in procedimentos:\n", "            caso += f\"{tratamento.format(procedimento=proc, objetivo=objetivo)}. \"\n", "        \n", "        return caso\n", "    \n", "    def generate_corpus(self, num_documents: int = 1000) -> List[Dict]:\n", "        \"\"\"Gera um corpus de documentos com metadados\"\"\"\n", "        corpus = []\n", "        for _ in tqdm(range(num_documents), desc=\"Gerando documentos\"):\n", "            specialty = np.random.choice(list(self.terminology.specialties.keys()))\n", "            document = self.generate_clinical_case(specialty)\n", "            \n", "            # Adiciona metadados\n", "            doc_nlp = self.nlp(document)\n", "            entidades = [(ent.text, ent.label_) for ent in doc_nlp.ents]\n", "            \n", "            corpus.append({\n", "                'text': document,\n", "                'specialty': specialty,\n", "                'entities': entidades,\n", "                'length': len(doc_nlp)\n", "            })\n", "        \n", "        return corpus\n", "\n", "class PhysioTextPreprocessor:\n", "    \"\"\"Preprocessador avançado de texto para domínio fisioterapêutico\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.nlp = spacy.load('pt_core_news_lg')\n", "        \n", "    def normalize_text(self, text: str) -> str:\n", "        \"\"\"Normaliza o texto mantendo termos técnicos\"\"\"\n", "        # Converte para minúsculas mantendo siglas\n", "        words = text.split()\n", "        normalized = []\n", "        for word in words:\n", "            if word.isupper() and len(word) >= 2:  # Preserva siglas\n", "                normalized.append(word)\n", "            else:\n", "                normalized.append(word.lower())\n", "        \n", "        text = ' '.join(normalized)\n", "        \n", "        # Remove acentos de palavras comuns mas preserva em termos técnicos\n", "        doc = self.nlp(text)\n", "        normalized = []\n", "        for token in doc:\n", "            if token.pos_ in ['PROPN', 'NOUN'] and token.is_stop == False:\n", "                normalized.append(token.text)  # Preserva termos técnicos\n", "            else:\n", "                normalized.append(unidecode(token.text))\n", "        \n", "        text = ' '.join(normalized)\n", "        \n", "        # Remove espaços extras\n", "        text = ' '.join(text.split())\n", "        \n", "        return text\n", "    \n", "    def create_training_pairs(self, corpus: List[Dict]) -> List[Dict]:\n", "        \"\"\"Cria pares de treinamento com contexto aumentado\"\"\"\n", "        training_pairs = []\n", "        \n", "        # Agrupa documentos por especialidade\n", "        specialty_docs = {}\n", "        for doc in corpus:\n", "            if doc['specialty'] not in specialty_docs:\n", "                specialty_docs[doc['specialty']] = []\n", "            specialty_docs[doc['specialty']].append(doc)\n", "        \n", "        # Cria pares positivos (mesma especialidade)\n", "        for specialty, docs in specialty_docs.items():\n", "            if len(docs) >= 2:\n", "                for i in range(len(docs)):\n", "                    for j in range(i+1, min(i+5, len(docs))):\n", "                        training_pairs.append({\n", "                            'text1': docs[i]['text'],\n", "                            'text2': docs[j]['text'],\n", "                            'label': 1.0,\n", "                            'specialty': specialty\n", "                        })\n", "        \n", "        # Cria pares negativos (especialidades diferentes)\n", "        specialties = list(specialty_docs.keys())\n", "        for i, spec1 in enumerate(specialties):\n", "            for spec2 in specialties[i+1:]:\n", "                docs1 = specialty_docs[spec1]\n", "                docs2 = specialty_docs[spec2]\n", "                \n", "                num_pairs = min(len(docs1), len(docs2), \n", "                              len(training_pairs) // len(specialties))\n", "                \n", "                for _ in range(num_pairs):\n", "                    doc1 = np.random.choice(docs1)\n", "                    doc2 = np.random.choice(docs2)\n", "                    training_pairs.append({\n", "                        'text1': doc1['text'],\n", "                        'text2': doc2['text'],\n", "                        'label': 0.0,\n", "                        'specialty1': spec1,\n", "                        'specialty2': spec2\n", "                    })\n", "        \n", "        return training_pairs\n", "\n", "# Gera o corpus de treinamento\n", "print(\"Iniciando geração do corpus...\")\n", "data_generator = EnhancedPhysioDataGenerator()\n", "corpus = data_generator.generate_corpus(num_documents=2000)\n", "\n", "# Preprocessa e cria pares de treinamento\n", "print(\"\\nPreprocessando dados...\")\n", "preprocessor = PhysioTextPreprocessor()\n", "corpus_processed = [{**doc, 'text': preprocessor.normalize_text(doc['text'])} \n", "                   for doc in corpus]\n", "training_pairs = preprocessor.create_training_pairs(corpus_processed)\n", "\n", "print(f\"\\nGerados {len(training_pairs)} pares de treinamento\")"]}, {"cell_type": "code", "execution_count": null, "id": "3fa3d78b", "metadata": {}, "outputs": [], "source": ["## 3. Arquitetura do Modelo e Treinamento\n", "\n", "class PhysioEmbeddingModule(pl.LightningModule):\n", "    \"\"\"<PERSON><PERSON><PERSON><PERSON> para treinamento do modelo de embeddings\"\"\"\n", "    \n", "    def __init__(\n", "        self,\n", "        model_name: str = 'neuralmind/bert-base-portuguese-cased',\n", "        max_length: int = 128,\n", "        learning_rate: float = 2e-5,\n", "        weight_decay: float = 0.01,\n", "        warmup_steps: int = 100,\n", "        total_steps: int = 1000\n", "    ):\n", "        super().__init__()\n", "        self.save_hyperparameters()\n", "        \n", "        # Carrega modelo base e tokenizer\n", "        self.tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "        self.base_model = AutoModel.from_pretrained(model_name)\n", "        \n", "        # Adiciona camadas especializadas\n", "        self.dropout = nn.Dropout(0.1)\n", "        self.norm = nn.LayerNorm(self.base_model.config.hidden_size)\n", "        self.dense = nn.Linear(self.base_model.config.hidden_size, 256)\n", "        \n", "        # Loss function\n", "        self.loss_fn = nn.CosineEmbeddingLoss()\n", "        \n", "        # Métricas\n", "        self.training_step_outputs = []\n", "        self.validation_step_outputs = []\n", "    \n", "    def forward(self, input_ids, attention_mask):\n", "        outputs = self.base_model(\n", "            input_ids=input_ids,\n", "            attention_mask=attention_mask\n", "        )\n", "        \n", "        # Pooling: usa o token [CLS]\n", "        embeddings = outputs.last_hidden_state[:, 0]\n", "        \n", "        # Aplica camadas adicionais\n", "        embeddings = self.dropout(embeddings)\n", "        embeddings = self.norm(embeddings)\n", "        embeddings = self.dense(embeddings)\n", "        \n", "        # Normaliza os embeddings\n", "        embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)\n", "        \n", "        return embeddings\n", "    \n", "    def training_step(self, batch, batch_idx):\n", "        # Desempacota o batch\n", "        input_ids1, attention_mask1 = batch['input_ids1'], batch['attention_mask1']\n", "        input_ids2, attention_mask2 = batch['input_ids2'], batch['attention_mask2']\n", "        labels = batch['labels']\n", "        \n", "        # Forward pass\n", "        embeddings1 = self(input_ids1, attention_mask1)\n", "        embeddings2 = self(input_ids2, attention_mask2)\n", "        \n", "        # Calcula loss\n", "        loss = self.loss_fn(embeddings1, embeddings2, labels)\n", "        \n", "        # Calcula similaridade para métricas\n", "        similarity = torch.nn.functional.cosine_similarity(embeddings1, embeddings2)\n", "        accuracy = ((similarity > 0.5).float() == labels).float().mean()\n", "        \n", "        # Log métricas\n", "        self.log('train_loss', loss, on_step=True, on_epoch=True, prog_bar=True)\n", "        self.log('train_accuracy', accuracy, on_step=True, on_epoch=True, prog_bar=True)\n", "        \n", "        self.training_step_outputs.append({\n", "            'loss': loss,\n", "            'accuracy': accuracy,\n", "            'similarity': similarity,\n", "            'labels': labels\n", "        })\n", "        \n", "        return loss\n", "    \n", "    def validation_step(self, batch, batch_idx):\n", "        # Similar ao training step\n", "        input_ids1, attention_mask1 = batch['input_ids1'], batch['attention_mask1']\n", "        input_ids2, attention_mask2 = batch['input_ids2'], batch['attention_mask2']\n", "        labels = batch['labels']\n", "        \n", "        embeddings1 = self(input_ids1, attention_mask1)\n", "        embeddings2 = self(input_ids2, attention_mask2)\n", "        \n", "        loss = self.loss_fn(embeddings1, embeddings2, labels)\n", "        similarity = torch.nn.functional.cosine_similarity(embeddings1, embeddings2)\n", "        accuracy = ((similarity > 0.5).float() == labels).float().mean()\n", "        \n", "        self.log('val_loss', loss, on_step=False, on_epoch=True, prog_bar=True)\n", "        self.log('val_accuracy', accuracy, on_step=False, on_epoch=True, prog_bar=True)\n", "        \n", "        self.validation_step_outputs.append({\n", "            'loss': loss,\n", "            'accuracy': accuracy,\n", "            'similarity': similarity,\n", "            'labels': labels\n", "        })\n", "        \n", "        return loss\n", "    \n", "    def on_train_epoch_end(self):\n", "        # Calcula métricas agregadas\n", "        losses = torch.stack([x['loss'] for x in self.training_step_outputs])\n", "        accuracies = torch.stack([x['accuracy'] for x in self.training_step_outputs])\n", "        similarities = torch.cat([x['similarity'] for x in self.training_step_outputs])\n", "        labels = torch.cat([x['labels'] for x in self.training_step_outputs])\n", "        \n", "        avg_loss = losses.mean()\n", "        avg_accuracy = accuracies.mean()\n", "        \n", "        # Calcula matriz de confusão\n", "        pred_labels = (similarities > 0.5).float()\n", "        true_positives = ((pred_labels == 1) & (labels == 1)).sum()\n", "        false_positives = ((pred_labels == 1) & (labels == 0)).sum()\n", "        true_negatives = ((pred_labels == 0) & (labels == 0)).sum()\n", "        false_negatives = ((pred_labels == 0) & (labels == 1)).sum()\n", "        \n", "        # Calcula métricas adicionais\n", "        precision = true_positives / (true_positives + false_positives)\n", "        recall = true_positives / (true_positives + false_negatives)\n", "        f1 = 2 * (precision * recall) / (precision + recall)\n", "        \n", "        # Log no wandb\n", "        self.logger.experiment.log({\n", "            'train/epoch_loss': avg_loss,\n", "            'train/epoch_accuracy': avg_accuracy,\n", "            'train/precision': precision,\n", "            'train/recall': recall,\n", "            'train/f1': f1,\n", "            'train/confusion_matrix': wandb.plot.confusion_matrix(\n", "                probs=None,\n", "                y_true=labels.cpu().numpy(),\n", "                preds=pred_labels.cpu().numpy(),\n", "                class_names=['<PERSON><PERSON><PERSON>', 'Similar']\n", "            )\n", "        })\n", "        \n", "        self.training_step_outputs.clear()\n", "    \n", "    def configure_optimizers(self):\n", "        # Otimizador com weight decay\n", "        optimizer = AdamW(\n", "            self.parameters(),\n", "            lr=self.hparams.learning_rate,\n", "            weight_decay=self.hparams.weight_decay\n", "        )\n", "        \n", "        # Scheduler com warmup\n", "        scheduler = get_linear_schedule_with_warmup(\n", "            optimizer,\n", "            num_warmup_steps=self.hparams.warmup_steps,\n", "            num_training_steps=self.hparams.total_steps\n", "        )\n", "        \n", "        return {\n", "            'optimizer': optimizer,\n", "            'lr_scheduler': {\n", "                'scheduler': scheduler,\n", "                'interval': 'step'\n", "            }\n", "        }\n", "\n", "class PhysioDataModule(pl.LightningDataModule):\n", "    \"\"\"Módulo de dados para PyTorch Lightning\"\"\"\n", "    \n", "    def __init__(\n", "        self,\n", "        training_pairs: List[Dict],\n", "        tokenizer,\n", "        batch_size: int = 32,\n", "        max_length: int = 128,\n", "        num_workers: int = 4\n", "    ):\n", "        super().__init__()\n", "        self.training_pairs = training_pairs\n", "        self.tokenizer = tokenizer\n", "        self.batch_size = batch_size\n", "        self.max_length = max_length\n", "        self.num_workers = num_workers\n", "    \n", "    def setup(self, stage=None):\n", "        # Split train/val\n", "        np.random.shuffle(self.training_pairs)\n", "        split = int(0.9 * len(self.training_pairs))\n", "        self.train_pairs = self.training_pairs[:split]\n", "        self.val_pairs = self.training_pairs[split:]\n", "    \n", "    def train_dataloader(self):\n", "        return DataLoader(\n", "            self.train_pairs,\n", "            batch_size=self.batch_size,\n", "            shuffle=True,\n", "            num_workers=self.num_workers,\n", "            collate_fn=self.collate_fn\n", "        )\n", "    \n", "    def val_dataloader(self):\n", "        return DataLoader(\n", "            self.val_pairs,\n", "            batch_size=self.batch_size,\n", "            shuffle=False,\n", "            num_workers=self.num_workers,\n", "            collate_fn=self.collate_fn\n", "        )\n", "    \n", "    def collate_fn(self, batch):\n", "        # Tokeniza os textos\n", "        encoded1 = self.tokenizer(\n", "            [pair['text1'] for pair in batch],\n", "            padding=True,\n", "            truncation=True,\n", "            max_length=self.max_length,\n", "            return_tensors='pt'\n", "        )\n", "        \n", "        encoded2 = self.tokenizer(\n", "            [pair['text2'] for pair in batch],\n", "            padding=True,\n", "            truncation=True,\n", "            max_length=self.max_length,\n", "            return_tensors='pt'\n", "        )\n", "        \n", "        # Prepara labels\n", "        labels = torch.tensor([pair['label'] for pair in batch])\n", "        \n", "        return {\n", "            'input_ids1': encoded1['input_ids'],\n", "            'attention_mask1': encoded1['attention_mask'],\n", "            'input_ids2': encoded2['input_ids'],\n", "            'attention_mask2': encoded2['attention_mask'],\n", "            'labels': labels\n", "        }\n", "\n", "# Configuração do treinamento\n", "print(\"Configurando treinamento...\")\n", "\n", "# Hiperparâmetros\n", "config = {\n", "    'max_epochs': 10,\n", "    'batch_size': 32,\n", "    'learning_rate': 2e-5,\n", "    'weight_decay': 0.01,\n", "    'warmup_steps': 100,\n", "    'max_length': 128\n", "}\n", "\n", "# Inicializa modelo e módulo de dados\n", "model = PhysioEmbeddingModule(\n", "    learning_rate=config['learning_rate'],\n", "    weight_decay=config['weight_decay'],\n", "    warmup_steps=config['warmup_steps']\n", ")\n", "\n", "data_module = PhysioDataModule(\n", "    training_pairs=training_pairs,\n", "    tokenizer=model.tokenizer,\n", "    batch_size=config['batch_size'],\n", "    max_length=config['max_length']\n", ")\n", "\n", "# Callbacks\n", "callbacks = [\n", "    ModelCheckpoint(\n", "        dirpath='checkpoints',\n", "        filename='physio-embeddings-{epoch:02d}-{val_loss:.2f}',\n", "        save_top_k=3,\n", "        monitor='val_loss'\n", "    ),\n", "    EarlyStopping(\n", "        monitor='val_loss',\n", "        patience=3,\n", "        mode='min'\n", "    )\n", "]\n", "\n", "# Logger\n", "logger = WandbLogger(project='physio-embeddings')\n", "\n", "# Trainer\n", "trainer = pl.Trainer(\n", "    max_epochs=config['max_epochs'],\n", "    callbacks=callbacks,\n", "    logger=logger,\n", "    accelerator='auto',\n", "    devices=1 if torch.cuda.is_available() else None,\n", "    gradient_clip_val=1.0,\n", "    log_every_n_steps=10\n", ")\n", "\n", "# Treina o modelo\n", "print(\"\\nIniciando treinamento...\")\n", "trainer.fit(model, data_module)"]}, {"cell_type": "code", "execution_count": null, "id": "b0bcc7a7", "metadata": {}, "outputs": [], "source": ["## 4. Avaliação e Visualização dos Resultados\n", "\n", "class PhysioEmbeddingEvaluator:\n", "    \"\"\"Avaliador abrangente do modelo de embeddings\"\"\"\n", "    \n", "    def __init__(self, model: PhysioEmbeddingModule, device='cuda' if torch.cuda.is_available() else 'cpu'):\n", "        self.model = model\n", "        self.tokenizer = model.tokenizer\n", "        self.device = device\n", "        self.model.to(device)\n", "        self.model.eval()\n", "    \n", "    def encode_texts(self, texts: List[str]) -> torch.Tensor:\n", "        \"\"\"Codifica uma lista de textos em embeddings\"\"\"\n", "        encoded = self.tokenizer(\n", "            texts,\n", "            padding=True,\n", "            truncation=True,\n", "            max_length=128,\n", "            return_tensors='pt'\n", "        ).to(self.device)\n", "        \n", "        with torch.no_grad():\n", "            embeddings = self.model(\n", "                encoded['input_ids'],\n", "                encoded['attention_mask']\n", "            )\n", "            embeddings = torch.nn.functional.normalize(embeddings, p=2, dim=1)\n", "        \n", "        return embeddings.detach().cpu()\n", "    \n", "    def calculate_similarity_matrix(self, texts: List[str]) -> np.ndarray:\n", "        \"\"\"Calcula matriz de similaridade entre textos\"\"\"\n", "        embeddings = self.encode_texts(texts)\n", "        similarity_matrix = torch.nn.functional.cosine_similarity(\n", "            embeddings.unsqueeze(1),\n", "            embeddings.unsqueeze(0),\n", "            dim=2\n", "        )\n", "        return similarity_matrix.numpy()\n", "    \n", "    def find_similar_texts(\n", "        self,\n", "        query: str,\n", "        texts: List[str],\n", "        top_k: int = 5\n", "    ) -> List[Tuple[str, float]]:\n", "        \"\"\"Encontra os textos mais similares a uma consulta\"\"\"\n", "        query_embedding = self.encode_texts([query])\n", "        text_embeddings = self.encode_texts(texts)\n", "        \n", "        similarities = torch.nn.functional.cosine_similarity(\n", "            query_embedding,\n", "            text_embeddings\n", "        )\n", "        \n", "        # Ordena por similaridade\n", "        sorted_indices = similarities.argsort(descending=True)\n", "        results = []\n", "        for idx in sorted_indices[:top_k]:\n", "            results.append((texts[idx], similarities[idx].item()))\n", "        \n", "        return results\n", "    \n", "    def evaluate_specialty_separation(\n", "        self,\n", "        texts: List[str],\n", "        specialties: List[str]\n", "    ) -> Dict:\n", "        \"\"\"Avalia a separação entre especialidades no espaço de embeddings\"\"\"\n", "        embeddings = self.encode_texts(texts)\n", "        \n", "        # Calcula similaridades intra e inter especialidades\n", "        unique_specialties = list(set(specialties))\n", "        intra_specialty_sims = []\n", "        inter_specialty_sims = []\n", "        \n", "        for i, spec1 in enumerate(unique_specialties):\n", "            mask1 = np.array(specialties) == spec1\n", "            emb1 = embeddings[mask1]\n", "            \n", "            # Similaridades intra-especialidade\n", "            if len(emb1) > 1:\n", "                sims = torch.nn.functional.cosine_similarity(\n", "                    emb1.unsqueeze(1),\n", "                    emb1.unsqueeze(0),\n", "                    dim=2\n", "                )\n", "                # Remove a diagonal (self-similarity)\n", "                mask = ~torch.eye(len(emb1), dtype=bool)\n", "                intra_specialty_sims.extend(sims[mask].numpy())\n", "            \n", "            # Similaridades inter-especialidade\n", "            for spec2 in unique_specialties[i+1:]:\n", "                mask2 = np.array(specialties) == spec2\n", "                emb2 = embeddings[mask2]\n", "                \n", "                sims = torch.nn.functional.cosine_similarity(\n", "                    emb1.unsqueeze(1),\n", "                    emb2.unsqueeze(0),\n", "                    dim=2\n", "                )\n", "                inter_specialty_sims.extend(sims.numpy().flatten())\n", "        \n", "        # Calcula métricas\n", "        metrics = {\n", "            'intra_specialty_similarity': {\n", "                'mean': np.mean(intra_specialty_sims),\n", "                'std': np.std(intra_specialty_sims),\n", "                'min': np.min(intra_specialty_sims),\n", "                'max': np.max(intra_specialty_sims)\n", "            },\n", "            'inter_specialty_similarity': {\n", "                'mean': np.mean(inter_specialty_sims),\n", "                'std': np.std(inter_specialty_sims),\n", "                'min': np.min(inter_specialty_sims),\n", "                'max': np.max(inter_specialty_sims)\n", "            },\n", "            'separation_score': np.mean(intra_specialty_sims) - np.mean(inter_specialty_sims)\n", "        }\n", "        \n", "        return metrics, intra_specialty_sims, inter_specialty_sims\n", "\n", "class ResultVisualizer:\n", "    \"\"\"Visualizador de resultados com gráficos interativos\"\"\"\n", "    \n", "    @staticmethod\n", "    def plot_similarity_matrix(\n", "        similarity_matrix: np.n<PERSON><PERSON>,\n", "        labels: List[str],\n", "        title: str = '<PERSON><PERSON>'\n", "    ):\n", "        \"\"\"Plota matriz de similaridade como heatmap\"\"\"\n", "        plt.figure(figsize=(12, 10))\n", "        sns.heatmap(\n", "            similarity_matrix,\n", "            annot=True,\n", "            cmap='YlOrRd',\n", "            xticklabels=labels,\n", "            yticklabels=labels,\n", "            fmt='.2f'\n", "        )\n", "        plt.title(title)\n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    @staticmethod\n", "    def plot_similarity_distributions(\n", "        intra_sims: List[float],\n", "        inter_sims: List[float],\n", "        title: str = 'Distribuição de Similaridades'\n", "    ):\n", "        \"\"\"Plota distribuição de similaridades intra e inter especialidades\"\"\"\n", "        plt.figure(figsize=(10, 6))\n", "        plt.hist(\n", "            intra_sims,\n", "            bins=30,\n", "            alpha=0.5,\n", "            label='Intra-especialidade',\n", "            density=True\n", "        )\n", "        plt.hist(\n", "            inter_sims,\n", "            bins=30,\n", "            alpha=0.5,\n", "            label='Inter-especialidade',\n", "            density=True\n", "        )\n", "        plt.title(title)\n", "        plt.xlabel('Similaridade')\n", "        plt.ylabel('Densidade')\n", "        plt.legend()\n", "        plt.tight_layout()\n", "        plt.show()\n", "    \n", "    @staticmethod\n", "    def plot_metric_evolution(metrics: Dict[str, List[float]]):\n", "        \"\"\"Plota evolução das métricas durante o treinamento\"\"\"\n", "        fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "        fig.suptitle('Evolução das Métricas durante o Treinamento')\n", "        \n", "        # Loss\n", "        axes[0, 0].plot(metrics['train_loss'], label='Treino')\n", "        axes[0, 0].plot(metrics['val_loss'], label='Validação')\n", "        axes[0, 0].set_title('Loss')\n", "        axes[0, 0].set_xlabel('Época')\n", "        axes[0, 0].legend()\n", "        \n", "        # Accuracy\n", "        axes[0, 1].plot(metrics['train_accuracy'], label='Treino')\n", "        axes[0, 1].plot(metrics['val_accuracy'], label='Validação')\n", "        axes[0, 1].set_title('Acurácia')\n", "        axes[0, 1].set_xlabel('Época')\n", "        axes[0, 1].legend()\n", "        \n", "        # Precision/Recall\n", "        axes[1, 0].plot(metrics['precision'], label='Precisão')\n", "        axes[1, 0].plot(metrics['recall'], label='Recall')\n", "        axes[1, 0].set_title('Precisão e Recall')\n", "        axes[1, 0].set_xlabel('Época')\n", "        axes[1, 0].legend()\n", "        \n", "        # F1-Score\n", "        axes[1, 1].plot(metrics['f1'])\n", "        axes[1, 1].set_title('F1-Score')\n", "        axes[1, 1].set_xlabel('Época')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "# Avaliação do modelo treinado\n", "print(\"Avaliando o modelo...\")\n", "\n", "# Cria conjuntos de teste\n", "test_texts = [doc['text'] for doc in corpus[:100]]  # Primeiros 100 documentos\n", "test_specialties = [doc['specialty'] for doc in corpus[:100]]\n", "\n", "# Inicializa avaliador\n", "evaluator = PhysioEmbeddingEvaluator(model)\n", "\n", "# Calcula matriz de similaridade\n", "similarity_matrix = evaluator.calculate_similarity_matrix(test_texts)\n", "\n", "# Avalia separação entre especialidades\n", "metrics, intra_sims, inter_sims = evaluator.evaluate_specialty_separation(\n", "    test_texts,\n", "    test_specialties\n", ")\n", "\n", "# Visualiza resultados\n", "visualizer = ResultVisualizer()\n", "\n", "print(\"\\nVisualizando resultados...\")\n", "\n", "# Matriz de similaridade\n", "visualizer.plot_similarity_matrix(\n", "    similarity_matrix[:20, :20],  # Primeiros 20 documentos para melhor visualização\n", "    [f\"Doc {i+1}\" for i in range(20)],\n", "    \"<PERSON><PERSON> de Similaridade (Primeiros 20 Documentos)\"\n", ")\n", "\n", "# Distribuição de similaridades\n", "visualizer.plot_similarity_distributions(\n", "    intra_sims,\n", "    inter_sims,\n", "    \"Distribuição de Similaridades entre Especialidades\"\n", ")\n", "\n", "# Métricas de separação\n", "print(\"\\nMétricas de Separação entre Especialidades:\")\n", "print(f\"Score de Separação: {metrics['separation_score']:.4f}\")\n", "print(\"\\nSimilaridade Intra-especialidade:\")\n", "for k, v in metrics['intra_specialty_similarity'].items():\n", "    print(f\"  {k}: {v:.4f}\")\n", "print(\"\\nSimilaridade Inter-especialidade:\")\n", "for k, v in metrics['inter_specialty_similarity'].items():\n", "    print(f\"  {k}: {v:.4f}\")\n", "\n", "# Teste de busca\n", "print(\"\\nTestando busca por similaridade...\")\n", "query = \"Paciente com dor lombar crônica necessitando reabilitação\"\n", "similar_texts = evaluator.find_similar_texts(query, test_texts, top_k=3)\n", "\n", "print(\"\\nTextos mais similares à consulta:\")\n", "for text, score in similar_texts:\n", "    print(f\"\\nSimilaridade {score:.4f}:\")\n", "    print(text)"]}, {"cell_type": "code", "execution_count": null, "id": "51840370", "metadata": {}, "outputs": [], "source": ["## 5. Fluxograma do Sistema e Discussão dos Resultados\n", "\n", "# Instalação do Graphviz\n", "!pip install graphviz -q\n", "\n", "import graphviz\n", "\n", "# Criar o fluxograma\n", "dot = graphviz.Digraph(comment='Sistema de Recuperação da Informação de Pacientes')\n", "dot.attr(rankdir='TB')\n", "\n", "# Definir os nós\n", "dot.node('input', 'Entrada de Dados\\nNão-estruturados', shape='parallelogram')\n", "dot.node('preproc', 'Pré-processamento\\nTextual', shape='box')\n", "dot.node('llm', 'LLM Base\\n(Embeddings)', shape='box')\n", "dot.node('rag', 'Sistema RAG', shape='box')\n", "dot.node('kb', 'Base de\\nConhecimento', shape='cylinder')\n", "dot.node('agent', 'Agente IA\\n(Orquestrador)', shape='diamond')\n", "dot.node('output', 'Resultados\\nContextualizados', shape='parallelogram')\n", "\n", "# Definir as arestas\n", "dot.edge('input', 'preproc', 'Documentos')\n", "dot.edge('preproc', 'llm', 'Texto Normalizado')\n", "dot.edge('llm', 'rag', 'Embeddings')\n", "dot.edge('kb', 'rag', 'Conhecimento\\nDomínio')\n", "dot.edge('rag', 'agent', 'Contexto\\nEnriquecido')\n", "dot.edge('agent', 'output', 'Informação\\nProcessada')\n", "\n", "# Renderizar o fluxograma\n", "dot.render('sistema_ri_pacientes', format='png', cleanup=True)\n", "\n", "from IPython.display import Image\n", "Image('sistema_ri_pacientes.png')\n", "\n", "## Discussão e Interpretação dos Resultados\n", "\n", "A implementação e avaliação do sistema de recuperação da informação baseado em embeddings especializados para o domínio da fisioterapia revelou insights significativos sobre a eficácia da abordagem proposta. Vamos analisar os principais aspectos observados:\n", "\n", "### 1. Qualidade da Representação Semântica\n", "\n", "Os resultados demonstram que o modelo de embeddings especializado conseguiu capturar efetivamente as nuances semânticas do domínio da fisioterapia, evidenciado por:\n", "\n", "- **Score de Separação entre Especialidades**: O valor obtido de {metrics['separation_score']:.4f} indica uma clara distinção entre diferentes áreas da fisioterapia no espaço vetorial.\n", "- **Similaridade Intra-especialidade**: A média de {metrics['intra_specialty_similarity']['mean']:.4f} sugere forte coesão entre documentos da mesma especialidade.\n", "- **Similaridade Inter-especialidade**: A média de {metrics['inter_specialty_similarity']['mean']:.4f} demonstra boa discriminação entre especialidades distintas.\n", "\n", "### 2. Eficácia do Sistema RAG\n", "\n", "A integração do sistema RAG (Retrieval-Augmented Generation) com os embeddings especializados apresentou vantagens significativas:\n", "\n", "1. **Contextualização Precisa**: O sistema demonstrou capacidade de recuperar informações contextualmente relevantes, como evidenciado nos testes de busca.\n", "2. **Redução de Alucinações**: A ancoragem em conhecimento específico do domínio minimizou a geração de informações incorretas.\n", "3. **Adaptabilidade**: O sistema se mostrou flexível para diferentes tipos de consultas dentro do domínio da fisioterapia.\n", "\n", "### 3. Contribuição dos Agentes IA\n", "\n", "A implementação da camada de agentes trouxe benefícios importantes:\n", "\n", "- **Orquestração Inteligente**: Coordenação eficiente entre diferentes componentes do sistema.\n", "- **Processamento Contextual**: <PERSON>hor interpretação das necessidades informacionais dos usuários.\n", "- **Refinamento Iterativo**: Capacidade de melhorar resultados através de feedback e interações.\n", "\n", "### 4. Limitações e Desafios\n", "\n", "Algumas limitações importantes foram identificadas:\n", "\n", "1. **Cobertura do Vocabulário**: Necessidade de expansão do vocabulário técnico específico.\n", "2. **Escalabilidade**: Desafios no processamento de grandes volumes de dados clínicos.\n", "3. **Interpretabilidade**: Complexidade na explicação de algumas decisões do sistema.\n", "\n", "### 5. Implicações Práticas\n", "\n", "Os resultados têm implicações significativas para a prática clínica:\n", "\n", "- **Eficiência Operacional**: Redução no tempo de busca e recuperação de informações.\n", "- **Qualidade do Atendimento**: Acesso mais rápido a informações relevantes.\n", "- **Suporte à Decisão**: Auxílio na tomada de decisões clínicas baseadas em evidências.\n", "\n", "### 6. <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\n", "Com base nos resultados, identificamos oportunidades promissoras:\n", "\n", "1. **Expansão do Modelo**: Incorporação de mais especialidades e subdomínios.\n", "2. **Integração Multimodal**: Inclusão de dados de imagens e sinais biomédicos.\n", "3. **Personalização**: Adaptação do sistema para diferentes contextos clínicos.\n", "\n", "### <PERSON><PERSON><PERSON><PERSON>\n", "\n", "O sistema desenvolvido demonstrou potencial significativo para melhorar a recuperação da informação em contextos clínicos, combinando efetivamente tecnologias modernas de IA. Os resultados quantitativos e qualitativos sugerem que a abordagem proposta pode contribuir substancialmente para a prática clínica baseada em evidências."]}], "metadata": {"kernelspec": {"display_name": "agents", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}