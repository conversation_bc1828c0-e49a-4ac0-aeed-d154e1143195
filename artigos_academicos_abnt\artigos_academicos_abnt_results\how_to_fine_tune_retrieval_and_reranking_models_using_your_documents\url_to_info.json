{"http://arxiv.org/abs/2312.16159v1": {"url": "http://arxiv.org/abs/2312.16159v1", "title": "Zero-Shot Cross-Lingual Reranking with Large Language Models for Low-Resource Languages", "description": "Large language models (LLMs) have shown impressive zero-shot capabilities in various document reranking tasks. Despite their successful implementations, there is still a gap in existing literature on their effectiveness in low-resource languages. To address this gap, we investigate how LLMs function as rerankers in cross-lingual information retrieval (CLIR) systems for African languages. Our implementation covers English and four African languages (Hausa, Somali, Swahili, and Yoruba) and we examine cross-lingual reranking with queries in English and passages in the African languages. Additionally, we analyze and compare the effectiveness of monolingual reranking using both query and document translations. We also evaluate the effectiveness of LLMs when leveraging their own generated translations. To get a grasp of the effectiveness of multiple LLMs, our study focuses on the proprietary models RankGPT-4 and RankGPT-3.5, along with the open-source model, RankZephyr. While reranking re...", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "published": "2023-12-26", "pdf_url": "http://arxiv.org/pdf/2312.16159v1", "categories": ["cs.IR", "cs.CL"], "source": "arxiv"}, "https://huggingface.co/blog/sdiazlor/fine-tune-modernbert-for-rag-with-synthetic-data": {"title": "Fine-tune ModernBERT for RAG with Synthetic Data - Hugging Face", "url": "https://huggingface.co/blog/sdiazlor/fine-tune-modernbert-for-rag-with-synthetic-data", "summary": "This blog post will showcase how to fine-tune retrieval and reranking models using your documents. Using these documents, we can create synthetic training data representing your domain. ... from generating synthetic data for RAG on our custom use case to fine-tuning the models for retrieval and reranking, and finally creating the full pipeline.", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}, "http://arxiv.org/abs/2006.05244v1": {"url": "http://arxiv.org/abs/2006.05244v1", "title": "Knowledge-Aided Open-Domain Question Answering", "description": "Open-domain question answering (QA) aims to find the answer to a question from a large collection of documents.Though many models for single-document machine comprehension have achieved strong performance, there is still much room for improving open-domain QA systems since document retrieval and answer reranking are still unsatisfactory. Golden documents that contain the correct answers may not be correctly scored by the retrieval component, and the correct answers that have been extracted may be wrongly ranked after other candidate answers by the reranking component. One of the reasons is derived from the independent principle in which each candidate document (or answer) is scored independently without considering its relationship to other documents (or answers). In this work, we propose a knowledge-aided open-domain QA (KAQA) method which targets at improving relevant document retrieval and candidate answer reranking by considering the relationship between a question and the docum...", "authors": ["<PERSON><PERSON><PERSON>", "Zhou<PERSON> Shi", "<PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2020-06-09", "pdf_url": "http://arxiv.org/pdf/2006.05244v1", "categories": ["cs.CL"], "source": "arxiv"}, "https://undercodenews.com/fine-tuning-modernbert-for-rag-with-synthetic-data-a-step-by-step-guide/": {"title": "Fine-Tuning ModernBERT for RAG with Synthetic Data: A Step-by-Step ...", "url": "https://undercodenews.com/fine-tuning-modernbert-for-rag-with-synthetic-data-a-step-by-step-guide/", "summary": "This article explores how to fine-tune retrieval and reranking models using synthetic data generated from your own documents. We'll walk through the process of creating synthetic datasets, fine-tuning ModernBERT models, and building a RAG pipeline tailored to a specific use case: answering questions about human and civil rights documentation.", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}, "http://arxiv.org/abs/2405.18414v1": {"url": "http://arxiv.org/abs/2405.18414v1", "title": "Don't Forget to Connect! Improving RAG with Graph-based Reranking", "description": "Retrieval Augmented Generation (RAG) has greatly improved the performance of Large Language Model (LLM) responses by grounding generation with context from existing documents. These systems work well when documents are clearly relevant to a question context. But what about when a document has partial information, or less obvious connections to the context? And how should we reason about connections between documents? In this work, we seek to answer these two core questions about RAG generation. We introduce G-RAG, a reranker based on graph neural networks (GNNs) between the retriever and reader in RAG. Our method combines both connections between documents and semantic information (via Abstract Meaning Representation graphs) to provide a context-informed ranker for RAG. G-RAG outperforms state-of-the-art approaches while having smaller computational footprint. Additionally, we assess the performance of PaLM 2 as a reranker and find it to significantly underperform G-RAG. This result...", "authors": ["<PERSON><PERSON><PERSON>", "Bahare <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "published": "2024-05-28", "pdf_url": "http://arxiv.org/pdf/2405.18414v1", "categories": ["cs.CL", "cs.AI", "cs.LG", "cs.SI"], "source": "arxiv"}, "https://medium.com/rahasak/optimizing-rag-supervised-embeddings-reranking-with-your-data-with-llamaindex-88344ff89da7": {"title": "Optimizing RAG, Fine-tuning Embedding and Reranking models with your ...", "url": "https://medium.com/rahasak/optimizing-rag-supervised-embeddings-reranking-with-your-data-with-llamaindex-88344ff89da7", "summary": "1. Introduction. This article will describe a cool trick you can use to improve retrieval performance in your RAG pipelines. That is fine-tuning the embedding model (for embedding) and the cross ...", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}, "http://arxiv.org/abs/2306.17322v1": {"url": "http://arxiv.org/abs/2306.17322v1", "title": "Citations as Queries: Source Attribution Using Language Models as Rerankers", "description": "This paper explores new methods for locating the sources used to write a text, by fine-tuning a variety of language models to rerank candidate sources. After retrieving candidates sources using a baseline BM25 retrieval model, a variety of reranking methods are tested to see how effective they are at the task of source attribution. We conduct experiments on two datasets, English Wikipedia and medieval Arabic historical writing, and employ a variety of retrieval and generation based reranking models. In particular, we seek to understand how the degree of supervision required affects the performance of various reranking models. We find that semisupervised methods can be nearly as effective as fully supervised methods while avoiding potentially costly span-level annotation of the target and source documents.", "authors": ["<PERSON>", "<PERSON>"], "published": "2023-06-29", "pdf_url": "http://arxiv.org/pdf/2306.17322v1", "categories": ["cs.CL"], "source": "arxiv"}, "https://colab.research.google.com/github/argilla-io/argilla/blob/v1.28.0/docs/_source/tutorials_and_integrations/tutorials/feedback/fine-tuning-sentencesimilarity-rag.ipynb": {"title": "Improving RAG by Optimizing Retrieval and Reranking Models", "url": "https://colab.research.google.com/github/argilla-io/argilla/blob/v1.28.0/docs/_source/tutorials_and_integrations/tutorials/feedback/fine-tuning-sentencesimilarity-rag.ipynb", "summary": "In this tutorial, we will show how to improve a RAG (Retrieval Augmented Generation) model by optimizing the retrieval and reranking models. For this purpose, we will use the ArgillaTrainer to fine-tune a bi-encoder and cross-encoder on two datasets with our own data. The steps are as follows: 📝 Set up a QA pipeline with RAG using Haystack", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}, "http://arxiv.org/abs/2502.11116v1": {"url": "http://arxiv.org/abs/2502.11116v1", "title": "Gumbel Reranking: Differentiable End-to-End Reranker Optimization", "description": "RAG systems rely on rerankers to identify relevant documents. However, fine-tuning these models remains challenging due to the scarcity of annotated query-document pairs. Existing distillation-based approaches suffer from training-inference misalignment and fail to capture interdependencies among candidate documents. To overcome these limitations, we reframe the reranking process as an attention-mask problem and propose Gumbel Reranking, an end-to-end training framework for rerankers aimed at minimizing the training-inference gap. In our approach, reranker optimization is reformulated as learning a stochastic, document-wise Top-$k$ attention mask using the Gumbel Trick and Relaxed Top-$k$ Sampling. This formulation enables end-to-end optimization by minimizing the overall language loss. Experiments across various settings consistently demonstrate performance gains, including a 10.4\\% improvement in recall on HotpotQA for distinguishing indirectly relevant documents.", "authors": ["<PERSON><PERSON>", "Zhiyuan Ma", "<PERSON><PERSON><PERSON>", "Changhua Meng", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2025-02-16", "pdf_url": "http://arxiv.org/pdf/2502.11116v1", "categories": ["cs.CL", "cs.IR"], "source": "arxiv"}, "https://cloud.google.com/vertex-ai/generative-ai/docs/retrieval-and-ranking": {"title": "Retrieval and ranking | Generative AI | Google Cloud", "url": "https://cloud.google.com/vertex-ai/generative-ai/docs/retrieval-and-ranking", "summary": "Fine-tune Gemini using custom settings for advanced use cases; Fine-tune Generative AI models with Vertex AI Supervised Fine-tuning; ... Post-retrieval reranking is a technique that enhances the relevance of retrieval results. ... Rank service reranker is based on the rank API that takes a list of documents and reranks those documents based on ...", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}, "http://arxiv.org/abs/2504.05731v1": {"url": "http://arxiv.org/abs/2504.05731v1", "title": "Retrieval Augmented Generation with Collaborative Filtering for Personalized Text Generation", "description": "Recently, the personalization of Large Language Models (LLMs) to generate content that aligns with individual user preferences has garnered widespread attention. Personalized Retrieval-Augmented Generation (RAG), which retrieves relevant documents from the user's history to reflect their preferences and enhance LLM generation, is one commonly used approach for personalization. However, existing personalized RAG methods do not consider that the histories of similar users can also assist in personalized generation for the current user, meaning that collaborative information between users can also benefit personalized generation. Inspired by the application of collaborative filtering in recommender systems, we propose a method called CFRAG, which adapts Collaborative Filtering to RAG for personalized text generation. However, this presents two challenges: (1)~how to incorporate collaborative information without explicit user similarity labels? (2)~how to retrieve documents that support...", "authors": ["<PERSON>g <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "published": "2025-04-08", "pdf_url": "http://arxiv.org/pdf/2504.05731v1", "categories": ["cs.IR", "cs.CL"], "source": "arxiv"}, "https://medium.com/data-reply-it-datatech/from-good-to-great-using-reranking-models-to-perfect-your-rags-a5e73a4d2815": {"title": "From Good to Great: Using Reranking Models to Perfect Your RAGs", "url": "https://medium.com/data-reply-it-datatech/from-good-to-great-using-reranking-models-to-perfect-your-rags-a5e73a4d2815", "summary": "Whether you're fine-tuning for accuracy, completeness, or user satisfaction, reranking models offer a way to ensure your RAG system performs according to expectations. The limitation of embeddings", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}, "http://arxiv.org/abs/2501.09186v1": {"url": "http://arxiv.org/abs/2501.09186v1", "title": "Guiding Retrieval using LLM-based Listwise Rankers", "description": "Large Language Models (LLMs) have shown strong promise as rerankers, especially in ``listwise'' settings where an LLM is prompted to rerank several search results at once. However, this ``cascading'' retrieve-and-rerank approach is limited by the bounded recall problem: relevant documents not retrieved initially are permanently excluded from the final ranking. Adaptive retrieval techniques address this problem, but do not work with listwise rerankers because they assume a document's score is computed independently from other documents. In this paper, we propose an adaptation of an existing adaptive retrieval method that supports the listwise setting and helps guide the retrieval process itself (thereby overcoming the bounded recall problem for LLM rerankers). Specifically, our proposed algorithm merges results both from the initial ranking and feedback documents provided by the most relevant documents seen up to that point. Through extensive experiments across diverse LLM rerankers,...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "published": "2025-01-15", "pdf_url": "http://arxiv.org/pdf/2501.09186v1", "categories": ["cs.IR", "cs.AI"], "source": "arxiv"}, "https://medium.com/@sahin.samia/what-is-reranking-in-retrieval-augmented-generation-rag-ee3dd93540ee": {"title": "What is Reranking in Retrieval-Augmented Generation (RAG)? - Medium", "url": "https://medium.com/@sahin.samia/what-is-reranking-in-retrieval-augmented-generation-rag-ee3dd93540ee", "summary": "In RAG systems, reranking is a two-step process following initial retrieval, where the goal is to filter and prioritize documents so that the Large Language Model (LLM) generates responses based ...", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}, "http://arxiv.org/abs/2402.02764v1": {"url": "http://arxiv.org/abs/2402.02764v1", "title": "List-aware Reranking-Truncation Joint Model for Search and Retrieval-augmented Generation", "description": "The results of information retrieval (IR) are usually presented in the form of a ranked list of candidate documents, such as web search for humans and retrieval-augmented generation for large language models (LLMs). List-aware retrieval aims to capture the list-level contextual features to return a better list, mainly including reranking and truncation. Reranking finely re-scores the documents in the list. Truncation dynamically determines the cut-off point of the ranked list to achieve the trade-off between overall relevance and avoiding misinformation from irrelevant documents. Previous studies treat them as two separate tasks and model them separately. However, the separation is not optimal. First, it is hard to share the contextual information of the ranking list between the two tasks. Second, the separate pipeline usually meets the error accumulation problem, where the small error from the reranking stage can largely affect the truncation stage. To solve these problems, we prop...", "authors": ["Shicheng Xu", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "published": "2024-02-05", "pdf_url": "http://arxiv.org/pdf/2402.02764v1", "categories": ["cs.IR", "cs.AI", "cs.CL"], "source": "arxiv"}, "https://www.maihem.com/articles/10-tips-to-improve-your-rag-system": {"title": "10 Tips to Improve Your RAG System - maihem.com", "url": "https://www.maihem.com/articles/10-tips-to-improve-your-rag-system", "summary": "6. Fine-tune your Embedding Model and Reranker. To maximize retrieval quality, consider fine-tuning your embedding model, reranker, or both on domain-specific data. Tailoring the retriever to your unique data helps it understand content and language patterns specific to your corpus, thereby improving retrieval precision.", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}, "http://arxiv.org/abs/2410.05168v3": {"url": "http://arxiv.org/abs/2410.05168v3", "title": "ReasoningRank: Teaching Student Models to Rank through Reasoning-Based Knowledge Distillation", "description": "Reranking documents based on their relevance to a given query is a critical task in information retrieval. Traditional reranking methods often lack transparency and rely on proprietary models, hindering reproducibility and interpretability. We propose Reason-to-Rank (R2R), a novel open-source reranking approach that enhances transparency by generating two types of reasoning: direct relevance reasoning, which explains how a document addresses the query, and comparison reasoning, which justifies the relevance of one document over another. We leverage large language models (LLMs) as teacher models to generate these explanations and distill this knowledge into smaller, openly available student models. Our student models are trained to generate meaningful reasoning and rerank documents, achieving competitive performance across multiple datasets, including MSMARCO and BRIGHT. Experiments demonstrate that R2R not only improves reranking accuracy but also provides valuable insights into the...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Daqing He"], "published": "2024-10-07", "pdf_url": "http://arxiv.org/pdf/2410.05168v3", "categories": ["cs.CL"], "source": "arxiv"}, "https://blog.gopenai.com/fine-tuning-re-ranking-models-a-beginners-guide-066b4b9c3ecf": {"title": "Fine-tune Re-ranking Models : A Beginner's Guide", "url": "https://blog.gopenai.com/fine-tuning-re-ranking-models-a-beginners-guide-066b4b9c3ecf", "summary": "Generate Synthetic Data for Fine-tuning. When fine-tuning reranking models, having high-quality training data is crucial. If you have access to real data from your deployed solution, this is the best option. Real data reflects the actual queries and documents your system handles, ensuring the fine-tuned model aligns with your specific use case.", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}, "http://arxiv.org/abs/2305.02156v1": {"url": "http://arxiv.org/abs/2305.02156v1", "title": "Zero-Shot Listwise Document Reranking with a Large Language Model", "description": "Supervised ranking methods based on bi-encoder or cross-encoder architectures have shown success in multi-stage text ranking tasks, but they require large amounts of relevance judgments as training data. In this work, we propose Listwise Reranker with a Large Language Model (LRL), which achieves strong reranking effectiveness without using any task-specific training data. Different from the existing pointwise ranking methods, where documents are scored independently and ranked according to the scores, LRL directly generates a reordered list of document identifiers given the candidate documents. Experiments on three TREC web search datasets demonstrate that LRL not only outperforms zero-shot pointwise methods when reranking first-stage retrieval results, but can also act as a final-stage reranker to improve the top-ranked results of a pointwise method for improved efficiency. Additionally, we apply our approach to subsets of MIRACL, a recent multilingual retrieval dataset, with resul...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "published": "2023-05-03", "pdf_url": "http://arxiv.org/pdf/2305.02156v1", "categories": ["cs.IR", "cs.CL"], "source": "arxiv"}, "https://www.databricks.com/blog/improving-retrieval-and-rag-embedding-model-finetuning": {"title": "Improving Retrieval and RAG with Embedding Model Finetuning - Databricks", "url": "https://www.databricks.com/blog/improving-retrieval-and-rag-embedding-model-finetuning", "summary": "Upgrade your LLM: If retrieval is strong but answers are weak, consider using a better generative model. Finetune an LLM: If your domain is unique and you have enough data, finetuning a model like Llama 3 can further boost RAG quality. See Mosaic AI Model Training: Fine-Tune Your LLM on Databricks for Specialized Tasks and Knowledge for more ...", "authors": ["Autor <PERSON><PERSON><PERSON><PERSON><PERSON>"], "published": "Data desconhecida", "source": "web"}}