{"version": "6_1_0", "md.comp.filled-button.container.color": "primary", "md.comp.filled-button.container.elevation": "md.sys.elevation.level0", "md.comp.filled-button.container.height": 40.0, "md.comp.filled-button.container.shadow-color": "shadow", "md.comp.filled-button.container.shape": "md.sys.shape.corner.full", "md.comp.filled-button.disabled.container.color": "onSurface", "md.comp.filled-button.disabled.container.elevation": "md.sys.elevation.level0", "md.comp.filled-button.disabled.container.opacity": 0.12, "md.comp.filled-button.disabled.label-text.color": "onSurface", "md.comp.filled-button.disabled.label-text.opacity": 0.38, "md.comp.filled-button.focus.container.elevation": "md.sys.elevation.level0", "md.comp.filled-button.focus.indicator.color": "secondary", "md.comp.filled-button.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.filled-button.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.filled-button.focus.label-text.color": "onPrimary", "md.comp.filled-button.focus.state-layer.color": "onPrimary", "md.comp.filled-button.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.filled-button.hover.container.elevation": "md.sys.elevation.level1", "md.comp.filled-button.hover.label-text.color": "onPrimary", "md.comp.filled-button.hover.state-layer.color": "onPrimary", "md.comp.filled-button.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.filled-button.label-text.color": "onPrimary", "md.comp.filled-button.label-text.text-style": "labelLarge", "md.comp.filled-button.pressed.container.elevation": "md.sys.elevation.level0", "md.comp.filled-button.pressed.label-text.color": "onPrimary", "md.comp.filled-button.pressed.state-layer.color": "onPrimary", "md.comp.filled-button.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.filled-button.with-icon.disabled.icon.color": "onSurface", "md.comp.filled-button.with-icon.disabled.icon.opacity": 0.38, "md.comp.filled-button.with-icon.focus.icon.color": "onPrimary", "md.comp.filled-button.with-icon.hover.icon.color": "onPrimary", "md.comp.filled-button.with-icon.icon.color": "onPrimary", "md.comp.filled-button.with-icon.icon.size": 18.0, "md.comp.filled-button.with-icon.pressed.icon.color": "onPrimary"}