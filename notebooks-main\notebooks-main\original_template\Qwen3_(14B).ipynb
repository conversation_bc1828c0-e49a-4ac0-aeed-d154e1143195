{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Placeholder"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Placeholder"]}, {"cell_type": "markdown", "metadata": {"id": "iajq1W8ipjyK"}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 573, "referenced_widgets": ["81a72e47a9c6403bb94ae587b4e88880", "6f06e96a81244a4da0685e046b10f311", "5342635acd26466799d7b83b6f156864", "420c28a5f17d4e07ab43082575b89bd0", "828e4bbc6d7c4a1a8736adafba1d4e1c", "b2223ef3d64240108ab9746386ae7aae", "8d7c18448ac34ccbb3c43eac3a040cfa", "c01e7626a26446058558557cb90b8d38", "ab5be9d272af4959bdf41b1353515e83", "885ee075092749bfa265b332981a08f7", "b2f883797baf4ae3a889b14c5443bd77", "85e6e8f8aa56491488b7a398aa91de9b", "11cddbdc86e34258be2e9297a0462a30", "b8469b3b3d374744aac958178271b2f5", "6303e675499c4c76af60560afb2ff606", "d926a659eae548b4999ddcfdaaf812f0", "bf4d2e74ba764e2d9b43b6ecb072aa7c", "78e7feba64e243ab9aaf6b537319374e", "4300e1ff00494beb97335fd273fd4575", "aa86c16464154022b259f8971ad52104", "0e8fa2b745a24e68a6d32013ddd3be4f", "18cadb06d06e4a60be3dc513450b4f9d", "109c541fead1451b90faf18bcfce3972", "6905360d5b6145f08ffe5bb5fcc2bf1e", "184f20e961624f0aba52eb8e4302dd71", "4113418872e14677afa56f68dd5f5a8f", "2fcecbeeb7d94b0f89885e6d3630302c", "39162c06b48d461d92a1a7a9934df7e0", "1b24f12612dd41109b086cad3b5ea64f", "f54064d1813248e390121fba848f28de", "9f0a3dd5bfe64f32a8c9b5cc567c1565", "adb4af9e743345f1ace5407d0986d550", "7c0bae1a5ce24ecd9d7c654c913006b0", "92df2a3115644a0e860a418d9f14470b", "094d09056fb84e3aa05f9dda790d1889", "06d07032489a4af085417b640b627c48", "dd37f3c6eb3e4a92bba78c78f047c169", "7914fcfc452a4bc3a15c784fba36bf74", "6454bc2f42524042a93ea8062282a3dc", "78cf4c6155984d0086784b5b6c82368e", "47178305d497478890eff7f7f817a3f8", "2c969a8b896c4c6dadc78e07d6277b9a", "591a8ed70b0c45c488c4de1e4d7764bb", "e23a15f2894448fa92e7fa56908459c1", "603473e921644aacb5d6381540a1f1f4", "d0c9604eb95d46cfa8cacfc9a1e98a2b", "ceb7f2d78f034aa494d24edfce8dc8c7", "d7effb8cc13b414088187a7dc82d7330", "facc8ea81c924abb8acd625b6b50010b", "16edee89e86042ac85b6954a15ae602d", "f7ebc15cb8ed43e28480b325fdd8bf8c", "b5fd6ab47d2044c3af938df1cdd860ac", "d26216cb75624e93a5890d34af07b01e", "e380123fb5844a44b623a1274aa37cba", "cb3effe1adb84093a67b1c0b0dba172d", "aee359b9b49340f8920e9caeb3ec8e78", "fe9c370b84cb4517ad98782c25b81f32", "d7d58dfe9ab44562a8dd463158669c99", "57d1e57a9f4a4bf0bf30c23639011b17", "0407e7f8ca0046f58ff5cbdb80253536", "efd99a04d6ac406cbd80c3412de9b438", "cd014a2a66a44232aa5bf6610464e8b8", "c9439543dd954c96ace33c3cfa72554c", "03d13f82c1a448b794cda90755151b93", "c392cc26d65746cd8976d0d0d11988c6", "dc953fd10b9a464b8b0dd4956296f627", "03ab5f26e7c9489391d305045b3579f6", "cf35ab02e77844cfb2095538d08ba2f8", "583418a57f4c4641b2f8a5c8413f17cb", "c60ce442dbc14f50afbdbec5995720a7", "0b3e00e8302e4f80879c37ec235ec874", "6d43e45cf51843c4a1514561dc5c33d7", "98af186ff22f4983aa2c096fa0108f1c", "40c490b300a9414a853a83d555f85cc1", "1dd923032a31497b9db59d84313b14fd", "b02961d85cb84e40a7cd4851aeed2f85", "84d342f58bcf41bcad2bd70bfcdb0682", "c492a0eebbf64897a65f8c941cab0768", "1a22981c1ee44d728babb4a48a06877f", "0143648b14b241fb826826ffa886fe53", "3075929f5bc94b7594cb70cea3be1960", "592b05178c97460eacd7df017f88c6fa", "f2d4e3c6df994a4289062a9d9ddbde8f", "efe6270bc0964037bb196a9dbf91ac30", "c64d9f07ad6445e880c5567dd721a58d", "670c7406c98341529bdf3db1e62186dd", "c6dcc548a138439d8158c390f4e94489", "05ddf97654b644eeb6de10ed873ef0ea", "e71b6263a6994c72a2da3c917d20fa4d", "a9a3efddba7a44febdc3b07e37baa8e8", "907839fb18044707869284323a33a1f2", "8174e9a7862442a290198ea7dcd35f9c", "96d7cb7490a54a8a95b756ebf938271c", "931adf926ae74e7399a0149996c755d7", "25b744e44d064163b921c52c572f7566", "6a9cf722ca3a4082b107dd7035590f62", "933a57f5c2ca45fc96a67151b3020319", "150354de583b44bcb3e6b435b4dad6e5", "337807dbe2534676bdadcbdfa84eba90", "969ce9bc31254beea5ce4aac02c8500a", "2076b12dd43c4179904790007e3a751a", "a249a3c3815a4bdeb319ebf61794d5fa", "88131ab28c1740d6ba30bd64b523d745", "e5c36e37ad0842488f44550057fdf729", "de4ee34be18e4eea87b41726d9f19e8d", "c11eee6913f04378acf7f396b9f1a734", "04b158e6643d41f38d13e3250f52f424", "a2343e3aceb941818f6860b218c41d41", "a59d72f2d4ec404297de561e3ad64a9b", "84d88407030a477082f392135a0cec99", "78d75df8b14c40089aeab3cde3d0abeb", "e1fad35110774a2d8e9fba7baf990f55", "a6da593e98e8495593eaaef2bf2398f5", "b5103ffa2d534500a4b0113508f8723e", "2924e7738270423c9889c3a4c6d40c8f", "2d04e92f1f434275b590498ed17815c0", "acf51bee55bc44f58d6158130895bb6c", "3327de0966e74c1280985ef9e8c2423b", "557ca0721eae4c9db0d26b5810f4a97a", "902044fbfa8043c5addb6570c3e55056", "1bf84b95e8f74e4f8a8cef89959a6aaa", "bfc76b16352a4400a95cc3d8d795d3ee", "3baf5cf6731e4edcbee71f8405b2b651", "b9d53a9529164b3fb47b7009020a4d57", "b0af84609e764d64a6b280f45cf02538", "1993f40db52c4ed8bd534bc447d11beb", "3d6ed55d348146779c3415ad0024fd24", "cab7d65476dc45faa61e7a922d687bb3", "1525ecf5235541ac941456dac66ea049", "f49e9c65a88d42c791664f86539df755", "1553fd7b50ad41349c382d9fa329ce87", "78a871c7d1064b99b496526baed75696", "128067a4a5ea479eb74f5124ac399955", "aef7a2d8a1ce467099790bf4195330d2", "2c1c37c73f44453ebd19f7218f9a98fb", "f00bbf1cbdad433c83663e630df0e6e1", "de77764352fa4693be77c888bf368828", "0dda0e72d0144188af23e8212a3c995e", "38cfde2f43b54eafbe33d6b1164ce9e8", "850eb69488ad45c1a0fb4ec12fd1dd79", "8e487ec7b4b44a308820f595ff619f0f", "475f33289d49494d923ecedd00369dc0", "bc8c89d0c5ef4d0b90ffeca1abc3e617"]}, "id": "QmUBVEnvCDJv", "outputId": "49e34d4d-08ec-4e93-81c8-dee45aa9e481"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "🦥 Unsloth Zoo will now patch everything to make training faster!\n", "==((====))==  Unsloth 2025.4.5: Fast Qwen3 patching. Transformers: 4.51.3.\n", "   \\\\   /|    Tesla T4. Num GPUs = 1. Max memory: 14.741 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.6.0+cu124. CUDA: 7.5. CUDA Toolkit: 12.4. Triton: 3.2.0\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.29.post3. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "81a72e47a9c6403bb94ae587b4e88880", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors.index.json:   0%|          | 0.00/168k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "85e6e8f8aa56491488b7a398aa91de9b", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00001-of-00003.safetensors:   0%|          | 0.00/4.97G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "109c541fead1451b90faf18bcfce3972", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00002-of-00003.safetensors:   0%|          | 0.00/4.59G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "92df2a3115644a0e860a418d9f14470b", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00003-of-00003.safetensors:   0%|          | 0.00/1.56G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "603473e921644aacb5d6381540a1f1f4", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/3 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "aee359b9b49340f8920e9caeb3ec8e78", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/242 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "03ab5f26e7c9489391d305045b3579f6", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/10.3k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c492a0eebbf64897a65f8c941cab0768", "version_major": 2, "version_minor": 0}, "text/plain": ["vocab.json:   0%|          | 0.00/2.78M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e71b6263a6994c72a2da3c917d20fa4d", "version_major": 2, "version_minor": 0}, "text/plain": ["merges.txt:   0%|          | 0.00/1.67M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "969ce9bc31254beea5ce4aac02c8500a", "version_major": 2, "version_minor": 0}, "text/plain": ["added_tokens.json:   0%|          | 0.00/707 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "78d75df8b14c40089aeab3cde3d0abeb", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/614 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "bfc76b16352a4400a95cc3d8d795d3ee", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/11.4M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "128067a4a5ea479eb74f5124ac399955", "version_major": 2, "version_minor": 0}, "text/plain": ["chat_template.jinja:   0%|          | 0.00/4.67k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "\n", "fourbit_models = [\n", "    \"unsloth/Qwen3-1.7B-unsloth-bnb-4bit\", # Qwen 14B 2x faster\n", "    \"unsloth/Qwen3-4B-unsloth-bnb-4bit\",\n", "    \"unsloth/Qwen3-8B-unsloth-bnb-4bit\",\n", "    \"unsloth/Qwen3-14B-unsloth-bnb-4bit\",\n", "    \"unsloth/Qwen3-32B-unsloth-bnb-4bit\",\n", "\n", "    # 4bit dynamic quants for superior accuracy and low memory use\n", "    \"unsloth/gemma-3-12b-it-unsloth-bnb-4bit\",\n", "    \"unsloth/Phi-4\",\n", "    \"unsloth/Llama-3.1-8B\",\n", "    \"unsloth/Llama-3.2-3B\",\n", "    \"unsloth/orpheus-3b-0.1-ft-unsloth-bnb-4bit\" # [NEW] We support TTS models!\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/Qwen3-14B\",\n", "    max_seq_length = 2048,   # Context length - can be longer, but uses more memory\n", "    load_in_4bit = True,     # 4bit uses much less memory\n", "    load_in_8bit = False,    # A bit more accurate, uses 2x memory\n", "    full_finetuning = False, # We have full finetuning now!\n", "    # token = \"hf_...\",      # use one if using gated models\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "SXd9bTZd1aaL"}, "source": ["We now add LoRA adapters so we only need to update 1 to 10% of all parameters!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6bZsfBuZDeCL", "outputId": "19e8ca26-7176-4bff-c525-b38b9dfa36a8"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth 2025.4.5 patched 40 layers with 40 QKV layers, 40 O layers and 40 MLP layers.\n"]}], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 32,           # Choose any number > 0! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 32,  # Best to choose alpha = rank or rank*2\n", "    lora_dropout = 0, # Supports any, but = 0 is optimized\n", "    bias = \"none\",    # Supports any, but = \"none\" is optimized\n", "    # [NEW] \"unsloth\" uses 30% less VRAM, fits 2x larger batch sizes!\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for very long context\n", "    random_state = 3407,\n", "    use_rslora = False,   # We support rank stabilized LoRA\n", "    loftq_config = None,  # And LoftQ\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "vITh0KVJ10qX"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "Qwen3 has both reasoning and a non reasoning mode. So, we should use 2 datasets:\n", "\n", "1. We use the [Open Math Reasoning]() dataset which was used to win the [AIMO](https://www.kaggle.com/competitions/ai-mathematical-olympiad-progress-prize-2/leaderboard) (AI Mathematical Olympiad - Progress Prize 2) challenge! We sample 10% of verifiable reasoning traces that used DeepSeek R1, and whicht got > 95% accuracy.\n", "\n", "2. We also leverage [Maxime Labonne's FineTome-100k](https://huggingface.co/datasets/mlabonne/FineTome-100k) dataset in ShareGPT style. But we need to convert it to HuggingFace's normal multiturn format as well."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5kyTw2n1edte"}, "outputs": [], "source": ["from datasets import load_dataset\n", "reasoning_dataset = load_dataset(\"unsloth/OpenMathReasoning-mini\", split = \"cot\")\n", "non_reasoning_dataset = load_dataset(\"mlabonne/FineTome-100k\", split = \"train\")"]}, {"cell_type": "markdown", "metadata": {"id": "PTZICZtie3lQ"}, "source": ["Let's see the structure of both datasets:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "DjgH3lt0e2Sz", "outputId": "5937c7fe-e39f-42c8-fb8e-b44b5e9acc36"}, "outputs": [{"data": {"text/plain": ["Dataset({\n", "    features: ['expected_answer', 'problem_type', 'problem_source', 'generation_model', 'pass_rate_72b_tir', 'problem', 'generated_solution', 'inference_mode'],\n", "    num_rows: 19252\n", "})"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["reasoning_dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_zoaygOAe3I2", "outputId": "f0b79815-f85d-4402-acb6-b870eec902b0"}, "outputs": [{"data": {"text/plain": ["Dataset({\n", "    features: ['conversations', 'source', 'score'],\n", "    num_rows: 100000\n", "})"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["non_reasoning_dataset"]}, {"cell_type": "markdown", "metadata": {"id": "YX8H3urDe00l"}, "source": ["We now convert the reasoning dataset into conversational format:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "LjY75GoYUCB8"}, "outputs": [], "source": ["def generate_conversation(examples):\n", "    problems  = examples[\"problem\"]\n", "    solutions = examples[\"generated_solution\"]\n", "    conversations = []\n", "    for problem, solution in zip(problems, solutions):\n", "        conversations.append([\n", "            {\"role\" : \"user\",      \"content\" : problem},\n", "            {\"role\" : \"assistant\", \"content\" : solution},\n", "        ])\n", "    return { \"conversations\": conversations, }"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gbh19fTOfHDB"}, "outputs": [], "source": ["reasoning_conversations = tokenizer.apply_chat_template(\n", "    reasoning_dataset.map(generate_conversation, batched = True)[\"conversations\"],\n", "    tokenize = False,\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "XTexROzQfJn5"}, "source": ["Let's see the first transformed row:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 175}, "id": "mkj4c6NrfIz3", "outputId": "d3d66005-f3c6-4165-bde3-f9ae753d69b9"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["\"<|im_start|>user\\nGiven $\\\\sqrt{x^2+165}-\\\\sqrt{x^2-52}=7$ and $x$ is positive, find all possible values of $x$.<|im_end|>\\n<|im_start|>assistant\\n<think>\\nOkay, let's see. I need to solve the equation √(x² + 165) - √(x² - 52) = 7, and find all positive values of x. Hmm, radicals can be tricky, but maybe if I can eliminate the square roots by squaring both sides. Let me try that.\\n\\n<PERSON><PERSON><PERSON>, let me write down the equation again to make sure I have it right:\\n\\n√(x² + 165) - √(x² - 52) = 7.\\n\\nOkay, so the idea is to isolate one of the radicals and then square both sides. Let me try moving the second radical to the other side:\\n\\n√(x² + 165) = 7 + √(x² - 52).\\n\\nNow, if I square both sides, maybe I can get rid of the square roots. Let's do that:\\n\\n(√(x² + 165))² = (7 + √(x² - 52))².\\n\\nSimplifying the left side:\\n\\nx² + 165 = 49 + 14√(x² - 52) + (√(x² - 52))².\\n\\nThe right side is expanded using the formula (a + b)² = a² + 2ab + b². So the right side becomes 7² + 2*7*√(x² - 52) + (√(x² - 52))², which is 49 + 14√(x² - 52) + (x² - 52).\\n\\nSo putting it all together:\\n\\nx² + 165 = 49 + 14√(x² - 52) + x² - 52.\\n\\nHmm, let's simplify the right side. The x² terms will cancel out, right? Let's subtract x² from both sides:\\n\\n165 = 49 + 14√(x² - 52) - 52.\\n\\nSimplify the constants on the right:\\n\\n49 - 52 is -3, so:\\n\\n165 = -3 + 14√(x² - 52).\\n\\nNow, add 3 to both sides to isolate the radical term:\\n\\n165 + 3 = 14√(x² - 52).\\n\\nSo 168 = 14√(x² - 52).\\n\\nDivide both sides by 14:\\n\\n168 / 14 = √(x² - 52).\\n\\n12 = √(x² - 52).\\n\\nNow, square both sides again to eliminate the square root:\\n\\n12² = x² - 52.\\n\\n144 = x² - 52.\\n\\nAdd 52 to both sides:\\n\\n144 + 52 = x².\\n\\n196 = x².\\n\\nSo x = √196 = 14.\\n\\nBut wait, since the problem states that x is positive, we only take the positive root. So x = 14.\\n\\nBut hold on, when dealing with squaring equations, sometimes extraneous solutions can come up. I should check if this solution actually satisfies the original equation.\\n\\nLet's plug x = 14 back into the original equation:\\n\\n√(14² + 165) - √(14² - 52) = ?\\n\\nCalculate each term:\\n\\n14² is 196.\\n\\nSo first radical: √(196 + 165) = √361 = 19.\\n\\nSecond radical: √(196 - 52) = √144 = 12.\\n\\nSo 19 - 12 = 7, which is exactly the right-hand side. So yes, it checks out.\\n\\nTherefore, the only solution is x = 14. Since the problem says x is positive, we don't have to consider negative roots. So I think that's the answer.\\n</think>\\n\\nTo solve the equation \\\\(\\\\sqrt{x^2 + 165} - \\\\sqrt{x^2 - 52} = 7\\\\) for positive \\\\(x\\\\), we proceed as follows:\\n\\n1. Start with the given equation:\\n   \\\\[\\n   \\\\sqrt{x^2 + 165} - \\\\sqrt{x^2 - 52} = 7\\n   \\\\]\\n\\n2. Isolate one of the square roots by moving \\\\(\\\\sqrt{x^2 - 52}\\\\) to the right side:\\n   \\\\[\\n   \\\\sqrt{x^2 + 165} = 7 + \\\\sqrt{x^2 - 52}\\n   \\\\]\\n\\n3. Square both sides to eliminate the square root on the left:\\n   \\\\[\\n   (\\\\sqrt{x^2 + 165})^2 = (7 + \\\\sqrt{x^2 - 52})^2\\n   \\\\]\\n   Simplifying both sides, we get:\\n   \\\\[\\n   x^2 + 165 = 49 + 14\\\\sqrt{x^2 - 52} + (x^2 - 52)\\n   \\\\]\\n\\n4. Combine like terms on the right side:\\n   \\\\[\\n   x^2 + 165 = x^2 - 52 + 49 + 14\\\\sqrt{x^2 - 52}\\n   \\\\]\\n   Simplifying further:\\n   \\\\[\\n   x^2 + 165 = x^2 - 3 + 14\\\\sqrt{x^2 - 52}\\n   \\\\]\\n\\n5. Subtract \\\\(x^2\\\\) from both sides:\\n   \\\\[\\n   165 = -3 + 14\\\\sqrt{x^2 - 52}\\n   \\\\]\\n\\n6. Add 3 to both sides to isolate the term with the square root:\\n   \\\\[\\n   168 = 14\\\\sqrt{x^2 - 52}\\n   \\\\]\\n\\n7. Divide both sides by 14:\\n   \\\\[\\n   12 = \\\\sqrt{x^2 - 52}\\n   \\\\]\\n\\n8. Square both sides again to eliminate the square root:\\n   \\\\[\\n   12^2 = x^2 - 52\\n   \\\\]\\n   Simplifying:\\n   \\\\[\\n   144 = x^2 - 52\\n   \\\\]\\n\\n9. Add 52 to both sides to solve for \\\\(x^2\\\\):\\n   \\\\[\\n   196 = x^2\\n   \\\\]\\n\\n10. Take the positive square root (since \\\\(x\\\\) is positive):\\n    \\\\[\\n    x = \\\\sqrt{196} = 14\\n    \\\\]\\n\\n11. Verify the solution by substituting \\\\(x = 14\\\\) back into the original equation:\\n    \\\\[\\n    \\\\sqrt{14^2 + 165} - \\\\sqrt{14^2 - 52} = \\\\sqrt{196 + 165} - \\\\sqrt{196 - 52} = \\\\sqrt{361} - \\\\sqrt{144} = 19 - 12 = 7\\n    \\\\]\\n    The solution checks out.\\n\\nThus, the only positive solution is:\\n\\\\[\\n\\\\boxed{14}\\n\\\\]<|im_end|>\\n\""]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["reasoning_conversations[0]"]}, {"cell_type": "markdown", "metadata": {"id": "5OMhyEXkfM5e"}, "source": ["Next we take the non reasoning dataset and convert it to conversational format as well.\n", "\n", "We have to use <PERSON><PERSON><PERSON><PERSON>'s `standardize_sharegpt` function to fix up the format of the dataset first."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 77, "referenced_widgets": ["2e449d59ddbd4e1a9e814fb6b768d479", "eebabe08bcc448a1adf62d568584d15e", "36b21d06fc964949a8e3dd7a55142e3a", "db3cecbb491f4d80aec0970338e93beb", "aad271b9eb3547ba9e200c1c1541b544", "35dc626d13314bdba882a46d12737b46", "04838ba3bb5c42b3aead6dfab186b3a5", "697cce3190c34039a722deffa985e576", "db1d83b3a29c4093b4518c0761106c10", "fd9ef41c670a4e16b72def5a2aa157e3", "e05f36698fa54883916b11363d32f72a"]}, "id": "nXBFaeQHfSxp", "outputId": "03da6c10-db7a-45d2-92f2-02eb844bb743"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2e449d59ddbd4e1a9e814fb6b768d479", "version_major": 2, "version_minor": 0}, "text/plain": ["Unsloth: Standardizing formats (num_proc=12):   0%|          | 0/100000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth.chat_templates import standardize_sharegpt\n", "dataset = standardize_sharegpt(non_reasoning_dataset)\n", "\n", "non_reasoning_conversations = tokenizer.apply_chat_template(\n", "    dataset[\"conversations\"],\n", "    tokenize = False,\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "Q9FcosGvfdNr"}, "source": ["Let's see the first row"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 175}, "id": "pb0hbEekfeqf", "outputId": "e1868b58-efa9-43ed-8ff0-805c7535bbbd"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'<|im_start|>user\\nExplain what boolean operators are, what they do, and provide examples of how they can be used in programming. Additionally, describe the concept of operator precedence and provide examples of how it affects the evaluation of boolean expressions. Discuss the difference between short-circuit evaluation and normal evaluation in boolean expressions and demonstrate their usage in code. \\n\\nFurthermore, add the requirement that the code must be written in a language that does not support short-circuit evaluation natively, forcing the test taker to implement their own logic for short-circuit evaluation.\\n\\nFinally, delve into the concept of truthiness and falsiness in programming languages, explaining how it affects the evaluation of boolean expressions. Add the constraint that the test taker must write code that handles cases where truthiness and falsiness are implemented differently across different programming languages.<|im_end|>\\n<|im_start|>assistant\\n<think>\\n\\n</think>\\n\\nBoolean operators are logical operators used in programming to manipulate boolean values. They operate on one or more boolean operands and return a boolean result. The three main boolean operators are \"AND\" (&&), \"OR\" (||), and \"NOT\" (!).\\n\\nThe \"AND\" operator returns true if both of its operands are true, and false otherwise. For example:\\n\\n```python\\nx = 5\\ny = 10\\nresult = (x > 0) and (y < 20)  # This expression evaluates to True\\n```\\n\\nThe \"OR\" operator returns true if at least one of its operands is true, and false otherwise. For example:\\n\\n```python\\nx = 5\\ny = 10\\nresult = (x > 0) or (y < 20)  # This expression evaluates to True\\n```\\n\\nThe \"NOT\" operator negates the boolean value of its operand. It returns true if the operand is false, and false if the operand is true. For example:\\n\\n```python\\nx = 5\\nresult = not (x > 10)  # This expression evaluates to True\\n```\\n\\nOperator precedence refers to the order in which operators are evaluated in an expression. It ensures that expressions are evaluated correctly. In most programming languages, logical AND has higher precedence than logical OR. For example:\\n\\n```python\\nresult = True or False and False  # This expression is evaluated as (True or (False and False)), which is True\\n```\\n\\nShort-circuit evaluation is a behavior where the second operand of a logical operator is not evaluated if the result can be determined based on the value of the first operand. In short-circuit evaluation, if the first operand of an \"AND\" operator is false, the second operand is not evaluated because the result will always be false. Similarly, if the first operand of an \"OR\" operator is true, the second operand is not evaluated because the result will always be true.\\n\\nIn programming languages that support short-circuit evaluation natively, you can use it to improve performance or avoid errors. For example:\\n\\n```python\\nif x != 0 and (y / x) > 10:\\n    # Perform some operation\\n```\\n\\nIn languages without native short-circuit evaluation, you can implement your own logic to achieve the same behavior. Here\\'s an example in pseudocode:\\n\\n```\\nif x != 0 {\\n    if (y / x) > 10 {\\n        // Perform some operation\\n    }\\n}\\n```\\n\\nTruthiness and falsiness refer to how non-boolean values are evaluated in boolean contexts. In many programming languages, non-zero numbers and non-empty strings are considered truthy, while zero, empty strings, and null/None values are considered falsy.\\n\\nWhen evaluating boolean expressions, truthiness and falsiness come into play. For example:\\n\\n```python\\nx = 5\\nresult = x  # The value of x is truthy, so result is also truthy\\n```\\n\\nTo handle cases where truthiness and falsiness are implemented differently across programming languages, you can explicitly check the desired condition. For example:\\n\\n```python\\nx = 5\\nresult = bool(x)  # Explicitly converting x to a boolean value\\n```\\n\\nThis ensures that the result is always a boolean value, regardless of the language\\'s truthiness and falsiness rules.<|im_end|>\\n'"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["non_reasoning_conversations[0]"]}, {"cell_type": "markdown", "metadata": {"id": "c_0L18QMfot4"}, "source": ["Now let's see how long both datasets are:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "unDFuUq1foWj", "outputId": "0f4849ea-b5ad-4449-bcaf-abaed55457c6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["19252\n", "100000\n"]}], "source": ["print(len(reasoning_conversations))\n", "print(len(non_reasoning_conversations))"]}, {"cell_type": "markdown", "metadata": {"id": "dgknnOf7fn3e"}, "source": ["The non reasoning dataset is much longer. Let's assume we want the model to retain some reasoning capabilities, but we specifically want a chat model.\n", "\n", "Let's define a ratio of chat only data. The goal is to define some mixture of both sets of data.\n", "\n", "Let's select 25% reasoning and 75% chat based:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_szfriCBgCkU"}, "outputs": [], "source": ["chat_percentage = 0.75"]}, {"cell_type": "markdown", "metadata": {"id": "DANuEJA7gL58"}, "source": ["Let's sample the reasoning dataset by 25% (or whatever is 100% - chat_percentage)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "7-e0KO9GgFy3"}, "outputs": [], "source": ["import pandas as pd\n", "non_reasoning_subset = pd.Series(non_reasoning_conversations)\n", "non_reasoning_subset = non_reasoning_subset.sample(\n", "    int(len(reasoning_conversations) * (1.0 - chat_percentage)),\n", "    random_state = 2407,\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "qR-4prS_gVel"}, "source": ["Finally combine both datasets:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jfV47_SXgXH4"}, "outputs": [], "source": ["data = pd.concat([\n", "    pd.Series(reasoning_conversations),\n", "    pd.Series(non_reasoning_subset)\n", "])\n", "data.name = \"text\"\n", "\n", "from datasets import Dataset\n", "combined_dataset = Dataset.from_pandas(pd.DataFrame(data))\n", "combined_dataset = combined_dataset.shuffle(seed = 3407)"]}, {"cell_type": "markdown", "metadata": {"id": "idAEIeSQ3xdS"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `SFTTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 60 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 77, "referenced_widgets": ["c8527b9baa824926a1da274d8afd3e8d", "caa243f39fd445ec8586bf49165012ee", "05746b97c0244aa1993550fb8222214b", "9a55426956ad4eb2a0f147266259e2d0", "fb211037708447019c3cf54387864711", "33c7d52958c14ebe9d69418cc33c9673", "991d58e11d0d4e0fa79c2f7bbb877d8a", "52169e1f788f49678255e246430c6703", "4b5718381b2543b5aa33dc78d8796f4d", "950c8b2d34b04debbf529d9807936014", "23d12dfa213a4880abb4448a22984f6f"]}, "id": "95_Nn-89DhsL", "outputId": "13e3962f-d752-4aa1-b34b-090d3eec2661"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c8527b9baa824926a1da274d8afd3e8d", "version_major": 2, "version_minor": 0}, "text/plain": ["Unsloth: Tokenizing [\"text\"] (num_proc=12):   0%|          | 0/24065 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from trl import SFTTrainer, SFTConfig\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = combined_dataset,\n", "    eval_dataset = None, # Can set up evaluation!\n", "    args = SFTConfig(\n", "        dataset_text_field = \"text\",\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4, # Use GA to mimic batch size!\n", "        warmup_steps = 5,\n", "        # num_train_epochs = 1, # Set this for 1 full training run.\n", "        max_steps = 30,\n", "        learning_rate = 2e-4, # Reduce to 2e-5 for long training runs\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "2ejIt2xSNKKp", "outputId": "68a731b3-eb4a-4a9f-b26e-53c3611e56f3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU = Tesla T4. Max memory = 14.741 GB.\n", "11.898 GB of memory reserved.\n"]}], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "markdown", "metadata": {"id": "M9fa371ShyhB"}, "source": ["Let's train the model! To resume a training run, set `trainer.train(resume_from_checkpoint = True)`"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "yqxqAZ7KJ4oL", "outputId": "a18636b6-830d-4e87-ef34-95240cb2bdc2"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 24,065 | Num Epochs = 1 | Total steps = 30\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient accumulation steps = 4\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (2 x 4 x 1) = 8\n", " \"-____-\"     Trainable parameters = 128,450,560/14,000,000,000 (0.92% trained)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Will smartly offload gradients to save VRAM!\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='30' max='30' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [30/30 04:00, Epoch 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.524400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.595800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.731300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.615200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.522400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.522500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.517300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.468400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.431400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.507700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.442300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.459600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.542300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.435000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.438400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.496400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.428300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.451900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.372500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.404100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.420600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.470000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.471600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.471100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.471800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.461900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.364100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.406500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.348300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.387700</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "pCqnaKmlO1U9", "outputId": "30cd8886-65f0-473c-8c22-bfdf90f43c2b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["261.8103 seconds used for training.\n", "4.36 minutes used for training.\n", "Peak reserved memory = 14.066 GB.\n", "Peak reserved memory for training = 0.0 GB.\n", "Peak reserved memory % of max memory = 35.559 %.\n", "Peak reserved memory for training % of max memory = 0.0 %.\n"]}], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {"id": "ekOmTR1hSNcr"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model via Unsloth native inference! According to the `Qwen-3` team, the recommended settings for reasoning inference are `temperature = 0.6, top_p = 0.95, top_k = 20`\n", "\n", "For normal chat based inference, `temperature = 0.7, top_p = 0.8, top_k = 20`"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kR3gIAX-SM2q", "outputId": "8e9214de-41be-4503-e910-a3bd8b5ef111"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["To solve the equation (x + 2)^2 = 0, we can take the square root of both sides. This gives us:\n", "\n", "x + 2 = 0\n", "\n", "Subtracting 2 from both sides, we get:\n", "\n", "x = -2\n", "\n", "Therefore, the solution to the equation is x = -2.<|im_end|>\n"]}], "source": ["messages = [\n", "    {\"role\" : \"user\", \"content\" : \"Solve (x + 2)^2 = 0.\"}\n", "]\n", "text = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = False,\n", "    add_generation_prompt = True, # Must add for generation\n", "    enable_thinking = False, # Disable thinking\n", ")\n", "\n", "from transformers import TextStreamer\n", "_ = model.generate(\n", "    **tokenizer(text, return_tensors = \"pt\").to(\"cuda\"),\n", "    max_new_tokens = 256, # Increase for longer outputs!\n", "    temperature = 0.7, top_p = 0.8, top_k = 20, # For non thinking\n", "    streamer = TextStreamer(tokenizer, skip_prompt = True),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "j873RMcEi9uq", "outputId": "1fe5215f-1aa3-4ff7-e8ec-fc65c5016573"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "Okay, so I need to solve the equation (x + 2)^2 = 0. Hmm, let's see. I remember that when you have something squared equals zero, the only solution is when the inside part is zero. Because if you square any real number, it's either positive or zero. So if the square is zero, the original number must be zero. So maybe I can take the square root of both sides?\n", "\n", "Wait, but the equation is (x + 2)^2 = 0. If I take the square root of both sides, that would give me x + 2 = 0, right? Because the square root of 0 is 0. Then solving for x would just be subtracting 2 from both sides, so x = -2. But wait, isn't that the only solution? Because squaring a number can't be negative, so the only way (x + 2)^2 is zero is if x + 2 is zero. So x must be -2. But since it's squared, does that mean there's a multiplicity here? Like, maybe x = -2 is a repeated root?\n", "\n", "Let me think. If we expand the equation, (x + 2)^2 = x^2 + 4x + 4. So the original equation is x^2 + 4x + 4 = 0. To solve this quadratic equation, we can factor it. Let's see, x^2 + 4x + 4 factors into (x + 2)(x + 2) = 0. So the solutions are x + 2 = 0 or x + 2 = 0. So both factors give the same solution, x = -2. Therefore, the equation has a repeated root at x = -2. So even though it's a quadratic equation, which usually has two solutions, in this case, both solutions are the same. So the answer is x = -2, but it's a double root. \n", "\n", "But the question just says \"solve\" the equation. So maybe they just want the value of x that satisfies the equation, which is -2. Even though it's a repeated root, the solution is still x = -2. So I think the answer is x = -2. Let me check again. If x = -2, then (x + 2)^2 = (-2 + 2)^2 = 0^2 = 0. Yep, that works. So that's the only solution. So the answer is x = -2. But maybe they want it in set notation or something? Like { -2 }, but probably just x = -2 is sufficient. I don't think there's any other solution here. So yeah, x = -2 is the only solution.\n", "</think>\n", "\n", "To solve the equation \\((x + 2)^2 = 0\\), we start by recognizing that a squared term equals zero only if the term inside the square is zero. \n", "\n", "1. Start with the given equation:\n", "   \\[\n", "   (x + 2)^2 = 0\n", "   \\]\n", "\n", "2. Take the square root of both sides. Since the square root of 0 is 0, we get:\n", "   \\[\n", "   x + 2 = 0\n", "   \\]\n", "\n", "3. Solve for \\(x\\) by subtracting 2 from both sides:\n", "   \\[\n", "   x = -2\n", "   \\]\n", "\n", "4. Verify the solution by substituting \\(x = -2\\) back into the original equation:\n", "   \\[\n", "   (-2 + 2)^2 = 0^2 = 0\n", "   \\]\n", "\n", "Thus, the solution is:\n", "\\[\n", "\\boxed{-2}\n", "\\]<|im_end|>\n"]}], "source": ["messages = [\n", "    {\"role\" : \"user\", \"content\" : \"Solve (x + 2)^2 = 0.\"}\n", "]\n", "text = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = False,\n", "    add_generation_prompt = True, # Must add for generation\n", "    enable_thinking = True, # Disable thinking\n", ")\n", "\n", "from transformers import TextStreamer\n", "_ = model.generate(\n", "    **tokenizer(text, return_tensors = \"pt\").to(\"cuda\"),\n", "    max_new_tokens = 1024, # Increase for longer outputs!\n", "    temperature = 0.6, top_p = 0.95, top_k = 20, # For thinking\n", "    streamer = TextStreamer(tokenizer, skip_prompt = True),\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "uMuVrWbjAzhc"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "upcOlWe7A1vc", "outputId": "6473d227-0a1f-443e-ef98-de3ca2373e81"}, "outputs": [{"data": {"text/plain": ["('lora_model/tokenizer_config.json',\n", " 'lora_model/special_tokens_map.json',\n", " 'lora_model/vocab.json',\n", " 'lora_model/merges.txt',\n", " 'lora_model/added_tokens.json',\n", " 'lora_model/tokenizer.json')"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "AEEcJ4qfC7Lp"}, "source": ["Now if you want to load the LoRA adapters we just saved for inference, set `False` to `True`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MKX_XKs_BNZR"}, "outputs": [], "source": ["if False:\n", "    from unsloth import FastLanguageModel\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name = \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        max_seq_length = 2048,\n", "        load_in_4bit = True,\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "f422JgM9sdVT"}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16 or `merged_4bit` for int4. We also allow `lora` adapters as a fallback. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iHjt_SMYsd3P"}, "outputs": [], "source": ["# Merge to 16bit\n", "if False:\n", "    model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_16bit\",)\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_16bit\", token = \"\")\n", "\n", "# Merge to 4bit\n", "if False:\n", "    model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_4bit\",)\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_4bit\", token = \"\")\n", "\n", "# Just LoRA adapters\n", "if False:\n", "    model.save_pretrained_merged(\"model\", tokenizer, save_method = \"lora\",)\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"lora\", token = \"\")"]}, {"cell_type": "markdown", "metadata": {"id": "TCv4vXHd61i7"}, "source": ["### GGUF / llama.cpp Conversion\n", "To save to `GGUF` / `llama.cpp`, we support it natively now! We clone `llama.cpp` and we default save it to `q8_0`. We allow all methods like `q4_k_m`. Use `save_pretrained_gguf` for local saving and `push_to_hub_gguf` for uploading to HF.\n", "\n", "Some supported quant methods (full list on our [Wiki page](https://github.com/unslothai/unsloth/wiki#gguf-quantization-options)):\n", "* `q8_0` - Fast conversion. High resource use, but generally acceptable.\n", "* `q4_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q4_K.\n", "* `q5_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q5_K.\n", "\n", "[**NEW**] To finetune and auto export to Ollama, try our [Ollama notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "FqfebeAdT073"}, "outputs": [], "source": ["# Save to 8bit Q8_0\n", "if False:\n", "    model.save_pretrained_gguf(\"model\", tokenizer,)\n", "# Remember to go to https://huggingface.co/settings/tokens for a token!\n", "# And change hf to your username!\n", "if False:\n", "    model.push_to_hub_gguf(\"hf/model\", tokenizer, token = \"\")\n", "\n", "# Save to 16bit GGUF\n", "if False:\n", "    model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"f16\")\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"f16\", token = \"\")\n", "\n", "# Save to q4_k_m GGUF\n", "if False:\n", "    model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"q4_k_m\")\n", "if False: # Pushing to HF Hub\n", "    model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"q4_k_m\", token = \"\")\n", "\n", "# Save to multiple GGUF options - much faster if you want multiple!\n", "if False:\n", "    model.push_to_hub_gguf(\n", "        \"hf/model\", # Change hf to your username!\n", "        tokenizer,\n", "        quantization_method = [\"q4_k_m\", \"q8_0\", \"q5_k_m\",],\n", "        token = \"\", # Get a token at https://huggingface.co/settings/tokens\n", "    )"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "include_colab_link": true, "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"0143648b14b241fb826826ffa886fe53": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c64d9f07ad6445e880c5567dd721a58d", "max": 2776833, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_670c7406c98341529bdf3db1e62186dd", "value": 2776833}}, "03ab5f26e7c9489391d305045b3579f6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_cf35ab02e77844cfb2095538d08ba2f8", "IPY_MODEL_583418a57f4c4641b2f8a5c8413f17cb", "IPY_MODEL_c60ce442dbc14f50afbdbec5995720a7"], "layout": "IPY_MODEL_0b3e00e8302e4f80879c37ec235ec874"}}, "03d13f82c1a448b794cda90755151b93": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0407e7f8ca0046f58ff5cbdb80253536": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "04838ba3bb5c42b3aead6dfab186b3a5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "04b158e6643d41f38d13e3250f52f424": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "05746b97c0244aa1993550fb8222214b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_52169e1f788f49678255e246430c6703", "max": 24065, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4b5718381b2543b5aa33dc78d8796f4d", "value": 24065}}, "05ddf97654b644eeb6de10ed873ef0ea": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "06d07032489a4af085417b640b627c48": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_47178305d497478890eff7f7f817a3f8", "max": 1555824768, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2c969a8b896c4c6dadc78e07d6277b9a", "value": 1555824620}}, "094d09056fb84e3aa05f9dda790d1889": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6454bc2f42524042a93ea8062282a3dc", "placeholder": "​", "style": "IPY_MODEL_78cf4c6155984d0086784b5b6c82368e", "value": "model-00003-of-00003.safetensors: 100%"}}, "0b3e00e8302e4f80879c37ec235ec874": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0dda0e72d0144188af23e8212a3c995e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0e8fa2b745a24e68a6d32013ddd3be4f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "109c541fead1451b90faf18bcfce3972": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6905360d5b6145f08ffe5bb5fcc2bf1e", "IPY_MODEL_184f20e961624f0aba52eb8e4302dd71", "IPY_MODEL_4113418872e14677afa56f68dd5f5a8f"], "layout": "IPY_MODEL_2fcecbeeb7d94b0f89885e6d3630302c"}}, "11cddbdc86e34258be2e9297a0462a30": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bf4d2e74ba764e2d9b43b6ecb072aa7c", "placeholder": "​", "style": "IPY_MODEL_78e7feba64e243ab9aaf6b537319374e", "value": "model-00001-of-00003.safetensors: 100%"}}, "128067a4a5ea479eb74f5124ac399955": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_aef7a2d8a1ce467099790bf4195330d2", "IPY_MODEL_2c1c37c73f44453ebd19f7218f9a98fb", "IPY_MODEL_f00bbf1cbdad433c83663e630df0e6e1"], "layout": "IPY_MODEL_de77764352fa4693be77c888bf368828"}}, "150354de583b44bcb3e6b435b4dad6e5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1525ecf5235541ac941456dac66ea049": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1553fd7b50ad41349c382d9fa329ce87": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "16edee89e86042ac85b6954a15ae602d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "184f20e961624f0aba52eb8e4302dd71": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f54064d1813248e390121fba848f28de", "max": 4589082716, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9f0a3dd5bfe64f32a8c9b5cc567c1565", "value": 4589082279}}, "18cadb06d06e4a60be3dc513450b4f9d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1993f40db52c4ed8bd534bc447d11beb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1a22981c1ee44d728babb4a48a06877f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f2d4e3c6df994a4289062a9d9ddbde8f", "placeholder": "​", "style": "IPY_MODEL_efe6270bc0964037bb196a9dbf91ac30", "value": "vocab.json: 100%"}}, "1b24f12612dd41109b086cad3b5ea64f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1bf84b95e8f74e4f8a8cef89959a6aaa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1dd923032a31497b9db59d84313b14fd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2076b12dd43c4179904790007e3a751a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_de4ee34be18e4eea87b41726d9f19e8d", "placeholder": "​", "style": "IPY_MODEL_c11eee6913f04378acf7f396b9f1a734", "value": "added_tokens.json: 100%"}}, "23d12dfa213a4880abb4448a22984f6f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "25b744e44d064163b921c52c572f7566": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2924e7738270423c9889c3a4c6d40c8f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2c1c37c73f44453ebd19f7218f9a98fb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_850eb69488ad45c1a0fb4ec12fd1dd79", "max": 4673, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_8e487ec7b4b44a308820f595ff619f0f", "value": 4673}}, "2c969a8b896c4c6dadc78e07d6277b9a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2d04e92f1f434275b590498ed17815c0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2e449d59ddbd4e1a9e814fb6b768d479": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_eebabe08bcc448a1adf62d568584d15e", "IPY_MODEL_36b21d06fc964949a8e3dd7a55142e3a", "IPY_MODEL_db3cecbb491f4d80aec0970338e93beb"], "layout": "IPY_MODEL_aad271b9eb3547ba9e200c1c1541b544"}}, "2fcecbeeb7d94b0f89885e6d3630302c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3075929f5bc94b7594cb70cea3be1960": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c6dcc548a138439d8158c390f4e94489", "placeholder": "​", "style": "IPY_MODEL_05ddf97654b644eeb6de10ed873ef0ea", "value": " 2.78M/2.78M [00:00&lt;00:00, 13.4MB/s]"}}, "3327de0966e74c1280985ef9e8c2423b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "337807dbe2534676bdadcbdfa84eba90": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "33c7d52958c14ebe9d69418cc33c9673": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "35dc626d13314bdba882a46d12737b46": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "36b21d06fc964949a8e3dd7a55142e3a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_697cce3190c34039a722deffa985e576", "max": 100000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_db1d83b3a29c4093b4518c0761106c10", "value": 100000}}, "38cfde2f43b54eafbe33d6b1164ce9e8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "39162c06b48d461d92a1a7a9934df7e0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3baf5cf6731e4edcbee71f8405b2b651": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3d6ed55d348146779c3415ad0024fd24", "placeholder": "​", "style": "IPY_MODEL_cab7d65476dc45faa61e7a922d687bb3", "value": "tokenizer.json: 100%"}}, "3d6ed55d348146779c3415ad0024fd24": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "40c490b300a9414a853a83d555f85cc1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4113418872e14677afa56f68dd5f5a8f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_adb4af9e743345f1ace5407d0986d550", "placeholder": "​", "style": "IPY_MODEL_7c0bae1a5ce24ecd9d7c654c913006b0", "value": " 4.59G/4.59G [00:30&lt;00:00, 533MB/s]"}}, "420c28a5f17d4e07ab43082575b89bd0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_885ee075092749bfa265b332981a08f7", "placeholder": "​", "style": "IPY_MODEL_b2f883797baf4ae3a889b14c5443bd77", "value": " 168k/168k [00:00&lt;00:00, 12.3MB/s]"}}, "4300e1ff00494beb97335fd273fd4575": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "47178305d497478890eff7f7f817a3f8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "475f33289d49494d923ecedd00369dc0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4b5718381b2543b5aa33dc78d8796f4d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "52169e1f788f49678255e246430c6703": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5342635acd26466799d7b83b6f156864": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c01e7626a26446058558557cb90b8d38", "max": 167747, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ab5be9d272af4959bdf41b1353515e83", "value": 167747}}, "557ca0721eae4c9db0d26b5810f4a97a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "57d1e57a9f4a4bf0bf30c23639011b17": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c392cc26d65746cd8976d0d0d11988c6", "placeholder": "​", "style": "IPY_MODEL_dc953fd10b9a464b8b0dd4956296f627", "value": " 242/242 [00:00&lt;00:00, 21.4kB/s]"}}, "583418a57f4c4641b2f8a5c8413f17cb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_40c490b300a9414a853a83d555f85cc1", "max": 10300, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1dd923032a31497b9db59d84313b14fd", "value": 10300}}, "591a8ed70b0c45c488c4de1e4d7764bb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "592b05178c97460eacd7df017f88c6fa": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "603473e921644aacb5d6381540a1f1f4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d0c9604eb95d46cfa8cacfc9a1e98a2b", "IPY_MODEL_ceb7f2d78f034aa494d24edfce8dc8c7", "IPY_MODEL_d7effb8cc13b414088187a7dc82d7330"], "layout": "IPY_MODEL_facc8ea81c924abb8acd625b6b50010b"}}, "6303e675499c4c76af60560afb2ff606": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0e8fa2b745a24e68a6d32013ddd3be4f", "placeholder": "​", "style": "IPY_MODEL_18cadb06d06e4a60be3dc513450b4f9d", "value": " 4.97G/4.97G [00:40&lt;00:00, 207MB/s]"}}, "6454bc2f42524042a93ea8062282a3dc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "670c7406c98341529bdf3db1e62186dd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6905360d5b6145f08ffe5bb5fcc2bf1e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_39162c06b48d461d92a1a7a9934df7e0", "placeholder": "​", "style": "IPY_MODEL_1b24f12612dd41109b086cad3b5ea64f", "value": "model-00002-of-00003.safetensors: 100%"}}, "697cce3190c34039a722deffa985e576": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6a9cf722ca3a4082b107dd7035590f62": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6d43e45cf51843c4a1514561dc5c33d7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6f06e96a81244a4da0685e046b10f311": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b2223ef3d64240108ab9746386ae7aae", "placeholder": "​", "style": "IPY_MODEL_8d7c18448ac34ccbb3c43eac3a040cfa", "value": "model.safetensors.index.json: 100%"}}, "78a871c7d1064b99b496526baed75696": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "78cf4c6155984d0086784b5b6c82368e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "78d75df8b14c40089aeab3cde3d0abeb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e1fad35110774a2d8e9fba7baf990f55", "IPY_MODEL_a6da593e98e8495593eaaef2bf2398f5", "IPY_MODEL_b5103ffa2d534500a4b0113508f8723e"], "layout": "IPY_MODEL_2924e7738270423c9889c3a4c6d40c8f"}}, "78e7feba64e243ab9aaf6b537319374e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7914fcfc452a4bc3a15c784fba36bf74": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7c0bae1a5ce24ecd9d7c654c913006b0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8174e9a7862442a290198ea7dcd35f9c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_150354de583b44bcb3e6b435b4dad6e5", "placeholder": "​", "style": "IPY_MODEL_337807dbe2534676bdadcbdfa84eba90", "value": " 1.67M/1.67M [00:00&lt;00:00, 6.25MB/s]"}}, "81a72e47a9c6403bb94ae587b4e88880": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6f06e96a81244a4da0685e046b10f311", "IPY_MODEL_5342635acd26466799d7b83b6f156864", "IPY_MODEL_420c28a5f17d4e07ab43082575b89bd0"], "layout": "IPY_MODEL_828e4bbc6d7c4a1a8736adafba1d4e1c"}}, "828e4bbc6d7c4a1a8736adafba1d4e1c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "84d342f58bcf41bcad2bd70bfcdb0682": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "84d88407030a477082f392135a0cec99": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "850eb69488ad45c1a0fb4ec12fd1dd79": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "85e6e8f8aa56491488b7a398aa91de9b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_11cddbdc86e34258be2e9297a0462a30", "IPY_MODEL_b8469b3b3d374744aac958178271b2f5", "IPY_MODEL_6303e675499c4c76af60560afb2ff606"], "layout": "IPY_MODEL_d926a659eae548b4999ddcfdaaf812f0"}}, "88131ab28c1740d6ba30bd64b523d745": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a59d72f2d4ec404297de561e3ad64a9b", "placeholder": "​", "style": "IPY_MODEL_84d88407030a477082f392135a0cec99", "value": " 707/707 [00:00&lt;00:00, 66.9kB/s]"}}, "885ee075092749bfa265b332981a08f7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8d7c18448ac34ccbb3c43eac3a040cfa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8e487ec7b4b44a308820f595ff619f0f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "902044fbfa8043c5addb6570c3e55056": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "907839fb18044707869284323a33a1f2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6a9cf722ca3a4082b107dd7035590f62", "max": 1671853, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_933a57f5c2ca45fc96a67151b3020319", "value": 1671853}}, "92df2a3115644a0e860a418d9f14470b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_094d09056fb84e3aa05f9dda790d1889", "IPY_MODEL_06d07032489a4af085417b640b627c48", "IPY_MODEL_dd37f3c6eb3e4a92bba78c78f047c169"], "layout": "IPY_MODEL_7914fcfc452a4bc3a15c784fba36bf74"}}, "931adf926ae74e7399a0149996c755d7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "933a57f5c2ca45fc96a67151b3020319": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "950c8b2d34b04debbf529d9807936014": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "969ce9bc31254beea5ce4aac02c8500a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2076b12dd43c4179904790007e3a751a", "IPY_MODEL_a249a3c3815a4bdeb319ebf61794d5fa", "IPY_MODEL_88131ab28c1740d6ba30bd64b523d745"], "layout": "IPY_MODEL_e5c36e37ad0842488f44550057fdf729"}}, "96d7cb7490a54a8a95b756ebf938271c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "98af186ff22f4983aa2c096fa0108f1c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "991d58e11d0d4e0fa79c2f7bbb877d8a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9a55426956ad4eb2a0f147266259e2d0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_950c8b2d34b04debbf529d9807936014", "placeholder": "​", "style": "IPY_MODEL_23d12dfa213a4880abb4448a22984f6f", "value": " 24065/24065 [00:55&lt;00:00, 497.47 examples/s]"}}, "9f0a3dd5bfe64f32a8c9b5cc567c1565": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a2343e3aceb941818f6860b218c41d41": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a249a3c3815a4bdeb319ebf61794d5fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_04b158e6643d41f38d13e3250f52f424", "max": 707, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a2343e3aceb941818f6860b218c41d41", "value": 707}}, "a59d72f2d4ec404297de561e3ad64a9b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a6da593e98e8495593eaaef2bf2398f5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3327de0966e74c1280985ef9e8c2423b", "max": 614, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_557ca0721eae4c9db0d26b5810f4a97a", "value": 614}}, "a9a3efddba7a44febdc3b07e37baa8e8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_931adf926ae74e7399a0149996c755d7", "placeholder": "​", "style": "IPY_MODEL_25b744e44d064163b921c52c572f7566", "value": "merges.txt: 100%"}}, "aa86c16464154022b259f8971ad52104": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "aad271b9eb3547ba9e200c1c1541b544": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ab5be9d272af4959bdf41b1353515e83": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "acf51bee55bc44f58d6158130895bb6c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "adb4af9e743345f1ace5407d0986d550": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aee359b9b49340f8920e9caeb3ec8e78": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_fe9c370b84cb4517ad98782c25b81f32", "IPY_MODEL_d7d58dfe9ab44562a8dd463158669c99", "IPY_MODEL_57d1e57a9f4a4bf0bf30c23639011b17"], "layout": "IPY_MODEL_0407e7f8ca0046f58ff5cbdb80253536"}}, "aef7a2d8a1ce467099790bf4195330d2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0dda0e72d0144188af23e8212a3c995e", "placeholder": "​", "style": "IPY_MODEL_38cfde2f43b54eafbe33d6b1164ce9e8", "value": "chat_template.jinja: 100%"}}, "b02961d85cb84e40a7cd4851aeed2f85": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b0af84609e764d64a6b280f45cf02538": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1553fd7b50ad41349c382d9fa329ce87", "placeholder": "​", "style": "IPY_MODEL_78a871c7d1064b99b496526baed75696", "value": " 11.4M/11.4M [00:00&lt;00:00, 118MB/s]"}}, "b2223ef3d64240108ab9746386ae7aae": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b2f883797baf4ae3a889b14c5443bd77": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b5103ffa2d534500a4b0113508f8723e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_902044fbfa8043c5addb6570c3e55056", "placeholder": "​", "style": "IPY_MODEL_1bf84b95e8f74e4f8a8cef89959a6aaa", "value": " 614/614 [00:00&lt;00:00, 51.1kB/s]"}}, "b5fd6ab47d2044c3af938df1cdd860ac": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b8469b3b3d374744aac958178271b2f5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4300e1ff00494beb97335fd273fd4575", "max": 4974351586, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_aa86c16464154022b259f8971ad52104", "value": 4974351112}}, "b9d53a9529164b3fb47b7009020a4d57": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1525ecf5235541ac941456dac66ea049", "max": 11422654, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_f49e9c65a88d42c791664f86539df755", "value": 11422654}}, "bc8c89d0c5ef4d0b90ffeca1abc3e617": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bf4d2e74ba764e2d9b43b6ecb072aa7c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bfc76b16352a4400a95cc3d8d795d3ee": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3baf5cf6731e4edcbee71f8405b2b651", "IPY_MODEL_b9d53a9529164b3fb47b7009020a4d57", "IPY_MODEL_b0af84609e764d64a6b280f45cf02538"], "layout": "IPY_MODEL_1993f40db52c4ed8bd534bc447d11beb"}}, "c01e7626a26446058558557cb90b8d38": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c11eee6913f04378acf7f396b9f1a734": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c392cc26d65746cd8976d0d0d11988c6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c492a0eebbf64897a65f8c941cab0768": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1a22981c1ee44d728babb4a48a06877f", "IPY_MODEL_0143648b14b241fb826826ffa886fe53", "IPY_MODEL_3075929f5bc94b7594cb70cea3be1960"], "layout": "IPY_MODEL_592b05178c97460eacd7df017f88c6fa"}}, "c60ce442dbc14f50afbdbec5995720a7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b02961d85cb84e40a7cd4851aeed2f85", "placeholder": "​", "style": "IPY_MODEL_84d342f58bcf41bcad2bd70bfcdb0682", "value": " 10.3k/10.3k [00:00&lt;00:00, 1.11MB/s]"}}, "c64d9f07ad6445e880c5567dd721a58d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c6dcc548a138439d8158c390f4e94489": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c8527b9baa824926a1da274d8afd3e8d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_caa243f39fd445ec8586bf49165012ee", "IPY_MODEL_05746b97c0244aa1993550fb8222214b", "IPY_MODEL_9a55426956ad4eb2a0f147266259e2d0"], "layout": "IPY_MODEL_fb211037708447019c3cf54387864711"}}, "c9439543dd954c96ace33c3cfa72554c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "caa243f39fd445ec8586bf49165012ee": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_33c7d52958c14ebe9d69418cc33c9673", "placeholder": "​", "style": "IPY_MODEL_991d58e11d0d4e0fa79c2f7bbb877d8a", "value": "Unsloth: Tokenizing [&quot;text&quot;] (num_proc=12): 100%"}}, "cab7d65476dc45faa61e7a922d687bb3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cb3effe1adb84093a67b1c0b0dba172d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cd014a2a66a44232aa5bf6610464e8b8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ceb7f2d78f034aa494d24edfce8dc8c7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b5fd6ab47d2044c3af938df1cdd860ac", "max": 3, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d26216cb75624e93a5890d34af07b01e", "value": 3}}, "cf35ab02e77844cfb2095538d08ba2f8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6d43e45cf51843c4a1514561dc5c33d7", "placeholder": "​", "style": "IPY_MODEL_98af186ff22f4983aa2c096fa0108f1c", "value": "tokenizer_config.json: 100%"}}, "d0c9604eb95d46cfa8cacfc9a1e98a2b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_16edee89e86042ac85b6954a15ae602d", "placeholder": "​", "style": "IPY_MODEL_f7ebc15cb8ed43e28480b325fdd8bf8c", "value": "Loading checkpoint shards: 100%"}}, "d26216cb75624e93a5890d34af07b01e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d7d58dfe9ab44562a8dd463158669c99": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c9439543dd954c96ace33c3cfa72554c", "max": 242, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_03d13f82c1a448b794cda90755151b93", "value": 242}}, "d7effb8cc13b414088187a7dc82d7330": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e380123fb5844a44b623a1274aa37cba", "placeholder": "​", "style": "IPY_MODEL_cb3effe1adb84093a67b1c0b0dba172d", "value": " 3/3 [00:45&lt;00:00, 13.51s/it]"}}, "d926a659eae548b4999ddcfdaaf812f0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "db1d83b3a29c4093b4518c0761106c10": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "db3cecbb491f4d80aec0970338e93beb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fd9ef41c670a4e16b72def5a2aa157e3", "placeholder": "​", "style": "IPY_MODEL_e05f36698fa54883916b11363d32f72a", "value": " 100000/100000 [00:01&lt;00:00, 131440.81 examples/s]"}}, "dc953fd10b9a464b8b0dd4956296f627": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "dd37f3c6eb3e4a92bba78c78f047c169": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_591a8ed70b0c45c488c4de1e4d7764bb", "placeholder": "​", "style": "IPY_MODEL_e23a15f2894448fa92e7fa56908459c1", "value": " 1.56G/1.56G [00:12&lt;00:00, 908MB/s]"}}, "de4ee34be18e4eea87b41726d9f19e8d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "de77764352fa4693be77c888bf368828": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e05f36698fa54883916b11363d32f72a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e1fad35110774a2d8e9fba7baf990f55": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2d04e92f1f434275b590498ed17815c0", "placeholder": "​", "style": "IPY_MODEL_acf51bee55bc44f58d6158130895bb6c", "value": "special_tokens_map.json: 100%"}}, "e23a15f2894448fa92e7fa56908459c1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e380123fb5844a44b623a1274aa37cba": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e5c36e37ad0842488f44550057fdf729": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e71b6263a6994c72a2da3c917d20fa4d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a9a3efddba7a44febdc3b07e37baa8e8", "IPY_MODEL_907839fb18044707869284323a33a1f2", "IPY_MODEL_8174e9a7862442a290198ea7dcd35f9c"], "layout": "IPY_MODEL_96d7cb7490a54a8a95b756ebf938271c"}}, "eebabe08bcc448a1adf62d568584d15e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_35dc626d13314bdba882a46d12737b46", "placeholder": "​", "style": "IPY_MODEL_04838ba3bb5c42b3aead6dfab186b3a5", "value": "Unsloth: Standardizing formats (num_proc=12): 100%"}}, "efd99a04d6ac406cbd80c3412de9b438": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "efe6270bc0964037bb196a9dbf91ac30": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f00bbf1cbdad433c83663e630df0e6e1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_475f33289d49494d923ecedd00369dc0", "placeholder": "​", "style": "IPY_MODEL_bc8c89d0c5ef4d0b90ffeca1abc3e617", "value": " 4.67k/4.67k [00:00&lt;00:00, 472kB/s]"}}, "f2d4e3c6df994a4289062a9d9ddbde8f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f49e9c65a88d42c791664f86539df755": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f54064d1813248e390121fba848f28de": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f7ebc15cb8ed43e28480b325fdd8bf8c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "facc8ea81c924abb8acd625b6b50010b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fb211037708447019c3cf54387864711": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fd9ef41c670a4e16b72def5a2aa157e3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fe9c370b84cb4517ad98782c25b81f32": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_efd99a04d6ac406cbd80c3412de9b438", "placeholder": "​", "style": "IPY_MODEL_cd014a2a66a44232aa5bf6610464e8b8", "value": "generation_config.json: 100%"}}}}}, "nbformat": 4, "nbformat_minor": 0}