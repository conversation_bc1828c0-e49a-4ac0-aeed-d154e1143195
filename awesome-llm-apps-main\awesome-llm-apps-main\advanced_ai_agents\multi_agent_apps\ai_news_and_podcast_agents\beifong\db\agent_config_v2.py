from agno.storage.sqlite import SqliteStorage
from db.config import get_agent_session_db_path
import json

AGENT_MODEL = "gpt-4o"
AVAILABLE_LANGS = [
    {"code": "en", "name": "English"},
    {"code": "zh", "name": "Chinese"},
    {"code": "de", "name": "German"},
    {"code": "es", "name": "Spanish"},
    {"code": "ru", "name": "Russian"},
    {"code": "ko", "name": "Korean"},
    {"code": "fr", "name": "French"},
    {"code": "ja", "name": "Japanese"},
    {"code": "pt", "name": "Portuguese"},
    {"code": "tr", "name": "Turkish"},
    {"code": "pl", "name": "Polish"},
    {"code": "ca", "name": "Catalan"},
    {"code": "nl", "name": "Dutch"},
    {"code": "ar", "name": "Arabic"},
    {"code": "sv", "name": "Swedish"},
    {"code": "it", "name": "Italian"},
    {"code": "id", "name": "Indonesian"},
    {"code": "hi", "name": "Hindi"},
    {"code": "fi", "name": "Finnish"},
    {"code": "vi", "name": "Vietnamese"},
    {"code": "he", "name": "Hebrew"},
    {"code": "uk", "name": "Ukrainian"},
    {"code": "el", "name": "Greek"},
    {"code": "ms", "name": "Malay"},
    {"code": "cs", "name": "Czech"},
    {"code": "ro", "name": "Romanian"},
    {"code": "da", "name": "Danish"},
    {"code": "hu", "name": "Hungarian"},
    {"code": "ta", "name": "Tamil"},
    {"code": "no", "name": "Norwegian"},
    {"code": "th", "name": "Thai"},
    {"code": "ur", "name": "Urdu"},
    {"code": "hr", "name": "Croatian"},
    {"code": "bg", "name": "Bulgarian"},
    {"code": "lt", "name": "Lithuanian"},
    {"code": "la", "name": "Latin"},
    {"code": "mi", "name": "Maori"},
    {"code": "ml", "name": "Malayalam"},
    {"code": "cy", "name": "Welsh"},
    {"code": "sk", "name": "Slovak"},
    {"code": "te", "name": "Telugu"},
    {"code": "fa", "name": "Persian"},
    {"code": "lv", "name": "Latvian"},
    {"code": "bn", "name": "Bengali"},
    {"code": "sr", "name": "Serbian"},
    {"code": "az", "name": "Azerbaijani"},
    {"code": "sl", "name": "Slovenian"},
    {"code": "kn", "name": "Kannada"},
    {"code": "et", "name": "Estonian"},
    {"code": "mk", "name": "Macedonian"},
    {"code": "br", "name": "Breton"},
    {"code": "eu", "name": "Basque"},
    {"code": "is", "name": "Icelandic"},
    {"code": "hy", "name": "Armenian"},
    {"code": "ne", "name": "Nepali"},
    {"code": "mn", "name": "Mongolian"},
    {"code": "bs", "name": "Bosnian"},
    {"code": "kk", "name": "Kazakh"},
    {"code": "sq", "name": "Albanian"},
    {"code": "sw", "name": "Swahili"},
    {"code": "gl", "name": "Galician"},
    {"code": "mr", "name": "Marathi"},
    {"code": "pa", "name": "Punjabi"},
    {"code": "si", "name": "Sinhala"},
    {"code": "km", "name": "Khmer"},
    {"code": "sn", "name": "Shona"},
    {"code": "yo", "name": "Yoruba"},
    {"code": "so", "name": "Somali"},
    {"code": "af", "name": "Afrikaans"},
    {"code": "oc", "name": "Occitan"},
    {"code": "ka", "name": "Georgian"},
    {"code": "be", "name": "Belarusian"},
    {"code": "tg", "name": "Tajik"},
    {"code": "sd", "name": "Sindhi"},
    {"code": "gu", "name": "Gujarati"},
    {"code": "am", "name": "Amharic"},
    {"code": "yi", "name": "Yiddish"},
    {"code": "lo", "name": "Lao"},
    {"code": "uz", "name": "Uzbek"},
    {"code": "fo", "name": "Faroese"},
    {"code": "ht", "name": "Haitian creole"},
    {"code": "ps", "name": "Pashto"},
    {"code": "tk", "name": "Turkmen"},
    {"code": "nn", "name": "Nynorsk"},
    {"code": "mt", "name": "Maltese"},
    {"code": "sa", "name": "Sanskrit"},
    {"code": "lb", "name": "Luxembourgish"},
    {"code": "my", "name": "Myanmar"},
    {"code": "bo", "name": "Tibetan"},
    {"code": "tl", "name": "Tagalog"},
    {"code": "mg", "name": "Malagasy"},
    {"code": "as", "name": "Assamese"},
    {"code": "tt", "name": "Tatar"},
    {"code": "haw", "name": "Hawaiian"},
    {"code": "ln", "name": "Lingala"},
    {"code": "ha", "name": "Hausa"},
    {"code": "ba", "name": "Bashkir"},
    {"code": "jw", "name": "Javanese"},
    {"code": "su", "name": "Sundanese"},
    {"code": "yue", "name": "Cantonese"},
]

TOGGLE_UI_STATES = [
    "show_sources_for_selection",
    "show_script_for_confirmation",
    "show_banner_for_confirmation",
    "show_audio_for_confirmation",
]

AGENT_DESCRIPTION = "You are name is Beifong, a helpful assistant that guides users to choose best sources for the podcast and allow them to generate the podcast script."

# sacred commandments, touch these with devotion.
AGENT_INSTRUCTIONS = [
    "Guide users to choose the best sources for the podcast and allow them to generate the podcast script and images for the podcast and audio for the podcast.",
    "1. Make sure you get the intent of the topic from the user. It can be fuzzy and contain spelling mistakes from the user, so act as intent detection and get clarification only if needed.",
    "1a. Keep this phase as quick as possible. Try to avoid too many back and forth conversations. Try to infer the intent if you're confident you can go right to the next search phase without confirming with the user. The less back and forth, the better for the user experience.",
    "2. Once you understand the intent of the topic, use the available search tools (we have search agent where you can pass the query and along with appropriate prompt search agent has lot of search tools and api access which you don't have so you can instruct if needed) to get diverse and high-quality sources for the topic.  and also make sure give appropriate short title for the chat and update the chat title using update_chat_title tool",
    "2a. Once we receive the search results. do the full scraping using appropriate scraping tool to get the full text of the each source.",
    "2b. Don't do back and forth during this source collection process with the user. Either you have the results or not, then inform the user and ask the user if they want to try again or give more details about the topic.",
    "3. Once you have the results, ask the user to pick which sources they want to use for the podcast. You don't have to list out the found sources; just tell them to pick from the list of sources that will be visible in the UI.",
    "4. User do the selection by selecting the index of sources from the list of sources that will be visible in the UI. so response will be a list of indices of sources selected by the user. sometime user will also send prefered language for the podcast along with the selection."
    "4a. You have to use user_source_selection tool to update the user selection. and after this point immediately switch off any UI states.",
    "4b. If user sends prefered language for the podcast along with the selection, you have to use update_language tool to update the user language if not leave it default is english. and after this point immediately switch off any UI states.",
    "5. Once you we have the confimed selection from the user let's immediatly call the podcast script agent to generate the podcast script for the given sources and query and perfered language (pass full lanage name).",
    "5a. Once podcast script is ready switch on the podcast_script UI state and so user will see if the generated podcast script is fine or not, you dont' have to show the script active podasshow_script_for_confirmation will take care of it.",
    "6. Once you got the confirmation from the user (throug UI) let's immediatly call the image generation agent to generate the image for the given podcast script.",
    "6a. Once image generation successfully generated switch on the image UI state and so user will see if the generated image is fine or not, you just ask user to confirm the image through UI. show_banner_for_confirmation will take care of it."
    "7. Once you got the confirmation from the user (throug UI) let's immediatly call the audio generation agent to generate the audio for the given podcast script.",
    "7a. Once audio generation successfully generated switch on the audio UI state and so user will see if the generated audio is fine or not, you just ask user to confirm the audio through UI. show_audio_for_confirmation will take care of it."
    "8. Once you got the confirmation from the user for audio (throug UI) let's immediatly call the mark_session_finished tool to mark the session as finished and if finish is successful then no further conversation are allowed and only new session can be started.",
    "8a. It's important mark_session_finished should be called only when we have all the stages search->selection->script->image->audio are completed."
    "APPENDIX:",
    "1. You can enable appropriate UI states using the ui_manager tool, which takes [state_type, active] as input, and it takes care of the appropriate UI state for activating appropriate UI state.",
    f"1a. Available UI state types: {TOGGLE_UI_STATES}",
    "1b. During the conversation, at any place you feel a UI state is not necessary, you can disable it using the ui_manager tool by setting active to False. For switching off all states, pass all to False.",
    f"2. Supported Languges: {json.dumps(AVAILABLE_LANGS)}",
    "3. Search Agent has a lot off tools, so you can instruct the search query as prompt to get the best results as because search agent has lot of tools you can instruct instead of directly passing the query to search agent when required.",
    "4. You are not allowed to include year or date in your seach query construction for the search agent unless that request explicilty with yeear or date  come for the users.",
]
DB_PATH = "databases"
PODCAST_DIR = "podcasts"
PODCAST_IMG_DIR = PODCAST_DIR + "/images"
PODCAST_AUIDO_DIR = PODCAST_DIR + "/audio"
PODCAST_RECORDINGS_DIR = PODCAST_DIR + "/recordings"

INITIAL_SESSION_STATE = {
    "search_results": [],
    "show_sources_for_selection": False,
    "show_script_for_confirmation": False,
    "generated_script": {},
    "selected_language": {"code": "en", "name": "English"},
    "available_languages": AVAILABLE_LANGS,
    "banner_images": [],
    "banner_url": "",
    "audio_url": "",
    "title": "Untitled",
    "created_at": "",
    "finished": False,
    "show_banner_for_confirmation": False,
    "show_audio_for_confirmation": False,
}

STORAGE = SqliteStorage(table_name="podcast_sessions", db_file=get_agent_session_db_path())
