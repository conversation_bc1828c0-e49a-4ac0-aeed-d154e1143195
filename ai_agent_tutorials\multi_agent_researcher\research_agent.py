import os
from dotenv import load_dotenv
from phi.assistant import Assistant
from phi.llm.base import LLM
from phi.tools.hackernews import HackerNews
from groq import Groq
from pydantic import Field, PrivateAttr
import streamlit as st
from typing import List, Dict

load_dotenv()

# Define GroqChat class
class GroqChat(LLM):
    model: str = Field(default="llama-3.1-8b-instant")
    api_key: str = Field(...)

    _client: Groq = PrivateAttr()

    def __init__(self, api_key: str, model: str = "llama-3.1-8b-instant"):
        super().__init__(model=model, api_key=api_key)
        self._client = Groq(api_key=api_key)

    def chat(self, messages: List[Dict[str, str]]) -> str:
        completion = self._client.chat.completions.create(
            messages=messages,
            model=self.model
        )
        return completion.choices[0].message.content

    def complete(self, prompt: str) -> str:
        return self.chat([{"role": "user", "content": prompt}])

    def response(self, messages: List[Dict[str, str]]) -> str:
        return self.chat(messages)

# Streamlit App Setup
st.title("Multi-Agent AI Researcher 🔍🤖")
st.caption("Research top stories and users on HackerNews and write reports with GroqChat.")

# Sidebar for settings
st.sidebar.header("Settings")
groq_api_key = st.sidebar.text_input("Enter Groq API Key", value=os.getenv("GROQ_API_KEY", ""), type="password")
model_selection = st.sidebar.selectbox("Choose AI Model", [
    "llama-3.1-8b-instant",
    "llama3-8b-8192",
    "llama-3.3-70b-versatile",
    "llama-3.2-3b-preview",
    "llama3-groq-70b-8192-tool-use-preview",
    "llama-3.2-1b-preview",
    "llama-3.2-11b-vision-preview"
])

# Initialize Assistants if API key is provided
if groq_api_key:
    llm = GroqChat(api_key=groq_api_key, model=model_selection)

    # Define HackerNews tools
    hackernews_tool = HackerNews()

    story_researcher = Assistant(
        name="HackerNews Story Researcher",
        role="Researches top stories on HackerNews.",
        llm=llm,
        tools=[hackernews_tool],
        instructions=[
            "Search for the top stories on HackerNews using the provided tools.",
            "Summarize the key details of each story, such as title, author, and score.",
            "Provide a concise list of top stories for further exploration."
        ]
    )

    user_researcher = Assistant(
        name="HackerNews User Researcher",
        role="Analyzes HackerNews users' activities.",
        llm=llm,
        tools=[hackernews_tool],
        instructions=[
            "Search for details about a specific HackerNews user.",
            "Summarize their recent activities, including comments and submissions."
        ]
    )

    # Combined team
    hn_assistant = Assistant(
        name="HackerNews Research Team",
        team=[story_researcher, user_researcher],
        llm=llm,
        description="A team to research HackerNews stories and users."
    )

    # Input field for queries
    query = st.text_input("Enter your research query")

    # Button for executing research
    if st.button("Research HackerNews") and query:
        with st.spinner("Researching..."):
            response = hn_assistant.run(query, stream=False)
            st.write(response)
else:
    st.sidebar.warning("Please enter a valid Groq API key.")
