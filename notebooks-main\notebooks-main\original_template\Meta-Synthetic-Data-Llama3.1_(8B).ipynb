{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Placehoder"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Placeholder"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Placeholder"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {"id": "dtV7V3HkfLZN"}, "source": ["### Synthetic-data-kit"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "K1hV9-YHp0FQ", "outputId": "834dd4db-df5f-408f-9744-ff07a34e0f46"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["nohup: redirecting stderr to stdout\n"]}], "source": ["# Load and run the model using vllm\n", "# we prepend \"nohup\" and postpend \"&\" to make the Colab cell run in background\n", "! nohup python -m vllm.entrypoints.openai.api_server \\\n", "                  --model unsloth/Llama-3.1-8B-Instruct-unsloth-bnb-4bit \\\n", "                  --trust-remote-code \\\n", "                  --dtype half \\\n", "                  --quantization bitsandbytes \\\n", "                  --max-model-len 10000 \\\n", "                  --tensor-parallel-size 1 \\\n", "                  --gpu-memory-utilization 0.7 \\\n", "                  --enable-chunked-prefill \\\n", "                  --port 8000 \\\n", "                  > vllm.log &"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "qiJcnjILx5kA", "outputId": "47274e9c-8469-4556-cd82-6099369a651a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["To enable the following instructions: AVX2 AVX512F AVX512_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "To enable the following instructions: AVX2 AVX512F AVX512_VNNI FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "WARNING 04-29 18:42:44 [config.py:2972] Casting torch.bfloat16 to torch.float16.\n", "WARNING 04-29 18:42:44 [config.py:2972] Casting torch.bfloat16 to torch.float16.\n", "WARNING 04-29 18:42:44 [config.py:2972] Casting torch.bfloat16 to torch.float16.\n", "INFO 04-29 18:43:00 [config.py:2003] Chunked prefill is enabled with max_num_batched_tokens=2048.\n", "E0000 00:00:1745952186.991164    2914 cuda_blas.cc:1418] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n", "E0000 00:00:1745952186.991164    2914 cuda_blas.cc:1418] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n", "INFO 04-29 18:43:16 [weight_utils.py:265] Using model weights format ['*.safetensors']\n", "INFO 04-29 18:43:16 [weight_utils.py:265] Using model weights format ['*.safetensors']\n", "INFO 04-29 18:43:16 [weight_utils.py:265] Using model weights format ['*.safetensors']\n", "Loading safetensors checkpoint shards:   0% Completed | 0/1 [00:00<?, ?it/s]\n", "INFO 04-29 18:43:35 [gpu_model_runner.py:1347] Model loading took 5.5859 GiB and 20.094828 seconds\n", "INFO 04-29 18:43:35 [gpu_model_runner.py:1347] Model loading took 5.5859 GiB and 20.094828 seconds\n", "INFO 04-29 18:43:35 [gpu_model_runner.py:1347] Model loading took 5.5859 GiB and 20.094828 seconds\n", "INFO 04-29 18:43:49 [backends.py:430] Dynamo bytecode transform time: 13.67 s\n", "INFO 04-29 18:43:53 [backends.py:136] Cache the graph of shape None for later use\n", "INFO 04-29 18:43:53 [backends.py:136] Cache the graph of shape None for later use\n", "INFO 04-29 18:43:53 [backends.py:136] Cache the graph of shape None for later use\n", "INFO 04-29 18:43:53 [backends.py:136] Cache the graph of shape None for later use\n", "INFO 04-29 18:43:53 [backends.py:136] Cache the graph of shape None for later use\n", "INFO 04-29 18:43:53 [backends.py:136] Cache the graph of shape None for later use\n", "INFO 04-29 18:43:53 [backends.py:136] Cache the graph of shape None for later use\n", "INFO 04-29 18:44:31 [backends.py:148] Compiling a graph for general shape takes 41.48 s\n", "INFO 04-29 18:44:31 [backends.py:148] Compiling a graph for general shape takes 41.48 s\n", "INFO 04-29 18:44:31 [backends.py:148] Compiling a graph for general shape takes 41.48 s\n", "INFO 04-29 18:44:31 [backends.py:148] Compiling a graph for general shape takes 41.48 s\n", "INFO 04-29 18:44:51 [kv_cache_utils.py:637] Maximum concurrency for 10,000 tokens per request: 16.71x\n", "INFO 04-29 18:44:51 [kv_cache_utils.py:637] Maximum concurrency for 10,000 tokens per request: 16.71x\n", "INFO 04-29 18:44:51 [kv_cache_utils.py:637] Maximum concurrency for 10,000 tokens per request: 16.71x\n", "INFO 04-29 18:44:51 [kv_cache_utils.py:637] Maximum concurrency for 10,000 tokens per request: 16.71x\n", "INFO 04-29 18:44:51 [kv_cache_utils.py:637] Maximum concurrency for 10,000 tokens per request: 16.71x\n", "INFO 04-29 18:44:51 [kv_cache_utils.py:637] Maximum concurrency for 10,000 tokens per request: 16.71x\n", "INFO 04-29 18:44:51 [kv_cache_utils.py:637] Maximum concurrency for 10,000 tokens per request: 16.71x\n", "INFO 04-29 18:44:51 [kv_cache_utils.py:637] Maximum concurrency for 10,000 tokens per request: 16.71x\n", "INFO 04-29 18:44:51 [kv_cache_utils.py:637] Maximum concurrency for 10,000 tokens per request: 16.71x\n"]}], "source": ["# tail vllm logs. Check server has been started correctly\n", "!while ! grep -q \"Application startup complete\" vllm.log; do tail -n 1 vllm.log; sleep 5; done"]}, {"cell_type": "markdown", "metadata": {"id": "NDGMqFWCiwMT"}, "source": ["Optional: Function to check if vllm server is running. Change False to True and run cell"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "1RXVBeib0kon"}, "outputs": [], "source": ["if False:\n", "  def is_vllm_server_running(api_base_url=None):\n", "      \"\"\"Simply check if VLLM server is running and reachable.\"\"\"\n", "      print(api_base_url)\n", "      try:\n", "          response = requests.get(f\"{api_base_url}/models\", timeout=2)\n", "          return response.status_code == 200\n", "      except:\n", "          return False\n", "  is_running = is_vllm_server_running(\"http://localhost:8000/v1\")\n", "  if is_running:\n", "      print(f\"VLLM server is running.\")\n", "  else:\n", "      print(f\"VLLM server is not available.\")"]}, {"cell_type": "markdown", "metadata": {"id": "woFPIrnMfZgK"}, "source": ["Create data directories"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "Mni2qxzGRkSW"}, "outputs": [], "source": ["!mkdir -p data/{pdf,html,youtube,docx,ppt,txt,output,generated,cleaned,final}"]}, {"cell_type": "markdown", "metadata": {"id": "go18XrL7fd2e"}, "source": ["### Ingest source file\n", "\n", "Ingest source file \"https://ai.meta.com/blog/llama-4-multimodal-intelligence/\" . Can also use pdf, docx, ppt and youtube video"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_9FCs0cq6Lwc", "outputId": "298e1c7b-811d-47fa-a9ec-935397b31acf"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Text successfully extracted to data/output/ai_meta_com.txt\n"]}], "source": ["from synthetic_data_kit.core.ingest import process_file\n", "import os\n", "\n", "# Set variables directly\n", "doc_source = \"https://ai.meta.com/blog/llama-4-multimodal-intelligence/\"\n", "output_dir = \"data/output\"\n", "name = None  # Let the process determine the filename automatically\n", "config = ctx.config if 'ctx' in locals() else None  # Use ctx if available, otherwise None\n", "\n", "try:\n", "    # Call process_file directly\n", "    output_path = process_file(doc_source, output_dir, name, config)\n", "    print(f\"Text successfully extracted to {output_path}\")\n", "except Exception as e:\n", "    print(f\"Error: {e}\")"]}, {"cell_type": "markdown", "metadata": {"id": "DmBog9CNfzZC"}, "source": ["### Generate QA pairs\n", "\n", "Generate QA pairs with the help of vllm and Llama-3.1-8B-Instruct-unsloth-bnb-4bit.\n", "set num_pairs to the number of required pairs"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "0VL8q7aqTEbJ", "outputId": "b89cf4ad-9bcf-49b1-eca2-dae8ba036371"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Proceeding with process_file call...\n", "Processing 1 chunks to generate QA pairs...\n", "Batch processing complete.\n", "Generated 10 QA pairs total\n", "Saving result to data/generated/ai_meta_com_qa_pairs.json\n", "Successfully wrote test file to data/generated/test_write.json\n", "Successfully wrote result to data/generated/ai_meta_com_qa_pairs.json\n", "Content saved to data/generated/ai_meta_com_qa_pairs.json\n", "\n", "Generated content (first 500 chars):\n", "{\n", "  \"summary\": \"Here is a summary of the document in 3-5 sentences, focusing on the main topic and key concepts:\\n\\nMeta AI has announced the Llama 4 herd, a new era of natively multimodal AI innovation, with the release of three models: Llama 4 Scout, Llama 4 Maverick, and Llama 4 Behemoth. These models are designed to enable people to build more personalized multimodal experiences, with Llama 4 Scout and Llama 4 Maverick offering industry-leading performance in image and text understanding, an...\n"]}], "source": ["from synthetic_data_kit.core.create import process_file\n", "import os\n", "import requests\n", "import json\n", "\n", "# Set parameters\n", "input_file = \"data/output/ai_meta_com.txt\"\n", "output_dir = \"data/generated\"\n", "config_path = ctx.config_path if 'ctx' in locals() else None  # Use ctx if available\n", "api_base = \"http://localhost:8000/v1\"  # Default VLLM API endpoint\n", "model = \"unsloth/Llama-3.1-8B-Instruct-unsloth-bnb-4bit\"\n", "content_type = \"qa\"\n", "num_pairs = 10\n", "verbose = False\n", "\n", "# Read the content of the input file\n", "with open(input_file, 'r') as f:\n", "    text_content = f.read()\n", "\n", "\n", "print(\"\\nGenerating QA pairs...\")\n", "try:\n", "    # Call process_file directly with all parameters\n", "    output_path = process_file(\n", "        input_file,\n", "        output_dir,\n", "        config_path,\n", "        api_base,\n", "        model,\n", "        content_type,\n", "        num_pairs,\n", "        verbose\n", "    )\n", "\n", "    if output_path:\n", "        print(f\"Content saved to {output_path}\")\n", "\n", "        # Additionally, print the content of the generated file\n", "        try:\n", "            with open(output_path, 'r') as f:\n", "                output_content = f.read()\n", "            print(\"\\nGenerated content (first 500 chars):\")\n", "            print(output_content[:500] + \"...\" if len(output_content) > 500 else output_content)\n", "        except Exception as e:\n", "            print(f\"Could not read generated file: {e}\")\n", "    else:\n", "        print(\"No output was generated\")\n", "except Exception as e:\n", "    print(f\"Error: {e}\")"]}, {"cell_type": "markdown", "metadata": {"id": "EkWS4c4hgQ89"}, "source": ["### Curate Data Pairs"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iH2oDbeRUGkp", "outputId": "1c85499c-f5a1-4305-9adc-5d4cfd3dcd26"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\\Curating generated pairs...\n", "Processing 1 batches of QA pairs...\n", "Batch processing complete.\n", "Rated 10 QA pairs\n", "Retained 10 pairs (threshold: 7.0)\n", "Average score: 8.2\n", "Cleaned content saved to data/cleaned/ai_meta_com_qa_pairs_cleaned.json\n", "\n", "Generated content (first 500 chars):\n", "{\n", "  \"summary\": \"Here is a summary of the document in 3-5 sentences, focusing on the main topic and key concepts:\\n\\nMeta AI has announced the Llama 4 herd, a new era of natively multimodal AI innovation, with the release of three models: Llama 4 Scout, Llama 4 Maverick, and Llama 4 Behemoth. These models are designed to enable people to build more personalized multimodal experiences, with Llama 4 Scout and Llama 4 Maverick offering industry-leading performance in image and text understanding, an...\n"]}], "source": ["from synthetic_data_kit.core.curate import curate_qa_pairs\n", "\n", "# Set all parameters directly\n", "input_file = \"data/generated/ai_meta_com_qa_pairs.json\"\n", "cleaned_dir = \"data/cleaned\"\n", "base_name = os.path.splitext(os.path.basename(input_file))[0]\n", "output = os.path.join(cleaned_dir, f\"{base_name}_cleaned.json\")\n", "\n", "threshold = None  # Use default threshold\n", "config_path = ctx.config_path if 'ctx' in locals() else None  # Use ctx if available\n", "verbose = False\n", "\n", "print(\"\\nCurating generated pairs...\")\n", "\n", "try:\n", "    # Call curate_qa_pairs directly\n", "    result_path = curate_qa_pairs(\n", "        input_file,\n", "        output,\n", "        threshold,\n", "        api_base,\n", "        model,\n", "        config_path,\n", "        verbose\n", "    )\n", "\n", "    print(f\"Cleaned content saved to {result_path}\")\n", "\n", "    # Display the content of the cleaned file\n", "    try:\n", "        with open(result_path, 'r') as f:\n", "            output_content = f.read()\n", "        print(\"\\nGenerated content (first 500 chars):\")\n", "        print(output_content[:500] + \"...\" if len(output_content) > 500 else output_content)\n", "    except Exception as e:\n", "        print(f\"Could not read cleaned file: {e}\")\n", "except Exception as e:\n", "    print(f\"Error: {e}\")"]}, {"cell_type": "markdown", "metadata": {"id": "AGNU5woJgTwl"}, "source": ["### Save to chatML format"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "oSOKEeQMaRCY", "outputId": "967e7979-2298-413d-8dd2-5fae81eb8871"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Converted to ft format and saved to data/final/ai_meta_com_qa_pairs_cleaned_ft.json\n", "\n", "Converted content (first 500 chars):\n", "[\n", "  {\n", "    \"messages\": [\n", "      {\n", "        \"role\": \"system\",\n", "        \"content\": \"You are a helpful assistant.\"\n", "      },\n", "      {\n", "        \"role\": \"user\",\n", "        \"content\": \"What is the name of the first open-weight natively multimodal models with unprecedented context length support in the Llama 4 series?\"\n", "      },\n", "      {\n", "        \"role\": \"assistant\",\n", "        \"content\": \"Llama 4 Scout and Llama 4 Maverick\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"messages\": [\n", "      {\n", "        \"role\": \"system\",\n", "        \"content\": ...\n"]}], "source": ["from synthetic_data_kit.core.save_as import convert_format\n", "import os\n", "import json\n", "\n", "# Set all parameters directly\n", "input_file = \"data/cleaned/ai_meta_com_qa_pairs_cleaned.json\"\n", "format_type = \"ft\"  # OpenAI fine-tuning format\n", "storage_format = \"json\"  # Default storage format\n", "\n", "# Set up output path\n", "final_dir = \"data/final\"\n", "#os.makedirs(final_dir, exist_ok=True)\n", "base_name = os.path.splitext(os.path.basename(input_file))[0]\n", "\n", "# Determine output file path\n", "if storage_format == \"hf\":\n", "    output_path = os.path.join(final_dir, f\"{base_name}_{format_type}_hf\")\n", "else:\n", "    if format_type == \"jsonl\":\n", "        output_path = os.path.join(final_dir, f\"{base_name}.jsonl\")\n", "    else:\n", "        output_path = os.path.join(final_dir, f\"{base_name}_{format_type}.json\")\n", "\n", "# Load config if available\n", "config = ctx.config if 'ctx' in locals() else None\n", "\n", "try:\n", "    # Call convert_format directly\n", "    result_path = convert_format(\n", "        input_file,\n", "        output_path,\n", "        format_type,\n", "        config,\n", "        storage_format=storage_format\n", "    )\n", "\n", "    print(f\"Converted to {format_type} format and saved to {result_path}\")\n", "\n", "    # Display the content of the converted file\n", "    try:\n", "        if os.path.isfile(result_path):\n", "            with open(result_path, 'r') as f:\n", "                output_content = f.read()\n", "            print(\"\\nConverted content (first 500 chars):\")\n", "            print(output_content[:500] + \"...\" if len(output_content) > 500 else output_content)\n", "        else:\n", "            # For HF datasets, it's a directory\n", "            print(f\"\\nSaved as HF dataset directory at {result_path}\")\n", "            if os.path.exists(os.path.join(result_path, \"dataset_info.json\")):\n", "                with open(os.path.join(result_path, \"dataset_info.json\"), 'r') as f:\n", "                    info = json.load(f)\n", "                print(f\"Dataset info: {info}\")\n", "    except Exception as e:\n", "        print(f\"Could not read converted file: {e}\")\n", "\n", "except Exception as e:\n", "    print(f\"Error: {e}\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kwXNRq_Bbpxb", "outputId": "b388fe75-e50c-4a8d-9a56-707723a4d509"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to terminate the VLLM server\n"]}], "source": ["# kill vllm server. Takes around 5 seconds.\n", "print(\"Attempting to terminate the VLLM server\")\n", "!pkill -f \"vllm.entrypoints.openai.api_server\""]}, {"cell_type": "markdown", "metadata": {"id": "Y4nXiXBRgoOf"}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 351, "referenced_widgets": ["ab6ba4f352de4f56abf4a6ff91d8fefa", "d319d99ce8e84c858be0fc5d0ee53a22", "f6c7ae8c66ea4323848e3da703d8c98d", "65441e44919b40248af806dbc37dcf78", "543c5052509f481f9922d851a5d03dad", "cc0876b62bd54c8fa02fc3148ced4f1b", "22ab3beb635a4fc4b9787d7857da9735", "42b76c2b475244709e06992cb1bc2b60", "079045628e3b4ed2b2ec06cc065d5de1", "71d3d14817f94ac9b69d0bca784fc171", "8b4e647f7b374d01bb4f019413f1d771", "23a43154454f4844b316614fbfc52879", "60f56871f426425bbeeb18cfe9856a8e", "9528c2e5e6c14019af14f165b241a75c", "34df1a077e10481aa7d0fe02df084a50", "c230bc1042f44b22aa33f5424719920a", "1e56214ea0654ea39dd2c36da1589109", "503a0aaec9964edd83eb9d55db83a425", "8eebd4e40b4643ce8c30c080af4de97d", "b846789249a34ab29594b442d7887972", "22ac359d555442b790c946d547e47413", "ca05ea6a911f47cd83be9b84c76cf476", "0c4b12071a864b248ecc1e8a59605324", "fd78c8249bb34c68bcee135f542d9ead", "6672d3af7dda4a9b81482729744ca8ec", "f214e13094854ed2a7c7f5da2fad6157", "c8b47800e7cc46d8839ec9658e498425", "b92edd7809b84a4db8288b8de3b0274c", "0e287a989a84403cbe8c16e298bfaf20", "b15238421c48467d8e74b7c76aed6c42", "0d7a249c7b0d408aa86037d4f0689e45", "069296ecd4d047808d0fdb6350c4e061", "d145f007f4d74467b71d7044e42eafe4", "40e360d77f1b48139460d1e4d0f7fb9f", "6be688d97f434348a453da3956f708a8", "301a948c88a0490897cf9cfb3c0125d1", "cf96a86f56ed44d19623c07190e3eb94", "803e1a09fe1f48bdbdf13683f29331e5", "7fedda50349342c0a2ef021f030cd82e", "3499305b74a94d91a1812c8545136e77", "ce9bbe4968714f9ba27c731a08f3aa36", "29e5efda305941be842d917391a13e42", "95c593c9249245b0816abd70e5c3377e", "3830709475f94c72842d1fdac9d7f253", "fae552d7f28e4a18b4a92288f2336d6d", "f4e093d3b96f472c9de2356b3d598484", "a8b76b225cba465c8a4c451cbb578065", "a322a3d796e24c47a29c7f78559dac23", "8590a4973e3c4dc4920d1d33ea979e14", "dbde86e0667341a697476e4c43ae0f15", "1fc11f3e552c4545bc8489c80862e535", "cd97acdfd05a4205b39634f5e0e4cd4d", "5c03f68bc3004e62b1fd9d779ddb63f7", "e7ac1b007eed4f288cb23c9f64bae637", "f473a9b930524635b73ea767d55060a3"]}, "id": "7NFJ3OaicA2P", "outputId": "630b8cb8-281a-4d4b-a8ac-313d963f2bcd"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "Unsloth: Failed to patch SmolVLMForConditionalGeneration forward function.\n", "🦥 Unsloth Zoo will now patch everything to make training faster!\n", "INFO 04-29 16:46:02 [__init__.py:239] Automatically detected platform cuda.\n", "==((====))==  Unsloth 2025.4.1: Fast Llama patching. Transformers: 4.51.3. vLLM: 0.8.2.\n", "   \\\\   /|    NVIDIA L4. Num GPUs = 1. Max memory: 22.161 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.6.0+cu124. CUDA: 8.9. CUDA Toolkit: 12.4. Triton: 3.2.0\n", "\\        /    Bfloat16 = TRUE. FA [Xformers = 0.0.29.post3. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ab6ba4f352de4f56abf4a6ff91d8fefa", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/2.35G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "23a43154454f4844b316614fbfc52879", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/234 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0c4b12071a864b248ecc1e8a59605324", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/54.7k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "40e360d77f1b48139460d1e4d0f7fb9f", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/17.2M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fae552d7f28e4a18b4a92288f2336d6d", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/454 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "max_seq_length = 2048 # Choose any! We auto support RoPE Scaling internally!\n", "dtype = None # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+\n", "load_in_4bit = True # Use 4bit quantization to reduce memory usage. Can be False.\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/mistral-7b-bnb-4bit\",\n", "    \"unsloth/mistral-7b-instruct-v0.2-bnb-4bit\",\n", "    \"unsloth/llama-2-7b-bnb-4bit\",\n", "    \"unsloth/llama-2-13b-bnb-4bit\",\n", "    \"unsloth/codellama-34b-bnb-4bit\",\n", "    \"unsloth/tinyllama-bnb-4bit\",\n", "    \"unsloth/gemma-7b-bnb-4bit\", # New Google 6 trillion tokens model 2.5x faster!\n", "    \"unsloth/gemma-2b-bnb-4bit\",\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/Llama-3.2-3B-Instruct\", # Choose ANY! eg teknium/OpenHermes-2.5-Mistral-7B\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit,\n", "    # token = \"hf_...\", # use one if using gated models like meta-llama/Llama-2-7b-hf\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "agHJ-U<PERSON><PERSON>hl"}, "source": ["We now add LoRA adapters so we only need to update 1 to 10% of all parameters!"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fdJ5NSg0cvLO", "outputId": "8738485b-f947-4afa-ea14-ee042c000915"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth 2025.4.1 patched 28 layers with 28 QKV layers, 28 O layers and 28 MLP layers.\n"]}], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 16, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 16,\n", "    lora_dropout = 0, # Supports any, but = 0 is optimized\n", "    bias = \"none\",    # Supports any, but = \"none\" is optimized\n", "    # [NEW] \"unsloth\" uses 30% less VRAM, fits 2x larger batch sizes!\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for very long context\n", "    random_state = 3407,\n", "    use_rslora = False,  # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "zXuiq_Qjc7iC"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We now use the `ChatML` format for conversation style finetunes. We use [Open Assistant conversations](https://huggingface.co/datasets/philschmid/guanaco-sharegpt-style) in ShareGPT style. ChatML renders multi turn conversations like below:\n", "\n", "```\n", "<|im_start|>system\n", "You are a helpful assistant.<|im_end|>\n", "<|im_start|>user\n", "What's the capital of France?<|im_end|>\n", "<|im_start|>assistant\n", "Paris.\n", "```\n", "\n", "**[NOTE]** To train only on completions (ignoring the user's input) read TRL's docs [here](https://huggingface.co/docs/trl/sft_trainer#train-on-completions-only).\n", "\n", "We use our `get_chat_template` function to get the correct chat template. We support `zephyr, chatml, mistral, llama, alpaca, vicuna, vicuna_old` and our own optimized `unsloth` template.\n", "\n", "Normally one has to train `<|im_start|>` and `<|im_end|>`. We instead map `<|im_end|>` to be the EOS token, and leave `<|im_start|>` as is. This requires no additional training of additional tokens.\n", "\n", "Note ShareGPT uses `{\"from\": \"human\", \"value\" : \"Hi\"}` and not `{\"role\": \"user\", \"content\" : \"Hi\"}`, so we use `mapping` to map it.\n", "\n", "For text completions like novel writing, try this [notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Mistral_(7B)-Text_Completion.ipynb)."]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 81, "referenced_widgets": ["267afe0c8fa34a86952f0f71e29594d5", "5d1138410605484bb760816513ba0d66", "0a845d380f334775bdeb75f0f678ebb7", "c12ad669eb6049e88f68383332be4a6f", "0f9c94795f084b9198f7a4646b266804", "ac29467028fb4a5fbf020e2adc629cd9", "4cdb1773a6344a97b36f305f8f0e5b81", "63e0016129124d8594c10e8b242d6e55", "a428fa2504b243dca5a0a8a31882bf4e", "22585ec7d5b544b28ccd4359ceaf40cf", "20804bce2f9f4faa9450c920ca92e96c", "f25e711fe26149aeb2806319f6ec50ba", "47135f7c1edb40659e4bd3a3f7ca767e", "9e34bc6269d64be8a198f6deeffe80f0", "0e05421251204969820a7b2462ab4942", "fced6c677e434bccaa2351fd8c09bf2f", "628738149eb2408b951f3fac42eb27e6", "9d8d72dcd36e483a81965682bb6c8dc3", "4e697867516d413da3078374f259e12b", "ce2d74f898a849da988bef9e0151da38", "f0f74a0cb99548c09e87dac3b768e8c4", "478bf5068443445b9343521a23a97977"]}, "id": "tQXQUtfmczE0", "outputId": "2cd0ae8e-1e85-4104-fe6d-ec830630cfdd"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "267afe0c8fa34a86952f0f71e29594d5", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split: 0 examples [00:00, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f25e711fe26149aeb2806319f6ec50ba", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/10 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth.chat_templates import get_chat_template\n", "\n", "# tokenizer = get_chat_template(\n", "#     tokenizer,\n", "#     chat_template = \"chatml\", # Supports zephyr, chatml, mistral, llama, alpaca, vicuna, vicuna_old, unsloth\n", "#     mapping = {\"role\" : \"from\", \"content\" : \"value\", \"user\" : \"human\", \"assistant\" : \"gpt\"}, # ShareGPT style\n", "#     map_eos_token = True, # Maps <|im_end|> to </s> instead\n", "# )\n", "\n", "def formatting_prompts_func(examples):\n", "    convos = examples[\"messages\"]\n", "    texts = [tokenizer.apply_chat_template(convo, tokenize = False, add_generation_prompt = False) for convo in convos]\n", "    return { \"text\" : texts, }\n", "pass\n", "\n", "from datasets import load_dataset, Dataset\n", "dataset = Dataset.from_json(\"/content/data/final/ai_meta_com_qa_pairs_cleaned_ft.json\")\n", "dataset = dataset.map(formatting_prompts_func, batched = True,)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RLJRdoTNdBmF", "outputId": "a3d42e6d-103c-46fd-9a38-09619f823454"}, "outputs": [{"data": {"text/plain": ["[{'content': 'You are a helpful assistant.', 'role': 'system'},\n", " {'content': 'What is the context window of Llama 4 Scout?', 'role': 'user'},\n", " {'content': '10M', 'role': 'assistant'}]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[1][\"messages\"]"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9ZcDMk_hdFUO", "outputId": "37f8e17c-3ba6-4229-a324-e1081f7f088f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "Cutting Knowledge Date: December 2023\n", "Today Date: 29 Apr 2025\n", "\n", "You are a helpful assistant.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "What is the context window of Llama 4 Scout?<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "10M<|eot_id|>\n"]}], "source": ["print(dataset[1][\"text\"])"]}, {"cell_type": "markdown", "metadata": {"id": "LaJo2gVKdWpg"}, "source": ["If you're looking to make your own chat template, that also is possible! You\n", "\n", "---\n", "\n", "must use the Jinja templating regime. We provide our own stripped down version of the `Unsloth template` which we find to be more efficient, and leverages ChatML, Zephyr and Alpaca styles.\n", "\n", "More info on chat templates on [our wiki page!](https://github.com/unslothai/unsloth/wiki#chat-templates)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"id": "pEx_zjZ2dI3P"}, "outputs": [], "source": ["unsloth_template = \\\n", "    \"{{ bos_token }}\"\\\n", "    \"{{ 'You are a helpful assistant to the user\\n' }}\"\\\n", "    \"{% for message in messages %}\"\\\n", "        \"{% if message['role'] == 'user' %}\"\\\n", "            \"{{ '>>> User: ' + message['content'] + '\\n' }}\"\\\n", "        \"{% elif message['role'] == 'assistant' %}\"\\\n", "            \"{{ '>>> Assistant: ' + message['content'] + eos_token + '\\n' }}\"\\\n", "        \"{% endif %}\"\\\n", "    \"{% endfor %}\"\\\n", "    \"{% if add_generation_prompt %}\"\\\n", "        \"{{ '>>> Assistant: ' }}\"\\\n", "    \"{% endif %}\"\n", "unsloth_eos_token = \"eos_token\"\n", "\n", "if False:\n", "    tokenizer = get_chat_template(\n", "        tokenizer,\n", "        chat_template = (unsloth_template, unsloth_eos_token,), # You must provide a template and EOS token\n", "        mapping = {\"role\" : \"from\", \"content\" : \"value\", \"user\" : \"human\", \"assistant\" : \"gpt\"}, # ShareGPT style\n", "        map_eos_token = True, # Maps <|im_end|> to </s> instead\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "5VfnAwcAdfI8"}, "source": ["[link text](https://)<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `SFTTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 60 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`. We also support TRL's `DPOTrainer`!"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 49, "referenced_widgets": ["1cf63a5709a3419ca9a25fc850e41ad9", "be62b64786fb45a6ad385afc3159695d", "306021728bfb4184b764980f4db086c7", "b5c4a874b09b4271926c5b775d144f43", "4762f640d04347f9b5d1828310917059", "8ce62450a4cc42b98d2e05ba830a09d4", "e0d1d7f2cb25401d971e2c88b40764f5", "3f66b3c3c3a040bdb70a1438f8c45513", "bce66a4b33b94e2aa9aa1b85d2f0fa5f", "c58c58a2f0354288963d90564d059dbf", "9d83f2442388443384ec9b7c58c4fb07"]}, "id": "sAhTN_47dbDx", "outputId": "909f9137-a041-4286-ba2f-78d27757542d"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1cf63a5709a3419ca9a25fc850e41ad9", "version_major": 2, "version_minor": 0}, "text/plain": ["Unsloth: Tokenizing [\"text\"] (num_proc=2):   0%|          | 0/10 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from trl import SFTConfig, SFTTrainer\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = dataset,\n", "    dataset_text_field = \"text\",\n", "    max_seq_length = max_seq_length,\n", "    dataset_num_proc = 2,\n", "    packing = False, # Can make training 5x faster for short sequences.\n", "    args = SFTConfig(\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4,\n", "        warmup_steps = 5,\n", "        max_steps = 60,\n", "        learning_rate = 2e-4,\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "K5O7s1eldppw", "outputId": "ff284c05-397d-4b81-b341-0c462ea10a15"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU = NVIDIA L4. Max memory = 22.161 GB.\n", "3.441 GB of memory reserved.\n"]}], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "gTbJG8PDdl49", "outputId": "2657923a-fe7b-4d8b-f07f-1e8f063b65be"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 10 | Num Epochs = 60 | Total steps = 60\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient accumulation steps = 4\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (2 x 4 x 1) = 8\n", " \"-____-\"     Trainable parameters = 24,313,856/3,000,000,000 (0.81% trained)\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='60' max='60' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [60/60 00:57, Epoch 30/60]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>4.694800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>4.790200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>4.632600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>4.684000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>4.017900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>3.816500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>3.191400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>2.652200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>2.399100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>2.333100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>2.041200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>1.589300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>1.634800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>1.502200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>1.406400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>1.417000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>1.276700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>1.009500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>1.134700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.987200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.927000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>1.141900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.877700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.784700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.737300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.731200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.661000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.643500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.568000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.528100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.480400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.509400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.447900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.424000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.429700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.342900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.369000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.427600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.373900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.282100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.345000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.318000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.339300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.293900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.308800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.345400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.313100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.275200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.289000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.326800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>0.283500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>0.299000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>0.284800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>0.248500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>0.266500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>0.286700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>0.271900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>0.238700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>0.257300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>0.273100</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "SSy1iIZ4dxFS", "outputId": "7b643b59-cbbe-4d3d-a659-de0709e681a8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["64.247 seconds used for training.\n", "1.07 minutes used for training.\n", "Peak reserved memory = 3.441 GB.\n", "Peak reserved memory for training = 0.0 GB.\n", "Peak reserved memory % of max memory = 15.527 %.\n", "Peak reserved memory for training % of max memory = 0.0 %.\n"]}], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "exxEe_OqeGBz"}, "source": ["*italicized text*<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model! Since we're using `ChatML`, use `apply_chat_template` with `add_generation_prompt` set to `True` for inference."]}, {"cell_type": "code", "execution_count": 26, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2GcqXN8ReGdm", "outputId": "3318490f-3ba8-45b2-9ff8-7b4163e70e1c"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth: Will map <|im_end|> to EOS = <|eot_id|>.\n", "The attention mask is not set and cannot be inferred from input because pad token is same as eos token. As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.\n"]}, {"data": {"text/plain": ["['<|im_start|>user\\nContinue the fi<PERSON><PERSON>i sequence: 1, 1, 2, 3, 5, 8,<|im_end|>\\n<|im_start|>assistant\\n10, 17, 34, 55, 89, 144, 233, 377, 610, 987, 1577, 2584, 4181, 6765, 10946, 17711, 28657, 46367, 75025']"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["from unsloth.chat_templates import get_chat_template\n", "\n", "tokenizer = get_chat_template(\n", "    tokenizer,\n", "    chat_template = \"chatml\", # Supports zephyr, chatml, mistral, llama, alpaca, vicuna, vicuna_old, unsloth\n", "    mapping = {\"role\" : \"from\", \"content\" : \"value\", \"user\" : \"human\", \"assistant\" : \"gpt\"}, # ShareGPT style\n", "    map_eos_token = True, # Maps <|im_end|> to </s> instead\n", ")\n", "\n", "FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "\n", "messages = [\n", "    {\"from\": \"human\", \"value\": \"Continue the fi<PERSON><PERSON><PERSON> sequence: 1, 1, 2, 3, 5, 8,\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = True,\n", "    add_generation_prompt = True, # Must add for generation\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "outputs = model.generate(input_ids = inputs, max_new_tokens = 64, use_cache = True)\n", "tokenizer.batch_decode(outputs)"]}, {"cell_type": "markdown", "metadata": {"id": "3A0o2bnEeTyn"}, "source": ["You can also use a TextStreamer for continuous inference - so you can see the generation token by token, instead of waiting the whole time!\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8VkB8p3FeMwF", "outputId": "fdbf4d2c-9ba1-4475-bb77-9ef328e766e1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|im_start|>user\n", "Continue the fi<PERSON><PERSON><PERSON> sequence: 1, 1, 2, 3, 5, 8,<|im_end|>\n", "<|im_start|>assistant\n", "8, 13<|im_end|>\n"]}], "source": ["FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "\n", "messages = [\n", "    {\"from\": \"human\", \"value\": \"Continue the fi<PERSON><PERSON><PERSON> sequence: 1, 1, 2, 3, 5, 8,\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = True,\n", "    add_generation_prompt = True, # Must add for generation\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer)\n", "_ = model.generate(input_ids = inputs, streamer = text_streamer, max_new_tokens = 128, use_cache = True)"]}, {"cell_type": "markdown", "metadata": {"id": "sXD2HbvqeY5P"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Oq0uQvJleVzM", "outputId": "2d6818d0-1a74-440b-afa2-e2739a3aaa78"}, "outputs": [{"data": {"text/plain": ["('lora_model/tokenizer_config.json',\n", " 'lora_model/special_tokens_map.json',\n", " 'lora_model/tokenizer.json')"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-skt9DSCej7i", "outputId": "5187f9b8-0a06-4277-baaf-81c102ed2b57"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<|im_start|>user\n", "What is a famous tall tower in Paris?<|im_end|>\n", "<|im_start|>assistant\n", "<PERSON><PERSON><PERSON>\n", "<|im_end|>assistant\n", "\n", "A tower in Paris is called <PERSON><PERSON>el.<|im_end|>\n"]}], "source": ["if False:\n", "    from unsloth import FastLanguageModel\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name = \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        max_seq_length = max_seq_length,\n", "        dtype = dtype,\n", "        load_in_4bit = load_in_4bit,\n", "    )\n", "    FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "\n", "messages = [\n", "    {\"from\": \"human\", \"value\": \"What is a famous tall tower in Paris?\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = True,\n", "    add_generation_prompt = True, # Must add for generation\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer)\n", "_ = model.generate(input_ids = inputs, streamer = text_streamer, max_new_tokens = 128, use_cache = True)"]}, {"cell_type": "markdown", "metadata": {"id": "psUxQUjVeqRH"}, "source": ["You can also use Hugging Face's `AutoModelForPeftCausalLM`. Only use this if you do not have `unsloth` installed. It can be hopelessly slow, since `4bit` model downloading is not supported, and Unsloth's **inference is 2x faster**."]}, {"cell_type": "code", "execution_count": 30, "metadata": {"id": "S0EjFtYNenVp"}, "outputs": [], "source": ["if False:\n", "    # I highly do NOT suggest - use Unsloth if possible\n", "    from peft import AutoModelForPeftCausalLM\n", "    from transformers import AutoTokenizer\n", "\n", "    model = AutoModelForPeftCausalLM.from_pretrained(\n", "        \"lora_model\",  # YOUR MODEL YOU USED FOR TRAINING\n", "        load_in_4bit=load_in_4bit,\n", "    )\n", "    tokenizer = AutoTokenizer.from_pretrained(\"lora_model\")"]}, {"cell_type": "markdown", "metadata": {"id": "k8K1z6UXeu56"}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16 or `merged_4bit` for int4. We also allow `lora` adapters as a fallback. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": 31, "metadata": {"id": "XH-zV3w6esgG"}, "outputs": [], "source": ["# Merge to 16bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_16bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_16bit\", token = \"\")\n", "\n", "# Merge to 4bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_4bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_4bit\", token = \"\")\n", "\n", "# Just LoRA adapters\n", "if False:\n", "    model.save_pretrained(\"model\")\n", "    tokenizer.save_pretrained(\"model\")\n", "if False:\n", "    model.push_to_hub(\"hf/model\", token = \"\")\n", "    tokenizer.push_to_hub(\"hf/model\", token = \"\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "k88rv58-e1vI"}, "source": ["### GGUF / llama.cpp Conversion\n", "To save to `GGUF` / `llama.cpp`, we support it natively now! We clone `llama.cpp` and we default save it to `q8_0`. We allow all methods like `q4_k_m`. Use `save_pretrained_gguf` for local saving and `push_to_hub_gguf` for uploading to HF.\n", "\n", "Some supported quant methods (full list on our [Wiki page](https://github.com/unslothai/unsloth/wiki#gguf-quantization-options)):\n", "* `q8_0` - Fast conversion. High resource use, but generally acceptable.\n", "* `q4_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q4_K.\n", "* `q5_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q5_K."]}, {"cell_type": "code", "execution_count": 32, "metadata": {"id": "IMkq_nIGexe5"}, "outputs": [], "source": ["# Save to 8bit Q8_0\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer,)\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, token = \"\")\n", "\n", "# Save to 16bit GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"f16\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"f16\", token = \"\")\n", "\n", "# Save to q4_k_m GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"q4_k_m\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"q4_k_m\", token = \"\")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "A100", "machine_shape": "hm", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"069296ecd4d047808d0fdb6350c4e061": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "079045628e3b4ed2b2ec06cc065d5de1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0a845d380f334775bdeb75f0f678ebb7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_63e0016129124d8594c10e8b242d6e55", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a428fa2504b243dca5a0a8a31882bf4e", "value": 1}}, "0c4b12071a864b248ecc1e8a59605324": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_fd78c8249bb34c68bcee135f542d9ead", "IPY_MODEL_6672d3af7dda4a9b81482729744ca8ec", "IPY_MODEL_f214e13094854ed2a7c7f5da2fad6157"], "layout": "IPY_MODEL_c8b47800e7cc46d8839ec9658e498425"}}, "0d7a249c7b0d408aa86037d4f0689e45": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0e05421251204969820a7b2462ab4942": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f0f74a0cb99548c09e87dac3b768e8c4", "placeholder": "​", "style": "IPY_MODEL_478bf5068443445b9343521a23a97977", "value": " 10/10 [00:00&lt;00:00, 460.57 examples/s]"}}, "0e287a989a84403cbe8c16e298bfaf20": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0f9c94795f084b9198f7a4646b266804": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1cf63a5709a3419ca9a25fc850e41ad9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_be62b64786fb45a6ad385afc3159695d", "IPY_MODEL_306021728bfb4184b764980f4db086c7", "IPY_MODEL_b5c4a874b09b4271926c5b775d144f43"], "layout": "IPY_MODEL_4762f640d04347f9b5d1828310917059"}}, "1e56214ea0654ea39dd2c36da1589109": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1fc11f3e552c4545bc8489c80862e535": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "20804bce2f9f4faa9450c920ca92e96c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "22585ec7d5b544b28ccd4359ceaf40cf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "22ab3beb635a4fc4b9787d7857da9735": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "22ac359d555442b790c946d547e47413": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "23a43154454f4844b316614fbfc52879": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_60f56871f426425bbeeb18cfe9856a8e", "IPY_MODEL_9528c2e5e6c14019af14f165b241a75c", "IPY_MODEL_34df1a077e10481aa7d0fe02df084a50"], "layout": "IPY_MODEL_c230bc1042f44b22aa33f5424719920a"}}, "267afe0c8fa34a86952f0f71e29594d5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5d1138410605484bb760816513ba0d66", "IPY_MODEL_0a845d380f334775bdeb75f0f678ebb7", "IPY_MODEL_c12ad669eb6049e88f68383332be4a6f"], "layout": "IPY_MODEL_0f9c94795f084b9198f7a4646b266804"}}, "29e5efda305941be842d917391a13e42": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "301a948c88a0490897cf9cfb3c0125d1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ce9bbe4968714f9ba27c731a08f3aa36", "max": 17209920, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_29e5efda305941be842d917391a13e42", "value": 17209920}}, "306021728bfb4184b764980f4db086c7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3f66b3c3c3a040bdb70a1438f8c45513", "max": 10, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bce66a4b33b94e2aa9aa1b85d2f0fa5f", "value": 10}}, "3499305b74a94d91a1812c8545136e77": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "34df1a077e10481aa7d0fe02df084a50": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_22ac359d555442b790c946d547e47413", "placeholder": "​", "style": "IPY_MODEL_ca05ea6a911f47cd83be9b84c76cf476", "value": " 234/234 [00:00&lt;00:00, 31.0kB/s]"}}, "3830709475f94c72842d1fdac9d7f253": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3f66b3c3c3a040bdb70a1438f8c45513": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "40e360d77f1b48139460d1e4d0f7fb9f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6be688d97f434348a453da3956f708a8", "IPY_MODEL_301a948c88a0490897cf9cfb3c0125d1", "IPY_MODEL_cf96a86f56ed44d19623c07190e3eb94"], "layout": "IPY_MODEL_803e1a09fe1f48bdbdf13683f29331e5"}}, "42b76c2b475244709e06992cb1bc2b60": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "47135f7c1edb40659e4bd3a3f7ca767e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_628738149eb2408b951f3fac42eb27e6", "placeholder": "​", "style": "IPY_MODEL_9d8d72dcd36e483a81965682bb6c8dc3", "value": "Map: 100%"}}, "4762f640d04347f9b5d1828310917059": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "478bf5068443445b9343521a23a97977": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4cdb1773a6344a97b36f305f8f0e5b81": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4e697867516d413da3078374f259e12b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "503a0aaec9964edd83eb9d55db83a425": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "543c5052509f481f9922d851a5d03dad": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5c03f68bc3004e62b1fd9d779ddb63f7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5d1138410605484bb760816513ba0d66": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ac29467028fb4a5fbf020e2adc629cd9", "placeholder": "​", "style": "IPY_MODEL_4cdb1773a6344a97b36f305f8f0e5b81", "value": "Generating train split: "}}, "60f56871f426425bbeeb18cfe9856a8e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1e56214ea0654ea39dd2c36da1589109", "placeholder": "​", "style": "IPY_MODEL_503a0aaec9964edd83eb9d55db83a425", "value": "generation_config.json: 100%"}}, "628738149eb2408b951f3fac42eb27e6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "63e0016129124d8594c10e8b242d6e55": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "65441e44919b40248af806dbc37dcf78": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_71d3d14817f94ac9b69d0bca784fc171", "placeholder": "​", "style": "IPY_MODEL_8b4e647f7b374d01bb4f019413f1d771", "value": " 2.35G/2.35G [00:05&lt;00:00, 570MB/s]"}}, "6672d3af7dda4a9b81482729744ca8ec": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b15238421c48467d8e74b7c76aed6c42", "max": 54674, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_0d7a249c7b0d408aa86037d4f0689e45", "value": 54674}}, "6be688d97f434348a453da3956f708a8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7fedda50349342c0a2ef021f030cd82e", "placeholder": "​", "style": "IPY_MODEL_3499305b74a94d91a1812c8545136e77", "value": "tokenizer.json: 100%"}}, "71d3d14817f94ac9b69d0bca784fc171": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7fedda50349342c0a2ef021f030cd82e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "803e1a09fe1f48bdbdf13683f29331e5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8590a4973e3c4dc4920d1d33ea979e14": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8b4e647f7b374d01bb4f019413f1d771": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8ce62450a4cc42b98d2e05ba830a09d4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8eebd4e40b4643ce8c30c080af4de97d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9528c2e5e6c14019af14f165b241a75c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8eebd4e40b4643ce8c30c080af4de97d", "max": 234, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b846789249a34ab29594b442d7887972", "value": 234}}, "95c593c9249245b0816abd70e5c3377e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9d83f2442388443384ec9b7c58c4fb07": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9d8d72dcd36e483a81965682bb6c8dc3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9e34bc6269d64be8a198f6deeffe80f0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4e697867516d413da3078374f259e12b", "max": 10, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ce2d74f898a849da988bef9e0151da38", "value": 10}}, "a322a3d796e24c47a29c7f78559dac23": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e7ac1b007eed4f288cb23c9f64bae637", "placeholder": "​", "style": "IPY_MODEL_f473a9b930524635b73ea767d55060a3", "value": " 454/454 [00:00&lt;00:00, 57.3kB/s]"}}, "a428fa2504b243dca5a0a8a31882bf4e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a8b76b225cba465c8a4c451cbb578065": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cd97acdfd05a4205b39634f5e0e4cd4d", "max": 454, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5c03f68bc3004e62b1fd9d779ddb63f7", "value": 454}}, "ab6ba4f352de4f56abf4a6ff91d8fefa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d319d99ce8e84c858be0fc5d0ee53a22", "IPY_MODEL_f6c7ae8c66ea4323848e3da703d8c98d", "IPY_MODEL_65441e44919b40248af806dbc37dcf78"], "layout": "IPY_MODEL_543c5052509f481f9922d851a5d03dad"}}, "ac29467028fb4a5fbf020e2adc629cd9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b15238421c48467d8e74b7c76aed6c42": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b5c4a874b09b4271926c5b775d144f43": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c58c58a2f0354288963d90564d059dbf", "placeholder": "​", "style": "IPY_MODEL_9d83f2442388443384ec9b7c58c4fb07", "value": " 10/10 [00:01&lt;00:00,  8.28 examples/s]"}}, "b846789249a34ab29594b442d7887972": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b92edd7809b84a4db8288b8de3b0274c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bce66a4b33b94e2aa9aa1b85d2f0fa5f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "be62b64786fb45a6ad385afc3159695d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8ce62450a4cc42b98d2e05ba830a09d4", "placeholder": "​", "style": "IPY_MODEL_e0d1d7f2cb25401d971e2c88b40764f5", "value": "Unsloth: Tokenizing [&quot;text&quot;] (num_proc=2): 100%"}}, "c12ad669eb6049e88f68383332be4a6f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_22585ec7d5b544b28ccd4359ceaf40cf", "placeholder": "​", "style": "IPY_MODEL_20804bce2f9f4faa9450c920ca92e96c", "value": " 10/0 [00:00&lt;00:00, 169.83 examples/s]"}}, "c230bc1042f44b22aa33f5424719920a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c58c58a2f0354288963d90564d059dbf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c8b47800e7cc46d8839ec9658e498425": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ca05ea6a911f47cd83be9b84c76cf476": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cc0876b62bd54c8fa02fc3148ced4f1b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cd97acdfd05a4205b39634f5e0e4cd4d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ce2d74f898a849da988bef9e0151da38": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ce9bbe4968714f9ba27c731a08f3aa36": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cf96a86f56ed44d19623c07190e3eb94": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_95c593c9249245b0816abd70e5c3377e", "placeholder": "​", "style": "IPY_MODEL_3830709475f94c72842d1fdac9d7f253", "value": " 17.2M/17.2M [00:00&lt;00:00, 100MB/s]"}}, "d145f007f4d74467b71d7044e42eafe4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d319d99ce8e84c858be0fc5d0ee53a22": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cc0876b62bd54c8fa02fc3148ced4f1b", "placeholder": "​", "style": "IPY_MODEL_22ab3beb635a4fc4b9787d7857da9735", "value": "model.safetensors: 100%"}}, "dbde86e0667341a697476e4c43ae0f15": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e0d1d7f2cb25401d971e2c88b40764f5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e7ac1b007eed4f288cb23c9f64bae637": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f0f74a0cb99548c09e87dac3b768e8c4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f214e13094854ed2a7c7f5da2fad6157": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_069296ecd4d047808d0fdb6350c4e061", "placeholder": "​", "style": "IPY_MODEL_d145f007f4d74467b71d7044e42eafe4", "value": " 54.7k/54.7k [00:00&lt;00:00, 6.51MB/s]"}}, "f25e711fe26149aeb2806319f6ec50ba": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_47135f7c1edb40659e4bd3a3f7ca767e", "IPY_MODEL_9e34bc6269d64be8a198f6deeffe80f0", "IPY_MODEL_0e05421251204969820a7b2462ab4942"], "layout": "IPY_MODEL_fced6c677e434bccaa2351fd8c09bf2f"}}, "f473a9b930524635b73ea767d55060a3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f4e093d3b96f472c9de2356b3d598484": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dbde86e0667341a697476e4c43ae0f15", "placeholder": "​", "style": "IPY_MODEL_1fc11f3e552c4545bc8489c80862e535", "value": "special_tokens_map.json: 100%"}}, "f6c7ae8c66ea4323848e3da703d8c98d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_42b76c2b475244709e06992cb1bc2b60", "max": 2354805470, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_079045628e3b4ed2b2ec06cc065d5de1", "value": 2354805246}}, "fae552d7f28e4a18b4a92288f2336d6d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f4e093d3b96f472c9de2356b3d598484", "IPY_MODEL_a8b76b225cba465c8a4c451cbb578065", "IPY_MODEL_a322a3d796e24c47a29c7f78559dac23"], "layout": "IPY_MODEL_8590a4973e3c4dc4920d1d33ea979e14"}}, "fced6c677e434bccaa2351fd8c09bf2f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fd78c8249bb34c68bcee135f542d9ead": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b92edd7809b84a4db8288b8de3b0274c", "placeholder": "​", "style": "IPY_MODEL_0e287a989a84403cbe8c16e298bfaf20", "value": "tokenizer_config.json: 100%"}}}}}, "nbformat": 4, "nbformat_minor": 0}