<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/scigemmaicon"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.SciGemma"
        tools:targetApi="31">
        <activity
            android:name="com.example.scigemma.MainActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.SciGemma">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <uses-native-library
            android:name="libOpenCL.so"
            android:required="false"/>
        <uses-native-library android:name="libOpenCL-car.so" android:required="false"/>
        <uses-native-library android:name="libOpenCL-pixel.so" android:required="false"/>

    </application>

</manifest>