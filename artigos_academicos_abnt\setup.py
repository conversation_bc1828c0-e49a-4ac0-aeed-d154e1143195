"""
Script de instalação para o aplicativo de artigos acadêmicos ABNT.
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = fh.read().splitlines()

setup(
    name="artigos_academicos_abnt",
    version="1.0.0",
    author="Seu Nome",
    author_email="<EMAIL>",
    description="Gerador de artigos acadêmicos ABNT usando Groq e Arxiv",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/seu-usuario/artigos-academicos-abnt",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "artigos-academicos-abnt=artigos_academicos_abnt.run:main",
        ],
    },
)
