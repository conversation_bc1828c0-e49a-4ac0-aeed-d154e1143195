# Pull Request

## Type of Change

- [ ] Project Addition
- [ ] Bug Fix
- [ ] Feature Implementation
- [ ] Documentation Update
- [ ] Other (please describe)

## Project Details

- Project Name: <!-- Use descriptive name with underscores (e.g., job_searching_agent) -->
- Target Directory: <!-- Choose one: @advance_ai_agents, @mcp_ai_agents, @rag_apps, @starter_ai_agents -->

## Description

<!-- Provide a detailed description of your changes -->

## Technical Details

<!-- List key technical aspects of your changes -->

- Primary Language/Framework:
- Key Dependencies:
- API Requirements:
- Changes to existing code:
- New files added:
- Dependencies added/removed:

## Installation Steps (if applicable)

<!-- Provide step-by-step installation instructions -->

```bash
1.
2.
3.
```

## Usage Examples (if applicable)

<!-- Provide code examples or usage instructions -->

```python
# Example code here
```

## Testing Done

<!-- Describe how you tested your changes -->

## Affected Files

<!-- List the files that were modified or added -->

-
-

## Screenshots/Demo

<!-- Add screenshots or GIFs of your changes in action -->

## Checklist

- [ ] Project name follows naming convention (descriptive with underscores)
- [ ] Added to the correct directory
- [ ] Includes comprehensive README.md (if new project)
- [ ] Code follows project structure and conventions
- [ ] All dependencies are properly listed in requirements.txt
- [ ] Added appropriate documentation
- [ ] Code is properly formatted and linted
- [ ] Added necessary tests (if applicable)
- [ ] No breaking changes introduced
- [ ] Documentation updated (if necessary)

## Additional Notes

<!-- Any other information that might be helpful -->
