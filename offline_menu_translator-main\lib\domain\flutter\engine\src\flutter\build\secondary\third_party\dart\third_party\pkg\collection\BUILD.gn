# Copyright 2021 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# TODO(flutter/flutter#85356): This file was originally generated by the
# fuchsia.git script: `package_importer.py`. The generated `BUILD.gn` files were
# copied to the flutter repo to support `dart_library` targets used for
# Flutter-Fuchsia integration tests. This file can be maintained by hand, but it
# would be better to implement a script for Flutter, to either generate these
# BUILD.gn files or dynamically generate the GN targets.

import("//flutter/tools/fuchsia/dart/dart_library.gni")

dart_library("collection") {
  package_name = "collection"

  language_version = "2.12"

  deps = []

  sources = [
    "algorithms.dart",
    "collection.dart",
    "equality.dart",
    "iterable_zip.dart",
    "priority_queue.dart",
    "src/algorithms.dart",
    "src/canonicalized_map.dart",
    "src/combined_wrappers/combined_iterable.dart",
    "src/combined_wrappers/combined_iterator.dart",
    "src/combined_wrappers/combined_list.dart",
    "src/combined_wrappers/combined_map.dart",
    "src/comparators.dart",
    "src/empty_unmodifiable_set.dart",
    "src/equality.dart",
    "src/equality_map.dart",
    "src/equality_set.dart",
    "src/functions.dart",
    "src/iterable_extensions.dart",
    "src/iterable_zip.dart",
    "src/list_extensions.dart",
    "src/priority_queue.dart",
    "src/queue_list.dart",
    "src/union_set.dart",
    "src/union_set_controller.dart",
    "src/unmodifiable_wrappers.dart",
    "src/utils.dart",
    "src/wrappers.dart",
    "wrappers.dart",
  ]
}
