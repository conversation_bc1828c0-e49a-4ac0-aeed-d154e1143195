"""
Script de teste para as funções de exportação.
"""

import os
import sys
import unittest

# Adicionar o diretório pai ao path para importar os módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.export import export_to_pdf, markdown_to_docx

class TestExport(unittest.TestCase):
    """Testes para as funções de exportação"""

    def test_export_functions_exist(self):
        """Teste para verificar se as funções de exportação existem"""
        self.assertTrue(callable(export_to_pdf))
        self.assertTrue(callable(markdown_to_docx))

if __name__ == "__main__":
    unittest.main()
