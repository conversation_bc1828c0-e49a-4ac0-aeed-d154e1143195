{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Unsloth now supports Text-to-Speech (TTS) models. Read our [guide here](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning).\n", "\n", "Read our **[Qwen3 Guide](https://docs.unsloth.ai/basics/qwen3-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "%%capture\nimport os\nos.environ[\"CUDA_VISIBLE_DEVICES\"] = \"0\"\n\n!pip install pip3-autoremove\n!pip install torch torchvision torchaudio xformers --index-url https://download.pytorch.org/whl/cu124\n!pip install unsloth\n!pip install --upgrade transformers==4.52.4\n"}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "E8-BWi7MzkRz", "outputId": "0626186f-6e70-47b7-db59-b8e7591d0de4"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/unsloth/__init__.py:67: UserWarning: CUDA is not linked properly.\n", "We shall run `ldconfig /usr/lib64-nvidia` to try to fix it.\n", "  warnings.warn(\n"]}], "source": ["# One must patch the DPO Trainer first!\n", "from unsloth import PatchDPOTrainer\n", "\n", "PatchDPOTrainer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 491, "referenced_widgets": ["4c7a9e6327fc4b839f1e2eee705e1574", "904fef1ac7404cc8a33e65ffe8938c8b", "f7562580501845e9a67ab9a8b4a96ba0", "96f9847627504ae7abe7461e4c0b3cff", "5165aa69bef148af9717d90cc5a870b1", "e6dfcf0456824556ad09fe7945674a14", "80568bca0c604eaaab2933fc76da2bf0", "079284acd5c6454eb03d8876a62e6f12", "1d917d774780421fbdca680009a706ac", "342cf2a9d6674b4d94fdbaf3b63b9b79", "16ace0251b954bc08a33eaf3b2a47216", "2cb8afe9c6e2441fa4e232146ecd08b4", "bc745fa3da114007af35ea15dbd7ab7d", "127ed424448840cea8b14fc9997b9cde", "803f5904c00d4cde8729226139e3c258", "4a273a69979b466dbf1c987552101fbf", "68893d4db1f74557932d8b24e0f72820", "3ccdc55cf6384695967eaf79325b1aef", "859bc87dfda746819298e213e4e0b067", "32478ec41d044773b70eba24e2ac0b43", "5aeb33ba799149c1ad6e580a2a1c0a51", "a2e31ba35dcf41c79c72dfd431da781f", "f7c089469828416aa89ade0cfa5a04cc", "3a52a2ec426b4fc2aaad23c7d8ce6d2b", "c298081665e844539b8d366325dbdab5", "65b1731e23364d4ea0bb292550d4a403", "5f6fd4c19b34458aa8f2019a25bfd605", "33dda465ac2f42fb8846171a974193ac", "bf6bc9099d1a475884be6f15115f01ad", "cdc411110d1b405ea12342b57fd8de30", "84d445570b31490fb658b08f819dd6a7", "ceb3830da80146da8fbe6423d25fd8a3", "d58599afce36484c8508c16beba13e36", "d43eea508a4d496e9460f01931659b52", "b146c0bbd15644678198977d63edebb7", "aa0323eef053447b9836931535984912", "effec7acec3d4b5384bcf481e3263b6c", "03ec8d28c0f34116b42e4e0dc65e3dd3", "9fd5df9115cf4deb8a36daf4a654bfc7", "cd6e07c3984a4e0e920d87c6ebd6ad1c", "b0831169530b402cac31b1e60484fbb2", "ccabf636e15e471cb82bb431b5ab4b5c", "e4120afe895742848cf756fd58cb42f8", "fe6eabd38a614113b36bafd1f8ce6920", "9b12628a46c546aebe2c749085ba2e61", "63d48b48f4ce449e85a55477df0b6967", "068c416eaaed476eacffb8f2f343e08f", "9fd3fe23f14b4403a18ec7d73b64dc6a", "cf639313cba7442893fdf880d368616d", "4bd0cf4d31604194a7761ea0345cc0a4", "783ad9a7097e4fcdb768c8ecb1f21227", "2557c3090a8d4760ae844fb518e1b555", "b508872df72d45beb26a625c7499cc8b", "cbc6fe3cc12a4d79ba08b67f23e5ce01", "d0d340fa13a54bcf9f1130cc2fc09cc8", "5d7fce052f8e4937adaaa670be8c1ae4", "77939b191e82425da0a5908fdbca2a2d", "30dc6079591b4908a57f5e285ebeda68", "960bd84fc3fe4d8eb7e79ecba0f3edf6", "bb7c7c1866af4641bc07d1ff0a5cd069", "0dc6aefaa24043768d656f5a61d79545", "d27056ab29e04c448fc62f9f18cbb82d", "926319902fe84c5095e0b5bd4b3e8187", "9f660e1b7b9c4ae7922d51353783acf9", "82d00942a39e465c8a6f869ef97cc250", "b9e2b829291247619a7b37c9f544d395", "3f6ccae685e34d05be9fde5538dc48cf", "3df8638084104cb7a0530a96ec6201af", "06d1fd85a45541609d11ec7f0bd3b444", "a9d05a8c3aaf4fe0a2f061c3eeb48b44", "8095a44a62804d6d86c24d62b9e1a1c1", "f61b85b63d5147db9f1040f30f4a3034", "e152d696872242ab8e0bc00ce9b50142", "623e912cfe404e69a64992fbb1c1bc47", "69f9e4bf820047658699d55d84d682c0", "a8306ce933304694aba188495743c2f4", "1cd708f679de4d319af26797aecca94d"]}, "id": "QmUBVEnvCDJv", "outputId": "f04a3ea4-0d0f-424d-8265-0fb99fd17ef3"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/huggingface_hub/utils/_token.py:72: UserWarning: \n", "The secret `HF_TOKEN` does not exist in your Colab secrets.\n", "To authenticate with the Hugging Face Hub, create a token in your settings tab (https://huggingface.co/settings/tokens), set it as secret in your Google Colab and restart your session.\n", "You will be able to reuse this secret in all of your notebooks.\n", "Please note that authentication is recommended but still optional to access public models or datasets.\n", "  warnings.warn(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4c7a9e6327fc4b839f1e2eee705e1574", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/1.04k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth: Fast Mistral patching release 2024.1\n", "   \\\\   /|    GPU: Tesla T4. Max memory: 14.748 GB\n", "O^O/ \\_/ \\    CUDA capability = 7.5. Xformers = 0.0.22.post7. FA = False.\n", "\\        /    Pytorch version: 2.1.0+cu121. CUDA Toolkit = 12.1\n", " \"-____-\"     bfloat16 = FALSE. Platform = Linux\n", "\n", "You passed `quantization_config` to `from_pretrained` but the model you're loading already has a `quantization_config` attribute. The `quantization_config` attribute will be overwritten with the one you passed to `from_pretrained`.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2cb8afe9c6e2441fa4e232146ecd08b4", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/4.13G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f7c089469828416aa89ade0cfa5a04cc", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/116 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d43eea508a4d496e9460f01931659b52", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/1.48k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9b12628a46c546aebe2c749085ba2e61", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.model:   0%|          | 0.00/493k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5d7fce052f8e4937adaaa670be8c1ae4", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/1.80M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3f6ccae685e34d05be9fde5538dc48cf", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/624 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "max_seq_length = 4096 # Choose any! We auto support RoPE Scaling internally!\n", "dtype = None # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+\n", "load_in_4bit = True # Use 4bit quantization to reduce memory usage. Can be False.\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/zephyr-sft-bnb-4bit\", # Choose ANY! eg mistralai/Mistral-7B-Instruct-v0.2\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit,\n", "    # token = \"hf_...\", # use one if using gated models like meta-llama/Llama-2-7b-hf\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "AqkY_wHdKyOl"}, "outputs": [], "source": ["# @title Alignment Handbook utils\n", "import os\n", "import re\n", "from typing import List, Literal, Optional\n", "\n", "from datasets import DatasetDict, concatenate_datasets, load_dataset, load_from_disk\n", "from datasets.builder import DatasetGenerationError\n", "\n", "\n", "DEFAULT_CHAT_TEMPLATE = \"{% for message in messages %}\\n{% if message['role'] == 'user' %}\\n{{ '<|user|>\\n' + message['content'] + eos_token }}\\n{% elif message['role'] == 'system' %}\\n{{ '<|system|>\\n' + message['content'] + eos_token }}\\n{% elif message['role'] == 'assistant' %}\\n{{ '<|assistant|>\\n'  + message['content'] + eos_token }}\\n{% endif %}\\n{% if loop.last and add_generation_prompt %}\\n{{ '<|assistant|>' }}\\n{% endif %}\\n{% endfor %}\"\n", "\n", "\n", "def apply_chat_template(\n", "    example,\n", "    tokenizer,\n", "    task: Literal[\"sft\", \"generation\", \"rm\", \"dpo\"] = \"sft\",\n", "    assistant_prefix=\"<|assistant|>\\n\",\n", "):\n", "    def _strip_prefix(s, pattern):\n", "        # Use re.escape to escape any special characters in the pattern\n", "        return re.sub(f\"^{re.escape(pattern)}\", \"\", s)\n", "\n", "    if task in [\"sft\", \"generation\"]:\n", "        messages = example[\"messages\"]\n", "        # We add an empty system message if there is none\n", "        if messages[0][\"role\"] != \"system\":\n", "            messages.insert(0, {\"role\": \"system\", \"content\": \"\"})\n", "        example[\"text\"] = tokenizer.apply_chat_template(\n", "            messages,\n", "            tokenize=False,\n", "            add_generation_prompt=True if task == \"generation\" else False,\n", "        )\n", "    elif task == \"rm\":\n", "        if all(k in example.keys() for k in (\"chosen\", \"rejected\")):\n", "            chosen_messages = example[\"chosen\"]\n", "            rejected_messages = example[\"rejected\"]\n", "            # We add an empty system message if there is none\n", "            if chosen_messages[0][\"role\"] != \"system\":\n", "                chosen_messages.insert(0, {\"role\": \"system\", \"content\": \"\"})\n", "            if rejected_messages[0][\"role\"] != \"system\":\n", "                rejected_messages.insert(0, {\"role\": \"system\", \"content\": \"\"})\n", "            example[\"text_chosen\"] = tokenizer.apply_chat_template(\n", "                chosen_messages, tokenize=False\n", "            )\n", "            example[\"text_rejected\"] = tokenizer.apply_chat_template(\n", "                rejected_messages, tokenize=False\n", "            )\n", "        else:\n", "            raise ValueError(\n", "                f\"Could not format example as dialogue for `rm` task! Require `[chosen, rejected]` keys but found {list(example.keys())}\"\n", "            )\n", "    elif task == \"dpo\":\n", "        if all(k in example.keys() for k in (\"chosen\", \"rejected\")):\n", "            # Compared to reward modeling, we filter out the prompt, so the text is everything after the last assistant token\n", "            prompt_messages = [\n", "                [msg for msg in example[\"chosen\"] if msg[\"role\"] == \"user\"][0]\n", "            ]\n", "            # Insert system message\n", "            if example[\"chosen\"][0][\"role\"] != \"system\":\n", "                prompt_messages.insert(0, {\"role\": \"system\", \"content\": \"\"})\n", "            else:\n", "                prompt_messages.insert(0, example[\"chosen\"][0])\n", "            # TODO: handle case where chosen/rejected also have system messages\n", "            chosen_messages = example[\"chosen\"][1:]\n", "            rejected_messages = example[\"rejected\"][1:]\n", "            example[\"text_chosen\"] = tokenizer.apply_chat_template(\n", "                chosen_messages, tokenize=False\n", "            )\n", "            example[\"text_rejected\"] = tokenizer.apply_chat_template(\n", "                rejected_messages, tokenize=False\n", "            )\n", "            example[\"text_prompt\"] = tokenizer.apply_chat_template(\n", "                prompt_messages, tokenize=False, add_generation_prompt=True\n", "            )\n", "            example[\"text_chosen\"] = _strip_prefix(\n", "                example[\"text_chosen\"], assistant_prefix\n", "            )\n", "            example[\"text_rejected\"] = _strip_prefix(\n", "                example[\"text_rejected\"], assistant_prefix\n", "            )\n", "        else:\n", "            raise ValueError(\n", "                f\"Could not format example as dialogue for `dpo` task! Require `[chosen, rejected]` keys but found {list(example.keys())}\"\n", "            )\n", "    else:\n", "        raise ValueError(\n", "            f\"Task {task} not supported, please ensure that the provided task is one of {['sft', 'generation', 'rm', 'dpo']}\"\n", "        )\n", "    return example\n", "\n", "\n", "def get_datasets(\n", "    data_config: dict,\n", "    splits: List[str] = [\"train\", \"test\"],\n", "    shuffle: bool = True,\n", ") -> DatasetDict:\n", "    \"\"\"\n", "    Loads one or more datasets with varying training set proportions.\n", "\n", "    Args:\n", "        data_config (`DataArguments` or `dict`):\n", "            Dataset configuration and split proportions.\n", "        splits (`List[str]`, *optional*, defaults to `['train', 'test']`):\n", "            Dataset splits to load and mix. Assumes the splits exist in all datasets and have a `train_` or `test_` prefix.\n", "        shuffle (`bool`, *optional*, defaults to `True`):\n", "            Whether to shuffle the training and testing/validation data.\n", "\n", "    Returns\n", "        [`DatasetDict`]: The dataset dictionary containing the loaded datasets.\n", "    \"\"\"\n", "\n", "    if type(data_config) is dict:\n", "        # Structure of the input is:\n", "        #     dataset_mixer = {\n", "        #             \"dataset1\": 0.5,\n", "        #             \"dataset1\": 0.3,\n", "        #             \"dataset1\": 0.2,\n", "        #         }\n", "        dataset_mixer = data_config\n", "    else:\n", "        raise ValueError(f\"Data config {data_config} not recognized.\")\n", "\n", "    raw_datasets = mix_datasets(dataset_mixer, splits=splits, shuffle=shuffle)\n", "    return raw_datasets\n", "\n", "\n", "def mix_datasets(\n", "    dataset_mixer: dict, splits: Optional[List[str]] = None, shuffle=True\n", ") -> DatasetDict:\n", "    \"\"\"\n", "    Loads and mixes datasets according to proportions specified in `dataset_mixer`.\n", "\n", "    Args:\n", "        dataset_mixer (`dict`):\n", "            Dictionary containing the dataset names and their training proportions. By default, all test proportions are 1.\n", "        splits (Optional[List[str]], *optional*, defaults to `None`):\n", "            Dataset splits to load and mix. Assumes the splits exist in all datasets and have a `train_` or `test_` prefix.\n", "        shuffle (`bool`, *optional*, defaults to `True`):\n", "            Whether to shuffle the training and testing/validation data.\n", "    \"\"\"\n", "    raw_datasets = DatasetDict()\n", "    raw_train_datasets = []\n", "    raw_val_datasets = []\n", "    fracs = []\n", "    for ds, frac in dataset_mixer.items():\n", "        fracs.append(frac)\n", "        for split in splits:\n", "            try:\n", "                # Try first if dataset on a Hub repo\n", "                dataset = load_dataset(ds, split=split)\n", "            except DatasetGenerationError:\n", "                # If not, check local dataset\n", "                dataset = load_from_disk(os.path.join(ds, split))\n", "\n", "            if \"train\" in split:\n", "                raw_train_datasets.append(dataset)\n", "            elif \"test\" in split:\n", "                raw_val_datasets.append(dataset)\n", "            else:\n", "                raise ValueError(\n", "                    f\"Split type {split} not recognized as one of test or train.\"\n", "                )\n", "\n", "    if any(frac < 0 for frac in fracs):\n", "        raise ValueError(\"Dataset fractions cannot be negative.\")\n", "\n", "    if len(raw_train_datasets) > 0:\n", "        train_subsets = []\n", "        for dataset, frac in zip(raw_train_datasets, fracs):\n", "            train_subset = dataset.select(range(int(frac * len(dataset))))\n", "            train_subsets.append(train_subset)\n", "        if shuffle:\n", "            raw_datasets[\"train\"] = concatenate_datasets(train_subsets).shuffle(seed=42)\n", "        else:\n", "            raw_datasets[\"train\"] = concatenate_datasets(train_subsets)\n", "    # No subsampling for test datasets to enable fair comparison across models\n", "    if len(raw_val_datasets) > 0:\n", "        if shuffle:\n", "            raw_datasets[\"test\"] = concatenate_datasets(raw_val_datasets).shuffle(\n", "                seed=42\n", "            )\n", "        else:\n", "            raw_datasets[\"test\"] = concatenate_datasets(raw_val_datasets)\n", "\n", "    if len(raw_datasets) == 0:\n", "        raise ValueError(\n", "            f\"Dataset {dataset_mixer} not recognized with split {split}. Check the dataset has been correctly formatted.\"\n", "        )\n", "\n", "    return raw_datasets"]}, {"cell_type": "markdown", "metadata": {"id": "EQ-Cp2V6kDcr"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We follow Huggingface's [Alignment Handbook](https://github.com/huggingface/alignment-handbook) for [Zephyr](https://huggingface.co/HuggingFaceH4/zephyr-7b-beta) and use the [Ultra Feedback dataset](https://huggingface.co/datasets/HuggingFaceH4/ultrafeedback_binarized), and sample 0.5% of it to speed things up. You can sample the full dataset for a full run."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 553, "referenced_widgets": ["5e875e8a442a41e8afef4ad494ef6897", "7adc769f384346b3a5835c73542a2d62", "fccd2f68b05d40c9b359133fbf383b1c", "2a197f7c6c4746e1a7fded1e0539bf29", "37a2eb8b91ed42a1be411ecb9079a082", "833097685a694d1e9cd5aa016b6b7d73", "4e48c220077d4546a51c02df9397a084", "f6200f86a4084c5f978f62481100b7a3", "530cd47150094e7fb925eb24ec071b5e", "9f6197721b90446091bf8b97de656d4b", "35edd118e5e54d19b87aa7e966e3c9fe", "3b258a09c7e0462e9d9fefa6746ebb9f", "ade70be3dc49443581a295e89faa65c6", "ad03a2ff7f974fa3a0f1be2dee56c9a0", "727d7d710957497c8bb91d52a04a00f3", "347ae8c1baf54004953d0eb9325f39f8", "6b419485aabe49c6bed15b5bf1316e43", "9eabdceae6484ba7b543ed1fa4f65141", "6cce73a8bdda4852968c807354a6d212", "7953ace8fec34f358506307a3efb208d", "ba6f8296f866419384de64f6e2cc727e", "d3fcca8f0d8c41a3b78bd2470542e950", "243999bf48174e72ae981effb01117ac", "77e6abd454c94559825201f34d5de044", "7522d496e9dd41828a4daddb9dd37883", "13b208926d4a4bae854e057bc9913100", "78ab252184ac46eba3e82c076f6b914a", "80308f7c18b244a698614fa86bc4c564", "9473389066664ed4aa2089c78adbb20d", "4a6807afa14049c781c7549467b85e1c", "4cc5ba58138d47129b776b04bc2d6646", "90b49ecfaf4a44409b0b405271c9797c", "9032335e102f465ea2e1adcc47462a8c", "6730cd77f7fa416b97f20fc5f5660488", "5a40fa44b76f44f88a24835c1bf00e56", "57f9a1fd43024e8bbe7c0df0867c8e12", "8f6c6f07bd254516ae4af2f80d2492f2", "5c28f5682c37476384f6a985d42e8360", "900f8248c6074ea5a93ef9372da4e958", "39a4f06a16f149789507232e4e28acb6", "c558b2bb11274910888b08138d9ee195", "3635323c2caf4f7ab2034306cdac61cb", "d855ad7e63ae45089832de406a3b00e6", "bf25365768a0441e8e4121ea1966971d", "757089cbb90f4d538e4a98a9ecee7dc6", "d28fb551bdac4910a04bb39b500b9228", "82cc0b43ea6d4ff4bddce1a1536fef62", "379878ecc7e44b448425c4155e106477", "5f8ada6bd56b4d79861a8f2bc36b7785", "3456b1a43ca742e3bd5bcf695a7b548e", "56da48c3277342b3ba6841d25a387202", "007871aa8b7f4de7bb0f96ebe08e5283", "0611164c2c6f4778adcbda713521155b", "a4199d34e668433a98b0fed23ab84541", "2b850e92569c4236a683b9df2de49775", "97a28a558b2a4ef997fb35547ef2f51d", "3760dc92a7b64410865257af9f95782c", "2189b92750854b1f9eaa70dc23d16c55", "37944b0ab46c4de7acb5e06b91f5da52", "461aeb76622449b6a0bb7a794ea16ced", "e98afd2ed7b44211932dc55a2a0c3e3a", "dfb40aa450f844e28319893415573906", "841f91867ff5489d92dfb1bbbe76ca70", "4631d92c2f3a44589478a981a4143836", "e6c183829cfb45e389348c0d5a1d943d", "feefb2db48a84e7f9808accd7fd68df7", "e10b53d12a134bc6a674bc568e1ab7bf", "80cbef017c5041aab8fe651fe5e5f0a0", "53d29032d6a64f958dffaced43b46f58", "1093b380f208425aadf0085c0aebed80", "4032d6814ad44a2f86c2f3b9cbb7afad", "35b0f9e4188f4c22bd2ec62fb8c7e228", "3243cc3a35e742048fb4d62f1c448752", "df9f065bf72b44f289a57855e5f38620", "7303bc3b53094dcd8fd4966f18622f20", "0003c40814494b279a4f21e422723caa", "124aae7bb4a94223b8ae7f6b8d20961a", "65ea6829bd484590a9e99f07bbadf52a", "404791f1b4034ed9ad68752480b55731", "84610b3317ec4cad9bfe284ab48f3a0f", "2ef3a65aadc24bd085f95acf4c3de5f5", "a818ce650c9c43deb8f674dd9e08c1ef", "3d786f96440a4a76b1b09f80c3d1c0e9", "ee568de931f1427f9fb252f24a6bdb01", "a77440b9604140a78d99cca3e9845a1f", "56021b483de347999853740bce5b4066", "c1e6987ebc5f40ce90d4c94056c74333", "7631d285c62c4c848930eef7390ef58b", "03b41c21d7634543a1b29eaadbf1cd40", "ff122787d9174a718b5d3223f53edf22", "70896fbd2fab4db29322fc78e2cd84c5", "4673095a06fc47b581f46754117e83fe", "435cd70149224c18aeff12ad5d2926b8", "1aa72a1d1d0d4ce4b36b10c8ecf021a8", "a004e9208a5f441e9e4e60cc1f6d7aa9", "1407812065e84df28e3cbfce5bc26fc9", "a86aecff032345c5a3f176851052bcf0", "cc8d776ab3b4482ba8a2cab6bce3f310", "5052e40d06ed4eefa2f6df4be951aa3d", "0c7cd9bca9994ee4abc817035db4f115", "7f622c1b4fef4ebb8c290cfc164f3a95", "e05e528fd80548c1b7147d1756d980c9", "073e4cf5a7f944048d507076589bcf7e", "1794dcbd585747adb0530f76ec4d5be8", "31f8708ee9444a82923272f09da1fe91", "2fad7fdcdb574f75bf994a363f8c31c9", "59086cae36714c9bb971cf48e9d3f78b", "0715885acccf4440aed4955f4aaff0f2", "6adc4e60532f4558ade289ad526aa87d", "25634e71ebbf4a41baf59a8cb8540bf3", "1d8bd9a5ec064157a2099a23621ccdad", "16ca05cd0a404e36b737bae31235fac6", "f4065d3723d649bfa930624fca67ddd9", "92a0e4161e0a4d7e88aaf764f4df92b7", "6deaa7e61cf443e99dce5869e1281c0c", "05d95e4de7b84da282818e23c8c22345", "04ac7654040b4002a1f359c4cd000cc5", "4fd685420f4e41edb9d9c97e6ac89585", "1a319e2196e3465989cbf69e736fbd3e", "a70c5ea79cba4f4bac16c1bebe5aa0b5", "dd2a3af3a6144a9ba59fb130090c0283", "baa1b968a1de4d029730f6a5a2301101", "4d080de8a7dc4809abafd654f1f62691", "16708105f96343c482ea0df18d42cbf3", "47fb8b7a47eb47179ccb0e2e1b313fbd", "f9fb3e9d512944f295b81b249119d025", "ee9b1c6c792143da997c0b563a95ffce", "7bc84a52af5d4b1398b0ef212155e857", "4ad4cbf343684ded9a8fc5f8274a261c", "e5d62477070e475c93ededb4de412063", "9e26503e85234dda982f4a731dacabbf", "c0e6950f66d84e5091051fbe173b2b3f", "b6c12557e5814d4db2ca39372d44a167", "59d72ddb22ee4efeb86af48c83dd8110", "ae4e761cd74642788667953acf5fd0aa", "40904407149e4ceea2f3bdf769aba0d1", "da08e39e0cbd43b68dd1058954a3d93f", "f1b1def618354651b0c7662b055d7cb2", "02672649160d44c3a21f0dbb4f1142c2", "c19e821b4a7f458085202f1d2bd148dc", "86215ff3b19748859771c04a654a92a6", "853df198b21147f390d68af8043610b1", "86af0415540c41658b4f125e3f372c52", "e6ed1837c16b47d3913f123a7c09d833", "f9af1d0e95b846e7923b0fbf48df969d", "7c58d359b4934b8c8b99b9aa13abb661", "d22351f5e6844faca41027ee44ead93e", "162e0a2a77b743868d3e6c53f2220b14", "22f984300d814049bb2cfa3c68c6461a", "1dc30290e4c4478695246cef4b785563", "da199afde5ed47cdad0801ce45cf34a7", "76cc2729929048d0a9ea919735fea53e", "2ab8ed5174004d60ba0ecfc7505e3c54", "061f919ac1c74f078b5c909ff889e894", "e63c3a8c021b4d30b0c93b99a7dba155", "45cc7c0a9c664353ba11a5049c0353b6", "514e5d566f734683bec2319b987c16dd", "21687939a40a491395c37bec223c37fa", "440101772f084da5b3e5724fb28c52d1", "32f7c34cd24c4d83976c96ee0cdc05cf", "57e3acfbf42c41d2b2a3774247e046b6", "bd508aa803f94073af7a91332e738668", "ddd131d700884af596f2b77c921f48c5", "8c785cb20818402a829e54ff3467e03f", "3d17ac67bb8a4fc9b4709f89de589fcd"]}, "id": "r6bUnxe6N3pf", "outputId": "ff62990d-3e46-4a6d-826f-d4533b596a98"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5e875e8a442a41e8afef4ad494ef6897", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading readme:   0%|          | 0.00/5.98k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3b258a09c7e0462e9d9fefa6746ebb9f", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/222M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "243999bf48174e72ae981effb01117ac", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/3.50M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6730cd77f7fa416b97f20fc5f5660488", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/180M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "757089cbb90f4d538e4a98a9ecee7dc6", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/2.84M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "97a28a558b2a4ef997fb35547ef2f51d", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/222M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e10b53d12a134bc6a674bc568e1ab7bf", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/7.12M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "65ea6829bd484590a9e99f07bbadf52a", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train_sft split:   0%|          | 0/61966 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "03b41c21d7634543a1b29eaadbf1cd40", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating test_sft split:   0%|          | 0/1000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0c7cd9bca9994ee4abc817035db4f115", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train_gen split:   0%|          | 0/61966 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1d8bd9a5ec064157a2099a23621ccdad", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating test_gen split:   0%|          | 0/1000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "baa1b968a1de4d029730f6a5a2301101", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train_prefs split:   0%|          | 0/61966 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b6c12557e5814d4db2ca39372d44a167", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating test_prefs split:   0%|          | 0/2000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e6ed1837c16b47d3913f123a7c09d833", "version_major": 2, "version_minor": 0}, "text/plain": ["Formatting comparisons with prompt template (num_proc=12):   0%|          | 0/309 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e63c3a8c021b4d30b0c93b99a7dba155", "version_major": 2, "version_minor": 0}, "text/plain": ["Formatting comparisons with prompt template (num_proc=12):   0%|          | 0/2000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["raw_datasets = get_datasets(\n", "    {\"HuggingFaceH4/ultrafeedback_binarized\" : 0.005}, # 0.5% sampled\n", "    splits = [\"train_prefs\", \"test_prefs\"],\n", ")\n", "column_names = list(raw_datasets[\"train\"].features)\n", "\n", "raw_datasets = raw_datasets.map(\n", "    apply_chat_template,\n", "    fn_kwargs = {\"tokenizer\": tokenizer, \"task\": \"dpo\"},\n", "    num_proc = 12,\n", "    remove_columns = column_names,\n", "    desc = \"Formatting comparisons with prompt template\",\n", ")\n", "\n", "# Replace column names with what TRL needs, text_chosen -> chosen and text_rejected -> rejected\n", "for split in [\"train\", \"test\"]:\n", "    raw_datasets[split] = raw_datasets[split].rename_columns(\n", "        {\"text_prompt\": \"prompt\", \"text_chosen\": \"chosen\", \"text_rejected\": \"rejected\"}\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "7AxUmeAGkjDd"}, "source": ["We shall print a random item from the dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "oF63zQqNlNJC", "outputId": "5e4e2858-9ac2-4023-f554-13bfeec367a8"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('<|system|>\\n'\n", " '</s>\\n'\n", " '<|user|>\\n'\n", " 'List two natural resources which was made in the factory.</s>\\n'\n", " '<|assistant|>\\n')\n", "('Natural resources are not made in factories. Natural resources are materials '\n", " 'and substances that occur naturally on Earth, such as water, minerals, '\n", " 'forests, and fossil fuels. Factories typically produce man-made materials or '\n", " 'process natural resources into finished products.</s>\\n')\n", "(\"I'm sorry, but it seems there might be some confusion in your question as \"\n", " 'natural resources are typically sourced from the earth or sea, and not made '\n", " 'in a factory. However, factories often use natural resources to create '\n", " 'various products. Two examples of natural resources that factories may use '\n", " 'are crude oil and iron ore. Crude oil is refined to produce various '\n", " 'petroleum products, such as gasoline and plastics, while iron ore is refined '\n", " 'to create steel, which is used in the construction industry, vehicle '\n", " 'manufacturing, and more. Does this help clarify things?</s>\\n')\n"]}], "source": ["import pprint\n", "\n", "row = raw_datasets[\"train\"][8]\n", "pprint.pprint(row[\"prompt\"])\n", "pprint.pprint(row[\"chosen\"])\n", "pprint.pprint(row[\"rejected\"])"]}, {"cell_type": "markdown", "metadata": {"id": "86wyNoeMj-Ph"}, "source": ["We now add LoRA adapters so we only need to update 1 to 10% of all parameters!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6bZsfBuZDeCL", "outputId": "677aef23-d549-44d3-aace-9efda302d1e8"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth 2024.1 patched 32 layers with 32 QKV layers, 32 O layers and 32 MLP layers.\n"]}], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 64, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 64,\n", "    lora_dropout = 0, # Currently only supports dropout = 0\n", "    bias = \"none\",    # Currently only supports bias = \"none\"\n", "    # [NEW] \"unsloth\" uses 30% less VRAM, fits 2x larger batch sizes!\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for very long context\n", "    random_state = 3407,\n", "    use_rslora = False,  # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "-kyd_iyz7DUM"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the DPO model\n", "Now let's use Huggingface TRL's `DPOTrainer`! More docs here: [TRL DPO docs](https://huggingface.co/docs/trl/dpo_trainer). We do 3 epochs on 0.5% of the dataset to speed things up."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "v-2BFpDWzo1K"}, "outputs": [], "source": ["# One must patch the DPO Trainer first!\n", "from unsloth import PatchDPOTrainer\n", "\n", "PatchDPOTrainer()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 104, "referenced_widgets": ["1410214ec6fd4e19a200b5e03464ceee", "0ba04e31d310422f9efaef634afb1296", "6ac7582965d341c383c806ae3a8b513a", "971371cfb1bd447e94f9f2e38f9f50c8", "3d4dbe5814d24b7dae1a7462a33e7f40", "6b4743fee43b40638d8f2df36fa9fed9", "77f706dc09eb43d7aa4522f8a0b36142", "175c7bb9e8c94b8daa3287dd773b6837", "a10418a202e14a96893e24267bc194c9", "8138d9f175df4a28b2e58e9c852cabb8", "43b39a8abcb94847bc21ec16fa373ed9"]}, "id": "QtoqUw80QDV0", "outputId": "40764ccc-6502-4be1-fb4f-e386f5460147"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/trl/trainer/dpo_trainer.py:294: UserWarning: When using DPODataCollatorWithPadding, you should set `remove_unused_columns=False` in your TrainingArguments we have set it for you, but you should do it yourself in the future.\n", "  warnings.warn(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1410214ec6fd4e19a200b5e03464ceee", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/309 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from transformers import TrainingArguments\n", "from trl import DPOTrainer, DPOConfig\n", "dpo_trainer = DPOTrainer(\n", "    model = model,\n", "    ref_model = None,\n", "    args = DPOConfig(\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4,\n", "        warmup_ratio = 0.1,\n", "        num_train_epochs = 3,\n", "        learning_rate = 5e-6,\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.0,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 42,\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", "    beta = 0.1,\n", "    train_dataset = raw_datasets[\"train\"],\n", "    # eval_dataset = raw_datasets[\"test\"],\n", "    tokenizer = tokenizer,\n", "    max_length = 1024,\n", "    max_prompt_length = 512,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "EWGFqAo5Q2me", "outputId": "1bcba65a-8253-4a49-a180-fde8e26ad4b5"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth: `use_cache=True` is incompatible with gradient checkpointing. Setting `use_cache=False`\n", "Could not estimate the number of tokens of the input, floating-point operations will not be computed\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='114' max='114' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [114/114 1:06:24, Epoch 2/3]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "      <th>rewards / chosen</th>\n", "      <th>rewards / rejected</th>\n", "      <th>rewards / accuracies</th>\n", "      <th>rewards / margins</th>\n", "      <th>logps / rejected</th>\n", "      <th>logps / chosen</th>\n", "      <th>logits / rejected</th>\n", "      <th>logits / chosen</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.693100</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>-297.338806</td>\n", "      <td>-218.968842</td>\n", "      <td>-2.758142</td>\n", "      <td>-2.924523</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.693100</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>-237.602417</td>\n", "      <td>-217.613892</td>\n", "      <td>-2.731790</td>\n", "      <td>-2.913610</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.692200</td>\n", "      <td>0.001937</td>\n", "      <td>-0.000008</td>\n", "      <td>0.625000</td>\n", "      <td>0.001945</td>\n", "      <td>-172.792877</td>\n", "      <td>-202.709259</td>\n", "      <td>-2.464616</td>\n", "      <td>-2.728198</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.692700</td>\n", "      <td>0.000855</td>\n", "      <td>-0.000130</td>\n", "      <td>0.750000</td>\n", "      <td>0.000985</td>\n", "      <td>-117.745728</td>\n", "      <td>-170.711212</td>\n", "      <td>-2.591983</td>\n", "      <td>-2.805891</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.692500</td>\n", "      <td>0.001272</td>\n", "      <td>-0.000044</td>\n", "      <td>0.500000</td>\n", "      <td>0.001315</td>\n", "      <td>-197.045288</td>\n", "      <td>-338.722626</td>\n", "      <td>-2.541809</td>\n", "      <td>-2.483452</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.693700</td>\n", "      <td>-0.000155</td>\n", "      <td>0.001034</td>\n", "      <td>0.500000</td>\n", "      <td>-0.001189</td>\n", "      <td>-279.681396</td>\n", "      <td>-188.683563</td>\n", "      <td>-2.870259</td>\n", "      <td>-2.633069</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.692600</td>\n", "      <td>-0.001525</td>\n", "      <td>-0.002655</td>\n", "      <td>0.625000</td>\n", "      <td>0.001130</td>\n", "      <td>-157.678253</td>\n", "      <td>-236.215118</td>\n", "      <td>-2.510528</td>\n", "      <td>-2.838598</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.692500</td>\n", "      <td>0.003246</td>\n", "      <td>0.001937</td>\n", "      <td>0.500000</td>\n", "      <td>0.001309</td>\n", "      <td>-451.776550</td>\n", "      <td>-493.112579</td>\n", "      <td>-2.677159</td>\n", "      <td>-2.677977</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.693600</td>\n", "      <td>-0.003702</td>\n", "      <td>-0.002773</td>\n", "      <td>0.500000</td>\n", "      <td>-0.000929</td>\n", "      <td>-198.079987</td>\n", "      <td>-405.686523</td>\n", "      <td>-2.366659</td>\n", "      <td>-2.640016</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.685900</td>\n", "      <td>0.009754</td>\n", "      <td>-0.004801</td>\n", "      <td>0.750000</td>\n", "      <td>0.014555</td>\n", "      <td>-192.067322</td>\n", "      <td>-238.161560</td>\n", "      <td>-2.735095</td>\n", "      <td>-2.766379</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.690100</td>\n", "      <td>0.006233</td>\n", "      <td>-0.000102</td>\n", "      <td>0.625000</td>\n", "      <td>0.006335</td>\n", "      <td>-279.058716</td>\n", "      <td>-242.637787</td>\n", "      <td>-2.741516</td>\n", "      <td>-2.707231</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.676800</td>\n", "      <td>0.029851</td>\n", "      <td>-0.003707</td>\n", "      <td>0.875000</td>\n", "      <td>0.033558</td>\n", "      <td>-293.294495</td>\n", "      <td>-297.151794</td>\n", "      <td>-2.636089</td>\n", "      <td>-2.797787</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.684900</td>\n", "      <td>0.035930</td>\n", "      <td>0.018558</td>\n", "      <td>0.625000</td>\n", "      <td>0.017372</td>\n", "      <td>-422.263824</td>\n", "      <td>-439.903442</td>\n", "      <td>-2.782973</td>\n", "      <td>-2.722686</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.691100</td>\n", "      <td>-0.007306</td>\n", "      <td>-0.012685</td>\n", "      <td>0.500000</td>\n", "      <td>0.005379</td>\n", "      <td>-229.290237</td>\n", "      <td>-517.691650</td>\n", "      <td>-2.761815</td>\n", "      <td>-2.876947</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.675700</td>\n", "      <td>0.052242</td>\n", "      <td>0.016329</td>\n", "      <td>0.875000</td>\n", "      <td>0.035913</td>\n", "      <td>-229.797638</td>\n", "      <td>-368.169067</td>\n", "      <td>-2.686517</td>\n", "      <td>-2.849208</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.671000</td>\n", "      <td>0.049620</td>\n", "      <td>0.002834</td>\n", "      <td>0.750000</td>\n", "      <td>0.046786</td>\n", "      <td>-153.669510</td>\n", "      <td>-214.005783</td>\n", "      <td>-2.551779</td>\n", "      <td>-2.668840</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.659200</td>\n", "      <td>0.031351</td>\n", "      <td>-0.039343</td>\n", "      <td>0.750000</td>\n", "      <td>0.070693</td>\n", "      <td>-145.433411</td>\n", "      <td>-178.029861</td>\n", "      <td>-2.550349</td>\n", "      <td>-2.744968</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.670900</td>\n", "      <td>0.042720</td>\n", "      <td>-0.003664</td>\n", "      <td>0.625000</td>\n", "      <td>0.046384</td>\n", "      <td>-193.517059</td>\n", "      <td>-306.975281</td>\n", "      <td>-2.544961</td>\n", "      <td>-2.794021</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.682400</td>\n", "      <td>0.049649</td>\n", "      <td>0.026523</td>\n", "      <td>0.625000</td>\n", "      <td>0.023126</td>\n", "      <td>-248.459091</td>\n", "      <td>-226.184174</td>\n", "      <td>-2.847072</td>\n", "      <td>-2.758221</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.667900</td>\n", "      <td>0.097072</td>\n", "      <td>0.045065</td>\n", "      <td>0.875000</td>\n", "      <td>0.052007</td>\n", "      <td>-265.443054</td>\n", "      <td>-420.395142</td>\n", "      <td>-2.906176</td>\n", "      <td>-2.950474</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.712400</td>\n", "      <td>0.009529</td>\n", "      <td>0.039224</td>\n", "      <td>0.500000</td>\n", "      <td>-0.029695</td>\n", "      <td>-156.176773</td>\n", "      <td>-202.126480</td>\n", "      <td>-2.445233</td>\n", "      <td>-2.536827</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.664700</td>\n", "      <td>0.027259</td>\n", "      <td>-0.032389</td>\n", "      <td>0.750000</td>\n", "      <td>0.059647</td>\n", "      <td>-241.187759</td>\n", "      <td>-258.569122</td>\n", "      <td>-2.660511</td>\n", "      <td>-2.759560</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.612400</td>\n", "      <td>0.111760</td>\n", "      <td>-0.064276</td>\n", "      <td>0.750000</td>\n", "      <td>0.176036</td>\n", "      <td>-172.977020</td>\n", "      <td>-305.922211</td>\n", "      <td>-2.420000</td>\n", "      <td>-2.796694</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.675200</td>\n", "      <td>0.064246</td>\n", "      <td>0.021886</td>\n", "      <td>0.375000</td>\n", "      <td>0.042360</td>\n", "      <td>-180.612473</td>\n", "      <td>-224.649567</td>\n", "      <td>-2.242796</td>\n", "      <td>-2.368723</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.638300</td>\n", "      <td>0.059822</td>\n", "      <td>-0.055270</td>\n", "      <td>0.875000</td>\n", "      <td>0.115092</td>\n", "      <td>-109.729645</td>\n", "      <td>-135.127365</td>\n", "      <td>-2.535609</td>\n", "      <td>-2.693033</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.682100</td>\n", "      <td>0.044842</td>\n", "      <td>0.019694</td>\n", "      <td>0.625000</td>\n", "      <td>0.025148</td>\n", "      <td>-217.967117</td>\n", "      <td>-216.428604</td>\n", "      <td>-2.346493</td>\n", "      <td>-2.305699</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.601700</td>\n", "      <td>0.134719</td>\n", "      <td>-0.065319</td>\n", "      <td>0.875000</td>\n", "      <td>0.200038</td>\n", "      <td>-254.221039</td>\n", "      <td>-275.279114</td>\n", "      <td>-2.407781</td>\n", "      <td>-2.619088</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.690600</td>\n", "      <td>0.026908</td>\n", "      <td>-0.005121</td>\n", "      <td>0.500000</td>\n", "      <td>0.032030</td>\n", "      <td>-241.214081</td>\n", "      <td>-239.618500</td>\n", "      <td>-2.711054</td>\n", "      <td>-2.787158</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.640600</td>\n", "      <td>0.031898</td>\n", "      <td>-0.091680</td>\n", "      <td>0.625000</td>\n", "      <td>0.123578</td>\n", "      <td>-176.982712</td>\n", "      <td>-201.977280</td>\n", "      <td>-2.830093</td>\n", "      <td>-2.735838</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.559400</td>\n", "      <td>0.228695</td>\n", "      <td>-0.072701</td>\n", "      <td>0.875000</td>\n", "      <td>0.301397</td>\n", "      <td>-289.870209</td>\n", "      <td>-314.927216</td>\n", "      <td>-2.553982</td>\n", "      <td>-2.775172</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.679700</td>\n", "      <td>0.034291</td>\n", "      <td>-0.020282</td>\n", "      <td>0.875000</td>\n", "      <td>0.054574</td>\n", "      <td>-251.074112</td>\n", "      <td>-229.884064</td>\n", "      <td>-2.657125</td>\n", "      <td>-2.649465</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.609900</td>\n", "      <td>0.073758</td>\n", "      <td>-0.113377</td>\n", "      <td>0.750000</td>\n", "      <td>0.187135</td>\n", "      <td>-183.661713</td>\n", "      <td>-286.482422</td>\n", "      <td>-2.423700</td>\n", "      <td>-2.839998</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.646400</td>\n", "      <td>0.127758</td>\n", "      <td>-0.020295</td>\n", "      <td>0.625000</td>\n", "      <td>0.148053</td>\n", "      <td>-394.591248</td>\n", "      <td>-512.621033</td>\n", "      <td>-2.927793</td>\n", "      <td>-2.909138</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.637400</td>\n", "      <td>0.028342</td>\n", "      <td>-0.090154</td>\n", "      <td>0.875000</td>\n", "      <td>0.118495</td>\n", "      <td>-214.089691</td>\n", "      <td>-186.278961</td>\n", "      <td>-2.780502</td>\n", "      <td>-2.638843</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.682000</td>\n", "      <td>0.006539</td>\n", "      <td>-0.016702</td>\n", "      <td>0.500000</td>\n", "      <td>0.023241</td>\n", "      <td>-210.243164</td>\n", "      <td>-83.531448</td>\n", "      <td>-2.730125</td>\n", "      <td>-2.316058</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.533200</td>\n", "      <td>0.213769</td>\n", "      <td>-0.195208</td>\n", "      <td>1.000000</td>\n", "      <td>0.408977</td>\n", "      <td>-279.895813</td>\n", "      <td>-429.939270</td>\n", "      <td>-2.432103</td>\n", "      <td>-2.655017</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.656300</td>\n", "      <td>0.063138</td>\n", "      <td>-0.016303</td>\n", "      <td>0.750000</td>\n", "      <td>0.079441</td>\n", "      <td>-141.416199</td>\n", "      <td>-138.177078</td>\n", "      <td>-2.473918</td>\n", "      <td>-2.547494</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.651000</td>\n", "      <td>0.094509</td>\n", "      <td>-0.019390</td>\n", "      <td>0.875000</td>\n", "      <td>0.113899</td>\n", "      <td>-200.108643</td>\n", "      <td>-165.778290</td>\n", "      <td>-2.236758</td>\n", "      <td>-2.336314</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.552100</td>\n", "      <td>0.149231</td>\n", "      <td>-0.200812</td>\n", "      <td>0.875000</td>\n", "      <td>0.350043</td>\n", "      <td>-232.878784</td>\n", "      <td>-223.073883</td>\n", "      <td>-2.657633</td>\n", "      <td>-2.716261</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.400600</td>\n", "      <td>0.367951</td>\n", "      <td>-0.639771</td>\n", "      <td>1.000000</td>\n", "      <td>1.007722</td>\n", "      <td>-267.012238</td>\n", "      <td>-173.167068</td>\n", "      <td>-2.681762</td>\n", "      <td>-2.732720</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.437000</td>\n", "      <td>0.394438</td>\n", "      <td>-0.288346</td>\n", "      <td>1.000000</td>\n", "      <td>0.682784</td>\n", "      <td>-186.329910</td>\n", "      <td>-195.446442</td>\n", "      <td>-2.616049</td>\n", "      <td>-2.464377</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.373200</td>\n", "      <td>0.502430</td>\n", "      <td>-0.394705</td>\n", "      <td>1.000000</td>\n", "      <td>0.897135</td>\n", "      <td>-184.657394</td>\n", "      <td>-346.536224</td>\n", "      <td>-2.492926</td>\n", "      <td>-2.774618</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.527200</td>\n", "      <td>0.093942</td>\n", "      <td>-0.300091</td>\n", "      <td>1.000000</td>\n", "      <td>0.394033</td>\n", "      <td>-130.909210</td>\n", "      <td>-115.344902</td>\n", "      <td>-2.750227</td>\n", "      <td>-2.718995</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.394000</td>\n", "      <td>0.518654</td>\n", "      <td>-0.385459</td>\n", "      <td>1.000000</td>\n", "      <td>0.904113</td>\n", "      <td>-229.264954</td>\n", "      <td>-317.110352</td>\n", "      <td>-2.540475</td>\n", "      <td>-2.699816</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.281200</td>\n", "      <td>0.686930</td>\n", "      <td>-0.696812</td>\n", "      <td>1.000000</td>\n", "      <td>1.383742</td>\n", "      <td>-244.271576</td>\n", "      <td>-283.048157</td>\n", "      <td>-2.240731</td>\n", "      <td>-2.588020</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.242200</td>\n", "      <td>0.650970</td>\n", "      <td>-0.705772</td>\n", "      <td>1.000000</td>\n", "      <td>1.356742</td>\n", "      <td>-203.522079</td>\n", "      <td>-258.910522</td>\n", "      <td>-2.546949</td>\n", "      <td>-2.711936</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.394500</td>\n", "      <td>0.561647</td>\n", "      <td>-0.338273</td>\n", "      <td>1.000000</td>\n", "      <td>0.899920</td>\n", "      <td>-142.371277</td>\n", "      <td>-239.892838</td>\n", "      <td>-2.526323</td>\n", "      <td>-2.609220</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.374800</td>\n", "      <td>0.471081</td>\n", "      <td>-0.427850</td>\n", "      <td>1.000000</td>\n", "      <td>0.898931</td>\n", "      <td>-160.959320</td>\n", "      <td>-212.728607</td>\n", "      <td>-2.422826</td>\n", "      <td>-2.570896</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.327000</td>\n", "      <td>0.703227</td>\n", "      <td>-0.639334</td>\n", "      <td>1.000000</td>\n", "      <td>1.342561</td>\n", "      <td>-219.936340</td>\n", "      <td>-206.114929</td>\n", "      <td>-2.642008</td>\n", "      <td>-2.712688</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.286000</td>\n", "      <td>0.396052</td>\n", "      <td>-0.969404</td>\n", "      <td>1.000000</td>\n", "      <td>1.365456</td>\n", "      <td>-357.370056</td>\n", "      <td>-308.878326</td>\n", "      <td>-2.753402</td>\n", "      <td>-2.674081</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>0.191800</td>\n", "      <td>0.713322</td>\n", "      <td>-1.066314</td>\n", "      <td>1.000000</td>\n", "      <td>1.779635</td>\n", "      <td>-405.730530</td>\n", "      <td>-336.061005</td>\n", "      <td>-2.561202</td>\n", "      <td>-2.704443</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>0.378700</td>\n", "      <td>0.434130</td>\n", "      <td>-0.507735</td>\n", "      <td>1.000000</td>\n", "      <td>0.941865</td>\n", "      <td>-231.648224</td>\n", "      <td>-199.180984</td>\n", "      <td>-2.683644</td>\n", "      <td>-2.706589</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>0.446800</td>\n", "      <td>0.479577</td>\n", "      <td>-0.329427</td>\n", "      <td>1.000000</td>\n", "      <td>0.809003</td>\n", "      <td>-130.374908</td>\n", "      <td>-295.123444</td>\n", "      <td>-2.684445</td>\n", "      <td>-2.744673</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>0.273000</td>\n", "      <td>0.722516</td>\n", "      <td>-0.610364</td>\n", "      <td>1.000000</td>\n", "      <td>1.332880</td>\n", "      <td>-239.890060</td>\n", "      <td>-334.665283</td>\n", "      <td>-2.653185</td>\n", "      <td>-2.843446</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>0.452400</td>\n", "      <td>0.024534</td>\n", "      <td>-0.629551</td>\n", "      <td>0.875000</td>\n", "      <td>0.654085</td>\n", "      <td>-188.501862</td>\n", "      <td>-130.375015</td>\n", "      <td>-2.748917</td>\n", "      <td>-2.645331</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>0.266100</td>\n", "      <td>0.659037</td>\n", "      <td>-0.710914</td>\n", "      <td>1.000000</td>\n", "      <td>1.369951</td>\n", "      <td>-213.674606</td>\n", "      <td>-326.524048</td>\n", "      <td>-2.625597</td>\n", "      <td>-2.745638</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>0.210400</td>\n", "      <td>1.066818</td>\n", "      <td>-0.894150</td>\n", "      <td>1.000000</td>\n", "      <td>1.960968</td>\n", "      <td>-356.119354</td>\n", "      <td>-584.736694</td>\n", "      <td>-2.604546</td>\n", "      <td>-2.943971</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>0.216600</td>\n", "      <td>0.581929</td>\n", "      <td>-0.984844</td>\n", "      <td>1.000000</td>\n", "      <td>1.566772</td>\n", "      <td>-251.694885</td>\n", "      <td>-202.865784</td>\n", "      <td>-2.672240</td>\n", "      <td>-2.820692</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>0.252000</td>\n", "      <td>0.775185</td>\n", "      <td>-0.855321</td>\n", "      <td>1.000000</td>\n", "      <td>1.630505</td>\n", "      <td>-229.775177</td>\n", "      <td>-243.479630</td>\n", "      <td>-2.541421</td>\n", "      <td>-2.667074</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>0.235700</td>\n", "      <td>0.678536</td>\n", "      <td>-0.890656</td>\n", "      <td>1.000000</td>\n", "      <td>1.569192</td>\n", "      <td>-245.954163</td>\n", "      <td>-237.501450</td>\n", "      <td>-2.326149</td>\n", "      <td>-2.406452</td>\n", "    </tr>\n", "    <tr>\n", "      <td>61</td>\n", "      <td>0.370800</td>\n", "      <td>0.287420</td>\n", "      <td>-0.702332</td>\n", "      <td>0.875000</td>\n", "      <td>0.989753</td>\n", "      <td>-188.207214</td>\n", "      <td>-228.028946</td>\n", "      <td>-2.577148</td>\n", "      <td>-2.665427</td>\n", "    </tr>\n", "    <tr>\n", "      <td>62</td>\n", "      <td>0.262900</td>\n", "      <td>0.559217</td>\n", "      <td>-0.917912</td>\n", "      <td>1.000000</td>\n", "      <td>1.477129</td>\n", "      <td>-235.731598</td>\n", "      <td>-242.911835</td>\n", "      <td>-2.600375</td>\n", "      <td>-2.666599</td>\n", "    </tr>\n", "    <tr>\n", "      <td>63</td>\n", "      <td>0.320000</td>\n", "      <td>0.677785</td>\n", "      <td>-0.526713</td>\n", "      <td>1.000000</td>\n", "      <td>1.204498</td>\n", "      <td>-244.343613</td>\n", "      <td>-247.966156</td>\n", "      <td>-2.786634</td>\n", "      <td>-2.922563</td>\n", "    </tr>\n", "    <tr>\n", "      <td>64</td>\n", "      <td>0.294500</td>\n", "      <td>0.573417</td>\n", "      <td>-0.764463</td>\n", "      <td>1.000000</td>\n", "      <td>1.337880</td>\n", "      <td>-262.969116</td>\n", "      <td>-332.956848</td>\n", "      <td>-2.830733</td>\n", "      <td>-2.896999</td>\n", "    </tr>\n", "    <tr>\n", "      <td>65</td>\n", "      <td>0.244300</td>\n", "      <td>0.549664</td>\n", "      <td>-1.087809</td>\n", "      <td>1.000000</td>\n", "      <td>1.637473</td>\n", "      <td>-247.386093</td>\n", "      <td>-324.000397</td>\n", "      <td>-2.571335</td>\n", "      <td>-2.723726</td>\n", "    </tr>\n", "    <tr>\n", "      <td>66</td>\n", "      <td>0.476500</td>\n", "      <td>0.178537</td>\n", "      <td>-0.566751</td>\n", "      <td>0.875000</td>\n", "      <td>0.745289</td>\n", "      <td>-118.356186</td>\n", "      <td>-180.834473</td>\n", "      <td>-2.530770</td>\n", "      <td>-2.648711</td>\n", "    </tr>\n", "    <tr>\n", "      <td>67</td>\n", "      <td>0.223600</td>\n", "      <td>0.633353</td>\n", "      <td>-1.162451</td>\n", "      <td>1.000000</td>\n", "      <td>1.795804</td>\n", "      <td>-307.716217</td>\n", "      <td>-219.696091</td>\n", "      <td>-2.212558</td>\n", "      <td>-2.263221</td>\n", "    </tr>\n", "    <tr>\n", "      <td>68</td>\n", "      <td>0.269500</td>\n", "      <td>0.654256</td>\n", "      <td>-1.030838</td>\n", "      <td>1.000000</td>\n", "      <td>1.685093</td>\n", "      <td>-197.056473</td>\n", "      <td>-239.991241</td>\n", "      <td>-2.563220</td>\n", "      <td>-2.735702</td>\n", "    </tr>\n", "    <tr>\n", "      <td>69</td>\n", "      <td>0.258400</td>\n", "      <td>0.750621</td>\n", "      <td>-0.853553</td>\n", "      <td>1.000000</td>\n", "      <td>1.604174</td>\n", "      <td>-317.039642</td>\n", "      <td>-243.264709</td>\n", "      <td>-2.802447</td>\n", "      <td>-2.661048</td>\n", "    </tr>\n", "    <tr>\n", "      <td>70</td>\n", "      <td>0.294800</td>\n", "      <td>0.779875</td>\n", "      <td>-0.589920</td>\n", "      <td>0.875000</td>\n", "      <td>1.369795</td>\n", "      <td>-231.162201</td>\n", "      <td>-303.384857</td>\n", "      <td>-2.517439</td>\n", "      <td>-2.746666</td>\n", "    </tr>\n", "    <tr>\n", "      <td>71</td>\n", "      <td>0.301700</td>\n", "      <td>0.602643</td>\n", "      <td>-0.635765</td>\n", "      <td>1.000000</td>\n", "      <td>1.238408</td>\n", "      <td>-216.019257</td>\n", "      <td>-264.793274</td>\n", "      <td>-2.675880</td>\n", "      <td>-2.789920</td>\n", "    </tr>\n", "    <tr>\n", "      <td>72</td>\n", "      <td>0.264200</td>\n", "      <td>0.664522</td>\n", "      <td>-0.786896</td>\n", "      <td>1.000000</td>\n", "      <td>1.451418</td>\n", "      <td>-268.222992</td>\n", "      <td>-250.234924</td>\n", "      <td>-2.888840</td>\n", "      <td>-2.899784</td>\n", "    </tr>\n", "    <tr>\n", "      <td>73</td>\n", "      <td>0.194500</td>\n", "      <td>1.065459</td>\n", "      <td>-0.701678</td>\n", "      <td>1.000000</td>\n", "      <td>1.767137</td>\n", "      <td>-376.451355</td>\n", "      <td>-372.076660</td>\n", "      <td>-2.590312</td>\n", "      <td>-2.763450</td>\n", "    </tr>\n", "    <tr>\n", "      <td>74</td>\n", "      <td>0.270400</td>\n", "      <td>0.859751</td>\n", "      <td>-0.819957</td>\n", "      <td>0.875000</td>\n", "      <td>1.679707</td>\n", "      <td>-149.462494</td>\n", "      <td>-414.388550</td>\n", "      <td>-2.319313</td>\n", "      <td>-2.621300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>75</td>\n", "      <td>0.243600</td>\n", "      <td>0.477052</td>\n", "      <td>-1.359335</td>\n", "      <td>1.000000</td>\n", "      <td>1.836387</td>\n", "      <td>-351.890869</td>\n", "      <td>-252.686096</td>\n", "      <td>-2.666570</td>\n", "      <td>-2.624575</td>\n", "    </tr>\n", "    <tr>\n", "      <td>76</td>\n", "      <td>0.521800</td>\n", "      <td>0.133218</td>\n", "      <td>-0.285669</td>\n", "      <td>0.750000</td>\n", "      <td>0.418887</td>\n", "      <td>-156.737289</td>\n", "      <td>-191.685196</td>\n", "      <td>-2.572031</td>\n", "      <td>-2.732099</td>\n", "    </tr>\n", "    <tr>\n", "      <td>77</td>\n", "      <td>0.251800</td>\n", "      <td>0.547161</td>\n", "      <td>-0.981502</td>\n", "      <td>1.000000</td>\n", "      <td>1.528663</td>\n", "      <td>-247.284332</td>\n", "      <td>-368.113342</td>\n", "      <td>-2.663839</td>\n", "      <td>-2.841396</td>\n", "    </tr>\n", "    <tr>\n", "      <td>78</td>\n", "      <td>0.483600</td>\n", "      <td>0.198344</td>\n", "      <td>-0.428733</td>\n", "      <td>1.000000</td>\n", "      <td>0.627078</td>\n", "      <td>-94.173538</td>\n", "      <td>-73.744232</td>\n", "      <td>-2.580647</td>\n", "      <td>-2.671262</td>\n", "    </tr>\n", "    <tr>\n", "      <td>79</td>\n", "      <td>0.235200</td>\n", "      <td>0.804393</td>\n", "      <td>-0.938474</td>\n", "      <td>1.000000</td>\n", "      <td>1.742867</td>\n", "      <td>-206.442154</td>\n", "      <td>-208.880112</td>\n", "      <td>-2.574049</td>\n", "      <td>-2.685945</td>\n", "    </tr>\n", "    <tr>\n", "      <td>80</td>\n", "      <td>0.214900</td>\n", "      <td>0.959676</td>\n", "      <td>-1.044084</td>\n", "      <td>1.000000</td>\n", "      <td>2.003760</td>\n", "      <td>-316.608826</td>\n", "      <td>-234.795319</td>\n", "      <td>-2.721649</td>\n", "      <td>-2.706306</td>\n", "    </tr>\n", "    <tr>\n", "      <td>81</td>\n", "      <td>0.147000</td>\n", "      <td>0.606548</td>\n", "      <td>-1.598809</td>\n", "      <td>1.000000</td>\n", "      <td>2.205357</td>\n", "      <td>-220.293610</td>\n", "      <td>-220.745911</td>\n", "      <td>-2.564118</td>\n", "      <td>-2.648017</td>\n", "    </tr>\n", "    <tr>\n", "      <td>82</td>\n", "      <td>0.158600</td>\n", "      <td>0.695428</td>\n", "      <td>-1.412461</td>\n", "      <td>1.000000</td>\n", "      <td>2.107888</td>\n", "      <td>-268.839172</td>\n", "      <td>-226.468628</td>\n", "      <td>-2.439530</td>\n", "      <td>-2.542942</td>\n", "    </tr>\n", "    <tr>\n", "      <td>83</td>\n", "      <td>0.249200</td>\n", "      <td>0.614686</td>\n", "      <td>-0.954372</td>\n", "      <td>1.000000</td>\n", "      <td>1.569058</td>\n", "      <td>-152.696320</td>\n", "      <td>-262.169922</td>\n", "      <td>-2.440640</td>\n", "      <td>-2.618743</td>\n", "    </tr>\n", "    <tr>\n", "      <td>84</td>\n", "      <td>0.175000</td>\n", "      <td>0.862260</td>\n", "      <td>-1.443703</td>\n", "      <td>1.000000</td>\n", "      <td>2.305963</td>\n", "      <td>-283.026184</td>\n", "      <td>-323.999420</td>\n", "      <td>-2.673517</td>\n", "      <td>-2.778322</td>\n", "    </tr>\n", "    <tr>\n", "      <td>85</td>\n", "      <td>0.152300</td>\n", "      <td>1.219675</td>\n", "      <td>-1.141970</td>\n", "      <td>1.000000</td>\n", "      <td>2.361645</td>\n", "      <td>-261.000122</td>\n", "      <td>-287.041412</td>\n", "      <td>-2.748819</td>\n", "      <td>-2.854210</td>\n", "    </tr>\n", "    <tr>\n", "      <td>86</td>\n", "      <td>0.261500</td>\n", "      <td>0.703294</td>\n", "      <td>-0.766885</td>\n", "      <td>1.000000</td>\n", "      <td>1.470178</td>\n", "      <td>-109.584824</td>\n", "      <td>-180.936401</td>\n", "      <td>-2.330353</td>\n", "      <td>-2.552672</td>\n", "    </tr>\n", "    <tr>\n", "      <td>87</td>\n", "      <td>0.057000</td>\n", "      <td>1.309202</td>\n", "      <td>-1.812632</td>\n", "      <td>1.000000</td>\n", "      <td>3.121835</td>\n", "      <td>-432.551086</td>\n", "      <td>-569.634766</td>\n", "      <td>-2.979058</td>\n", "      <td>-2.924992</td>\n", "    </tr>\n", "    <tr>\n", "      <td>88</td>\n", "      <td>0.113400</td>\n", "      <td>1.130503</td>\n", "      <td>-1.825813</td>\n", "      <td>1.000000</td>\n", "      <td>2.956316</td>\n", "      <td>-415.485809</td>\n", "      <td>-326.073120</td>\n", "      <td>-2.788409</td>\n", "      <td>-2.560507</td>\n", "    </tr>\n", "    <tr>\n", "      <td>89</td>\n", "      <td>0.181200</td>\n", "      <td>0.784262</td>\n", "      <td>-1.230724</td>\n", "      <td>1.000000</td>\n", "      <td>2.014986</td>\n", "      <td>-262.161804</td>\n", "      <td>-377.537537</td>\n", "      <td>-2.736547</td>\n", "      <td>-2.671477</td>\n", "    </tr>\n", "    <tr>\n", "      <td>90</td>\n", "      <td>0.102100</td>\n", "      <td>1.122743</td>\n", "      <td>-1.496860</td>\n", "      <td>1.000000</td>\n", "      <td>2.619603</td>\n", "      <td>-396.485840</td>\n", "      <td>-384.253632</td>\n", "      <td>-2.682523</td>\n", "      <td>-2.880081</td>\n", "    </tr>\n", "    <tr>\n", "      <td>91</td>\n", "      <td>0.145400</td>\n", "      <td>1.151220</td>\n", "      <td>-1.096753</td>\n", "      <td>1.000000</td>\n", "      <td>2.247973</td>\n", "      <td>-216.382874</td>\n", "      <td>-324.985718</td>\n", "      <td>-2.543500</td>\n", "      <td>-2.719545</td>\n", "    </tr>\n", "    <tr>\n", "      <td>92</td>\n", "      <td>0.135600</td>\n", "      <td>0.915327</td>\n", "      <td>-1.424227</td>\n", "      <td>1.000000</td>\n", "      <td>2.339553</td>\n", "      <td>-353.060242</td>\n", "      <td>-352.221008</td>\n", "      <td>-2.704442</td>\n", "      <td>-2.904936</td>\n", "    </tr>\n", "    <tr>\n", "      <td>93</td>\n", "      <td>0.265600</td>\n", "      <td>0.771701</td>\n", "      <td>-0.672446</td>\n", "      <td>1.000000</td>\n", "      <td>1.444147</td>\n", "      <td>-121.571732</td>\n", "      <td>-253.565125</td>\n", "      <td>-2.585968</td>\n", "      <td>-2.709661</td>\n", "    </tr>\n", "    <tr>\n", "      <td>94</td>\n", "      <td>0.210300</td>\n", "      <td>0.828890</td>\n", "      <td>-1.182284</td>\n", "      <td>1.000000</td>\n", "      <td>2.011175</td>\n", "      <td>-220.569305</td>\n", "      <td>-219.065125</td>\n", "      <td>-2.253163</td>\n", "      <td>-2.439387</td>\n", "    </tr>\n", "    <tr>\n", "      <td>95</td>\n", "      <td>0.167300</td>\n", "      <td>0.742948</td>\n", "      <td>-1.223057</td>\n", "      <td>1.000000</td>\n", "      <td>1.966005</td>\n", "      <td>-239.375641</td>\n", "      <td>-261.817993</td>\n", "      <td>-2.562728</td>\n", "      <td>-2.784313</td>\n", "    </tr>\n", "    <tr>\n", "      <td>96</td>\n", "      <td>0.289100</td>\n", "      <td>0.318967</td>\n", "      <td>-1.059428</td>\n", "      <td>1.000000</td>\n", "      <td>1.378395</td>\n", "      <td>-198.750443</td>\n", "      <td>-181.136566</td>\n", "      <td>-2.669433</td>\n", "      <td>-2.856450</td>\n", "    </tr>\n", "    <tr>\n", "      <td>97</td>\n", "      <td>0.193100</td>\n", "      <td>0.473150</td>\n", "      <td>-1.672879</td>\n", "      <td>1.000000</td>\n", "      <td>2.146029</td>\n", "      <td>-219.311478</td>\n", "      <td>-153.676514</td>\n", "      <td>-2.851680</td>\n", "      <td>-2.796827</td>\n", "    </tr>\n", "    <tr>\n", "      <td>98</td>\n", "      <td>0.207800</td>\n", "      <td>0.637917</td>\n", "      <td>-1.200011</td>\n", "      <td>1.000000</td>\n", "      <td>1.837928</td>\n", "      <td>-158.673050</td>\n", "      <td>-235.140503</td>\n", "      <td>-2.663034</td>\n", "      <td>-2.721454</td>\n", "    </tr>\n", "    <tr>\n", "      <td>99</td>\n", "      <td>0.260400</td>\n", "      <td>0.444965</td>\n", "      <td>-0.946055</td>\n", "      <td>1.000000</td>\n", "      <td>1.391021</td>\n", "      <td>-161.993805</td>\n", "      <td>-174.353287</td>\n", "      <td>-2.403941</td>\n", "      <td>-2.721178</td>\n", "    </tr>\n", "    <tr>\n", "      <td>100</td>\n", "      <td>0.145000</td>\n", "      <td>0.601810</td>\n", "      <td>-1.648575</td>\n", "      <td>1.000000</td>\n", "      <td>2.250385</td>\n", "      <td>-209.413452</td>\n", "      <td>-150.945694</td>\n", "      <td>-2.433372</td>\n", "      <td>-2.527679</td>\n", "    </tr>\n", "    <tr>\n", "      <td>101</td>\n", "      <td>0.202400</td>\n", "      <td>0.970933</td>\n", "      <td>-0.949473</td>\n", "      <td>1.000000</td>\n", "      <td>1.920407</td>\n", "      <td>-248.121094</td>\n", "      <td>-240.405640</td>\n", "      <td>-2.550437</td>\n", "      <td>-2.803463</td>\n", "    </tr>\n", "    <tr>\n", "      <td>102</td>\n", "      <td>0.144400</td>\n", "      <td>1.012043</td>\n", "      <td>-1.278788</td>\n", "      <td>1.000000</td>\n", "      <td>2.290831</td>\n", "      <td>-260.144043</td>\n", "      <td>-226.668945</td>\n", "      <td>-2.700693</td>\n", "      <td>-2.540809</td>\n", "    </tr>\n", "    <tr>\n", "      <td>103</td>\n", "      <td>0.234300</td>\n", "      <td>0.627674</td>\n", "      <td>-0.960762</td>\n", "      <td>1.000000</td>\n", "      <td>1.588436</td>\n", "      <td>-154.225189</td>\n", "      <td>-159.628006</td>\n", "      <td>-2.725933</td>\n", "      <td>-2.771981</td>\n", "    </tr>\n", "    <tr>\n", "      <td>104</td>\n", "      <td>0.241300</td>\n", "      <td>0.620007</td>\n", "      <td>-1.055792</td>\n", "      <td>1.000000</td>\n", "      <td>1.675798</td>\n", "      <td>-208.798172</td>\n", "      <td>-225.909439</td>\n", "      <td>-2.412787</td>\n", "      <td>-2.513871</td>\n", "    </tr>\n", "    <tr>\n", "      <td>105</td>\n", "      <td>0.130100</td>\n", "      <td>1.341354</td>\n", "      <td>-1.176110</td>\n", "      <td>1.000000</td>\n", "      <td>2.517464</td>\n", "      <td>-260.019531</td>\n", "      <td>-385.742279</td>\n", "      <td>-2.458776</td>\n", "      <td>-2.786789</td>\n", "    </tr>\n", "    <tr>\n", "      <td>106</td>\n", "      <td>0.184600</td>\n", "      <td>0.906436</td>\n", "      <td>-0.938855</td>\n", "      <td>1.000000</td>\n", "      <td>1.845291</td>\n", "      <td>-239.872620</td>\n", "      <td>-370.590424</td>\n", "      <td>-2.573937</td>\n", "      <td>-2.770343</td>\n", "    </tr>\n", "    <tr>\n", "      <td>107</td>\n", "      <td>0.193000</td>\n", "      <td>0.909325</td>\n", "      <td>-1.122883</td>\n", "      <td>1.000000</td>\n", "      <td>2.032207</td>\n", "      <td>-195.062927</td>\n", "      <td>-323.660950</td>\n", "      <td>-2.605916</td>\n", "      <td>-2.813245</td>\n", "    </tr>\n", "    <tr>\n", "      <td>108</td>\n", "      <td>0.182900</td>\n", "      <td>0.899943</td>\n", "      <td>-1.053020</td>\n", "      <td>1.000000</td>\n", "      <td>1.952963</td>\n", "      <td>-203.585938</td>\n", "      <td>-228.325348</td>\n", "      <td>-2.465954</td>\n", "      <td>-2.610229</td>\n", "    </tr>\n", "    <tr>\n", "      <td>109</td>\n", "      <td>0.219900</td>\n", "      <td>0.795095</td>\n", "      <td>-1.129289</td>\n", "      <td>1.000000</td>\n", "      <td>1.924384</td>\n", "      <td>-185.338791</td>\n", "      <td>-262.326508</td>\n", "      <td>-2.308619</td>\n", "      <td>-2.451966</td>\n", "    </tr>\n", "    <tr>\n", "      <td>110</td>\n", "      <td>0.256900</td>\n", "      <td>0.728312</td>\n", "      <td>-0.873666</td>\n", "      <td>1.000000</td>\n", "      <td>1.601979</td>\n", "      <td>-181.493530</td>\n", "      <td>-167.788956</td>\n", "      <td>-2.650259</td>\n", "      <td>-2.627371</td>\n", "    </tr>\n", "    <tr>\n", "      <td>111</td>\n", "      <td>0.130700</td>\n", "      <td>1.187222</td>\n", "      <td>-1.249772</td>\n", "      <td>1.000000</td>\n", "      <td>2.436995</td>\n", "      <td>-304.987030</td>\n", "      <td>-357.321716</td>\n", "      <td>-2.437974</td>\n", "      <td>-2.653895</td>\n", "    </tr>\n", "    <tr>\n", "      <td>112</td>\n", "      <td>0.345400</td>\n", "      <td>0.525752</td>\n", "      <td>-0.475295</td>\n", "      <td>0.875000</td>\n", "      <td>1.001047</td>\n", "      <td>-97.024063</td>\n", "      <td>-129.096161</td>\n", "      <td>-2.403481</td>\n", "      <td>-2.571845</td>\n", "    </tr>\n", "    <tr>\n", "      <td>113</td>\n", "      <td>0.336500</td>\n", "      <td>0.365618</td>\n", "      <td>-0.640655</td>\n", "      <td>1.000000</td>\n", "      <td>1.006273</td>\n", "      <td>-111.093918</td>\n", "      <td>-75.943321</td>\n", "      <td>-2.735875</td>\n", "      <td>-2.555395</td>\n", "    </tr>\n", "    <tr>\n", "      <td>114</td>\n", "      <td>0.081500</td>\n", "      <td>1.106894</td>\n", "      <td>-1.968600</td>\n", "      <td>1.000000</td>\n", "      <td>3.075494</td>\n", "      <td>-341.697296</td>\n", "      <td>-328.623779</td>\n", "      <td>-2.506866</td>\n", "      <td>-2.463103</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["TrainOutput(global_step=114, training_loss=0.397163258179238, metrics={'train_runtime': 4017.5003, 'train_samples_per_second': 0.231, 'train_steps_per_second': 0.028, 'total_flos': 0.0, 'train_loss': 0.397163258179238, 'epoch': 2.94})"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["dpo_trainer.train()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"0003c40814494b279a4f21e422723caa": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "007871aa8b7f4de7bb0f96ebe08e5283": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "02672649160d44c3a21f0dbb4f1142c2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "03b41c21d7634543a1b29eaadbf1cd40": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ff122787d9174a718b5d3223f53edf22", "IPY_MODEL_70896fbd2fab4db29322fc78e2cd84c5", "IPY_MODEL_4673095a06fc47b581f46754117e83fe"], "layout": "IPY_MODEL_435cd70149224c18aeff12ad5d2926b8"}}, "03ec8d28c0f34116b42e4e0dc65e3dd3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "04ac7654040b4002a1f359c4cd000cc5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "05d95e4de7b84da282818e23c8c22345": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0611164c2c6f4778adcbda713521155b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "061f919ac1c74f078b5c909ff889e894": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "068c416eaaed476eacffb8f2f343e08f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2557c3090a8d4760ae844fb518e1b555", "max": 493443, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b508872df72d45beb26a625c7499cc8b", "value": 493443}}, "06d1fd85a45541609d11ec7f0bd3b444": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_623e912cfe404e69a64992fbb1c1bc47", "max": 624, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_69f9e4bf820047658699d55d84d682c0", "value": 624}}, "0715885acccf4440aed4955f4aaff0f2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "073e4cf5a7f944048d507076589bcf7e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6adc4e60532f4558ade289ad526aa87d", "placeholder": "​", "style": "IPY_MODEL_25634e71ebbf4a41baf59a8cb8540bf3", "value": " 61966/61966 [00:02&lt;00:00, 32772.44 examples/s]"}}, "079284acd5c6454eb03d8876a62e6f12": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0ba04e31d310422f9efaef634afb1296": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6b4743fee43b40638d8f2df36fa9fed9", "placeholder": "​", "style": "IPY_MODEL_77f706dc09eb43d7aa4522f8a0b36142", "value": "Map: 100%"}}, "0c7cd9bca9994ee4abc817035db4f115": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7f622c1b4fef4ebb8c290cfc164f3a95", "IPY_MODEL_e05e528fd80548c1b7147d1756d980c9", "IPY_MODEL_073e4cf5a7f944048d507076589bcf7e"], "layout": "IPY_MODEL_1794dcbd585747adb0530f76ec4d5be8"}}, "0dc6aefaa24043768d656f5a61d79545": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1093b380f208425aadf0085c0aebed80": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0003c40814494b279a4f21e422723caa", "placeholder": "​", "style": "IPY_MODEL_124aae7bb4a94223b8ae7f6b8d20961a", "value": " 7.12M/7.12M [00:03&lt;00:00, 1.38MB/s]"}}, "124aae7bb4a94223b8ae7f6b8d20961a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "127ed424448840cea8b14fc9997b9cde": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_859bc87dfda746819298e213e4e0b067", "max": 4125687685, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_32478ec41d044773b70eba24e2ac0b43", "value": 4125687685}}, "13b208926d4a4bae854e057bc9913100": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_90b49ecfaf4a44409b0b405271c9797c", "placeholder": "​", "style": "IPY_MODEL_9032335e102f465ea2e1adcc47462a8c", "value": " 3.50M/3.50M [00:01&lt;00:00, 2.19MB/s]"}}, "1407812065e84df28e3cbfce5bc26fc9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1410214ec6fd4e19a200b5e03464ceee": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0ba04e31d310422f9efaef634afb1296", "IPY_MODEL_6ac7582965d341c383c806ae3a8b513a", "IPY_MODEL_971371cfb1bd447e94f9f2e38f9f50c8"], "layout": "IPY_MODEL_3d4dbe5814d24b7dae1a7462a33e7f40"}}, "162e0a2a77b743868d3e6c53f2220b14": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "16708105f96343c482ea0df18d42cbf3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4ad4cbf343684ded9a8fc5f8274a261c", "max": 61966, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e5d62477070e475c93ededb4de412063", "value": 61966}}, "16ace0251b954bc08a33eaf3b2a47216": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "16ca05cd0a404e36b737bae31235fac6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_05d95e4de7b84da282818e23c8c22345", "placeholder": "​", "style": "IPY_MODEL_04ac7654040b4002a1f359c4cd000cc5", "value": "Generating test_gen split: 100%"}}, "175c7bb9e8c94b8daa3287dd773b6837": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1794dcbd585747adb0530f76ec4d5be8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1a319e2196e3465989cbf69e736fbd3e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1aa72a1d1d0d4ce4b36b10c8ecf021a8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1cd708f679de4d319af26797aecca94d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1d8bd9a5ec064157a2099a23621ccdad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_16ca05cd0a404e36b737bae31235fac6", "IPY_MODEL_f4065d3723d649bfa930624fca67ddd9", "IPY_MODEL_92a0e4161e0a4d7e88aaf764f4df92b7"], "layout": "IPY_MODEL_6deaa7e61cf443e99dce5869e1281c0c"}}, "1d917d774780421fbdca680009a706ac": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1dc30290e4c4478695246cef4b785563": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "21687939a40a491395c37bec223c37fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8c785cb20818402a829e54ff3467e03f", "placeholder": "​", "style": "IPY_MODEL_3d17ac67bb8a4fc9b4709f89de589fcd", "value": " 2000/2000 [00:03&lt;00:00, 1125.54 examples/s]"}}, "2189b92750854b1f9eaa70dc23d16c55": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_841f91867ff5489d92dfb1bbbe76ca70", "max": 221598833, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4631d92c2f3a44589478a981a4143836", "value": 221598833}}, "22f984300d814049bb2cfa3c68c6461a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "243999bf48174e72ae981effb01117ac": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_77e6abd454c94559825201f34d5de044", "IPY_MODEL_7522d496e9dd41828a4daddb9dd37883", "IPY_MODEL_13b208926d4a4bae854e057bc9913100"], "layout": "IPY_MODEL_78ab252184ac46eba3e82c076f6b914a"}}, "2557c3090a8d4760ae844fb518e1b555": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "25634e71ebbf4a41baf59a8cb8540bf3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2a197f7c6c4746e1a7fded1e0539bf29": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9f6197721b90446091bf8b97de656d4b", "placeholder": "​", "style": "IPY_MODEL_35edd118e5e54d19b87aa7e966e3c9fe", "value": " 5.98k/5.98k [00:00&lt;00:00, 248kB/s]"}}, "2ab8ed5174004d60ba0ecfc7505e3c54": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2b850e92569c4236a683b9df2de49775": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2cb8afe9c6e2441fa4e232146ecd08b4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_bc745fa3da114007af35ea15dbd7ab7d", "IPY_MODEL_127ed424448840cea8b14fc9997b9cde", "IPY_MODEL_803f5904c00d4cde8729226139e3c258"], "layout": "IPY_MODEL_4a273a69979b466dbf1c987552101fbf"}}, "2ef3a65aadc24bd085f95acf4c3de5f5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c1e6987ebc5f40ce90d4c94056c74333", "placeholder": "​", "style": "IPY_MODEL_7631d285c62c4c848930eef7390ef58b", "value": " 61966/61966 [00:02&lt;00:00, 24679.32 examples/s]"}}, "2fad7fdcdb574f75bf994a363f8c31c9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "30dc6079591b4908a57f5e285ebeda68": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_926319902fe84c5095e0b5bd4b3e8187", "max": 1795303, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9f660e1b7b9c4ae7922d51353783acf9", "value": 1795303}}, "31f8708ee9444a82923272f09da1fe91": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3243cc3a35e742048fb4d62f1c448752": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "32478ec41d044773b70eba24e2ac0b43": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "32f7c34cd24c4d83976c96ee0cdc05cf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "33dda465ac2f42fb8846171a974193ac": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "342cf2a9d6674b4d94fdbaf3b63b9b79": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3456b1a43ca742e3bd5bcf695a7b548e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "347ae8c1baf54004953d0eb9325f39f8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "35b0f9e4188f4c22bd2ec62fb8c7e228": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "35edd118e5e54d19b87aa7e966e3c9fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3635323c2caf4f7ab2034306cdac61cb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3760dc92a7b64410865257af9f95782c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e98afd2ed7b44211932dc55a2a0c3e3a", "placeholder": "​", "style": "IPY_MODEL_dfb40aa450f844e28319893415573906", "value": "Downloading data: 100%"}}, "37944b0ab46c4de7acb5e06b91f5da52": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e6c183829cfb45e389348c0d5a1d943d", "placeholder": "​", "style": "IPY_MODEL_feefb2db48a84e7f9808accd7fd68df7", "value": " 222M/222M [00:51&lt;00:00, 2.84MB/s]"}}, "379878ecc7e44b448425c4155e106477": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a4199d34e668433a98b0fed23ab84541", "placeholder": "​", "style": "IPY_MODEL_2b850e92569c4236a683b9df2de49775", "value": " 2.84M/2.84M [00:01&lt;00:00, 1.96MB/s]"}}, "37a2eb8b91ed42a1be411ecb9079a082": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "39a4f06a16f149789507232e4e28acb6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3a52a2ec426b4fc2aaad23c7d8ce6d2b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_33dda465ac2f42fb8846171a974193ac", "placeholder": "​", "style": "IPY_MODEL_bf6bc9099d1a475884be6f15115f01ad", "value": "generation_config.json: 100%"}}, "3b258a09c7e0462e9d9fefa6746ebb9f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ade70be3dc49443581a295e89faa65c6", "IPY_MODEL_ad03a2ff7f974fa3a0f1be2dee56c9a0", "IPY_MODEL_727d7d710957497c8bb91d52a04a00f3"], "layout": "IPY_MODEL_347ae8c1baf54004953d0eb9325f39f8"}}, "3ccdc55cf6384695967eaf79325b1aef": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3d17ac67bb8a4fc9b4709f89de589fcd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3d4dbe5814d24b7dae1a7462a33e7f40": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3d786f96440a4a76b1b09f80c3d1c0e9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3df8638084104cb7a0530a96ec6201af": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f61b85b63d5147db9f1040f30f4a3034", "placeholder": "​", "style": "IPY_MODEL_e152d696872242ab8e0bc00ce9b50142", "value": "special_tokens_map.json: 100%"}}, "3f6ccae685e34d05be9fde5538dc48cf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3df8638084104cb7a0530a96ec6201af", "IPY_MODEL_06d1fd85a45541609d11ec7f0bd3b444", "IPY_MODEL_a9d05a8c3aaf4fe0a2f061c3eeb48b44"], "layout": "IPY_MODEL_8095a44a62804d6d86c24d62b9e1a1c1"}}, "4032d6814ad44a2f86c2f3b9cbb7afad": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "404791f1b4034ed9ad68752480b55731": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3d786f96440a4a76b1b09f80c3d1c0e9", "placeholder": "​", "style": "IPY_MODEL_ee568de931f1427f9fb252f24a6bdb01", "value": "Generating train_sft split: 100%"}}, "40904407149e4ceea2f3bdf769aba0d1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_853df198b21147f390d68af8043610b1", "placeholder": "​", "style": "IPY_MODEL_86af0415540c41658b4f125e3f372c52", "value": " 2000/2000 [00:00&lt;00:00, 21572.25 examples/s]"}}, "435cd70149224c18aeff12ad5d2926b8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "43b39a8abcb94847bc21ec16fa373ed9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "440101772f084da5b3e5724fb28c52d1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "45cc7c0a9c664353ba11a5049c0353b6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_32f7c34cd24c4d83976c96ee0cdc05cf", "placeholder": "​", "style": "IPY_MODEL_57e3acfbf42c41d2b2a3774247e046b6", "value": "Formatting comparisons with prompt template (num_proc=12): 100%"}}, "461aeb76622449b6a0bb7a794ea16ced": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4631d92c2f3a44589478a981a4143836": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "4673095a06fc47b581f46754117e83fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cc8d776ab3b4482ba8a2cab6bce3f310", "placeholder": "​", "style": "IPY_MODEL_5052e40d06ed4eefa2f6df4be951aa3d", "value": " 1000/1000 [00:00&lt;00:00, 10542.80 examples/s]"}}, "47fb8b7a47eb47179ccb0e2e1b313fbd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9e26503e85234dda982f4a731dacabbf", "placeholder": "​", "style": "IPY_MODEL_c0e6950f66d84e5091051fbe173b2b3f", "value": " 61966/61966 [00:02&lt;00:00, 32103.21 examples/s]"}}, "4a273a69979b466dbf1c987552101fbf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4a6807afa14049c781c7549467b85e1c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4ad4cbf343684ded9a8fc5f8274a261c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4bd0cf4d31604194a7761ea0345cc0a4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4c7a9e6327fc4b839f1e2eee705e1574": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_904fef1ac7404cc8a33e65ffe8938c8b", "IPY_MODEL_f7562580501845e9a67ab9a8b4a96ba0", "IPY_MODEL_96f9847627504ae7abe7461e4c0b3cff"], "layout": "IPY_MODEL_5165aa69bef148af9717d90cc5a870b1"}}, "4cc5ba58138d47129b776b04bc2d6646": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "4d080de8a7dc4809abafd654f1f62691": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ee9b1c6c792143da997c0b563a95ffce", "placeholder": "​", "style": "IPY_MODEL_7bc84a52af5d4b1398b0ef212155e857", "value": "Generating train_prefs split: 100%"}}, "4e48c220077d4546a51c02df9397a084": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "4fd685420f4e41edb9d9c97e6ac89585": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5052e40d06ed4eefa2f6df4be951aa3d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "514e5d566f734683bec2319b987c16dd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bd508aa803f94073af7a91332e738668", "max": 2000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ddd131d700884af596f2b77c921f48c5", "value": 2000}}, "5165aa69bef148af9717d90cc5a870b1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "530cd47150094e7fb925eb24ec071b5e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "53d29032d6a64f958dffaced43b46f58": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_df9f065bf72b44f289a57855e5f38620", "max": 7116519, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7303bc3b53094dcd8fd4966f18622f20", "value": 7116519}}, "56021b483de347999853740bce5b4066": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "56da48c3277342b3ba6841d25a387202": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "57e3acfbf42c41d2b2a3774247e046b6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "57f9a1fd43024e8bbe7c0df0867c8e12": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c558b2bb11274910888b08138d9ee195", "max": 179811573, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_3635323c2caf4f7ab2034306cdac61cb", "value": 179811573}}, "59086cae36714c9bb971cf48e9d3f78b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "59d72ddb22ee4efeb86af48c83dd8110": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f1b1def618354651b0c7662b055d7cb2", "placeholder": "​", "style": "IPY_MODEL_02672649160d44c3a21f0dbb4f1142c2", "value": "Generating test_prefs split: 100%"}}, "5a40fa44b76f44f88a24835c1bf00e56": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_900f8248c6074ea5a93ef9372da4e958", "placeholder": "​", "style": "IPY_MODEL_39a4f06a16f149789507232e4e28acb6", "value": "Downloading data: 100%"}}, "5aeb33ba799149c1ad6e580a2a1c0a51": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5c28f5682c37476384f6a985d42e8360": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5d7fce052f8e4937adaaa670be8c1ae4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_77939b191e82425da0a5908fdbca2a2d", "IPY_MODEL_30dc6079591b4908a57f5e285ebeda68", "IPY_MODEL_960bd84fc3fe4d8eb7e79ecba0f3edf6"], "layout": "IPY_MODEL_bb7c7c1866af4641bc07d1ff0a5cd069"}}, "5e875e8a442a41e8afef4ad494ef6897": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7adc769f384346b3a5835c73542a2d62", "IPY_MODEL_fccd2f68b05d40c9b359133fbf383b1c", "IPY_MODEL_2a197f7c6c4746e1a7fded1e0539bf29"], "layout": "IPY_MODEL_37a2eb8b91ed42a1be411ecb9079a082"}}, "5f6fd4c19b34458aa8f2019a25bfd605": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5f8ada6bd56b4d79861a8f2bc36b7785": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "623e912cfe404e69a64992fbb1c1bc47": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "63d48b48f4ce449e85a55477df0b6967": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4bd0cf4d31604194a7761ea0345cc0a4", "placeholder": "​", "style": "IPY_MODEL_783ad9a7097e4fcdb768c8ecb1f21227", "value": "tokenizer.model: 100%"}}, "65b1731e23364d4ea0bb292550d4a403": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ceb3830da80146da8fbe6423d25fd8a3", "placeholder": "​", "style": "IPY_MODEL_d58599afce36484c8508c16beba13e36", "value": " 116/116 [00:00&lt;00:00, 8.55kB/s]"}}, "65ea6829bd484590a9e99f07bbadf52a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_404791f1b4034ed9ad68752480b55731", "IPY_MODEL_84610b3317ec4cad9bfe284ab48f3a0f", "IPY_MODEL_2ef3a65aadc24bd085f95acf4c3de5f5"], "layout": "IPY_MODEL_a818ce650c9c43deb8f674dd9e08c1ef"}}, "6730cd77f7fa416b97f20fc5f5660488": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5a40fa44b76f44f88a24835c1bf00e56", "IPY_MODEL_57f9a1fd43024e8bbe7c0df0867c8e12", "IPY_MODEL_8f6c6f07bd254516ae4af2f80d2492f2"], "layout": "IPY_MODEL_5c28f5682c37476384f6a985d42e8360"}}, "68893d4db1f74557932d8b24e0f72820": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "69f9e4bf820047658699d55d84d682c0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6ac7582965d341c383c806ae3a8b513a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_175c7bb9e8c94b8daa3287dd773b6837", "max": 309, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a10418a202e14a96893e24267bc194c9", "value": 309}}, "6adc4e60532f4558ade289ad526aa87d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6b419485aabe49c6bed15b5bf1316e43": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6b4743fee43b40638d8f2df36fa9fed9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6cce73a8bdda4852968c807354a6d212": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6deaa7e61cf443e99dce5869e1281c0c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "70896fbd2fab4db29322fc78e2cd84c5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1407812065e84df28e3cbfce5bc26fc9", "max": 1000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_a86aecff032345c5a3f176851052bcf0", "value": 1000}}, "727d7d710957497c8bb91d52a04a00f3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ba6f8296f866419384de64f6e2cc727e", "placeholder": "​", "style": "IPY_MODEL_d3fcca8f0d8c41a3b78bd2470542e950", "value": " 222M/222M [00:37&lt;00:00, 5.79MB/s]"}}, "7303bc3b53094dcd8fd4966f18622f20": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7522d496e9dd41828a4daddb9dd37883": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4a6807afa14049c781c7549467b85e1c", "max": 3499355, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_4cc5ba58138d47129b776b04bc2d6646", "value": 3499355}}, "757089cbb90f4d538e4a98a9ecee7dc6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d28fb551bdac4910a04bb39b500b9228", "IPY_MODEL_82cc0b43ea6d4ff4bddce1a1536fef62", "IPY_MODEL_379878ecc7e44b448425c4155e106477"], "layout": "IPY_MODEL_5f8ada6bd56b4d79861a8f2bc36b7785"}}, "7631d285c62c4c848930eef7390ef58b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "76cc2729929048d0a9ea919735fea53e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "77939b191e82425da0a5908fdbca2a2d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0dc6aefaa24043768d656f5a61d79545", "placeholder": "​", "style": "IPY_MODEL_d27056ab29e04c448fc62f9f18cbb82d", "value": "tokenizer.json: 100%"}}, "77e6abd454c94559825201f34d5de044": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_80308f7c18b244a698614fa86bc4c564", "placeholder": "​", "style": "IPY_MODEL_9473389066664ed4aa2089c78adbb20d", "value": "Downloading data: 100%"}}, "77f706dc09eb43d7aa4522f8a0b36142": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "783ad9a7097e4fcdb768c8ecb1f21227": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "78ab252184ac46eba3e82c076f6b914a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7953ace8fec34f358506307a3efb208d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "7adc769f384346b3a5835c73542a2d62": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_833097685a694d1e9cd5aa016b6b7d73", "placeholder": "​", "style": "IPY_MODEL_4e48c220077d4546a51c02df9397a084", "value": "Downloading readme: 100%"}}, "7bc84a52af5d4b1398b0ef212155e857": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7c58d359b4934b8c8b99b9aa13abb661": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_da199afde5ed47cdad0801ce45cf34a7", "max": 309, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_76cc2729929048d0a9ea919735fea53e", "value": 309}}, "7f622c1b4fef4ebb8c290cfc164f3a95": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_31f8708ee9444a82923272f09da1fe91", "placeholder": "​", "style": "IPY_MODEL_2fad7fdcdb574f75bf994a363f8c31c9", "value": "Generating train_gen split: 100%"}}, "80308f7c18b244a698614fa86bc4c564": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "803f5904c00d4cde8729226139e3c258": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5aeb33ba799149c1ad6e580a2a1c0a51", "placeholder": "​", "style": "IPY_MODEL_a2e31ba35dcf41c79c72dfd431da781f", "value": " 4.13G/4.13G [00:36&lt;00:00, 186MB/s]"}}, "80568bca0c604eaaab2933fc76da2bf0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8095a44a62804d6d86c24d62b9e1a1c1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "80cbef017c5041aab8fe651fe5e5f0a0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_35b0f9e4188f4c22bd2ec62fb8c7e228", "placeholder": "​", "style": "IPY_MODEL_3243cc3a35e742048fb4d62f1c448752", "value": "Downloading data: 100%"}}, "8138d9f175df4a28b2e58e9c852cabb8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "82cc0b43ea6d4ff4bddce1a1536fef62": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_007871aa8b7f4de7bb0f96ebe08e5283", "max": 2842622, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_0611164c2c6f4778adcbda713521155b", "value": 2842622}}, "82d00942a39e465c8a6f869ef97cc250": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "833097685a694d1e9cd5aa016b6b7d73": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "841f91867ff5489d92dfb1bbbe76ca70": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "84610b3317ec4cad9bfe284ab48f3a0f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a77440b9604140a78d99cca3e9845a1f", "max": 61966, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_56021b483de347999853740bce5b4066", "value": 61966}}, "84d445570b31490fb658b08f819dd6a7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "853df198b21147f390d68af8043610b1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "859bc87dfda746819298e213e4e0b067": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "86215ff3b19748859771c04a654a92a6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "86af0415540c41658b4f125e3f372c52": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8c785cb20818402a829e54ff3467e03f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8f6c6f07bd254516ae4af2f80d2492f2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d855ad7e63ae45089832de406a3b00e6", "placeholder": "​", "style": "IPY_MODEL_bf25365768a0441e8e4121ea1966971d", "value": " 180M/180M [00:30&lt;00:00, 5.88MB/s]"}}, "900f8248c6074ea5a93ef9372da4e958": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9032335e102f465ea2e1adcc47462a8c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "904fef1ac7404cc8a33e65ffe8938c8b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e6dfcf0456824556ad09fe7945674a14", "placeholder": "​", "style": "IPY_MODEL_80568bca0c604eaaab2933fc76da2bf0", "value": "config.json: 100%"}}, "90b49ecfaf4a44409b0b405271c9797c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "926319902fe84c5095e0b5bd4b3e8187": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "92a0e4161e0a4d7e88aaf764f4df92b7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a70c5ea79cba4f4bac16c1bebe5aa0b5", "placeholder": "​", "style": "IPY_MODEL_dd2a3af3a6144a9ba59fb130090c0283", "value": " 1000/1000 [00:00&lt;00:00, 17232.50 examples/s]"}}, "9473389066664ed4aa2089c78adbb20d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "960bd84fc3fe4d8eb7e79ecba0f3edf6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_82d00942a39e465c8a6f869ef97cc250", "placeholder": "​", "style": "IPY_MODEL_b9e2b829291247619a7b37c9f544d395", "value": " 1.80M/1.80M [00:00&lt;00:00, 22.0MB/s]"}}, "96f9847627504ae7abe7461e4c0b3cff": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_342cf2a9d6674b4d94fdbaf3b63b9b79", "placeholder": "​", "style": "IPY_MODEL_16ace0251b954bc08a33eaf3b2a47216", "value": " 1.04k/1.04k [00:00&lt;00:00, 48.4kB/s]"}}, "971371cfb1bd447e94f9f2e38f9f50c8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8138d9f175df4a28b2e58e9c852cabb8", "placeholder": "​", "style": "IPY_MODEL_43b39a8abcb94847bc21ec16fa373ed9", "value": " 309/309 [00:01&lt;00:00, 225.45 examples/s]"}}, "97a28a558b2a4ef997fb35547ef2f51d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3760dc92a7b64410865257af9f95782c", "IPY_MODEL_2189b92750854b1f9eaa70dc23d16c55", "IPY_MODEL_37944b0ab46c4de7acb5e06b91f5da52"], "layout": "IPY_MODEL_461aeb76622449b6a0bb7a794ea16ced"}}, "9b12628a46c546aebe2c749085ba2e61": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_63d48b48f4ce449e85a55477df0b6967", "IPY_MODEL_068c416eaaed476eacffb8f2f343e08f", "IPY_MODEL_9fd3fe23f14b4403a18ec7d73b64dc6a"], "layout": "IPY_MODEL_cf639313cba7442893fdf880d368616d"}}, "9e26503e85234dda982f4a731dacabbf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9eabdceae6484ba7b543ed1fa4f65141": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9f6197721b90446091bf8b97de656d4b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9f660e1b7b9c4ae7922d51353783acf9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9fd3fe23f14b4403a18ec7d73b64dc6a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cbc6fe3cc12a4d79ba08b67f23e5ce01", "placeholder": "​", "style": "IPY_MODEL_d0d340fa13a54bcf9f1130cc2fc09cc8", "value": " 493k/493k [00:00&lt;00:00, 28.2MB/s]"}}, "9fd5df9115cf4deb8a36daf4a654bfc7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a004e9208a5f441e9e4e60cc1f6d7aa9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a10418a202e14a96893e24267bc194c9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a2e31ba35dcf41c79c72dfd431da781f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a4199d34e668433a98b0fed23ab84541": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a70c5ea79cba4f4bac16c1bebe5aa0b5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a77440b9604140a78d99cca3e9845a1f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a818ce650c9c43deb8f674dd9e08c1ef": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a8306ce933304694aba188495743c2f4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a86aecff032345c5a3f176851052bcf0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a9d05a8c3aaf4fe0a2f061c3eeb48b44": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a8306ce933304694aba188495743c2f4", "placeholder": "​", "style": "IPY_MODEL_1cd708f679de4d319af26797aecca94d", "value": " 624/624 [00:00&lt;00:00, 30.7kB/s]"}}, "aa0323eef053447b9836931535984912": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b0831169530b402cac31b1e60484fbb2", "max": 1482, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ccabf636e15e471cb82bb431b5ab4b5c", "value": 1482}}, "ad03a2ff7f974fa3a0f1be2dee56c9a0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6cce73a8bdda4852968c807354a6d212", "max": 221598833, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7953ace8fec34f358506307a3efb208d", "value": 221598833}}, "ade70be3dc49443581a295e89faa65c6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6b419485aabe49c6bed15b5bf1316e43", "placeholder": "​", "style": "IPY_MODEL_9eabdceae6484ba7b543ed1fa4f65141", "value": "Downloading data: 100%"}}, "ae4e761cd74642788667953acf5fd0aa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c19e821b4a7f458085202f1d2bd148dc", "max": 2000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_86215ff3b19748859771c04a654a92a6", "value": 2000}}, "b0831169530b402cac31b1e60484fbb2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b146c0bbd15644678198977d63edebb7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9fd5df9115cf4deb8a36daf4a654bfc7", "placeholder": "​", "style": "IPY_MODEL_cd6e07c3984a4e0e920d87c6ebd6ad1c", "value": "tokenizer_config.json: 100%"}}, "b508872df72d45beb26a625c7499cc8b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b6c12557e5814d4db2ca39372d44a167": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_59d72ddb22ee4efeb86af48c83dd8110", "IPY_MODEL_ae4e761cd74642788667953acf5fd0aa", "IPY_MODEL_40904407149e4ceea2f3bdf769aba0d1"], "layout": "IPY_MODEL_da08e39e0cbd43b68dd1058954a3d93f"}}, "b9e2b829291247619a7b37c9f544d395": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ba6f8296f866419384de64f6e2cc727e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "baa1b968a1de4d029730f6a5a2301101": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4d080de8a7dc4809abafd654f1f62691", "IPY_MODEL_16708105f96343c482ea0df18d42cbf3", "IPY_MODEL_47fb8b7a47eb47179ccb0e2e1b313fbd"], "layout": "IPY_MODEL_f9fb3e9d512944f295b81b249119d025"}}, "bb7c7c1866af4641bc07d1ff0a5cd069": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bc745fa3da114007af35ea15dbd7ab7d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_68893d4db1f74557932d8b24e0f72820", "placeholder": "​", "style": "IPY_MODEL_3ccdc55cf6384695967eaf79325b1aef", "value": "model.safetensors: 100%"}}, "bd508aa803f94073af7a91332e738668": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf25365768a0441e8e4121ea1966971d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "bf6bc9099d1a475884be6f15115f01ad": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c0e6950f66d84e5091051fbe173b2b3f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c19e821b4a7f458085202f1d2bd148dc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c1e6987ebc5f40ce90d4c94056c74333": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c298081665e844539b8d366325dbdab5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cdc411110d1b405ea12342b57fd8de30", "max": 116, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_84d445570b31490fb658b08f819dd6a7", "value": 116}}, "c558b2bb11274910888b08138d9ee195": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cbc6fe3cc12a4d79ba08b67f23e5ce01": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cc8d776ab3b4482ba8a2cab6bce3f310": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ccabf636e15e471cb82bb431b5ab4b5c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "cd6e07c3984a4e0e920d87c6ebd6ad1c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cdc411110d1b405ea12342b57fd8de30": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ceb3830da80146da8fbe6423d25fd8a3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cf639313cba7442893fdf880d368616d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d0d340fa13a54bcf9f1130cc2fc09cc8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d22351f5e6844faca41027ee44ead93e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2ab8ed5174004d60ba0ecfc7505e3c54", "placeholder": "​", "style": "IPY_MODEL_061f919ac1c74f078b5c909ff889e894", "value": " 309/309 [00:02&lt;00:00, 189.14 examples/s]"}}, "d27056ab29e04c448fc62f9f18cbb82d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d28fb551bdac4910a04bb39b500b9228": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3456b1a43ca742e3bd5bcf695a7b548e", "placeholder": "​", "style": "IPY_MODEL_56da48c3277342b3ba6841d25a387202", "value": "Downloading data: 100%"}}, "d3fcca8f0d8c41a3b78bd2470542e950": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d43eea508a4d496e9460f01931659b52": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b146c0bbd15644678198977d63edebb7", "IPY_MODEL_aa0323eef053447b9836931535984912", "IPY_MODEL_effec7acec3d4b5384bcf481e3263b6c"], "layout": "IPY_MODEL_03ec8d28c0f34116b42e4e0dc65e3dd3"}}, "d58599afce36484c8508c16beba13e36": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d855ad7e63ae45089832de406a3b00e6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "da08e39e0cbd43b68dd1058954a3d93f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "da199afde5ed47cdad0801ce45cf34a7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dd2a3af3a6144a9ba59fb130090c0283": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ddd131d700884af596f2b77c921f48c5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "df9f065bf72b44f289a57855e5f38620": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dfb40aa450f844e28319893415573906": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e05e528fd80548c1b7147d1756d980c9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_59086cae36714c9bb971cf48e9d3f78b", "max": 61966, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_0715885acccf4440aed4955f4aaff0f2", "value": 61966}}, "e10b53d12a134bc6a674bc568e1ab7bf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_80cbef017c5041aab8fe651fe5e5f0a0", "IPY_MODEL_53d29032d6a64f958dffaced43b46f58", "IPY_MODEL_1093b380f208425aadf0085c0aebed80"], "layout": "IPY_MODEL_4032d6814ad44a2f86c2f3b9cbb7afad"}}, "e152d696872242ab8e0bc00ce9b50142": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e4120afe895742848cf756fd58cb42f8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e5d62477070e475c93ededb4de412063": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e63c3a8c021b4d30b0c93b99a7dba155": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_45cc7c0a9c664353ba11a5049c0353b6", "IPY_MODEL_514e5d566f734683bec2319b987c16dd", "IPY_MODEL_21687939a40a491395c37bec223c37fa"], "layout": "IPY_MODEL_440101772f084da5b3e5724fb28c52d1"}}, "e6c183829cfb45e389348c0d5a1d943d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e6dfcf0456824556ad09fe7945674a14": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e6ed1837c16b47d3913f123a7c09d833": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f9af1d0e95b846e7923b0fbf48df969d", "IPY_MODEL_7c58d359b4934b8c8b99b9aa13abb661", "IPY_MODEL_d22351f5e6844faca41027ee44ead93e"], "layout": "IPY_MODEL_162e0a2a77b743868d3e6c53f2220b14"}}, "e98afd2ed7b44211932dc55a2a0c3e3a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ee568de931f1427f9fb252f24a6bdb01": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ee9b1c6c792143da997c0b563a95ffce": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "effec7acec3d4b5384bcf481e3263b6c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e4120afe895742848cf756fd58cb42f8", "placeholder": "​", "style": "IPY_MODEL_fe6eabd38a614113b36bafd1f8ce6920", "value": " 1.48k/1.48k [00:00&lt;00:00, 94.7kB/s]"}}, "f1b1def618354651b0c7662b055d7cb2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f4065d3723d649bfa930624fca67ddd9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4fd685420f4e41edb9d9c97e6ac89585", "max": 1000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1a319e2196e3465989cbf69e736fbd3e", "value": 1000}}, "f61b85b63d5147db9f1040f30f4a3034": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f6200f86a4084c5f978f62481100b7a3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f7562580501845e9a67ab9a8b4a96ba0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_079284acd5c6454eb03d8876a62e6f12", "max": 1042, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1d917d774780421fbdca680009a706ac", "value": 1042}}, "f7c089469828416aa89ade0cfa5a04cc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_3a52a2ec426b4fc2aaad23c7d8ce6d2b", "IPY_MODEL_c298081665e844539b8d366325dbdab5", "IPY_MODEL_65b1731e23364d4ea0bb292550d4a403"], "layout": "IPY_MODEL_5f6fd4c19b34458aa8f2019a25bfd605"}}, "f9af1d0e95b846e7923b0fbf48df969d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_22f984300d814049bb2cfa3c68c6461a", "placeholder": "​", "style": "IPY_MODEL_1dc30290e4c4478695246cef4b785563", "value": "Formatting comparisons with prompt template (num_proc=12): 100%"}}, "f9fb3e9d512944f295b81b249119d025": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fccd2f68b05d40c9b359133fbf383b1c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f6200f86a4084c5f978f62481100b7a3", "max": 5979, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_530cd47150094e7fb925eb24ec071b5e", "value": 5979}}, "fe6eabd38a614113b36bafd1f8ce6920": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "feefb2db48a84e7f9808accd7fd68df7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ff122787d9174a718b5d3223f53edf22": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1aa72a1d1d0d4ce4b36b10c8ecf021a8", "placeholder": "​", "style": "IPY_MODEL_a004e9208a5f441e9e4e60cc1f6d7aa9", "value": "Generating test_sft split: 100%"}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}