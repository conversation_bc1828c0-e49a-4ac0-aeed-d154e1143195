PODS:
  - Flutter (1.0.0)
  - flutter_gemma (0.8.6):
    - Flutter
    - MediaPipeTasksGenAI (= 0.10.24)
    - MediaPipeTasksGenAIC (= 0.10.24)
  - image_picker_ios (0.0.1):
    - Flutter
  - integration_test (0.0.1):
    - Flutter
  - large_file_handler (0.0.1):
    - Flutter
  - MediaPipeTasksGenAI (0.10.24):
    - MediaPipeTasksGenAIC (= 0.10.24)
  - MediaPipeTasksGenAIC (0.10.24)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - Flutter (from `Flutter`)
  - flutter_gemma (from `.symlinks/plugins/flutter_gemma/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - large_file_handler (from `.symlinks/plugins/large_file_handler/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - MediaPipeTasksGenAI
    - MediaPipeTasksGenAIC

EXTERNAL SOURCES:
  Flutter:
    :path: Flutter
  flutter_gemma:
    :path: ".symlinks/plugins/flutter_gemma/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  large_file_handler:
    :path: ".symlinks/plugins/large_file_handler/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_gemma: c104e5c505eac327a0877a7cb21a593982703154
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  integration_test: 4a889634ef21a45d28d50d622cf412dc6d9f586e
  large_file_handler: b37481e9b4972562ffcdc8f75700f47cd592bcec
  MediaPipeTasksGenAI: 076ba7032a6e9da16db9c7cf0c3b67c751c18bc1
  MediaPipeTasksGenAIC: ec35d9f431f6a6b651a0bc9f67a4ed149ffa575c
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d

PODFILE CHECKSUM: e40bdc06774834b7c72a307eba56ffeb505660e1

COCOAPODS: 1.16.2
