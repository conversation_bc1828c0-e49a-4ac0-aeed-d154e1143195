{"cells": [{"cell_type": "markdown", "metadata": {"id": "Tce3stUlHN0L"}, "source": ["##### Copyright 2024 Google LLC."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"cellView": "form", "id": "tuOe1ymfHZPu"}, "outputs": [], "source": ["# @title Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "# https://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "metadata": {"id": "dfsDR_omdNea"}, "source": ["# Integrating PaliGemma with Mesop\n", "This notebook demonstrates how to use [PaliGemma](https://ai.google.dev/gemma/docs/paligemma) models with [Mesop](https://google.github.io/mesop/) to create a simple GUI application.\n", "<table align=\"left\">\n", "  <td>\n", "    <a target=\"_blank\" href=\"https://colab.research.google.com/github/google-gemini/gemma-cookbook/blob/main/PaliGemma/[PaliGemma_1]Using_with_Mesop.ipynb\"><img src=\"https://www.tensorflow.org/images/colab_logo_32px.png\" />Run in Google Colab</a>\n", "  </td>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {"id": "FaqZItBdeokU"}, "source": ["## Setup\n", "\n", "### Select the Colab runtime\n", "To complete this tutorial, you'll need to have a Colab runtime with sufficient resources to run the Gemma model. In this case, you can use a T4 GPU:\n", "\n", "1. In the upper-right of the Colab window, select **▾ (Additional connection options)**.\n", "2. Select **Change runtime type**.\n", "3. Under **Hardware accelerator**, select **T4 GPU**.\n", "\n", "### Gemma setup\n", "\n", "To complete this tutorial, you'll first need to complete the setup instructions at [Gemma setup](https://ai.google.dev/gemma/docs/setup). The Gemma setup instructions show you how to do the following:\n", "\n", "* Get access to <PERSON> on kaggle.com.\n", "* Select a Colab runtime with sufficient resources to run\n", "  the Gemma 2B model.\n", "* Generate and configure a Kaggle username and an API key as Colab secrets.\n", "\n", "After you've completed the Gemma setup, move on to the next section, where you'll set environment variables for your Colab environment.\n"]}, {"cell_type": "markdown", "metadata": {"id": "CY2kGtsyYpHF"}, "source": ["### Configure your credentials\n", "\n", "Add your your Kaggle credentials to the Colab Secrets manager to securely store it.\n", "\n", "1. Open your Google Colab notebook and click on the 🔑 Secrets tab in the left panel. <img src=\"https://storage.googleapis.com/generativeai-downloads/images/secrets.jpg\" alt=\"The Secrets tab is found on the left panel.\" width=50%>\n", "2. Create new secrets: `<PERSON><PERSON><PERSON><PERSON>_USERNAME` and `<PERSON>AGG<PERSON>_KEY`\n", "3. Copy/paste your username into `KAGGLE_USERNAME`\n", "3. Copy/paste your key into `KAGGLE_KEY`\n", "4. Toggle the buttons on the left to allow notebook access to the secrets.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "A9sUQ4WrP-Yr"}, "outputs": [], "source": ["import os\n", "from google.colab import userdata\n", "\n", "# Note: `userdata.get` is a Colab API. If you're not using Colab, set the env\n", "# vars as appropriate for your system.\n", "os.environ[\"KAGGLE_USERNAME\"] = userdata.get(\"KAGGLE_USERNAME\")\n", "os.environ[\"KAGGLE_KEY\"] = userdata.get(\"KAGGLE_KEY\")"]}, {"cell_type": "markdown", "metadata": {"id": "iwjo5_Uucxkw"}, "source": ["### Install dependencies\n", "Run the cell below to install all the required dependencies."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "r_nXPEsF7UWQ"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m571.8/571.8 kB\u001b[0m \u001b[31m37.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m347.7/347.7 kB\u001b[0m \u001b[31m26.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.2/5.2 MB\u001b[0m \u001b[31m46.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m590.6/590.6 MB\u001b[0m \u001b[31m2.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.3/5.3 MB\u001b[0m \u001b[31m69.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m73.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.5/5.5 MB\u001b[0m \u001b[31m69.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "tf-keras 2.15.1 requires tensorflow<2.16,>=2.15, but you have tensorflow 2.16.2 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m77.9/77.9 kB\u001b[0m \u001b[31m1.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25l\u001b[?25hdone\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m43.2/43.2 kB\u001b[0m \u001b[31m3.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h  Building wheel for ml_collections (setup.py) ... \u001b[?25l\u001b[?25hdone\n"]}], "source": ["!pip install -q -U keras keras-nlp\n", "!pip install -q overrides ml_collections \"einops~=0.7\" sentencepiece"]}, {"cell_type": "markdown", "metadata": {"id": "pOAEiJmnBE0D"}, "source": ["## Exploring prompting capabilities"]}, {"cell_type": "markdown", "metadata": {"id": "HdcJ0WgI_tb7"}, "source": ["### PaliGemma"]}, {"cell_type": "markdown", "metadata": {"id": "DfMHtnStiIh-"}, "source": ["PaliGemma is a lightweight open vision-language model (VLM) inspired by PaLI-3, and based on open components like the SigLIP vision model and the Gemma language model. PaliGemma takes both images and text as inputs and can answer questions about images with detail and context, meaning that PaliGemma can perform deeper analysis of images and provide useful insights, such as captioning for images and short videos, object detection, and reading text embedded within images.\n", "\n", "Prompting:\n", "\n", "* `cap {lang}\\n`: Very raw short caption (from WebLI-alt)\n", "* `caption {lang}\\n`: Nice, COCO-like short captions\n", "* `describe {lang}\\n`: Somewhat longer, more descriptive captions\n", "* `ocr`: Optical character recognition\n", "* `answer en {question}\\n`: Question answering about the image contents\n", "* `question {lang} {answer}\\n`: Question generation for a given answer\n", "* `detect {object} ; {object}\\n`: Count objects in a scene and return the bounding boxes for the objects\n", "* `segment {object}\\n`: Do image segmentation of the object in the scene"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "twvbOLey_3tW"}, "outputs": [], "source": ["import os\n", "import sys\n", "import keras\n", "import keras_nlp\n", "\n", "keras.config.set_floatx(\"bfloat16\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "IuQlLU09_qb3"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Downloading from https://www.kaggle.com/api/v1/models/keras/paligemma/keras/pali_gemma_3b_mix_224/1/download/model.safetensors...\n", "Downloading from https://www.kaggle.com/api/v1/models/keras/paligemma/keras/pali_gemma_3b_mix_224/1/download/model.safetensors.index.json...\n", "Downloading from https://www.kaggle.com/api/v1/models/keras/paligemma/keras/pali_gemma_3b_mix_224/1/download/metadata.json...\n", "100%|██████████| 143/143 [00:00<00:00, 112kB/s]\n", "Downloading from https://www.kaggle.com/api/v1/models/keras/paligemma/keras/pali_gemma_3b_mix_224/1/download/task.json...\n", "Downloading from https://www.kaggle.com/api/v1/models/keras/paligemma/keras/pali_gemma_3b_mix_224/1/download/config.json...\n", "100%|██████████| 861/861 [00:00<00:00, 1.19MB/s]\n", "Downloading from https://www.kaggle.com/api/v1/models/keras/paligemma/keras/pali_gemma_3b_mix_224/1/download/model.safetensors...\n", "Downloading from https://www.kaggle.com/api/v1/models/keras/paligemma/keras/pali_gemma_3b_mix_224/1/download/model.safetensors.index.json...\n", "Downloading from https://www.kaggle.com/api/v1/models/keras/paligemma/keras/pali_gemma_3b_mix_224/1/download/model.weights.h5...\n", "100%|██████████| 5.45G/5.45G [01:15<00:00, 77.7MB/s]\n", "Downloading from https://www.kaggle.com/api/v1/models/keras/paligemma/keras/pali_gemma_3b_mix_224/1/download/model.safetensors...\n", "Downloading from https://www.kaggle.com/api/v1/models/keras/paligemma/keras/pali_gemma_3b_mix_224/1/download/model.safetensors.index.json...\n", "Downloading from https://www.kaggle.com/api/v1/models/keras/paligemma/keras/pali_gemma_3b_mix_224/1/download/preprocessor.json...\n", "Downloading from https://www.kaggle.com/api/v1/models/keras/paligemma/keras/pali_gemma_3b_mix_224/1/download/tokenizer.json...\n", "100%|██████████| 410/410 [00:00<00:00, 471kB/s]\n", "Downloading from https://www.kaggle.com/api/v1/models/keras/paligemma/keras/pali_gemma_3b_mix_224/1/download/assets/tokenizer/vocabulary.spm...\n", "100%|██████████| 4.07M/4.07M [00:00<00:00, 15.8MB/s]\n"]}], "source": ["# Load PaliGemma\n", "paligemma = keras_nlp.models.PaliGemmaCausalLM.from_preset(\"pali_gemma_3b_mix_224\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "ewNkh5lE-UAt"}, "outputs": [], "source": ["if not os.path.exists(\"big_vision_repo\"):\n", "  !git clone --quiet --branch=main --depth=1 \\\n", "     https://github.com/google-research/big_vision big_vision_repo\n", "\n", "# Append big_vision code to python import path\n", "if \"big_vision_repo\" not in sys.path:\n", "  sys.path.append(\"big_vision_repo\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "ubRH-t77AJIx"}, "outputs": [], "source": ["import io\n", "import re\n", "import PIL\n", "import requests\n", "import numpy as np\n", "from PIL import Image\n", "import matplotlib.pyplot as plt\n", "import matplotlib.patches as patches\n", "import big_vision.evaluators.proj.paligemma.transfers.segmentation as segeval\n", "\n", "# Helpers:\n", "\n", "\n", "def crop_and_resize(image, target_size):\n", "    \"\"\"A helper function the resizes given image to the given shape\"\"\"\n", "    width, height = image.size\n", "    source_size = min(image.size)\n", "    left = width // 2 - source_size // 2\n", "    top = height // 2 - source_size // 2\n", "    right, bottom = left + source_size, top + source_size\n", "    return image.resize(target_size, box=(left, top, right, bottom))\n", "\n", "\n", "def read_image(url, target_size=(224, 224)):\n", "    \"\"\"Loads images from URL\"\"\"\n", "    headers = {\"User-Agent\": \"My User Agent 1.0\"}\n", "    contents = io.BytesIO(requests.get(url, headers=headers, stream=True).content)\n", "    image = Image.open(contents)\n", "    image = crop_and_resize(image, target_size)\n", "    image = np.array(image)\n", "\n", "    # Remove alpha channel if neccessary.\n", "    if image.shape[2] == 4:\n", "        image = image[:, :, :3]\n", "    return image\n", "\n", "\n", "def parse_bbox_and_labels(detokenized_output: str):\n", "    \"\"\"Parses model output to extract bounding boxes\"\"\"\n", "    matches = re.finditer(\n", "        \"<loc(?P<y0>\\d\\d\\d\\d)><loc(?P<x0>\\d\\d\\d\\d)><loc(?P<y1>\\d\\d\\d\\d)><loc(?P<x1>\\d\\d\\d\\d)>\"\n", "        \" (?P<label>.+?)( ;|$)\",\n", "        detokenized_output,\n", "    )\n", "    labels, boxes = [], []\n", "    fmt = lambda x: float(x) / 1024.0\n", "    for m in matches:\n", "        d = m.groupdict()\n", "        boxes.append([fmt(d[\"y0\"]), fmt(d[\"x0\"]), fmt(d[\"y1\"]), fmt(d[\"x1\"])])\n", "        labels.append(d[\"label\"])\n", "    return np.array(boxes), np.array(labels)\n", "\n", "\n", "def display_boxes(image, boxes, labels, target_image_size):\n", "    \"\"\"Draws bouding boxes on the given image\"\"\"\n", "    h, l = target_image_size\n", "    fig, ax = plt.subplots()\n", "    ax.imshow(image)\n", "\n", "    for i in range(boxes.shape[0]):\n", "        y, x, y2, x2 = boxes[i] * h\n", "        width = x2 - x\n", "        height = y2 - y\n", "        # Create a Rectangle patch\n", "        rect = patches.Rectangle(\n", "            (x, y), width, height, linewidth=1, edgecolor=\"r\", facecolor=\"none\"\n", "        )\n", "        # Add label\n", "        plt.text(x, y, labels[i], color=\"red\", fontsize=12)\n", "        # Add the patch to the Axes\n", "        ax.add_patch(rect)\n", "\n", "    plt.show()\n", "\n", "\n", "def parse_segments(detokenized_output: str) -> tuple[np.ndarray, np.ndarray]:\n", "    reconstruct_masks = segeval.get_reconstruct_masks(\"oi\")\n", "    matches = re.finditer(\n", "        \"<loc(?P<y0>\\d\\d\\d\\d)><loc(?P<x0>\\d\\d\\d\\d)><loc(?P<y1>\\d\\d\\d\\d)><loc(?P<x1>\\d\\d\\d\\d)>\"\n", "        + \"\".join(f\"<seg(?P<s{i}>\\d\\d\\d)>\" for i in range(16)),\n", "        detokenized_output,\n", "    )\n", "    boxes, segs = [], []\n", "    fmt_box = lambda x: float(x) / 1024.0\n", "    for m in matches:\n", "        d = m.groupdict()\n", "        boxes.append(\n", "            [fmt_box(d[\"y0\"]), fmt_box(d[\"x0\"]), fmt_box(d[\"y1\"]), fmt_box(d[\"x1\"])]\n", "        )\n", "        segs.append([int(d[f\"s{i}\"]) for i in range(16)])\n", "\n", "    if len(boxes) == 0 or len(segs) == 0:\n", "        return boxes, segs\n", "\n", "    return np.array(boxes), np.array(reconstruct_masks(np.array(segs)))"]}, {"cell_type": "markdown", "metadata": {"id": "Z4-eFxD0CAyr"}, "source": ["#### Prompting example: Visual Question Answering"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "e9tOWyAoAm37"}, "outputs": [{"data": {"text/html": ["<style>\n", "      .ndarray_repr .ndarray_raw_data {\n", "        display: none;\n", "      }\n", "      .ndarray_repr.show_array .ndarray_raw_data {\n", "        display: block;\n", "      }\n", "      .ndarray_repr.show_array .ndarray_image_preview {\n", "        display: none;\n", "      }\n", "      </style>\n", "      <div id=\"id-fd0c2108-49de-4854-8d13-9a9a34f21c83\" class=\"ndarray_repr\"><pre>ndarray (224, 224, 3) <button style=\"padding: 0 2px;\">show data</button></pre><img src=\"data:image/png;base64,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********************************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\" class=\"ndarray_image_preview\" /><pre class=\"ndarray_raw_data\">array([[[123, 122, 113],\n", "        [ 91,  91,  69],\n", "        [ 89,  92,  65],\n", "        ...,\n", "        [ 67,  76,  40],\n", "        [ 54,  64,  28],\n", "        [ 66,  76,  41]],\n", "\n", "       [[ 90,  88,  76],\n", "        [ 83,  83,  65],\n", "        [ 89,  92,  73],\n", "        ...,\n", "        [ 60,  69,  34],\n", "        [ 49,  60,  26],\n", "        [ 61,  72,  38]],\n", "\n", "       [[112, 121, 112],\n", "        [143, 152, 152],\n", "        [152, 163, 165],\n", "        ...,\n", "        [ 74,  89,  49],\n", "        [ 70,  85,  44],\n", "        [ 74,  89,  50]],\n", "\n", "       ...,\n", "\n", "       [[127, 175,  86],\n", "        [129, 179,  87],\n", "        [123, 171,  80],\n", "        ...,\n", "        [117, 165,  74],\n", "        [133, 179,  87],\n", "        [131, 176,  92]],\n", "\n", "       [[157, 200, 107],\n", "        [149, 195,  99],\n", "        [146, 195,  97],\n", "        ...,\n", "        [154, 200, 112],\n", "        [131, 177,  90],\n", "        [133, 183,  99]],\n", "\n", "       [[172, 217, 124],\n", "        [152, 200, 105],\n", "        [150, 202, 105],\n", "        ...,\n", "        [168, 218, 125],\n", "        [148, 196, 110],\n", "        [139, 190, 101]]], dtype=uint8)</pre></div><script>\n", "      (() => {\n", "      const titles = ['show data', 'hide data'];\n", "      let index = 0\n", "      document.querySelector('#id-fd0c2108-49de-4854-8d13-9a9a34f21c83 button').onclick = (e) => {\n", "        document.querySelector('#id-fd0c2108-49de-4854-8d13-9a9a34f21c83').classList.toggle('show_array');\n", "        index = (++index) % 2;\n", "        document.querySelector('#id-fd0c2108-49de-4854-8d13-9a9a34f21c83 button').textContent = titles[index];\n", "        e.prevent<PERSON><PERSON><PERSON>();\n", "        e.stopPropagation();\n", "      }\n", "      })();\n", "    </script>"], "text/plain": ["array([[[123, 122, 113],\n", "        [ 91,  91,  69],\n", "        [ 89,  92,  65],\n", "        ...,\n", "        [ 67,  76,  40],\n", "        [ 54,  64,  28],\n", "        [ 66,  76,  41]],\n", "\n", "       [[ 90,  88,  76],\n", "        [ 83,  83,  65],\n", "        [ 89,  92,  73],\n", "        ...,\n", "        [ 60,  69,  34],\n", "        [ 49,  60,  26],\n", "        [ 61,  72,  38]],\n", "\n", "       [[112, 121, 112],\n", "        [143, 152, 152],\n", "        [152, 163, 165],\n", "        ...,\n", "        [ 74,  89,  49],\n", "        [ 70,  85,  44],\n", "        [ 74,  89,  50]],\n", "\n", "       ...,\n", "\n", "       [[127, 175,  86],\n", "        [129, 179,  87],\n", "        [123, 171,  80],\n", "        ...,\n", "        [117, 165,  74],\n", "        [133, 179,  87],\n", "        [131, 176,  92]],\n", "\n", "       [[157, 200, 107],\n", "        [149, 195,  99],\n", "        [146, 195,  97],\n", "        ...,\n", "        [154, 200, 112],\n", "        [131, 177,  90],\n", "        [133, 183,  99]],\n", "\n", "       [[172, 217, 124],\n", "        [152, 200, 105],\n", "        [150, 202, 105],\n", "        ...,\n", "        [168, 218, 125],\n", "        [148, 196, 110],\n", "        [139, 190, 101]]], dtype=uint8)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# Image by: <PERSON><PERSON><PERSON><PERSON>, CC BY-SA 4.0, via Wikimedia Commons\n", "image_url = \"https://upload.wikimedia.org/wikipedia/commons/8/8b/Bird-8077%2C_Kapiti%2C_North_Island%2C_New_Zealand.jpg\"\n", "image = read_image(image_url)\n", "image"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "Zq14V5PEU8aH"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["In this image we can see a bird on the ground. In the background there is water.\n"]}], "source": ["# Describing the image\n", "prompt = \"describe en\\n\"\n", "output = paligemma.generate(\n", "    inputs={\n", "        \"images\": image,\n", "        \"prompts\": prompt,\n", "    }\n", ")\n", "print(output[len(prompt) :])"]}, {"cell_type": "markdown", "metadata": {"id": "cZUYYAqrmQ_F"}, "source": ["### Integration with Mesop\n", "\n", "We will create a simple GUI application that allows users to upload images, specify prompt styles, and other arguments, such as the question or type of object, that are necessary to generate the answer.\n", "\n", "The application will be able to provide both images and text as the output."]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "Di3Rn5JQZvPp"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.0/5.0 MB\u001b[0m \u001b[31m18.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.6/76.6 kB\u001b[0m \u001b[31m12.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m83.0/83.0 kB\u001b[0m \u001b[31m13.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h"]}], "source": ["!pip install -q mesop"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "UKBYCRE6jk5v"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[32mRunning server on: http://localhost:32123\u001b[0m\n", " * Serving Flask app 'mesop.server.server'\n", " * Debug mode: off\n"]}], "source": ["import mesop as me\n", "import mesop.labs as mel\n", "\n", "me.colab_run()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "HeR8TAtBAWul"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:werkzeug:\u001b[31m\u001b[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.\u001b[0m\n", " * Running on all addresses (::)\n", " * Running on http://[::1]:32123\n", " * Running on http://[::1]:32123\n", "INFO:werkzeug:\u001b[33mPress CTRL+C to quit\u001b[0m\n"]}], "source": ["from typing import Any, Dict, Literal\n", "\n", "\n", "@me.stateclass\n", "class State:\n", "    \"\"\"This class is responsible for storing data in the application\"\"\"\n", "\n", "    image: list = None\n", "    selected_task: str = None\n", "    additional_parameter: str = None\n", "    output: str = None\n", "    output_type: Literal[\"text\", \"object_detection\", \"object_segmentation\"] = \"text\"\n", "\n", "\n", "def me_image_to_array(data, target_size=(224, 224)):\n", "    \"\"\"Transforms image represented by bytes to an array\"\"\"\n", "    contents = io.BytesIO(data)\n", "    image = Image.open(contents)\n", "    image = crop_and_resize(image, target_size)\n", "    image = np.array(image)\n", "\n", "    # removes alpha channel if present\n", "    if image.shape[2] == 4:\n", "        image = image[:, :, :3]\n", "    return image\n", "\n", "\n", "def update_state_by_task(name: str) -> Dict[str, Any]:\n", "    _task_to_params = {\n", "        # <no params> -> Text\n", "        \"raw_short_caption\": {\n", "            \"prompt\": \"cap en\\n\",\n", "        },\n", "        \"coco_like_short_caption\": {\n", "            \"prompt\": \"caption en\\n\",\n", "        },\n", "        \"descriptive_caption\": {\n", "            \"prompt\": \"describe en\\n\",\n", "        },\n", "        \"ocr\": {\n", "            \"prompt\": \"ocr\",\n", "        },\n", "        # Text -> Text\n", "        \"qa\": {\n", "            \"prompt\": \"answer en {}\\n\",\n", "            \"requires_additional_parameter\": True,\n", "            \"additional_param_label\": \"What would you like to ask the model?\",\n", "        },\n", "        \"qg\": {\n", "            \"prompt\": \"question en {}\\n\",\n", "            \"requires_additional_parameter\": True,\n", "            \"additional_param_label\": \"What would be the answer for the questions?\",\n", "        },\n", "        # Text -> Image\n", "        \"object_detection\": {\n", "            \"prompt\": \"detect {}\\n\",\n", "            \"output_type\": \"object_detection\",\n", "            \"requires_additional_parameter\": True,\n", "            \"additional_param_label\": \"What object would you like to detect?\",\n", "        },\n", "        \"object_segmentation\": {\n", "            \"prompt\": \"segment {}\\n\",\n", "            \"output_type\": \"object_segmentation\",\n", "            \"requires_additional_parameter\": True,\n", "            \"additional_param_label\": \"What object would you like to segment?\",\n", "        },\n", "    }\n", "\n", "    params = _task_to_params[name]\n", "    state = me.state(State)\n", "    state.prompt_template = params[\"prompt\"]\n", "    state.output_type = params.get(\"output_type\", \"text\")\n", "    state.requires_additional_parameter = params.get(\n", "        \"requires_additional_parameter\", False\n", "    )\n", "    state.additional_param_label = params.get(\n", "        \"additional_param_label\", \"Additional parameter:\"\n", "    )\n", "\n", "\n", "def handle_image_upload(event: me.UploadEvent):\n", "    state = me.state(State)\n", "    image = me_image_to_array(event.file.getvalue())\n", "    state.image = image.tolist()\n", "    state.output = None\n", "\n", "\n", "def handle_select_task(event: me.SelectSelectionChangeEvent):\n", "    state = me.state(State)\n", "    state.output = None\n", "    state.selected_task = event.value\n", "\n", "\n", "def handle_additional_parameter_input(e: me.InputEvent):\n", "    state = me.state(State)\n", "    state.additional_parameter = e.value\n", "\n", "\n", "def display_object_detection(boxes, labels, target_image_size=(224, 224)):\n", "    \"\"\"Displays the image with boxes around detected objects\"\"\"\n", "    state = me.state(State)\n", "    h, l = target_image_size\n", "    fig, ax = plt.subplots()\n", "    ax.imshow(state.image)\n", "    ax.get_xaxis().set_visible(False)\n", "    ax.get_yaxis().set_visible(False)\n", "\n", "    for i in range(boxes.shape[0]):\n", "        y, x, y2, x2 = boxes[i] * h\n", "        width = x2 - x\n", "        height = y2 - y\n", "        # Create a Rectangle patch\n", "        rect = patches.Rectangle(\n", "            (x, y), width, height, linewidth=1, edgecolor=\"r\", facecolor=\"none\"\n", "        )\n", "        # Add label\n", "        plt.text(x, y, labels[i], color=\"red\", fontsize=12)\n", "        # Add the patch to the Axes\n", "        ax.add_patch(rect)\n", "    me.plot(fig, style=me.Style(width=\"100%\"))\n", "\n", "\n", "def display_object_segmentation(\n", "    bounding_box, segment_mask, target_image_size=(224, 224)\n", "):\n", "    # Initialize a full mask with the target size\n", "    state = me.state(State)\n", "    image = np.array(state.image)\n", "    full_mask = np.zeros(target_image_size, dtype=np.uint8)\n", "    target_width, target_height = target_image_size\n", "\n", "    for bbox, mask in zip(bounding_box, segment_mask):\n", "        y1, x1, y2, x2 = bbox\n", "        x1 = int(x1 * target_width)\n", "        y1 = int(y1 * target_height)\n", "        x2 = int(x2 * target_width)\n", "        y2 = int(y2 * target_height)\n", "\n", "        # Ensure mask is 2D before converting to Image\n", "        if mask.ndim == 3:\n", "            mask = mask.squeeze(axis=-1)\n", "        mask = Image.fromarray(mask)\n", "        mask = mask.resize((x2 - x1, y2 - y1), resample=Image.NEAREST)\n", "        mask = np.array(mask)\n", "        binary_mask = (mask > 0.5).astype(np.uint8)\n", "\n", "        # Place the binary mask onto the full mask\n", "        full_mask[y1:y2, x1:x2] = np.maximum(full_mask[y1:y2, x1:x2], binary_mask)\n", "\n", "    cmap = plt.get_cmap(\"jet\")\n", "    colored_mask = cmap(full_mask / 1.0)\n", "    colored_mask = (colored_mask[:, :, :3] * 255).astype(np.uint8)\n", "    blended_image = image.copy()\n", "    mask_indices = full_mask > 0\n", "    alpha = 0.5\n", "\n", "    for c in range(3):\n", "        blended_image[:, :, c] = np.where(\n", "            mask_indices,\n", "            (1 - alpha) * image[:, :, c] + alpha * colored_mask[:, :, c],\n", "            image[:, :, c],\n", "        )\n", "    fig, ax = plt.subplots()\n", "    ax.imshow(blended_image)\n", "    me.plot(fig, style=me.Style(width=\"100%\"))\n", "\n", "\n", "def generate_content(e: me.ClickEvent):\n", "    \"\"\"Generates an answer to the users query\"\"\"\n", "    state = me.state(State)\n", "    prompt = (\n", "        state.prompt_template.format(state.additional_parameter)\n", "        if state.requires_additional_parameter\n", "        else state.prompt_template\n", "    )\n", "    image = np.array(state.image)\n", "    output = paligemma.generate(\n", "        inputs={\n", "            \"images\": image,\n", "            \"prompts\": prompt,\n", "        }\n", "    )\n", "    state.output = output[len(prompt) :]\n", "\n", "\n", "@me.page(path=\"/app\")\n", "def app():\n", "    \"\"\"the main function of the application\"\"\"\n", "    state = me.state(State)\n", "\n", "    with me.box(\n", "        style=me.Style(\n", "            display=\"flex\",\n", "            flex_direction=\"column\",\n", "            gap=10,\n", "            padding=me.Padding.symmetric(horizontal=\"30%\"),\n", "            margin=me.Margin.symmetric(vertical=48),\n", "        )\n", "    ):\n", "\n", "        # Image\n", "        me.text(\n", "            \"Upload an image to get started:\",\n", "            style=me.Style(width=\"100%\", text_align=\"center\"),\n", "            type=\"headline-5\",\n", "        )\n", "        with me.box(\n", "            style=me.Style(\n", "                display=\"flex\", flex_direction=\"column\", align_items=\"center\"\n", "            )\n", "        ):\n", "            me.uploader(\n", "                label=\"Upload Image\",\n", "                accepted_file_types=[\"image/jpeg\", \"image/png\"],\n", "                on_upload=handle_image_upload,\n", "            )\n", "\n", "        # Task\n", "        if state.image:\n", "            me.text(\n", "                \"Choose a task:\",\n", "                style=me.Style(width=\"100%\", text_align=\"center\"),\n", "                type=\"headline-5\",\n", "            )\n", "            me.select(\n", "                label=\"Choose <PERSON> task\",\n", "                options=[\n", "                    me.SelectOption(\n", "                        label=\"Raw short caption\", value=\"raw_short_caption\"\n", "                    ),\n", "                    me.SelectOption(\n", "                        label=\"COCO-like short caption\", value=\"coco_like_short_caption\"\n", "                    ),\n", "                    me.SelectOption(\n", "                        label=\"Longer, descriptive caption\", value=\"descriptive_caption\"\n", "                    ),\n", "                    me.SelectOption(label=\"Optical character recognition\", value=\"ocr\"),\n", "                    me.SelectOption(label=\"Question answering\", value=\"qa\"),\n", "                    me.SelectOption(label=\"Question generation\", value=\"qg\"),\n", "                    me.SelectOption(label=\"Object detection\", value=\"object_detection\"),\n", "                    me.SelectOption(\n", "                        label=\"Object segmentation\", value=\"object_segmentation\"\n", "                    ),\n", "                ],\n", "                on_selection_change=handle_select_task,\n", "                style=me.Style(width=\"100%\"),\n", "            )\n", "\n", "        # Generation\n", "        if state.image and state.selected_task:\n", "\n", "            update_state_by_task(state.selected_task)\n", "            if state.requires_additional_parameter:\n", "                me.text(\n", "                    state.additional_param_label,\n", "                    style=me.Style(width=\"100%\", text_align=\"center\"),\n", "                    type=\"headline-5\",\n", "                )\n", "                me.input(\n", "                    label=\"Define an object (e.g. car, tree, building...)\",\n", "                    on_blur=handle_additional_parameter_input,\n", "                    style=me.Style(width=\"100%\"),\n", "                )\n", "\n", "            with me.box(\n", "                style=me.Style(\n", "                    display=\"flex\", flex_direction=\"column\", align_items=\"center\"\n", "                )\n", "            ):\n", "                me.button(\"Generate!\", on_click=generate_content, type=\"flat\")\n", "\n", "            if state.output is not None:\n", "                if state.output_type == \"text\":\n", "                    me.textarea(\n", "                        label=\"output\",\n", "                        readonly=True,\n", "                        value=state.output,\n", "                        style=me.Style(width=\"100%\"),\n", "                    )\n", "                elif state.output_type == \"object_detection\":\n", "                    boxes, labels = parse_bbox_and_labels(state.output)\n", "                    display_object_detection(boxes, labels)\n", "                elif state.output_type == \"object_segmentation\":\n", "                    bboxes, seg_masks = parse_segments(state.output)\n", "                    if len(bboxes) and len(seg_masks):\n", "                        display_object_segmentation(bboxes, seg_masks)\n", "                    else:\n", "                        me.text(\n", "                            \"Sorry, cannot find the specified object on the image.\",\n", "                            style=me.Style(width=\"100%\", text_align=\"center\"),\n", "                            type=\"headline-5\",\n", "                        )"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "tGngB24yk2vX"}, "outputs": [{"data": {"application/javascript": "(async (port, path, width, height, cache, element) => {\n    if (!google.colab.kernel.accessAllowed && !cache) {\n      return;\n    }\n    element.appendChild(document.createTextNode(''));\n    const url = await google.colab.kernel.proxyPort(port, {cache});\n    const iframe = document.createElement('iframe');\n    iframe.src = new URL(path, url).toString();\n    iframe.height = height;\n    iframe.width = width;\n    iframe.style.border = 0;\n    iframe.allow = [\n        'accelerometer',\n        'autoplay',\n        'camera',\n        'clipboard-read',\n        'clipboard-write',\n        'gyroscope',\n        'magnetometer',\n        'microphone',\n        'serial',\n        'usb',\n        'xr-spatial-tracking',\n    ].join('; ');\n    element.appendChild(iframe);\n  })(32123, \"/app\", \"100%\", 800, false, window.element)", "text/plain": ["<IPython.core.display.Javascript object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["me.colab_show(path=\"/app\", height=800)"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "[PaliGemma_1]Using_with_Mesop.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}