{"version": "6_1_0", "md.comp.input-chip.container.elevation": "md.sys.elevation.level0", "md.comp.input-chip.container.height": 32.0, "md.comp.input-chip.container.shape": "md.sys.shape.corner.small", "md.comp.input-chip.disabled.label-text.color": "onSurface", "md.comp.input-chip.disabled.label-text.opacity": 0.38, "md.comp.input-chip.disabled.selected.container.color": "onSurface", "md.comp.input-chip.disabled.selected.container.opacity": 0.12, "md.comp.input-chip.disabled.unselected.outline.color": "onSurface", "md.comp.input-chip.disabled.unselected.outline.opacity": 0.12, "md.comp.input-chip.dragged.container.elevation": "md.sys.elevation.level4", "md.comp.input-chip.focus.indicator.color": "secondary", "md.comp.input-chip.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.input-chip.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.input-chip.label-text.text-style": "labelLarge", "md.comp.input-chip.selected.container.color": "secondaryContainer", "md.comp.input-chip.selected.dragged.label-text.color": "onSecondaryContainer", "md.comp.input-chip.selected.dragged.state-layer.color": "onSecondaryContainer", "md.comp.input-chip.selected.dragged.state-layer.opacity": "md.sys.state.dragged.state-layer-opacity", "md.comp.input-chip.selected.focus.label-text.color": "onSecondaryContainer", "md.comp.input-chip.selected.focus.state-layer.color": "onSecondaryContainer", "md.comp.input-chip.selected.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.input-chip.selected.hover.label-text.color": "onSecondaryContainer", "md.comp.input-chip.selected.hover.state-layer.color": "onSecondaryContainer", "md.comp.input-chip.selected.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.input-chip.selected.label-text.color": "onSecondaryContainer", "md.comp.input-chip.selected.outline.width": 0.0, "md.comp.input-chip.selected.pressed.label-text.color": "onSecondaryContainer", "md.comp.input-chip.selected.pressed.state-layer.color": "onSecondaryContainer", "md.comp.input-chip.selected.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.input-chip.unselected.dragged.label-text.color": "onSurfaceVariant", "md.comp.input-chip.unselected.dragged.state-layer.color": "onSurfaceVariant", "md.comp.input-chip.unselected.dragged.state-layer.opacity": "md.sys.state.dragged.state-layer-opacity", "md.comp.input-chip.unselected.focus.label-text.color": "onSurfaceVariant", "md.comp.input-chip.unselected.focus.outline.color": "onSurfaceVariant", "md.comp.input-chip.unselected.focus.state-layer.color": "onSurfaceVariant", "md.comp.input-chip.unselected.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.input-chip.unselected.hover.label-text.color": "onSurfaceVariant", "md.comp.input-chip.unselected.hover.state-layer.color": "onSurfaceVariant", "md.comp.input-chip.unselected.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.input-chip.unselected.label-text.color": "onSurfaceVariant", "md.comp.input-chip.unselected.outline.color": "outlineVariant", "md.comp.input-chip.unselected.outline.width": 1.0, "md.comp.input-chip.unselected.pressed.label-text.color": "onSurfaceVariant", "md.comp.input-chip.unselected.pressed.state-layer.color": "onSurfaceVariant", "md.comp.input-chip.unselected.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.input-chip.with-avatar.avatar.shape": "md.sys.shape.corner.full", "md.comp.input-chip.with-avatar.avatar.size": 24.0, "md.comp.input-chip.with-avatar.disabled.avatar.opacity": 0.38, "md.comp.input-chip.with-leading-icon.disabled.leading-icon.color": "onSurface", "md.comp.input-chip.with-leading-icon.disabled.leading-icon.opacity": 0.38, "md.comp.input-chip.with-leading-icon.leading-icon.size": 18.0, "md.comp.input-chip.with-leading-icon.selected.dragged.leading-icon.color": "onSecondaryContainer", "md.comp.input-chip.with-leading-icon.selected.focus.leading-icon.color": "primary", "md.comp.input-chip.with-leading-icon.selected.hover.leading-icon.color": "primary", "md.comp.input-chip.with-leading-icon.selected.leading-icon.color": "primary", "md.comp.input-chip.with-leading-icon.selected.pressed.leading-icon.color": "primary", "md.comp.input-chip.with-leading-icon.unselected.dragged.leading-icon.color": "onSurfaceVariant", "md.comp.input-chip.with-leading-icon.unselected.focus.leading-icon.color": "primary", "md.comp.input-chip.with-leading-icon.unselected.hover.leading-icon.color": "primary", "md.comp.input-chip.with-leading-icon.unselected.leading-icon.color": "onSurfaceVariant", "md.comp.input-chip.with-leading-icon.unselected.pressed.leading-icon.color": "primary", "md.comp.input-chip.with-trailing-icon.disabled.trailing-icon.color": "onSurface", "md.comp.input-chip.with-trailing-icon.disabled.trailing-icon.opacity": 0.38, "md.comp.input-chip.with-trailing-icon.selected.dragged.trailing-icon.color": "primary", "md.comp.input-chip.with-trailing-icon.selected.focus.trailing-icon.color": "onSecondaryContainer", "md.comp.input-chip.with-trailing-icon.selected.hover.trailing-icon.color": "onSecondaryContainer", "md.comp.input-chip.with-trailing-icon.selected.pressed.trailing-icon.color": "onSecondaryContainer", "md.comp.input-chip.with-trailing-icon.selected.trailing-icon.color": "onSecondaryContainer", "md.comp.input-chip.with-trailing-icon.trailing-icon.size": 18.0, "md.comp.input-chip.with-trailing-icon.unselected.dragged.trailing-icon.color": "primary", "md.comp.input-chip.with-trailing-icon.unselected.focus.trailing-icon.color": "onSurfaceVariant", "md.comp.input-chip.with-trailing-icon.unselected.hover.trailing-icon.color": "onSurfaceVariant", "md.comp.input-chip.with-trailing-icon.unselected.pressed.trailing-icon.color": "onSurfaceVariant", "md.comp.input-chip.with-trailing-icon.unselected.trailing-icon.color": "onSurfaceVariant"}