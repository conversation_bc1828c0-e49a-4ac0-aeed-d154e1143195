"""
Módulo para busca de informações na internet.
"""

import os
import json
import logging
import requests
from typing import List, Dict, Any, Optional
from tenacity import retry, stop_after_attempt, wait_exponential

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebSearch:
    """Classe para busca de informações na internet."""
    
    def __init__(self, max_results: int = 10):
        """
        Inicializa o objeto de busca na internet.
        
        Args:
            max_results (int, optional): Número máximo de resultados a serem retornados. Padrão é 10.
        """
        self.max_results = max_results
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def _search_duckduckgo(self, query: str) -> List[Dict[str, Any]]:
        """
        Realiza uma busca usando a API do DuckDuckGo.
        
        Args:
            query (str): Consulta de busca.
            
        Returns:
            List[Dict[str, Any]]: Lista de resultados da busca.
        """
        try:
            # Importar a biblioteca duckduckgo_search
            from duckduckgo_search import DDGS
            
            # Criar o objeto de busca
            ddgs = DDGS()
            
            # Realizar a busca
            results = []
            for r in ddgs.text(query, max_results=self.max_results):
                results.append({
                    'title': r.get('title', ''),
                    'url': r.get('href', ''),
                    'summary': r.get('body', ''),
                    'authors': [],  # DuckDuckGo não fornece autores
                    'published': '',  # DuckDuckGo não fornece data de publicação
                    'source': 'web'
                })
            
            return results
        except Exception as e:
            logger.error(f"Erro ao realizar busca no DuckDuckGo: {e}")
            return []
    
    def _extract_content(self, url: str) -> Dict[str, Any]:
        """
        Extrai conteúdo de uma URL.
        
        Args:
            url (str): URL para extrair conteúdo.
            
        Returns:
            Dict[str, Any]: Dicionário com o conteúdo extraído.
        """
        try:
            # Fazer requisição HTTP
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            # Extrair informações básicas
            content = response.text
            
            # Tentar extrair título
            title = ""
            title_start = content.find("<title>")
            title_end = content.find("</title>")
            if title_start != -1 and title_end != -1:
                title = content[title_start + 7:title_end].strip()
            
            # Extrair texto (simplificado)
            # Em uma implementação real, você usaria uma biblioteca como BeautifulSoup
            text = content.replace("<", " <").replace(">", "> ").replace("\n", " ")
            text = ' '.join(text.split())
            
            return {
                'title': title,
                'content': text[:5000],  # Limitar o tamanho do conteúdo
                'url': url
            }
        except Exception as e:
            logger.error(f"Erro ao extrair conteúdo de {url}: {e}")
            return {
                'title': 'Erro ao extrair conteúdo',
                'content': f'Não foi possível extrair o conteúdo de {url}. Erro: {str(e)}',
                'url': url
            }
    
    def search(self, query: str) -> List[Dict[str, Any]]:
        """
        Realiza uma busca na internet.
        
        Args:
            query (str): Consulta de busca.
            
        Returns:
            List[Dict[str, Any]]: Lista de resultados da busca.
        """
        # Realizar busca no DuckDuckGo
        results = self._search_duckduckgo(query)
        
        # Limitar o número de resultados
        results = results[:self.max_results]
        
        # Adicionar informações acadêmicas fictícias para compatibilidade
        for result in results:
            if not result.get('authors'):
                result['authors'] = ['Autor desconhecido']
            if not result.get('published'):
                result['published'] = 'Data desconhecida'
        
        return results
