import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_gemma/flutter_gemma.dart';
import 'package:path_provider/path_provider.dart';

/// Service for downloading and managing Gemma-3N model locally
/// Based on the flutter_gemma package example from the article
class GemmaDownloaderService {
  static final GemmaDownloaderService _instance =
      GemmaDownloaderService._internal();
  factory GemmaDownloaderService() => _instance;
  GemmaDownloaderService._internal();

  static GemmaDownloaderService get instance => _instance;

  // Gemma 3N model configuration following the article example
  static const String _modelUrl =
      'https://huggingface.co/google/gemma-3n-E2B-it-litert-preview/resolve/main/gemma-3n-E2B-it-int4.task';
  static const String _modelFilename = 'gemma-3n-E2B-it-int4.task';

  String? _modelPath;
  bool _isDownloading = false;
  double _downloadProgress = 0.0;

  /// Check if the Gemma-3N model exists locally
  Future<bool> checkModelExistence() async {
    try {
      if (kDebugMode) {
        print('🔍 Checking if Gemma-3N model exists locally...');
      }

      // Check if model file exists in the app's documents directory
      final directory = await getApplicationDocumentsDirectory();
      final modelFile = File('${directory.path}/$_modelFilename');

      final exists = await modelFile.exists();

      if (exists) {
        _modelPath = modelFile.path;
        if (kDebugMode) {
          print('✅ Gemma-3N model found at: $_modelPath');
          final sizeBytes = await modelFile.length();
          final sizeMB = (sizeBytes / (1024 * 1024)).toStringAsFixed(1);
          print('📊 Model size: ${sizeMB}MB');
        }
      } else {
        if (kDebugMode) {
          print('❌ Gemma-3N model not found locally');
        }
      }

      return exists;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking model existence: $e');
      }
      return false;
    }
  }

  /// Download the Gemma-3N model from Hugging Face using flutter_gemma
  Future<bool> downloadModel({
    required String accessToken,
    Function(double)? onProgress,
  }) async {
    if (_isDownloading) {
      if (kDebugMode) {
        print('⚠️ Model download already in progress');
      }
      return false;
    }

    try {
      _isDownloading = true;
      _downloadProgress = 0.0;

      if (kDebugMode) {
        print('🚀 Starting Gemma-3N model download...');
        print('📥 Model URL: $_modelUrl');
        print('📁 Filename: $_modelFilename');
        print('🔑 Using access token: ${accessToken.substring(0, 10)}...');
      }

      // Get the flutter_gemma plugin instance and model manager
      final gemma = FlutterGemmaPlugin.instance;
      final modelManager = gemma.modelManager;

      // Download the model using ModelFileManager with progress tracking
      await for (final progress
          in modelManager.downloadModelFromNetworkWithProgress(_modelUrl)) {
        _downloadProgress = progress / 100.0; // Convert percentage to 0.0-1.0

        if (kDebugMode) {
          print('📊 Download progress: ${progress.toStringAsFixed(1)}%');
        }

        // Call the progress callback if provided
        onProgress?.call(_downloadProgress);
      }

      // Verify the download was successful
      final isDownloaded = await checkModelExistence();

      if (isDownloaded) {
        if (kDebugMode) {
          print('✅ Gemma-3N model downloaded successfully!');
          print('📁 Model path: $_modelPath');
        }
        return true;
      } else {
        throw Exception('Model download completed but file not found');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to download Gemma-3N model: $e');
      }
      return false;
    } finally {
      _isDownloading = false;
    }
  }

  /// Get the local model path
  String? get modelPath => _modelPath;

  /// Get current download progress (0.0 to 1.0)
  double get downloadProgress => _downloadProgress;

  /// Check if model is currently downloading
  bool get isDownloading => _isDownloading;

  /// Get model file size in MB (if exists)
  Future<double?> getModelSizeMB() async {
    if (_modelPath == null) return null;

    try {
      final file = File(_modelPath!);
      if (await file.exists()) {
        final sizeBytes = await file.length();
        return sizeBytes / (1024 * 1024); // Convert to MB
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting model size: $e');
      }
    }

    return null;
  }

  /// Delete the local model file
  Future<bool> deleteModel() async {
    if (_modelPath == null) return true;

    try {
      final file = File(_modelPath!);
      if (await file.exists()) {
        await file.delete();
        if (kDebugMode) {
          print('🗑️ Gemma-3N model deleted successfully');
        }
      }

      _modelPath = null;
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting model: $e');
      }
      return false;
    }
  }

  /// Get model information
  Map<String, dynamic> getModelInfo() {
    return {
      'modelName': 'Gemma-3N-E2B-it',
      'modelFilename': _modelFilename,
      'modelUrl': _modelUrl,
      'isDownloaded': _modelPath != null,
      'modelPath': _modelPath,
      'isDownloading': _isDownloading,
      'downloadProgress': _downloadProgress,
      'capabilities': [
        'Text Translation',
        'Image OCR + Translation',
        'Multimodal AI',
        'Offline Processing',
        '35 Languages Support',
        '140+ Text Languages',
        'No Internet Required',
        'Privacy Focused',
        'No API Costs',
      ],
      'benefits': [
        'Offline Functionality',
        'Enhanced Privacy',
        'No Server Costs',
        'Real-time Processing',
        'Multimodal Capabilities',
      ],
    };
  }

  /// Get model URL for reference
  String get modelUrl => _modelUrl;

  /// Get model filename for reference
  String get modelFilename => _modelFilename;
}
