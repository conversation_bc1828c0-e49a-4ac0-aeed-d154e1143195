# Gerador de Artigos Acadêmicos ABNT

Este aplicativo gera artigos acadêmicos no estilo Wikipedia usando a API Groq e busca de dados no arxiv.org, seguindo as normas ABNT (Associação Brasileira de Normas Técnicas) para citações e referências bibliográficas.

## Funcionalidades

- **Pesquisa Automática**: Busca artigos relacionados ao tópico no arxiv.org
- **Geração de Esboço**: Cria um esboço estruturado para o artigo
- **Geração de Artigo**: Produz um artigo completo com base no esboço e nas fontes de pesquisa
- **Polimento de Artigo**: Melhora a qualidade do texto, gramática e formatação
- **Verificação de Plágio**: Detecta trechos potencialmente plagiados
- **Exportação para PDF**: Converte o artigo para PDF com formatação ABNT
- **Sistema de Histórico e Favoritos**: Gerencia artigos gerados e favoritos

## Instalação Rápida

1. Execute o script de instalação:
   ```
   python install.py
   ```

   Este script irá:
   - Instalar todas as dependências necessárias
   - Solicitar sua chave da API Groq
   - Configurar o arquivo de segredos

2. Execute o aplicativo:
   ```
   python run.py
   ```

## Instalação Manual

1. Instale as dependências:
   ```
   pip install -r requirements.txt
   ```

2. Configure sua chave da API Groq:
   - Crie um diretório `.streamlit` no diretório do aplicativo
   - Crie um arquivo `.streamlit/secrets.toml` com o seguinte conteúdo:
     ```toml
     GROQ_API_KEY = "sua-chave-da-api-groq-aqui"
     ```

3. Execute o aplicativo:
   ```
   streamlit run app.py
   ```

## Solução de Problemas

### Erro com o módulo json

Se você encontrar o erro "name 'json' is not defined", verifique se o módulo json está importado em todos os arquivos que o utilizam. Este erro já foi corrigido na versão atual.

### Erro com a exportação para PDF

Esta versão usa FPDF2 em vez de WeasyPrint para evitar problemas de dependências. Se você encontrar problemas com a exportação para PDF, verifique se a biblioteca FPDF2 está instalada corretamente:

```
pip install fpdf2
```

## Uso

### Criar um Novo Artigo

1. Acesse a aba "Criar Novo Artigo"
2. Digite o tópico do artigo
3. Clique em "Gerar Artigo"
4. Acompanhe o progresso da geração

### Gerenciar Artigos

- **Meus Artigos**: Visualize, filtre e ordene todos os artigos gerados
- **Favoritos**: Acesse rapidamente seus artigos favoritos

### Exportar Artigos

- **PDF**: Exporte o artigo para PDF com formatação ABNT
- **Diretório**: Exporte o artigo e seus arquivos para outro diretório

## Estrutura do Projeto

```
artigos_academicos_abnt/
├── app.py                  # Aplicativo principal
├── run.py                  # Script para executar o aplicativo
├── install.py              # Script de instalação
├── requirements.txt        # Dependências do projeto
├── README.md               # Documentação
└── modules/                # Módulos do aplicativo
    ├── __init__.py
    ├── arxiv_search.py     # Busca no Arxiv
    ├── article_generator.py # Geração de artigos
    ├── cache.py            # Sistema de cache
    ├── history.py          # Gerenciamento de histórico
    ├── pdf_export.py       # Exportação para PDF
    ├── plagiarism.py       # Verificação de plágio
    └── utils.py            # Funções utilitárias
```

## Requisitos

- Python 3.8+
- Streamlit 1.22.0+
- Groq API Key

## Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo LICENSE para detalhes.
