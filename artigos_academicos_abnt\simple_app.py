"""
Versão simplificada do aplicativo de artigos acadêmicos ABNT.
"""

import streamlit as st

def main():
    """Função principal do aplicativo simplificado"""
    st.title("Gerador de Artigos Acadêmicos ABNT (Simplificado)")
    
    # Sidebar para configurações
    with st.sidebar:
        st.header("Configurações")
        
        # Formato do artigo
        st.subheader("Formato do Artigo")
        article_format = st.radio(
            "Escolha o formato:",
            ["Padrão (estilo Wikipedia)", "Acadêmico (com resumo, abstract, etc.)"],
            index=0
        )
        
        # Opções avançadas para o formato acadêmico
        if article_format == "Acadêmico (com resumo, abstract, etc.)":
            st.subheader("Contagem de Palavras")
            word_count_option = st.radio(
                "Distribuição de palavras:",
                ["Automática (balanceada)", "Personalizada"],
                index=0
            )
            
            if word_count_option == "Personalizada":
                st.info("Defina a quantidade aproximada de palavras para cada seção do artigo.")
                
                # Definir contagens para cada seção
                resumo_words = st.number_input("Resumo/Abstract", min_value=100, max_value=500, value=200, step=50)
                intro_words = st.number_input("Introdução", min_value=200, max_value=1000, value=400, step=50)
                metodo_words = st.number_input("Metodologia", min_value=200, max_value=1000, value=400, step=50)
                
                # Calcular e mostrar o total
                total_words = resumo_words * 2 + intro_words + metodo_words
                st.info(f"Total aproximado: {total_words} palavras")
        
        # Configurações avançadas
        st.subheader("Configurações avançadas")
        
        # Usar expander com variável
        advanced_expander = st.expander("Configurações de retry e cache")
        with advanced_expander:
            st.slider("Número máximo de tentativas", 1, 10, 5)
            st.checkbox("Usar cache", value=True)
    
    # Área principal
    st.header("Criar Novo Artigo")
    
    # Formulário para criar novo artigo
    with st.form(key="new_article_form"):
        topic = st.text_input("Tópico do artigo:", placeholder="Ex: Inteligência Artificial")
        submit_button = st.form_submit_button(label="Gerar Artigo")
    
    if submit_button and topic:
        st.success(f"Artigo sobre '{topic}' seria gerado aqui.")

if __name__ == "__main__":
    main()
