{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Unsloth now supports Text-to-Speech (TTS) models. Read our [guide here](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning).\n", "\n", "Read our **[Qwen3 Guide](https://docs.unsloth.ai/basics/qwen3-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "%%capture\nimport os\nif \"COLAB_\" not in \"\".join(os.environ.keys()):\n    !pip install unsloth\nelse:\n    # Do this only in Colab notebooks! Otherwise use pip install unsloth\n    !pip install --no-deps bitsandbytes accelerate xformers==0.0.29.post3 peft trl triton cut_cross_entropy unsloth_zoo\n    !pip install sentencepiece protobuf \"datasets>=3.4.1\" huggingface_hub hf_transfer\n    !pip install --no-deps unsloth"}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "max_seq_length = 2048 # Choose any! We auto support RoPE Scaling internally!\n", "dtype = None # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+\n", "load_in_4bit = True # Use 4bit quantization to reduce memory usage. Can be False.\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/Meta-Llama-3.1-8B-bnb-4bit\",      # Llama-3.1 2x faster\n", "    \"unsloth/Meta-Llama-3.1-8B-Instruct-bnb-4bit\",\n", "    \"unsloth/Meta-Llama-3.1-70B-bnb-4bit\",\n", "    \"unsloth/Meta-Llama-3.1-405B-bnb-4bit\",    # 4bit for 405b!\n", "    \"unsloth/Mistral-Small-Instruct-2409\",     # Mistral 22b 2x faster!\n", "    \"unsloth/mistral-7b-instruct-v0.3-bnb-4bit\",\n", "    \"unsloth/Phi-3.5-mini-instruct\",           # Phi-3.5 2x faster!\n", "    \"unsloth/Phi-3-medium-4k-instruct\",\n", "    \"unsloth/gemma-2-9b-bnb-4bit\",\n", "    \"unsloth/gemma-2-27b-bnb-4bit\",            # <PERSON> 2x faster!\n", "\n", "    \"unsloth/Llama-3.2-1B-bnb-4bit\",           # NEW! Llama 3.2 models\n", "    \"unsloth/Llama-3.2-1B-Instruct-bnb-4bit\",\n", "    \"unsloth/Llama-3.2-3B-bnb-4bit\",\n", "    \"unsloth/Llama-3.2-3B-Instruct-bnb-4bit\",\n", "\n", "    \"unsloth/Llama-3.3-70B-Instruct-bnb-4bit\" # NEW! Llama 3.3 70B!\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/Llama-3.2-3B-Instruct\", # or choose \"unsloth/Llama-3.2-1B-Instruct\"\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit,\n", "    # token = \"hf_...\", # use one if using gated models like meta-llama/Llama-2-7b-hf\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "SXd9bTZd1aaL"}, "source": ["We now add LoRA adapters so we only need to update 1 to 10% of all parameters!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6bZsfBuZDeCL", "outputId": "acc0f9f5-59a6-46fe-d5bb-cd09965bb8c9"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth 2024.10.0 patched 28 layers with 28 QKV layers, 28 O layers and 28 MLP layers.\n"]}], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 16, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 16,\n", "    lora_dropout = 0, # Supports any, but = 0 is optimized\n", "    bias = \"none\",    # Supports any, but = \"none\" is optimized\n", "    # [NEW] \"unsloth\" uses 30% less VRAM, fits 2x larger batch sizes!\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for very long context\n", "    random_state = 3407,\n", "    use_rslora = False,  # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "vITh0KVJ10qX"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We now use the `Llama-3.1` format for conversation style finetunes. We use [Maxime Labonne's FineTome-100k](https://huggingface.co/datasets/mlabonne/FineTome-100k) dataset in ShareGPT style. But we convert it to HuggingFace's normal multiturn format `(\"role\", \"content\")` instead of `(\"from\", \"value\")`/ Llama-3 renders multi turn conversations like below:\n", "\n", "```\n", "<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n", "\n", "Hello!<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "Hey there! How are you?<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "I'm great thanks!<|eot_id|>\n", "```\n", "\n", "We use our `get_chat_template` function to get the correct chat template. We support `zephyr, chatml, mistral, llama, alpaca, vicuna, vicuna_old, phi3, llama3` and more."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 113, "referenced_widgets": ["39bf1c29894f43acb6d2919e64a4fd28", "007a35a241b346ec9a5cdd6f3e4ddd27", "969a119573f942b29951ae2933e61cde", "b8c4d378ea0e4bcd9f572a191a7c136f", "7d37dd0e06724b53b4f31cc0a4321b0d", "4083b2ef8e6348e18b69d116508b46ff", "9555be409a2c4a97b18d4978ed13d35f", "5628ed38f304438faf5442b29a9511d6", "6e0fe945001140b3959e617a2f55c353", "0c30ded692064dc7bf36a93897f2b68f", "8c5ad85b4da14b239340ac95244d8ed4", "39684b70f2ff48cab454617c721f7777", "e8445e90b1054aacbecf198c7979a0b6", "d1cc50fb6d5849888af5d765dc51ab62", "2b359412d4914aa38a6e21284c12ecbc", "a4ceb6dbc8de4fa798ee39d28e5ebc40", "d6ab4d4143ff49bcae30be1bc2d76762", "904e7bac43bd4333b321cacfed5dcb60", "2bb75539976c49ed805c4ff6c58fb1d2", "45bc9d882a8f4a7e813245b1590d4427", "ddee625828cb4c22927aa73a02cd2dd9", "fd46f381983f49179de05497c171c805", "785d9147f4a341afafc5c5743892df16", "5e9825466cd2481b92cfe89f33b11fe3", "bfbb37b6f4b247b5bf5aaf7e1d80bcf9", "2a6ca29a76ff430d86213f910858db5b", "92d981a21b204f6c8b52e3caa16d1784", "c685f29a5d2c461ca3dda867bab6df50", "e2f16d56b21c4ff2918872d70e5ca847", "0bfbfe620ff446a0a47f7d5de7c88975", "5c9ee920068a47d89dbf5cbdd9e848a3", "95249b8fb6a84054a01f22c5f73f207b", "2ed2b017b9a24f36a4222c5c27753991"]}, "id": "LjY75GoYUCB8", "outputId": "94095b01-dac6-4f9c-cbc3-ca78e007ba12"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "39bf1c29894f43acb6d2919e64a4fd28", "version_major": 2, "version_minor": 0}, "text/plain": ["README.md:   0%|          | 0.00/982 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "39684b70f2ff48cab454617c721f7777", "version_major": 2, "version_minor": 0}, "text/plain": ["train-00000-of-00001.parquet:   0%|          | 0.00/117M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "785d9147f4a341afafc5c5743892df16", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split:   0%|          | 0/100000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth.chat_templates import get_chat_template\n", "\n", "tokenizer = get_chat_template(\n", "    tokenizer,\n", "    chat_template = \"llama-3.1\",\n", ")\n", "\n", "def formatting_prompts_func(examples):\n", "    convos = examples[\"conversations\"]\n", "    texts = [tokenizer.apply_chat_template(convo, tokenize = False, add_generation_prompt = False) for convo in convos]\n", "    return { \"text\" : texts, }\n", "pass\n", "\n", "from datasets import load_dataset\n", "dataset = load_dataset(\"mlabonne/FineTome-100k\", split = \"train\")"]}, {"cell_type": "markdown", "metadata": {"id": "K9CBpiISFa6C"}, "source": ["We now use `standardize_sharegpt` to convert ShareGPT style datasets into HuggingFace's generic format. This changes the dataset from looking like:\n", "```\n", "{\"from\": \"system\", \"value\": \"You are an assistant\"}\n", "{\"from\": \"human\", \"value\": \"What is 2+2?\"}\n", "{\"from\": \"gpt\", \"value\": \"It's 4.\"}\n", "```\n", "to\n", "```\n", "{\"role\": \"system\", \"content\": \"You are an assistant\"}\n", "{\"role\": \"user\", \"content\": \"What is 2+2?\"}\n", "{\"role\": \"assistant\", \"content\": \"It's 4.\"}\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 81, "referenced_widgets": ["dd9e90f2c16541e8a72c6771c4685b9a", "a326b2e89f1c46f28cd166afc7490e2b", "eb855a0fcb554a8eb245351b3593623d", "bd71b6cb29e147ab9b10d1b85908c413", "b1b0a4e3f00043b0a0eb7a053815a4a5", "58ce4633471c438db6e103a1ca3806a0", "cf1b769b7a744b5f8bccf6798566582f", "1c0c2835705f41089de4caea98127c04", "e2d886444f0047fa9e2245b9773ced9e", "c03b9410af384397849ef63b62f2c689", "098bd8ace574423da763eb0eae1d3bb6", "d08e764aa8b94e7f9e1c727b53980abe", "e62f6eb58a744d38b837e47d8a16db67", "bcf8e36d938a4d959c31ea4ff3c8d4cf", "ae2464c1cbc442a383de7577d2986116", "9a8f1b8079fe478ebf0b16096cb224f5", "e4bf3f8e63bb4c01bbe821d438445d91", "d7e0024b98a94a9fa12dc4154ff2b2fc", "cc0bd79ca9e847fba88aafe2d612ffe4", "76e2e47c93e541ff820bcbab9264381d", "4b41aa65c6894e918b04709f8e9270d2", "cdae06929214464ea25e343f17b4a843"]}, "id": "oPXzJZzHEgXe", "outputId": "dd1c72fa-39ea-48a2-9ed2-c263a4549b91"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dd9e90f2c16541e8a72c6771c4685b9a", "version_major": 2, "version_minor": 0}, "text/plain": ["Standardizing format:   0%|          | 0/100000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d08e764aa8b94e7f9e1c727b53980abe", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/100000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth.chat_templates import standardize_sharegpt\n", "dataset = standardize_sharegpt(dataset)\n", "dataset = dataset.map(formatting_prompts_func, batched = True,)"]}, {"cell_type": "markdown", "metadata": {"id": "ndDUB23CGAC5"}, "source": ["We look at how the conversations are structured for item 5:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "gGFzmplrEy9I", "outputId": "9f3f66fc-8649-40c8-829c-db3f11f88728"}, "outputs": [{"data": {"text/plain": ["[{'content': 'How do astronomers determine the original wavelength of light emitted by a celestial body at rest, which is necessary for measuring its speed using the Do<PERSON>ler effect?',\n", "  'role': 'user'},\n", " {'content': 'Astronomers make use of the unique spectral fingerprints of elements found in stars. These elements emit and absorb light at specific, known wavelengths, forming an absorption spectrum. By analyzing the light received from distant stars and comparing it to the laboratory-measured spectra of these elements, astronomers can identify the shifts in these wavelengths due to the Do<PERSON>ler effect. The observed shift tells them the extent to which the light has been redshifted or blueshifted, thereby allowing them to calculate the speed of the star along the line of sight relative to Earth.',\n", "  'role': 'assistant'}]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[5][\"conversations\"]"]}, {"cell_type": "markdown", "metadata": {"id": "GfzTdMtvGE6w"}, "source": ["And we see how the chat template transformed these conversations.\n", "\n", "**[Notice]** Llama 3.1 Instruct's default chat template default adds `\"Cutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\"`, so do not be alarmed!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 159}, "id": "vhXv0xFMGNKE", "outputId": "07bf64e3-4c5c-430e-e4d5-3ed3cdf21b81"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\nHow do astronomers determine the original wavelength of light emitted by a celestial body at rest, which is necessary for measuring its speed using the Doppler effect?<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\nAstronomers make use of the unique spectral fingerprints of elements found in stars. These elements emit and absorb light at specific, known wavelengths, forming an absorption spectrum. By analyzing the light received from distant stars and comparing it to the laboratory-measured spectra of these elements, astronomers can identify the shifts in these wavelengths due to the Doppler effect. The observed shift tells them the extent to which the light has been redshifted or blueshifted, thereby allowing them to calculate the speed of the star along the line of sight relative to Earth.<|eot_id|>'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[5][\"text\"]"]}, {"cell_type": "markdown", "metadata": {"id": "idAEIeSQ3xdS"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `SFTTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 60 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`. We also support TRL's `DPOTrainer`!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 67, "referenced_widgets": ["3ffe42931dcf4a69972f4d50ee4dd3dd", "ee9dcec2d5c44fd883f16c06b9f76264", "982b6b94642d49fa85fab6ad621392fe", "42990f347a8c42f7b510e2d17c7d3c6e", "3cd95b7c5e2f4c6883333045db11c6d6", "5b34a4e8fc7747e78b49ad5bf67a6580", "23907906314743938db4e484c15480cc", "378176d2f0c9466d8762a584edf4217d", "e221482cbe95465191212d85d539938c", "74dc78a38e30465a96d2c8a22a27b127", "c6b4759ce826421081508270cb30334b"]}, "id": "95_Nn-89DhsL", "outputId": "97211c96-b8e2-4b35-8691-892550ee0e7a"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3ffe42931dcf4a69972f4d50ee4dd3dd", "version_major": 2, "version_minor": 0}, "text/plain": ["Map (num_proc=2):   0%|          | 0/100000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["max_steps is given, it will override any value given in num_train_epochs\n"]}], "source": ["from trl import SFTConfig, SFTTrainer\n", "from transformers import DataCollatorForSeq2Seq\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = dataset,\n", "    dataset_text_field = \"text\",\n", "    max_seq_length = max_seq_length,\n", "    data_collator = DataCollatorForSeq2Seq(tokenizer = tokenizer),\n", "    dataset_num_proc = 2,\n", "    packing = False, # Can make training 5x faster for short sequences.\n", "    args = SFTConfig(\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4,\n", "        warmup_steps = 5,\n", "        # num_train_epochs = 1, # Set this for 1 full training run.\n", "        max_steps = 60,\n", "        learning_rate = 2e-4,\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "C_sGp5XlG6dq"}, "source": ["We also use <PERSON><PERSON><PERSON><PERSON>'s `train_on_completions` method to only train on the assistant outputs and ignore the loss on the user's inputs."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 49, "referenced_widgets": ["6064feeea79040409e18a1e2a289b09a", "bb241a26ca4d4d7186ba46cda1f8a802", "c9abb42da1734388a7d2f1a06832ecc6", "7c3a37494e5848b9994b37a4c8bac132", "c668ae4c7d174f2dad3fb837ff873e57", "dd30f3ead6394317be5a72aa890adfb9", "1e4ea03959b3496f8e75cc3588cf347c", "d356b597dda14c7ab023403ee6959cf8", "870ff8f17c7b47ec8d49cac84216b04c", "d5cfa138483f4007b2a95be833043235", "6d52daf29c90402a9762acdde765713f"]}, "id": "juQiExuBG5Bt", "outputId": "dca88e73-ac69-4199-9c83-cb6300e8ce9a"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6064feeea79040409e18a1e2a289b09a", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/100000 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth.chat_templates import train_on_responses_only\n", "trainer = train_on_responses_only(\n", "    trainer,\n", "    instruction_part = \"<|start_header_id|>user<|end_header_id|>\\n\\n\",\n", "    response_part = \"<|start_header_id|>assistant<|end_header_id|>\\n\\n\",\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "Dv1NBUozV78l"}, "source": ["We verify masking is actually done:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 159}, "id": "LtsMVtlkUhja", "outputId": "84735ea5-8489-4a34-f501-afe91901d542"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\nHow do astronomers determine the original wavelength of light emitted by a celestial body at rest, which is necessary for measuring its speed using the Doppler effect?<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\nAstronomers make use of the unique spectral fingerprints of elements found in stars. These elements emit and absorb light at specific, known wavelengths, forming an absorption spectrum. By analyzing the light received from distant stars and comparing it to the laboratory-measured spectra of these elements, astronomers can identify the shifts in these wavelengths due to the Doppler effect. The observed shift tells them the extent to which the light has been redshifted or blueshifted, thereby allowing them to calculate the speed of the star along the line of sight relative to Earth.<|eot_id|>'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.decode(trainer.train_dataset[5][\"input_ids\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 106}, "id": "_rD6fl8EUxnG", "outputId": "7b0d0ab4-06c3-4f2c-bb94-0ec853a4d0cc"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'                                                                \\n\\nAstronomers make use of the unique spectral fingerprints of elements found in stars. These elements emit and absorb light at specific, known wavelengths, forming an absorption spectrum. By analyzing the light received from distant stars and comparing it to the laboratory-measured spectra of these elements, astronomers can identify the shifts in these wavelengths due to the Do<PERSON>ler effect. The observed shift tells them the extent to which the light has been redshifted or blueshifted, thereby allowing them to calculate the speed of the star along the line of sight relative to Earth.<|eot_id|>'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["space = tokenizer(\" \", add_special_tokens = False).input_ids[0]\n", "tokenizer.decode([space if x == -100 else x for x in trainer.train_dataset[5][\"labels\"]])"]}, {"cell_type": "markdown", "metadata": {"id": "3enWUM0jV-jV"}, "source": ["We can see the System and Instruction prompts are successfully masked!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "2ejIt2xSNKKp", "outputId": "ac07343f-67db-44e4-f9d3-83539724e6af"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU = Tesla T4. Max memory = 14.748 GB.\n", "2.635 GB of memory reserved.\n"]}], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "yqxqAZ7KJ4oL", "outputId": "fb3dc2a2-5cd6-4aa0-dfc5-ad734359f397"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs = 1\n", "   \\\\   /|    Num examples = 100,000 | Num Epochs = 1\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient Accumulation steps = 4\n", "\\        /    Total batch size = 8 | Total steps = 60\n", " \"-____-\"     Number of trainable parameters = 24,313,856\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='60' max='60' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [60/60 07:12, Epoch 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.826200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.811700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>1.132200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.927300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.775200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.967900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.630600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>1.027400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.788400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.753300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.841900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.965600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.914400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.674500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.815000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.617800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>1.036200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.782800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.817000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.952500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.731900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.742500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.942600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.881000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.648800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.803100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.814900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.768900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.974300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.894600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.656200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.539600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.657700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.552900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.749700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.845200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.877400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.714900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.731400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.980200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.755500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.941300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.819900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.826300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.762400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.941000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.806900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.609200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.974600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>1.055500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>0.561200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>0.998100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>1.006000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>0.686900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>1.043900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>1.140300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>0.683900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>0.810200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>0.796500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>0.832400</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "pCqnaKmlO1U9", "outputId": "fcbecf7f-b8a1-45d5-f415-eec2bdf96576"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["446.5262 seconds used for training.\n", "7.44 minutes used for training.\n", "Peak reserved memory = 6.531 GB.\n", "Peak reserved memory for training = 3.896 GB.\n", "Peak reserved memory % of max memory = 44.284 %.\n", "Peak reserved memory for training % of max memory = 26.417 %.\n"]}], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {"id": "ekOmTR1hSNcr"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model! You can change the instruction and input - leave the output blank!\n", "\n", "\n", "\n", "We use `min_p = 0.1` and `temperature = 1.5`. Read this [Tweet](https://x.com/menhguin/status/1826132708508213629) for more information on why."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kR3gIAX-SM2q", "outputId": "53188d07-ba68-420e-874b-1bace9929aa0"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The attention mask is not set and cannot be inferred from input because pad token is same as eos token. As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.\n"]}, {"data": {"text/plain": ["['<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 26 July 2024\\n\\n<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\nContinue the fibonnaci sequence: 1, 1, 2, 3, 5, 8,<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\nThe Fibonacci sequence is a series of numbers in which each number is the sum of the two preceding numbers. The sequence is: 1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144.<|eot_id|>']"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["from unsloth.chat_templates import get_chat_template\n", "\n", "tokenizer = get_chat_template(\n", "    tokenizer,\n", "    chat_template = \"llama-3.1\",\n", ")\n", "FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"Continue the fi<PERSON><PERSON><PERSON> sequence: 1, 1, 2, 3, 5, 8,\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = True,\n", "    add_generation_prompt = True, # Must add for generation\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "outputs = model.generate(input_ids = inputs, max_new_tokens = 64, use_cache = True,\n", "                         temperature = 1.5, min_p = 0.1)\n", "tokenizer.batch_decode(outputs)"]}, {"cell_type": "markdown", "metadata": {"id": "CrSvZObor0lY"}, "source": [" You can also use a `TextStreamer` for continuous inference - so you can see the generation token by token, instead of waiting the whole time!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "e2pEuRb1r2Vg", "outputId": "d4096dc5-c359-49c0-c08f-f8c890e414e5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The <PERSON><PERSON><PERSON><PERSON> sequence is a series of numbers where each number is the sum of the two preceding numbers. \n", "\n", "The sequence you provided was: 1, 1, 2, 3, 5, 8, 13\n", "\n", "The next number in the sequence would be 21, which is 8 + 13. The sequence continues as: 21, 34, 55, 89, 144, 233.<|eot_id|>\n"]}], "source": ["FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"Continue the fi<PERSON><PERSON><PERSON> sequence: 1, 1, 2, 3, 5, 8,\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = True,\n", "    add_generation_prompt = True, # Must add for generation\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(input_ids = inputs, streamer = text_streamer, max_new_tokens = 128,\n", "                   use_cache = True, temperature = 1.5, min_p = 0.1)"]}, {"cell_type": "markdown", "metadata": {"id": "uMuVrWbjAzhc"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "upcOlWe7A1vc", "outputId": "c962e43a-2027-4a39-ea03-870b707a22d4"}, "outputs": [{"data": {"text/plain": ["('lora_model/tokenizer_config.json',\n", " 'lora_model/special_tokens_map.json',\n", " 'lora_model/tokenizer.json')"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "AEEcJ4qfC7Lp"}, "source": ["Now if you want to load the LoRA adapters we just saved for inference, set `False` to `True`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MKX_XKs_BNZR", "outputId": "f22dbd75-ea37-48bb-9f75-4178aebe9353"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Eiffel Tower, located in the heart of Paris, stands tall among the city's historic and cultural landmarks. This iron structure, standing at an impressive 324 meters high, offers breathtaking views of the City of Light's iconic landscape. The Eiffel Tower was built for the 1889 World's Fair and has since become a symbol of French engineering and culture.<|eot_id|>\n"]}], "source": ["if False:\n", "    from unsloth import FastLanguageModel\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name = \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        max_seq_length = max_seq_length,\n", "        dtype = dtype,\n", "        load_in_4bit = load_in_4bit,\n", "    )\n", "    FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"Describe a tall tower in the capital of France.\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = True,\n", "    add_generation_prompt = True, # Must add for generation\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(input_ids = inputs, streamer = text_streamer, max_new_tokens = 128,\n", "                   use_cache = True, temperature = 1.5, min_p = 0.1)"]}, {"cell_type": "markdown", "metadata": {"id": "QQMjaNrjsU5_"}, "source": ["You can also use Hugging Face's `AutoModelForPeftCausalLM`. Only use this if you do not have `unsloth` installed. It can be hopelessly slow, since `4bit` model downloading is not supported, and Unsloth's **inference is 2x faster**."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "yFfaXG0WsQuE"}, "outputs": [], "source": ["if False:\n", "    # I highly do NOT suggest - use Unsloth if possible\n", "    from peft import AutoPeftModelForCausalLM\n", "    from transformers import AutoTokenizer\n", "    model = AutoPeftModelForCausalLM.from_pretrained(\n", "        \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        load_in_4bit = load_in_4bit,\n", "    )\n", "    tokenizer = AutoTokenizer.from_pretrained(\"lora_model\")"]}, {"cell_type": "markdown", "metadata": {"id": "f422JgM9sdVT"}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16 or `merged_4bit` for int4. We also allow `lora` adapters as a fallback. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iHjt_SMYsd3P"}, "outputs": [], "source": ["# Merge to 16bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_16bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_16bit\", token = \"\")\n", "\n", "# Merge to 4bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_4bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_4bit\", token = \"\")\n", "\n", "# Just LoRA adapters\n", "if False:\n", "    model.save_pretrained(\"model\")\n", "    tokenizer.save_pretrained(\"model\")\n", "if False:\n", "    model.push_to_hub(\"hf/model\", token = \"\")\n", "    tokenizer.push_to_hub(\"hf/model\", token = \"\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "TCv4vXHd61i7"}, "source": ["### GGUF / llama.cpp Conversion\n", "To save to `GGUF` / `llama.cpp`, we support it natively now! We clone `llama.cpp` and we default save it to `q8_0`. We allow all methods like `q4_k_m`. Use `save_pretrained_gguf` for local saving and `push_to_hub_gguf` for uploading to HF.\n", "\n", "Some supported quant methods (full list on our [Wiki page](https://github.com/unslothai/unsloth/wiki#gguf-quantization-options)):\n", "* `q8_0` - Fast conversion. High resource use, but generally acceptable.\n", "* `q4_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q4_K.\n", "* `q5_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q5_K.\n", "\n", "[**NEW**] To finetune and auto export to Ollama, try our [Ollama notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "FqfebeAdT073"}, "outputs": [], "source": ["# Save to 8bit Q8_0\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer,)\n", "# Remember to go to https://huggingface.co/settings/tokens for a token!\n", "# And change hf to your username!\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, token = \"\")\n", "\n", "# Save to 16bit GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"f16\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"f16\", token = \"\")\n", "\n", "# Save to q4_k_m GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"q4_k_m\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"q4_k_m\", token = \"\")\n", "\n", "# Save to multiple GGUF options - much faster if you want multiple!\n", "if False:\n", "    model.push_to_hub_gguf(\n", "        \"hf/model\", # Change hf to your username!\n", "        tokenizer,\n", "        quantization_method = [\"q4_k_m\", \"q8_0\", \"q5_k_m\",],\n", "        token = \"\", # Get a token at https://huggingface.co/settings/tokens\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, use the `model-unsloth.gguf` file or `model-unsloth-Q4_K_M.gguf` file in llama.cpp or a UI based system like Jan or Open WebUI. You can install Jan [here](https://github.com/janhq/jan) and Open WebUI [here](https://github.com/open-webui/open-webui)\n", "\n", "And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"007a35a241b346ec9a5cdd6f3e4ddd27": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4083b2ef8e6348e18b69d116508b46ff", "placeholder": "​", "style": "IPY_MODEL_9555be409a2c4a97b18d4978ed13d35f", "value": "README.md: 100%"}}, "098bd8ace574423da763eb0eae1d3bb6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0b8cf23562ae4e428ab4f00979e7ce07": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0bfbfe620ff446a0a47f7d5de7c88975": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0c30ded692064dc7bf36a93897f2b68f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0d384a63bb5f4b7384ac5f20d1a399b0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0faf73ed502649c4855adca930e3c8ff": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "18c4cb423f8744afa2b85ee361dcc121": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_76fb012b818f4bfb88c103262fb9c20e", "IPY_MODEL_8158ce20191d40fba140ed2c2f4ed859", "IPY_MODEL_595694711ab148e8bbf9824896c10468"], "layout": "IPY_MODEL_4b355eddebb548a7b60d28c12403ebfd"}}, "1c0c2835705f41089de4caea98127c04": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1e4ea03959b3496f8e75cc3588cf347c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "20e75852859845f697760003945b952f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4a91a45aba634535a53a49a873b25dfe", "placeholder": "​", "style": "IPY_MODEL_d773266178564b6cb739d756ff8de459", "value": "model.safetensors: 100%"}}, "23907906314743938db4e484c15480cc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "252c3d25fe3341379e5fc31d30dee7bc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "254473850de1471e9584a9c03962bd6f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_56e59a69f84e487f91e263313f5d2b72", "IPY_MODEL_524b13439d53402eb35388606058c288", "IPY_MODEL_fc7d250163bb470fa19503356dfe9e81"], "layout": "IPY_MODEL_9dc2697fe6984649b0ac36018f6904c9"}}, "2a6ca29a76ff430d86213f910858db5b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_95249b8fb6a84054a01f22c5f73f207b", "placeholder": "​", "style": "IPY_MODEL_2ed2b017b9a24f36a4222c5c27753991", "value": " 100000/100000 [00:01&lt;00:00, 63603.83 examples/s]"}}, "2aea4e74b6c4449ea24812d11675daee": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2b359412d4914aa38a6e21284c12ecbc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ddee625828cb4c22927aa73a02cd2dd9", "placeholder": "​", "style": "IPY_MODEL_fd46f381983f49179de05497c171c805", "value": " 117M/117M [00:00&lt;00:00, 210MB/s]"}}, "2bb75539976c49ed805c4ff6c58fb1d2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2ed2b017b9a24f36a4222c5c27753991": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "35aee367658940b9b2f6884ea87b3e4b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3601b111b63f401cb5c75ba08ae74b61": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d8c3cd1d08b747b690a59b7793aaf742", "placeholder": "​", "style": "IPY_MODEL_95a6cb0e04984f3c96bbe9ee9c5fa287", "value": " 9.09M/9.09M [00:00&lt;00:00, 34.6MB/s]"}}, "378176d2f0c9466d8762a584edf4217d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "39684b70f2ff48cab454617c721f7777": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e8445e90b1054aacbecf198c7979a0b6", "IPY_MODEL_d1cc50fb6d5849888af5d765dc51ab62", "IPY_MODEL_2b359412d4914aa38a6e21284c12ecbc"], "layout": "IPY_MODEL_a4ceb6dbc8de4fa798ee39d28e5ebc40"}}, "39bf1c29894f43acb6d2919e64a4fd28": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_007a35a241b346ec9a5cdd6f3e4ddd27", "IPY_MODEL_969a119573f942b29951ae2933e61cde", "IPY_MODEL_b8c4d378ea0e4bcd9f572a191a7c136f"], "layout": "IPY_MODEL_7d37dd0e06724b53b4f31cc0a4321b0d"}}, "3cd95b7c5e2f4c6883333045db11c6d6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3ea24021851a4341b3cb30d4ff0d495f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3ebff50e3c0b4cb4b99c93adde35b712": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3ffe42931dcf4a69972f4d50ee4dd3dd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ee9dcec2d5c44fd883f16c06b9f76264", "IPY_MODEL_982b6b94642d49fa85fab6ad621392fe", "IPY_MODEL_42990f347a8c42f7b510e2d17c7d3c6e"], "layout": "IPY_MODEL_3cd95b7c5e2f4c6883333045db11c6d6"}}, "4083b2ef8e6348e18b69d116508b46ff": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "42990f347a8c42f7b510e2d17c7d3c6e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_74dc78a38e30465a96d2c8a22a27b127", "placeholder": "​", "style": "IPY_MODEL_c6b4759ce826421081508270cb30334b", "value": " 100000/100000 [03:00&lt;00:00, 544.59 examples/s]"}}, "45bc9d882a8f4a7e813245b1590d4427": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "4606a430f0214a0bb524a06ab78af1a9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_700b2fc6799c41f4984e07c36502f2c9", "IPY_MODEL_be744decce12471388b689a085576c7f", "IPY_MODEL_e8efa5951c4b4f3e9922e029c57a5ab3"], "layout": "IPY_MODEL_afa86659651d48a7b4a3043a0909322f"}}, "4a91a45aba634535a53a49a873b25dfe": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4b355eddebb548a7b60d28c12403ebfd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4b41aa65c6894e918b04709f8e9270d2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4f0aaa3ce8ca43fdaea4cd4f7b595218": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6d163ef8e07b4a1cba351ec44175efd5", "placeholder": "​", "style": "IPY_MODEL_9a4ea589183346dab240125cd38ba0f9", "value": "tokenizer.json: 100%"}}, "524b13439d53402eb35388606058c288": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f7f53b3a53eb4b8e9fa47cd233f4df7f", "max": 454, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_73efb213f4a84996a353495b912393d9", "value": 454}}, "5628ed38f304438faf5442b29a9511d6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "56e59a69f84e487f91e263313f5d2b72": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3ebff50e3c0b4cb4b99c93adde35b712", "placeholder": "​", "style": "IPY_MODEL_35aee367658940b9b2f6884ea87b3e4b", "value": "special_tokens_map.json: 100%"}}, "58ce4633471c438db6e103a1ca3806a0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "595694711ab148e8bbf9824896c10468": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0b8cf23562ae4e428ab4f00979e7ce07", "placeholder": "​", "style": "IPY_MODEL_b774631435d4450da64f0a2b40cbf3aa", "value": " 54.6k/54.6k [00:00&lt;00:00, 3.20MB/s]"}}, "5b34a4e8fc7747e78b49ad5bf67a6580": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5c9ee920068a47d89dbf5cbdd9e848a3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "5e9825466cd2481b92cfe89f33b11fe3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c685f29a5d2c461ca3dda867bab6df50", "placeholder": "​", "style": "IPY_MODEL_e2f16d56b21c4ff2918872d70e5ca847", "value": "Generating train split: 100%"}}, "6064feeea79040409e18a1e2a289b09a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_bb241a26ca4d4d7186ba46cda1f8a802", "IPY_MODEL_c9abb42da1734388a7d2f1a06832ecc6", "IPY_MODEL_7c3a37494e5848b9994b37a4c8bac132"], "layout": "IPY_MODEL_c668ae4c7d174f2dad3fb837ff873e57"}}, "672262ede93c413cbc219ca4fec5b98a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "683dd21b9eef4bb4a0c9bc15c984703a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6d163ef8e07b4a1cba351ec44175efd5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6d52daf29c90402a9762acdde765713f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6e0fe945001140b3959e617a2f55c353": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "700b2fc6799c41f4984e07c36502f2c9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9edc5f3740e54d7ab6fd38f270f4a40e", "placeholder": "​", "style": "IPY_MODEL_2aea4e74b6c4449ea24812d11675daee", "value": "generation_config.json: 100%"}}, "7028f81557e54d0d8df4cc1a0f85b12c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "70b0793d26c14afbbe8af319d38dbb07": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_20e75852859845f697760003945b952f", "IPY_MODEL_f0c7c3402e6549148ac293dcf01ae8da", "IPY_MODEL_b1c8acb40b924d8f854408252c1e061c"], "layout": "IPY_MODEL_aa28da40d8074e89ad32b5cb5ad04fa6"}}, "73efb213f4a84996a353495b912393d9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "74dc78a38e30465a96d2c8a22a27b127": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "757a4328afef4293b6d31ce9b90eec79": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "76e2e47c93e541ff820bcbab9264381d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "76fb012b818f4bfb88c103262fb9c20e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0faf73ed502649c4855adca930e3c8ff", "placeholder": "​", "style": "IPY_MODEL_b0670b92e79744c5ba11501bafdc101b", "value": "tokenizer_config.json: 100%"}}, "785d9147f4a341afafc5c5743892df16": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5e9825466cd2481b92cfe89f33b11fe3", "IPY_MODEL_bfbb37b6f4b247b5bf5aaf7e1d80bcf9", "IPY_MODEL_2a6ca29a76ff430d86213f910858db5b"], "layout": "IPY_MODEL_92d981a21b204f6c8b52e3caa16d1784"}}, "7c3a37494e5848b9994b37a4c8bac132": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d5cfa138483f4007b2a95be833043235", "placeholder": "​", "style": "IPY_MODEL_6d52daf29c90402a9762acdde765713f", "value": " 100000/100000 [01:07&lt;00:00, 2101.01 examples/s]"}}, "7d37dd0e06724b53b4f31cc0a4321b0d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8158ce20191d40fba140ed2c2f4ed859": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3ea24021851a4341b3cb30d4ff0d495f", "max": 54598, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_7028f81557e54d0d8df4cc1a0f85b12c", "value": 54598}}, "8578be7c3f4044d3bfe0eae94fe2c21a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4f0aaa3ce8ca43fdaea4cd4f7b595218", "IPY_MODEL_8611f4c6d12641dd960377346daba5d1", "IPY_MODEL_3601b111b63f401cb5c75ba08ae74b61"], "layout": "IPY_MODEL_a5bde1dcf7f745faa140cb6728c415fc"}}, "8611f4c6d12641dd960377346daba5d1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d8f739452aa5410aa5eba87b2760c2af", "max": 9085657, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_dc098c4a448243ddb82ab769dc261171", "value": 9085657}}, "870ff8f17c7b47ec8d49cac84216b04c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "8b09015cc22240cc8e736f7050c0f37c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8c5ad85b4da14b239340ac95244d8ed4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "904e7bac43bd4333b321cacfed5dcb60": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "92d981a21b204f6c8b52e3caa16d1784": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "95249b8fb6a84054a01f22c5f73f207b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9555be409a2c4a97b18d4978ed13d35f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "95a6cb0e04984f3c96bbe9ee9c5fa287": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "969a119573f942b29951ae2933e61cde": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5628ed38f304438faf5442b29a9511d6", "max": 982, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6e0fe945001140b3959e617a2f55c353", "value": 982}}, "982b6b94642d49fa85fab6ad621392fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_378176d2f0c9466d8762a584edf4217d", "max": 100000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e221482cbe95465191212d85d539938c", "value": 100000}}, "9a4ea589183346dab240125cd38ba0f9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9a8f1b8079fe478ebf0b16096cb224f5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9dc2697fe6984649b0ac36018f6904c9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9edc5f3740e54d7ab6fd38f270f4a40e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a326b2e89f1c46f28cd166afc7490e2b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_58ce4633471c438db6e103a1ca3806a0", "placeholder": "​", "style": "IPY_MODEL_cf1b769b7a744b5f8bccf6798566582f", "value": "Standardizing format: 100%"}}, "a4ceb6dbc8de4fa798ee39d28e5ebc40": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a5bde1dcf7f745faa140cb6728c415fc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aa28da40d8074e89ad32b5cb5ad04fa6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ae2464c1cbc442a383de7577d2986116": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4b41aa65c6894e918b04709f8e9270d2", "placeholder": "​", "style": "IPY_MODEL_cdae06929214464ea25e343f17b4a843", "value": " 100000/100000 [00:20&lt;00:00, 7158.71 examples/s]"}}, "afa86659651d48a7b4a3043a0909322f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b0670b92e79744c5ba11501bafdc101b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b1b0a4e3f00043b0a0eb7a053815a4a5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b1c8acb40b924d8f854408252c1e061c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f0377d014b2e4e89b9932750f04a6a3c", "placeholder": "​", "style": "IPY_MODEL_0d384a63bb5f4b7384ac5f20d1a399b0", "value": " 2.24G/2.24G [00:18&lt;00:00, 400MB/s]"}}, "b774631435d4450da64f0a2b40cbf3aa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b8c4d378ea0e4bcd9f572a191a7c136f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0c30ded692064dc7bf36a93897f2b68f", "placeholder": "​", "style": "IPY_MODEL_8c5ad85b4da14b239340ac95244d8ed4", "value": " 982/982 [00:00&lt;00:00, 21.3kB/s]"}}, "bb241a26ca4d4d7186ba46cda1f8a802": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_dd30f3ead6394317be5a72aa890adfb9", "placeholder": "​", "style": "IPY_MODEL_1e4ea03959b3496f8e75cc3588cf347c", "value": "Map: 100%"}}, "bcf8e36d938a4d959c31ea4ff3c8d4cf": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cc0bd79ca9e847fba88aafe2d612ffe4", "max": 100000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_76e2e47c93e541ff820bcbab9264381d", "value": 100000}}, "bd71b6cb29e147ab9b10d1b85908c413": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c03b9410af384397849ef63b62f2c689", "placeholder": "​", "style": "IPY_MODEL_098bd8ace574423da763eb0eae1d3bb6", "value": " 100000/100000 [00:08&lt;00:00, 16117.89 examples/s]"}}, "be744decce12471388b689a085576c7f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_252c3d25fe3341379e5fc31d30dee7bc", "max": 184, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_672262ede93c413cbc219ca4fec5b98a", "value": 184}}, "bfbb37b6f4b247b5bf5aaf7e1d80bcf9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0bfbfe620ff446a0a47f7d5de7c88975", "max": 100000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5c9ee920068a47d89dbf5cbdd9e848a3", "value": 100000}}, "c03b9410af384397849ef63b62f2c689": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c668ae4c7d174f2dad3fb837ff873e57": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c685f29a5d2c461ca3dda867bab6df50": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c6b4759ce826421081508270cb30334b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c7907c51d16a4aeb87974c36b3b9e7c8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c9abb42da1734388a7d2f1a06832ecc6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d356b597dda14c7ab023403ee6959cf8", "max": 100000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_870ff8f17c7b47ec8d49cac84216b04c", "value": 100000}}, "cc0bd79ca9e847fba88aafe2d612ffe4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cdae06929214464ea25e343f17b4a843": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cf1b769b7a744b5f8bccf6798566582f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cff4f20460a442839c26bfa468b35dee": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d08e764aa8b94e7f9e1c727b53980abe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e62f6eb58a744d38b837e47d8a16db67", "IPY_MODEL_bcf8e36d938a4d959c31ea4ff3c8d4cf", "IPY_MODEL_ae2464c1cbc442a383de7577d2986116"], "layout": "IPY_MODEL_9a8f1b8079fe478ebf0b16096cb224f5"}}, "d1cc50fb6d5849888af5d765dc51ab62": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2bb75539976c49ed805c4ff6c58fb1d2", "max": 116531415, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_45bc9d882a8f4a7e813245b1590d4427", "value": 116531404}}, "d356b597dda14c7ab023403ee6959cf8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d5cfa138483f4007b2a95be833043235": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d6ab4d4143ff49bcae30be1bc2d76762": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d773266178564b6cb739d756ff8de459": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d7e0024b98a94a9fa12dc4154ff2b2fc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d8c3cd1d08b747b690a59b7793aaf742": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d8f739452aa5410aa5eba87b2760c2af": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dc098c4a448243ddb82ab769dc261171": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "dd30f3ead6394317be5a72aa890adfb9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dd9e90f2c16541e8a72c6771c4685b9a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a326b2e89f1c46f28cd166afc7490e2b", "IPY_MODEL_eb855a0fcb554a8eb245351b3593623d", "IPY_MODEL_bd71b6cb29e147ab9b10d1b85908c413"], "layout": "IPY_MODEL_b1b0a4e3f00043b0a0eb7a053815a4a5"}}, "ddee625828cb4c22927aa73a02cd2dd9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e221482cbe95465191212d85d539938c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e2d886444f0047fa9e2245b9773ced9e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e2f16d56b21c4ff2918872d70e5ca847": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "e4bf3f8e63bb4c01bbe821d438445d91": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e62f6eb58a744d38b837e47d8a16db67": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e4bf3f8e63bb4c01bbe821d438445d91", "placeholder": "​", "style": "IPY_MODEL_d7e0024b98a94a9fa12dc4154ff2b2fc", "value": "Map: 100%"}}, "e8445e90b1054aacbecf198c7979a0b6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d6ab4d4143ff49bcae30be1bc2d76762", "placeholder": "​", "style": "IPY_MODEL_904e7bac43bd4333b321cacfed5dcb60", "value": "train-00000-of-00001.parquet: 100%"}}, "e8efa5951c4b4f3e9922e029c57a5ab3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ea0f4a924ce14b97a53ba2b045ba1b1d", "placeholder": "​", "style": "IPY_MODEL_757a4328afef4293b6d31ce9b90eec79", "value": " 184/184 [00:00&lt;00:00, 14.6kB/s]"}}, "ea0f4a924ce14b97a53ba2b045ba1b1d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "eb855a0fcb554a8eb245351b3593623d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1c0c2835705f41089de4caea98127c04", "max": 100000, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e2d886444f0047fa9e2245b9773ced9e", "value": 100000}}, "ee9dcec2d5c44fd883f16c06b9f76264": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5b34a4e8fc7747e78b49ad5bf67a6580", "placeholder": "​", "style": "IPY_MODEL_23907906314743938db4e484c15480cc", "value": "Map (num_proc=2): 100%"}}, "f0377d014b2e4e89b9932750f04a6a3c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f0c7c3402e6549148ac293dcf01ae8da": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "danger", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cff4f20460a442839c26bfa468b35dee", "max": 2242762780, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_683dd21b9eef4bb4a0c9bc15c984703a", "value": 2242762567}}, "f7f53b3a53eb4b8e9fa47cd233f4df7f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fc7d250163bb470fa19503356dfe9e81": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8b09015cc22240cc8e736f7050c0f37c", "placeholder": "​", "style": "IPY_MODEL_c7907c51d16a4aeb87974c36b3b9e7c8", "value": " 454/454 [00:00&lt;00:00, 21.8kB/s]"}}, "fd46f381983f49179de05497c171c805": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}