## 📊 AI Finance Agent with xAI Grok
This application creates a financial analysis agent powered by xAI's Grok model, combining real-time stock data with web search capabilities. It provides structured financial insights through an interactive playground interface.

### Features

- Powered by xAI's Grok-beta model
- Real-time stock data analysis via YFinance
- Web search capabilities through DuckDuckGo
- Formatted output with tables for financial data
- Interactive playground interface

### How to get Started?

1. Clone the GitHub repository
```bash
git clone https://github.com/Shubhamsaboo/awesome-llm-apps.git
cd awesome-llm-apps/ai_agent_tutorials/xai_finance_agent
```

2. Install the required dependencies:

```bash
cd awesome-llm-apps/ai_agent_tutorials/xai_finance_agent
pip install -r requirements.txt
```

3. Get your OpenAI API Key

- Sign up for an [xAI API account](https://console.x.ai/)
- Set your XAI_API_KEY environment variable.
```bash
export XAI_API_KEY='your-api-key-here'
```

4. Run the team of AI Agents
```bash
python xai_finance_agent.py
```

5. Open your web browser and navigate to the URL provided in the console output to interact with the AI financial agent through the playground interface.
