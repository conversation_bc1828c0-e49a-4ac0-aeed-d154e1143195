"""
Script para executar o aplicativo de artigos acadêmicos ABNT.
"""

import os
import sys
import subprocess

def main():
    """Função principal para executar o aplicativo"""
    # Verificar se o diretório .streamlit existe
    streamlit_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), ".streamlit")
    secrets_file = os.path.join(streamlit_dir, "secrets.toml")
    
    if not os.path.exists(streamlit_dir):
        os.makedirs(streamlit_dir)
    
    # Verificar se o arquivo secrets.toml existe
    if not os.path.exists(secrets_file):
        # Solicitar a chave da API Groq
        print("Chave da API Groq não encontrada.")
        api_key = input("Digite sua chave da API Groq: ")
        
        # Criar o arquivo secrets.toml
        with open(secrets_file, "w") as f:
            f.write(f'GROQ_API_KEY = "{api_key}"\n')
        
        print(f"Arquivo de segredos criado em {secrets_file}")
    
    # Executar o aplicativo Streamlit
    app_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "app.py")
    subprocess.run([sys.executable, "-m", "streamlit", "run", app_path])

if __name__ == "__main__":
    main()
