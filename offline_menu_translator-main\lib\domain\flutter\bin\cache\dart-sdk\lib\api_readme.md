# Welcome!

Welcome to the Dart API reference documentation, covering the
[Dart core libraries](https://dart.dev/guides/libraries). These include:

  * [dart:core](dart-core/dart-core-library.html): Core functionality such as
    strings, numbers, collections, errors, dates, and URIs.
  * [dart:io](dart-io/dart-io-library.html): I/O for non-web apps.
  * [dart:async](dart-async/dart-async-library.html): Functionality for
    asynchronous programming with futures, streams, and zones.

You'll also find reference documentation covering Dart's
various platform interoperability options, such as:

  * [dart:js_interop](dart-js_interop/dart-js_interop-library.html):
    Library including a sound type hierarchy and helper functions
    for interoperability with JavaScript.
  * [package:web](https://pub.dev/documentation/web):
    DOM manipulation for web apps.
  * [dart:ffi](dart-ffi/dart-ffi-library.html): Foreign function interfaces for
    interoperability with the C language.

The core libraries - except for `dart:core` - must be imported before they're
available for use:

```dart
import 'dart:math';
```

Additionally, you can find Dart packages at [pub.dev](https://pub.dev).

## Language docs

The main site for learning and using Dart is [dart.dev](https://dart.dev).
Check out these pages:

  * [Dart overview](https://dart.dev/overview)
  * [Dart language documentation](https://dart.dev/language)
  * [Library tour](https://dart.dev/libraries)
  * [Tutorials](https://dart.dev/tutorials)

This API reference is generated from the SDK source at
[dart-lang/sdk](https://github.com/dart-lang/sdk). If you'd like to give
feedback on or edit this documentation, see
[Contributing](https://github.com/dart-lang/sdk/blob/main/CONTRIBUTING.md).
