# utils.py
import pandas as pd
import numpy as np

def apply_conditional_imputation(df, conditional_map):
    """Aplica imputação condicional com base nas variáveis dependentes."""
    for parent_col, child_cols in conditional_map.items():
        for child_col in child_cols:
            if child_col in df.columns:
                try:
                    # Garantir que a coluna pai seja numérica
                    df[parent_col] = pd.to_numeric(df[parent_col], errors='coerce').fillna(0)

                    # Verificar o tipo de dados da coluna filha
                    if df[child_col].dtype == 'float64':
                        # Para colunas float, usar 0.0 em vez de False
                        df.loc[df[parent_col] == 0, child_col] = 0.0
                    else:
                        # Para outros tipos, usar False
                        df.loc[df[parent_col] == 0, child_col] = False
                except Exception as e:
                    print(f"Erro ao processar {parent_col} -> {child_col}: {e}")
    return df

def handle_missing_values(df):
    """Trata valores ausentes em colunas numéricas e categóricas."""
    numeric_cols = df.select_dtypes(include=['float64']).columns
    df[numeric_cols] = df[numeric_cols].replace('missing', np.nan)

    for col in numeric_cols:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    df[numeric_cols] = df[numeric_cols].fillna(df[numeric_cols].median())

    category_cols = df.select_dtypes(include=['category', 'object']).columns
    df[category_cols] = df[category_cols].fillna('missing')
    return df

def one_hot_encode(df, cols):
    """Realiza one-hot encoding nas colunas especificadas."""
    return pd.get_dummies(df, columns=cols, prefix=cols, dummy_na=False)

def label_encode(df, col_map):
    """Realiza label encoding com base nos mapeamentos fornecidos."""
    for col, mapping in col_map.items():
        if col in df.columns:
            df[col] = df[col].map(mapping)
    return df

def validate_data(df, data_types):
    """Valida os tipos de dados após transformações."""
    for col, dtype in data_types.items():
        if col in df.columns:
            try:
                if dtype == 'category':
                    df[col] = df[col].astype('category')
                elif dtype == 'datetime64[ns]':
                    df[col] = pd.to_datetime(df[col], errors='coerce')
                elif dtype == 'float64':
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                else:
                    df[col] = df[col].astype(dtype)
            except Exception as e:
                print(f"Erro ao converter {col} para {dtype}: {e}")
    return df