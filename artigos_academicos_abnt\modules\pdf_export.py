"""
Módulo de exportação para PDF para o aplicativo de artigos acadêmicos ABNT.
Implementa a conversão de artigos em Markdown para PDF usando FPDF2.
"""

import re
import base64
from datetime import datetime
from fpdf import FPDF

class MarkdownPDF(FPDF):
    """Classe personalizada para converter Markdown para PDF usando FPDF2"""

    def __init__(self, orientation='P', unit='mm', format='A4'):
        super().__init__(orientation, unit, format)
        # Usar fontes padrão em vez de DejaVu
        self.set_font('helvetica', '', 12)

        # Configurar margens ABNT (3cm esquerda e superior, 2cm direita e inferior)
        self.set_margins(30, 30, 20)
        self.set_auto_page_break(True, margin=20)

        # Variáveis para cabeçalho e rodapé
        self.title = ""
        self.is_first_page = True

    def header(self):
        """Cabeçalho personalizado para o PDF"""
        if not self.is_first_page and self.title:
            self.set_font('helvetica', '', 9)
            self.set_text_color(128, 128, 128)
            self.cell(0, 10, self.title, 0, 0, 'C')
        self.ln(10)

    def footer(self):
        """Rodapé personalizado para o PDF"""
        if not self.is_first_page:
            self.set_y(-15)
            self.set_font('helvetica', '', 9)
            self.set_text_color(128, 128, 128)
            self.cell(0, 10, str(self.page_no()), 0, 0, 'C')

    def add_cover_page(self, title, author="", date=None):
        """Adiciona uma capa ao PDF seguindo o padrão ABNT"""
        self.add_page()
        self.is_first_page = True

        if not date:
            date = datetime.now().strftime("%d/%m/%Y")

        # Instituição (topo da página)
        self.set_font('helvetica', 'B', 14)
        self.set_text_color(0, 0, 0)
        self.cell(0, 20, "UNIVERSIDADE", 0, 1, 'C')

        # Título (centro da página)
        self.ln(60)
        self.set_font('helvetica', 'B', 16)

        # Dividir o título em linhas se for muito longo
        title_lines = self._wrap_text(title, 160, 'helvetica', 'B', 16)
        for line in title_lines:
            self.cell(0, 10, line, 0, 1, 'C')

        # Autor (parte inferior)
        if author:
            self.ln(60)
            self.set_font('helvetica', '', 12)
            self.cell(0, 10, author, 0, 1, 'C')

        # Data (rodapé)
        self.ln(40)
        self.set_font('helvetica', '', 12)
        self.cell(0, 10, date, 0, 1, 'C')

    def _wrap_text(self, text, width, font_family, font_style, font_size):
        """Divide o texto em linhas para caber na largura especificada"""
        self.set_font(font_family, font_style, font_size)
        words = text.split(' ')
        lines = []
        current_line = ""

        for word in words:
            test_line = current_line + " " + word if current_line else word
            if self.get_string_width(test_line) < width:
                current_line = test_line
            else:
                lines.append(current_line)
                current_line = word

        if current_line:
            lines.append(current_line)

        return lines

    def add_toc(self, toc_items):
        """Adiciona um sumário ao PDF"""
        self.add_page()
        self.is_first_page = False

        self.set_font('helvetica', 'B', 14)
        self.cell(0, 10, "SUMÁRIO", 0, 1, 'C')
        self.ln(10)

        for item in toc_items:
            level = item['level']
            text = item['text']
            page = item['page']

            # Indentação baseada no nível
            indent = (level - 1) * 10

            self.set_font('helvetica', '', 12)
            self.set_x(30 + indent)

            # Calcular a largura do texto
            text_width = self.get_string_width(text)

            # Adicionar pontos de liderança
            dots_width = self.w - 50 - indent - text_width - self.get_string_width(str(page))
            dots_count = int(dots_width / self.get_string_width('.'))
            dots = '.' * dots_count

            self.cell(0, 8, f"{text} {dots} {page}", 0, 1)

    def add_markdown_content(self, markdown_text):
        """Adiciona conteúdo Markdown ao PDF"""
        self.add_page()
        self.is_first_page = False

        # Processar o texto Markdown diretamente
        lines = markdown_text.split('\n')

        for line in lines:
            # Pular linhas vazias, mas adicionar espaço
            if not line.strip():
                self.ln(5)
                continue

            # Processar cabeçalhos
            if line.startswith('#'):
                # Contar o número de # para determinar o nível do cabeçalho
                level = 0
                for char in line:
                    if char == '#':
                        level += 1
                    else:
                        break

                # Extrair o texto do cabeçalho
                header_text = line[level:].strip()

                # Definir o estilo com base no nível
                if level == 1:
                    self.set_font('helvetica', 'B', 16)
                    self.ln(5)
                    self.cell(0, 10, header_text, 0, 1, 'C')
                    self.ln(5)
                elif level == 2:
                    self.set_font('helvetica', 'B', 14)
                    self.ln(5)
                    self.cell(0, 10, header_text, 0, 1, 'L')
                    self.ln(5)
                elif level == 3:
                    self.set_font('helvetica', 'B', 12)
                    self.cell(0, 10, header_text, 0, 1, 'L')
                else:
                    self.set_font('helvetica', 'B', 12)
                    self.cell(0, 10, header_text, 0, 1, 'L')

                # Restaurar a fonte padrão
                self.set_font('helvetica', '', 12)

            # Processar parágrafos normais
            else:
                # Verificar se é uma citação (começa com >)
                if line.startswith('>'):
                    citation_text = line[1:].strip()
                    self.set_font('helvetica', 'I', 10)
                    self.set_x(40)  # Recuo de 4cm
                    self.multi_cell(0, 5, citation_text)
                    self.set_font('helvetica', '', 12)
                    self.ln(5)
                # Texto normal
                else:
                    self.set_font('helvetica', '', 12)
                    self.multi_cell(0, 5, line)
                    self.ln(2)

class PDFExporter:
    """Classe para exportar artigos para PDF com formatação ABNT"""

    @staticmethod
    def _extract_metadata(markdown_text):
        """Extrai metadados do texto markdown (título, autor, data)"""
        metadata = {
            "title": "Artigo Acadêmico",
            "date": datetime.now().strftime("%d/%m/%Y"),
            "author": ""
        }

        # Tentar extrair o título (primeiro h1)
        title_match = re.search(r'^#\s+(.+)$', markdown_text, re.MULTILINE)
        if title_match:
            metadata["title"] = title_match.group(1).strip()

        # Tentar extrair o autor (se houver um parágrafo com "Autor:" ou similar)
        author_match = re.search(r'(?:Autor|Author):\s*(.+?)(?:\n|$)', markdown_text, re.IGNORECASE)
        if author_match:
            metadata["author"] = author_match.group(1).strip()

        return metadata

    @staticmethod
    def markdown_to_pdf(markdown_text, title=None, include_cover=True):
        """Converte texto markdown para PDF usando FPDF2"""
        # Extrair metadados
        metadata = PDFExporter._extract_metadata(markdown_text)
        if title:
            metadata["title"] = title

        # Criar PDF
        pdf = MarkdownPDF()
        pdf.title = metadata["title"]

        # Adicionar capa se solicitado
        if include_cover:
            pdf.add_cover_page(
                title=metadata["title"],
                author=metadata["author"],
                date=metadata["date"]
            )

        # Adicionar conteúdo
        pdf.add_markdown_content(markdown_text)

        # Obter os bytes do PDF
        # FPDF2 já retorna um bytearray, não precisamos codificar
        pdf_bytes = pdf.output(dest='S')

        return pdf_bytes

    @staticmethod
    def get_download_link(pdf_bytes, filename="artigo_academico.pdf"):
        """Gera um link de download para o PDF"""
        b64 = base64.b64encode(pdf_bytes).decode()
        href = f'''
        <a href="data:application/pdf;base64,{b64}"
           download="{filename}"
           class="download-button"
           style="display: inline-block;
                  padding: 0.5em 1em;
                  background-color: #4CAF50;
                  color: white;
                  text-decoration: none;
                  border-radius: 4px;
                  margin: 1em 0;">
            Baixar PDF
        </a>
        '''
        return href
