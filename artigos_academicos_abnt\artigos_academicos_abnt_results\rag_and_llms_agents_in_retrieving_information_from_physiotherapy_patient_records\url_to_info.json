{"http://arxiv.org/abs/2502.15698v1": {"url": "http://arxiv.org/abs/2502.15698v1", "title": "Developing an Artificial Intelligence Tool for Personalized Breast Cancer Treatment Plans based on the NCCN Guidelines", "description": "Cancer treatments require personalized approaches based on a patient's clinical condition, medical history, and evidence-based guidelines. The National Comprehensive Cancer Network (NCCN) provides frequently updated, complex guidelines through visuals like flowcharts and diagrams, which can be time consuming for oncologists to stay current with treatment protocols. This study presents an AI (Artificial Intelligence)-driven methodology to accurately automate treatment regimens following NCCN guidelines for breast cancer patients.   We proposed two AI-driven methods: Agentic-RAG (Retrieval-Augmented Generation) and Graph-RAG. Agentic-RAG used a three-step Large Language Model (LLM) process to select clinical titles from NCCN guidelines, retrieve matching JSON content, and iteratively refine recommendations based on insufficiency checks. Graph-RAG followed a Microsoft-developed framework with proprietary prompts, where JSON data was converted to text via an LLM, summarized, and mapped ...", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "published": "2025-01-06", "pdf_url": "http://arxiv.org/pdf/2502.15698v1", "categories": ["cs.IR"]}, "http://arxiv.org/abs/2410.13509v2": {"url": "http://arxiv.org/abs/2410.13509v2", "title": "RAG-DDR: Optimizing Retrieval-Augmented Generation Using Differentiable Data Rewards", "description": "Retrieval-Augmented Generation (RAG) has proven its effectiveness in mitigating hallucinations in Large Language Models (LLMs) by retrieving knowledge from external resources. To adapt LLMs for the RAG systems, current approaches use instruction tuning to optimize LLMs, improving their ability to utilize retrieved knowledge. This supervised fine-tuning (SFT) approach focuses on equipping LLMs to handle diverse RAG tasks using different instructions. However, it trains RAG modules to overfit training signals and overlooks the varying data preferences among agents within the RAG system. In this paper, we propose a Differentiable Data Rewards (DDR) method, which end-to-end trains RAG systems by aligning data preferences between different RAG modules. DDR works by collecting the rewards to optimize each agent in the RAG system with the rollout method, which prompts agents to sample some potential responses as perturbations, evaluates the impact of these perturbations on the whole RAG sy...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Maosong Sun", "<PERSON><PERSON>"], "published": "2024-10-17", "pdf_url": "http://arxiv.org/pdf/2410.13509v2", "categories": ["cs.CL"]}, "http://arxiv.org/abs/2409.18924v2": {"url": "http://arxiv.org/abs/2409.18924v2", "title": "AIPatient: Simulating Patients with EHRs and LLM Powered Agentic Workflow", "description": "Simulated patient systems play a crucial role in modern medical education and research, providing safe, integrative learning environments and enabling clinical decision-making simulations. Large Language Models (LLM) could advance simulated patient systems by replicating medical conditions and patient-doctor interactions with high fidelity and low cost. However, ensuring the effectiveness and trustworthiness of these systems remains a challenge, as they require a large, diverse, and precise patient knowledgebase, along with a robust and stable knowledge diffusion to users. Here, we developed AIPatient, an advanced simulated patient system with AIPatient Knowledge Graph (AIPatient KG) as the input and the Reasoning Retrieval-Augmented Generation (Reasoning RAG) agentic workflow as the generation backbone. AIPatient KG samples data from Electronic Health Records (EHRs) in the Medical Information Mart for Intensive Care (MIMIC)-III database, producing a clinically diverse and relevant ...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "published": "2024-09-27", "pdf_url": "http://arxiv.org/pdf/2409.18924v2", "categories": ["cs.CL", "cs.AI"]}, "http://arxiv.org/abs/2407.18044v1": {"url": "http://arxiv.org/abs/2407.18044v1", "title": "The Geometry of Queries: Query-Based Innovations in Retrieval-Augmented Generation", "description": "Digital health chatbots powered by Large Language Models (LLMs) have the potential to significantly improve personal health management for chronic conditions by providing accessible and on-demand health coaching and question-answering. However, these chatbots risk providing unverified and inaccurate information because LLMs generate responses based on patterns learned from diverse internet data. Retrieval Augmented Generation (RAG) can help mitigate hallucinations and inaccuracies in LLM responses by grounding it on reliable content. However, efficiently and accurately retrieving most relevant set of content for real-time user questions remains a challenge. In this work, we introduce Query-Based Retrieval Augmented Generation (QB-RAG), a novel approach that pre-computes a database of potential queries from a content base using LLMs. For an incoming patient question, QB-RAG efficiently matches it against this pre-generated query database using vector search, improving alignment betwe...", "authors": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "published": "2024-07-25", "pdf_url": "http://arxiv.org/pdf/2407.18044v1", "categories": ["cs.LG"]}, "http://arxiv.org/abs/2501.00332v1": {"url": "http://arxiv.org/abs/2501.00332v1", "title": "MAIN-RAG: Multi-Agent Filtering Retrieval-Augmented Generation", "description": "Large Language Models (LLMs) are becoming essential tools for various natural language processing tasks but often suffer from generating outdated or incorrect information. Retrieval-Augmented Generation (RAG) addresses this issue by incorporating external, real-time information retrieval to ground LLM responses. However, the existing RAG systems frequently struggle with the quality of retrieval documents, as irrelevant or noisy documents degrade performance, increase computational overhead, and undermine response reliability. To tackle this problem, we propose Multi-Agent Filtering Retrieval-Augmented Generation (MAIN-RAG), a training-free RAG framework that leverages multiple LLM agents to collaboratively filter and score retrieved documents. Specifically, MAIN-RAG introduces an adaptive filtering mechanism that dynamically adjusts the relevance filtering threshold based on score distributions, effectively minimizing noise while maintaining high recall of relevant documents. The pr...", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mingzhi Hu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "published": "2024-12-31", "pdf_url": "http://arxiv.org/pdf/2501.00332v1", "categories": ["cs.CL", "cs.IR"]}, "http://arxiv.org/abs/2501.09136v3": {"url": "http://arxiv.org/abs/2501.09136v3", "title": "Agentic Retrieval-Augmented Generation: A Survey on Agentic RAG", "description": "Large Language Models (LLMs) have revolutionized artificial intelligence (AI) by enabling human like text generation and natural language understanding. However, their reliance on static training data limits their ability to respond to dynamic, real time queries, resulting in outdated or inaccurate outputs. Retrieval Augmented Generation (RAG) has emerged as a solution, enhancing LLMs by integrating real time data retrieval to provide contextually relevant and up-to-date responses. Despite its promise, traditional RAG systems are constrained by static workflows and lack the adaptability required for multistep reasoning and complex task management.   Agentic Retrieval-Augmented Generation (Agentic RAG) transcends these limitations by embedding autonomous AI agents into the RAG pipeline. These agents leverage agentic design patterns reflection, planning, tool use, and multiagent collaboration to dynamically manage retrieval strategies, iteratively refine contextual understanding, and ...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2025-01-15", "pdf_url": "http://arxiv.org/pdf/2501.09136v3", "categories": ["cs.AI", "cs.CL", "cs.IR"]}, "http://arxiv.org/abs/2402.13547v2": {"url": "http://arxiv.org/abs/2402.13547v2", "title": "ActiveRAG: Autonomously Knowledge Assimilation and Accommodation through Retrieval-Augmented Agents", "description": "Retrieval-Augmented Generation (RAG) enables Large Language Models (LLMs) to leverage external knowledge, enhancing their performance on knowledge-intensive tasks. However, existing RAG models often treat LLMs as passive recipients of information, which can lead to interference from noisy retrieved content. In this paper, we introduce ActiveRAG, a multi-agent framework that mimics human learning behavior to help LLMs actively engage with and learn from retrieved evidence. ActiveRAG designs a knowledge assimilation agent to form the knowledge understanding by associating external knowledge with the parametric memory of LLMs. Then our model employs the thought accommodation agent to calibrate the internal thought of LLMs for response refinement. Our experiments show that ActiveRAG achieves a 10\\% improvement over vanilla RAG on various question-answering benchmarks. Further analysis reveals that ActiveRAG mitigates the impact of noisy retrievals, alleviates conflicts between external ...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2024-02-21", "pdf_url": "http://arxiv.org/pdf/2402.13547v2", "categories": ["cs.CL"]}, "http://arxiv.org/abs/2503.17933v1": {"url": "http://arxiv.org/abs/2503.17933v1", "title": "Experience Retrieval-Augmentation with Electronic Health Records Enables Accurate Discharge QA", "description": "To improve the reliability of Large Language Models (LLMs) in clinical applications, retrieval-augmented generation (RAG) is extensively applied to provide factual medical knowledge. However, beyond general medical knowledge from open-ended datasets, clinical case-based knowledge is also critical for effective medical reasoning, as it provides context grounded in real-world patient experiences. Motivated by this, we propose Experience Retrieval Augmentation - ExpRAG framework based on Electronic Health Record (EHR), aiming to offer the relevant context from other patients' discharge reports. ExpRAG performs retrieval through a coarse-to-fine process, utilizing an EHR-based report ranker to efficiently identify similar patients, followed by an experience retriever to extract task-relevant content for enhanced medical reasoning. To evaluate ExpRAG, we introduce DischargeQA, a clinical QA dataset with 1,280 discharge-related questions across diagnosis, medication, and instruction tasks...", "authors": ["Justice <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Peiqing Lu", "<PERSON>"], "published": "2025-03-23", "pdf_url": "http://arxiv.org/pdf/2503.17933v1", "categories": ["cs.CL", "cs.AI", "cs.IR"]}, "http://arxiv.org/abs/2409.01666v1": {"url": "http://arxiv.org/abs/2409.01666v1", "title": "In Defense of RAG in the Era of Long-Context Language Models", "description": "Overcoming the limited context limitations in early-generation LLMs, retrieval-augmented generation (RAG) has been a reliable solution for context-based answer generation in the past. Recently, the emergence of long-context LLMs allows the models to incorporate much longer text sequences, making RAG less attractive. Recent studies show that long-context LLMs significantly outperform RAG in long-context applications. Unlike the existing works favoring the long-context LLM over RAG, we argue that the extremely long context in LLMs suffers from a diminished focus on relevant information and leads to potential degradation in answer quality. This paper revisits the RAG in long-context answer generation. We propose an order-preserve retrieval-augmented generation (OP-RAG) mechanism, which significantly improves the performance of RAG for long-context question-answer applications. With OP-RAG, as the number of retrieved chunks increases, the answer quality initially rises, and then decline...", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "published": "2024-09-03", "pdf_url": "http://arxiv.org/pdf/2409.01666v1", "categories": ["cs.CL"]}, "http://arxiv.org/abs/2410.07176v1": {"url": "http://arxiv.org/abs/2410.07176v1", "title": "Astute RAG: Overcoming Imperfect Retrieval Augmentation and Knowledge Conflicts for Large Language Models", "description": "Retrieval-Augmented Generation (RAG), while effective in integrating external knowledge to address the limitations of large language models (LLMs), can be undermined by imperfect retrieval, which may introduce irrelevant, misleading, or even malicious information. Despite its importance, previous studies have rarely explored the behavior of RAG through joint analysis on how errors from imperfect retrieval attribute and propagate, and how potential conflicts arise between the LLMs' internal knowledge and external sources. We find that imperfect retrieval augmentation might be inevitable and quite harmful, through controlled analysis under realistic conditions. We identify the knowledge conflicts between LLM-internal and external knowledge from retrieval as a bottleneck to overcome in the post-retrieval stage of RAG. To render LLMs resilient to imperfect retrieval, we propose Astute RAG, a novel RAG approach that adaptively elicits essential information from LLMs' internal knowledge, ...", "authors": ["<PERSON><PERSON>", "Xingchen <PERSON>", "Ruoxi Sun", "<PERSON><PERSON><PERSON>", "Sercan Ö. Arık"], "published": "2024-10-09", "pdf_url": "http://arxiv.org/pdf/2410.07176v1", "categories": ["cs.CL", "cs.AI", "cs.LG"]}}