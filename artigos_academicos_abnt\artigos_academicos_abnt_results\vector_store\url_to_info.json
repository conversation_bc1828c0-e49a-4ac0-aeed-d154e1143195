{"http://arxiv.org/abs/1605.03325v1": {"url": "http://arxiv.org/abs/1605.03325v1", "title": "Multi-class Vector AutoRegressive Models for Multi-store Sales Data", "description": "Retailers use the Vector AutoRegressive (VAR) model as a standard tool to estimate the effects of prices, promotions and sales in one product category on the sales of another product category. Besides, these price, promotion and sales data are available for not just one store, but a whole chain of stores. We propose to study cross-category effects using a multi-class VAR model: we jointly estimate cross-category effects for several distinct but related VAR models, one for each store. Our methodology encourages effects to be similar across stores, while still allowing for small differences between stores to account for store heterogeneity. Moreover, our estimator is sparse: unimportant effects are estimated as exactly zero, which facilitates the interpretation of the results. A simulation study shows that the proposed multi-class estimator improves estimation accuracy by borrowing strength across classes. Finally, we provide three visual tools showing (i) the clustering of stores on ...", "authors": ["<PERSON><PERSON> Wilms", "<PERSON>", "<PERSON>"], "published": "2016-05-11", "pdf_url": "http://arxiv.org/pdf/1605.03325v1", "categories": ["stat.AP"]}, "http://arxiv.org/abs/1510.00413v1": {"url": "http://arxiv.org/abs/1510.00413v1", "title": "Decay Lifetimes of the Vector and Tensor Polarization of a Stored Deuteron Beam", "description": "An rf solenoid was operated in the IUCF Cooler to induce a depolarizing spin resonance for a stored polarized deuteron beam. The decay lifetimes of the vector and tensor polarizations were found to be in the ratio $1.9:1$, as opposed to the expected ratio of $3:1$ from standard angular momentum theory. This report describes my investigations and attempts to formulate a theoretical explanation of the above phenomenon.", "authors": ["<PERSON><PERSON> <PERSON><PERSON>"], "published": "2015-10-02", "pdf_url": "http://arxiv.org/pdf/1510.00413v1", "categories": ["physics.acc-ph"]}, "http://arxiv.org/abs/2501.10534v1": {"url": "http://arxiv.org/abs/2501.10534v1", "title": "4bit-Quantization in Vector-Embedding for RAG", "description": "Retrieval-augmented generation (RAG) is a promising technique that has shown great potential in addressing some of the limitations of large language models (LLMs). LLMs have two major limitations: they can contain outdated information due to their training data, and they can generate factually inaccurate responses, a phenomenon known as hallucinations. RAG aims to mitigate these issues by leveraging a database of relevant documents, which are stored as embedding vectors in a high-dimensional space. However, one of the challenges of using high-dimensional embeddings is that they require a significant amount of memory to store. This can be a major issue, especially when dealing with large databases of documents. To alleviate this problem, we propose the use of 4-bit quantization to store the embedding vectors. This involves reducing the precision of the vectors from 32-bit floating-point numbers to 4-bit integers, which can significantly reduce the memory requirements. Our approach ha...", "authors": ["<PERSON><PERSON><PERSON>"], "published": "2025-01-17", "pdf_url": "http://arxiv.org/pdf/2501.10534v1", "categories": ["cs.LG", "cs.AI"]}, "http://arxiv.org/abs/1604.08572v2": {"url": "http://arxiv.org/abs/1604.08572v2", "title": "Stored energies for electric and magnetic current densities", "description": "Electric and magnetic current densities are an essential part of electromagnetic theory. The goal of the present paper is to define and investigate stored energies that are valid for structures that can support both electric and magnetic current densities. Stored energies normalized with the dissipated power give us the Q factor, or antenna Q, for the structure. Lower bounds of the Q factor provide information about the available bandwidth for passive antennas that can be realized in the structure. The definition that we propose is valid beyond the leading order small antenna limit. Our starting point is the energy density with subtracted far-field form which we obtain an explicit and numerically attractive current density representation. This representation gives us the insight to propose a coordinate independent stored energy. Furthermore, we find here that lower bounds on antenna Q for structures with e.g. electric dipole radiation can be formulated as convex optimization problem...", "authors": ["B. L<PERSON> <PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2016-04-28", "pdf_url": "http://arxiv.org/pdf/1604.08572v2", "categories": ["physics.class-ph", "78A50, 78A25, 47N10"]}, "http://arxiv.org/abs/2409.07478v1": {"url": "http://arxiv.org/abs/2409.07478v1", "title": "A Note on the Objectivity (Rotational Invariance) of the Stored Energy Density in Continuum Physics", "description": "This short note is concerned with the rotational invariance of the stored energy density in continuum physics as a scalar function of a few vectors. A simple derivation is presented for the determination of the general form of the energy density in the case of a two-dimensional space. It is also shown that the general form of the energy density so determined may be further reduced. The three-dimensional case is also discussed.", "authors": ["<PERSON><PERSON>"], "published": "2024-08-31", "pdf_url": "http://arxiv.org/pdf/2409.07478v1", "categories": ["physics.class-ph"]}, "http://arxiv.org/abs/2409.14683v1": {"url": "http://arxiv.org/abs/2409.14683v1", "title": "Reducing the Footprint of Multi-Vector Retrieval with Minimal Performance Impact via Token Pooling", "description": "Over the last few years, multi-vector retrieval methods, spearheaded by ColBERT, have become an increasingly popular approach to Neural IR. By storing representations at the token level rather than at the document level, these methods have demonstrated very strong retrieval performance, especially in out-of-domain settings. However, the storage and memory requirements necessary to store the large number of associated vectors remain an important drawback, hindering practical adoption. In this paper, we introduce a simple clustering-based token pooling approach to aggressively reduce the number of vectors that need to be stored. This method can reduce the space & memory footprint of ColBERT indexes by 50% with virtually no retrieval performance degradation. This method also allows for further reductions, reducing the vector count by 66%-to-75% , with degradation remaining below 5% on a vast majority of datasets. Importantly, this approach requires no architectural change nor query-tim...", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "published": "2024-09-23", "pdf_url": "http://arxiv.org/pdf/2409.14683v1", "categories": ["cs.IR", "cs.AI", "cs.CL"]}, "http://arxiv.org/abs/1412.3328v7": {"url": "http://arxiv.org/abs/1412.3328v7", "title": "Memory vectors for similarity search in high-dimensional spaces", "description": "We study an indexing architecture to store and search in a database of high-dimensional vectors from the perspective of statistical signal processing and decision theory. This architecture is composed of several memory units, each of which summarizes a fraction of the database by a single representative vector. The potential similarity of the query to one of the vectors stored in the memory unit is gauged by a simple correlation with the memory unit's representative vector. This representative optimizes the test of the following hypothesis: the query is independent from any vector in the memory unit vs. the query is a simple perturbation of one of the stored vectors.   Compared to exhaustive search, our approach finds the most similar database vectors significantly faster without a noticeable reduction in search quality. Interestingly, the reduction of complexity is provably better in high-dimensional spaces. We empirically demonstrate its practical interest in a large-scale image s...", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "published": "2014-12-10", "pdf_url": "http://arxiv.org/pdf/1412.3328v7", "categories": ["cs.CV", "cs.<PERSON>"]}, "http://arxiv.org/abs/1911.04975v3": {"url": "http://arxiv.org/abs/1911.04975v3", "title": "word2ket: Space-efficient Word Embeddings inspired by Quantum Entanglement", "description": "Deep learning natural language processing models often use vector word embeddings, such as word2vec or GloVe, to represent words. A discrete sequence of words can be much more easily integrated with downstream neural layers if it is represented as a sequence of continuous vectors. Also, semantic relationships between words, learned from a text corpus, can be encoded in the relative configurations of the embedding vectors. However, storing and accessing embedding vectors for all words in a dictionary requires large amount of space, and may stain systems with limited GPU memory. Here, we used approaches inspired by quantum computing to propose two related methods, {\\em word2ket} and {\\em word2ketXS}, for storing word embedding matrix during training and inference in a highly efficient way. Our approach achieves a hundred-fold or more reduction in the space required to store the embeddings with almost no relative drop in accuracy in practical natural language processing tasks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "published": "2019-11-12", "pdf_url": "http://arxiv.org/pdf/1911.04975v3", "categories": ["cs.LG", "stat.ML"]}, "http://arxiv.org/abs/2501.11301v3": {"url": "http://arxiv.org/abs/2501.11301v3", "title": "Question-to-Question Retrieval for Hallucination-Free Knowledge Access: An Approach for Wikipedia and Wikidata Question Answering", "description": "This paper introduces an approach to question answering over knowledge bases like Wikipedia and Wikidata by performing \"question-to-question\" matching and retrieval from a dense vector embedding store. Instead of embedding document content, we generate a comprehensive set of questions for each logical content unit using an instruction-tuned LLM. These questions are vector-embedded and stored, mapping to the corresponding content. Vector embedding of user queries are then matched against this question vector store. The highest similarity score leads to direct retrieval of the associated article content, eliminating the need for answer generation. Our method achieves high cosine similarity ( > 0.9 ) for relevant question pairs, enabling highly precise retrieval. This approach offers several advantages including computational efficiency, rapid response times, and increased scalability. We demonstrate its effectiveness on Wikipedia and Wikidata, including multimedia content through stru...", "authors": ["<PERSON><PERSON><PERSON>"], "published": "2025-01-20", "pdf_url": "http://arxiv.org/pdf/2501.11301v3", "categories": ["cs.CL", "cs.AI"]}, "http://arxiv.org/abs/1811.05922v2": {"url": "http://arxiv.org/abs/1811.05922v2", "title": "Bandana: Using Non-volatile Memory for Storing Deep Learning Models", "description": "Typical large-scale recommender systems use deep learning models that are stored on a large amount of DRAM. These models often rely on embeddings, which consume most of the required memory. We present Bandana, a storage system that reduces the DRAM footprint of embeddings, by using Non-volatile Memory (NVM) as the primary storage medium, with a small amount of DRAM as cache. The main challenge in storing embeddings on NVM is its limited read bandwidth compared to DRAM. Bandana uses two primary techniques to address this limitation: first, it stores embedding vectors that are likely to be read together in the same physical location, using hypergraph partitioning, and second, it decides the number of embedding vectors to cache in DRAM by simulating dozens of small caches. These techniques allow Bandana to increase the effective read bandwidth of NVM by 2-3x and thereby significantly reduce the total cost of ownership.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Asaf Cidon", "<PERSON><PERSON>"], "published": "2018-11-14", "pdf_url": "http://arxiv.org/pdf/1811.05922v2", "categories": ["cs.LG", "stat.ML"]}}