{"version": "6_1_0", "md.comp.carousel-item.container.color": "surface", "md.comp.carousel-item.container.elevation": "md.sys.elevation.level0", "md.comp.carousel-item.container.shadow-color": "shadow", "md.comp.carousel-item.container.shape": "md.sys.shape.corner.extra-large", "md.comp.carousel-item.disabled.container.color": "surface", "md.comp.carousel-item.disabled.container.elevation": "md.sys.elevation.level0", "md.comp.carousel-item.disabled.container.opacity": 0.38, "md.comp.carousel-item.focus.container.elevation": "md.sys.elevation.level0", "md.comp.carousel-item.focus.indicator.color": "secondary", "md.comp.carousel-item.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.carousel-item.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.carousel-item.focus.state-layer.color": "onSurface", "md.comp.carousel-item.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.carousel-item.hover.container.elevation": "md.sys.elevation.level1", "md.comp.carousel-item.hover.state-layer.color": "onSurface", "md.comp.carousel-item.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.carousel-item.pressed.container.elevation": "md.sys.elevation.level0", "md.comp.carousel-item.pressed.state-layer.color": "onSurface", "md.comp.carousel-item.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.carousel-item.with-outline.disabled.outline.color": "outline", "md.comp.carousel-item.with-outline.disabled.outline.opacity": 0.12, "md.comp.carousel-item.with-outline.focus.outline.color": "onSurface", "md.comp.carousel-item.with-outline.hover.outline.color": "outline", "md.comp.carousel-item.with-outline.outline.color": "outline", "md.comp.carousel-item.with-outline.outline.width": 1.0, "md.comp.carousel-item.with-outline.pressed.outline.color": "outline"}