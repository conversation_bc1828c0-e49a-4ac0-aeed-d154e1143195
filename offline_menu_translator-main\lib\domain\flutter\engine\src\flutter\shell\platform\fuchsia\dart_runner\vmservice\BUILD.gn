# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/compiled_action.gni")
import("//flutter/common/config.gni")
import("//flutter/tools/fuchsia/dart.gni")
import("//flutter/tools/fuchsia/dart_kernel.gni")
import("//flutter/tools/fuchsia/gn-sdk/src/gn_configs.gni")
import("$dart_src/utils/compile_platform.gni")

dart_kernel("vmservice_product_aot_kernel") {
  kernel_platform_files = "../kernel:kernel_platform_files"
  main_dart = "empty.dart"
  product = true
  aot = true
}

dart_kernel("vmservice_aot_kernel") {
  kernel_platform_files = "../kernel:kernel_platform_files"
  main_dart = "empty.dart"
  product = false
  aot = true
}

template("aot_snapshot") {
  compiled_action(target_name) {
    kernel_name = target_name

    if (defined(invoker.kernel_name)) {
      kernel_name = invoker.kernel_name
    }

    product = defined(invoker.product) && invoker.product

    product_suffix = ""
    if (product) {
      product_suffix = "_product"
    }

    shim_target = ":vmservice${product_suffix}_aot_kernel($host_toolchain)"
    shim_kernel = get_label_info(shim_target, "target_gen_dir") +
                  "/vmservice${product_suffix}_aot_kernel.dill"

    deps = [ shim_target ]

    snapshot_path = "$target_gen_dir/${kernel_name}_snapshot.so"

    inputs = [ shim_kernel ]

    outputs = [ snapshot_path ]

    if (product) {
      tool = gen_snapshot_product
    } else {
      tool = gen_snapshot
    }

    args = [
      "--deterministic",
      "--snapshot_kind=app-aot-elf",
      "--elf=" + rebase_path(snapshot_path),
    ]

    # No asserts in debug or release product.
    # No asserts in release with flutter_profile=true (non-product)
    # Yes asserts in non-product debug.
    if (!product && (flutter_runtime_mode == "debug" || is_debug)) {
      args += [ "--enable_asserts" ]
    }

    args += [ rebase_path(shim_kernel) ]
  }
}

aot_snapshot("vmservice_snapshot") {
  kernel_name = "vmservice"
  product = false
}
