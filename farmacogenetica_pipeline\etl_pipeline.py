# etl_pipeline.py
import pandas as pd
from config import COLUMN_RENAMES, DATA_TYPES, CATEGORY_ENCODINGS, CONDITIONAL_COLUMNS
from utils import (
    apply_conditional_imputation,
    handle_missing_values,
    one_hot_encode,
    label_encode,
    validate_data
)
import os

def load_data(file_path):
    df = pd.read_csv(file_path, low_memory=False)
    print(f"✅ Dados brutos carregados com {df.shape[0]} linhas e {df.shape[1]} colunas")
    return df

def rename_columns(df, rename_map):
    df.rename(columns=rename_map, inplace=True)
    print("✅ Colunas renomeadas")
    return df

def encode_labels(df, encoding_map):
    df = label_encode(df, encoding_map)
    print("✅ Label encoding aplicado")
    return df

def apply_conditionals(df, conditionals):
    df = apply_conditional_imputation(df, conditionals)
    print("✅ Regras condicionais aplicadas")
    return df

def manage_missing_values(df):
    df = handle_missing_values(df)
    print("✅ Dados ausentes tratados")
    return df

def encode_one_hot(df):
    categorical_cols = [
        'ethnicity_race',
        'hospital_origin',
        'alcohol_consumption',
        'exercise_frequency',
        'angioplasty_urgency',
        'timi_age_category',
        'diabetes_type'
    ]
    df = one_hot_encode(df, categorical_cols)
    print("✅ One-hot encoding aplicado")
    return df

def validate_final(df, data_types):
    df = validate_data(df, data_types)
    print("✅ Validação de tipos de dados concluída")
    return df

def save_processed(df, output_path='processed_data/processed_data.csv'):
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    df.to_csv(output_path, index=False)
    print(f"✅ Dados processados salvos em {output_path}")

def main():
    input_file = 'banco_farmacogeneticabrasil_processado.csv'

    df = load_data(input_file)
    df = rename_columns(df, COLUMN_RENAMES)
    df = encode_labels(df, CATEGORY_ENCODINGS)
    df = apply_conditionals(df, CONDITIONAL_COLUMNS)
    df = manage_missing_values(df)
    df = encode_one_hot(df)
    df = validate_final(df, DATA_TYPES)
    save_processed(df)

if __name__ == '__main__':
    main()