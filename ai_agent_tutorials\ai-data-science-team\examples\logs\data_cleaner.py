# Disclaimer: This function was generated by AI. Please review before using.
# Agent Name: data_cleaning_agent
# Time Created: 2025-01-31 15:19:26

def replace_and_convert(col):
    import pandas as pd
    return pd.to_numeric(col.replace(None, 0), errors='coerce').astype(float)

gender = replace_and_convert('gender')
height = replace_and_convert('height')
weight = replace_and_convert('weight')
height2 = replace_and_convert('height2')
weight2 = replace_and_convert('weight2')