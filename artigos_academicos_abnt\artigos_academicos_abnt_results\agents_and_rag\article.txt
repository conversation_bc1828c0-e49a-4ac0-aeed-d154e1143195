**Agents and RAG: A Novel Approach to Enhance Large Language Models**
===========================================================

**I. Introduction**
---------------

Large Language Models (LLMs) have revolutionized the field of artificial intelligence by enabling human-like text generation and natural language understanding. However, their reliance on static training data limits their ability to respond to dynamic, real-time queries, resulting in outdated or inaccurate outputs. Retrieval-Augmented Generation (RAG) has emerged as a solution, enhancing LLMs by integrating real-time data retrieval to provide contextually relevant and up-to-date responses. Despite its promise, traditional RAG systems are constrained by static workflows and lack the adaptability required for multistep reasoning and complex task management. Agentic Retrieval-Augmented Generation (Agentic RAG) transcends these limitations by embedding autonomous AI agents into the RAG pipeline, enabling dynamic management of retrieval strategies and iterative refinement of contextual understanding.

**II. Traditional RAG Systems and their Limitations**
---------------------------------------------------

Traditional RAG systems rely on static workflows, which restrict their ability to adapt to complex tasks and multistep reasoning. These systems are designed to retrieve information from external sources and generate responses based on the retrieved data. However, they lack the capability to dynamically manage retrieval strategies and refine contextual understanding, leading to limitations in their performance and adaptability.

As noted by Singh et al. (2025), traditional RAG systems are constrained by their static nature, which hinders their ability to respond to dynamic, real-time queries. The authors emphasize the need for more advanced RAG systems that can dynamically manage retrieval strategies and refine contextual understanding.

**III. Agentic RAG: A Novel Approach**
------------------------------------

Agentic RAG integrates autonomous AI agents into the RAG pipeline, enabling dynamic management of retrieval strategies and iterative refinement of contextual understanding. This approach allows for more adaptive and responsive RAG systems, capable of handling complex tasks and multistep reasoning.

Agentic RAG leverages agentic design patterns, such as reflection, planning, tool use, and multiagent collaboration, to dynamically manage retrieval strategies and refine contextual understanding. This approach enables RAG systems to respond more accurately and adaptably to dynamic, real-time queries.

**IV. Optimizing RAG Systems**
---------------------------

Several approaches have been proposed to optimize RAG systems, including Differentiable Data Rewards (DDR), Multi-Agent Reinforcement Learning, and Process Supervision.

DDR, as proposed by Li et al. (2024), end-to-end trains RAG systems by aligning data preferences between different RAG modules. This approach enables RAG systems to adapt to diverse RAG tasks and improves their performance.

Multi-Agent Reinforcement Learning, as proposed by Chen et al. (2025), treats the RAG pipeline as a multi-agent cooperative task, with each component regarded as an agent. This approach enables the RAG system to optimize the objectives of individual modules and the overarching aim of generating accurate answers.

Process Supervision, as proposed by Xiong et al. (2025), enhances information-seeking agents through fine-grained process supervision at each search step. This approach improves the performance of RAG systems by up to 25.6% across various agent architectures.

**V. Advanced RAG Architectures**
------------------------------

Several advanced RAG architectures have been proposed, including RAG-Gym, RAG-KG-IL, and MAIN-RAG.

RAG-Gym, as proposed by Xiong et al. (2025), is a unified optimization framework that enhances information-seeking agents through fine-grained process supervision at each search step.

RAG-KG-IL, as proposed by Yu et al. (2025), integrates Retrieval-Augmented Generation (RAG) and Knowledge Graphs (KGs) with an Incremental Learning (IL) approach. This approach enables RAG systems to incorporate structured knowledge and mitigate hallucinations.

MAIN-RAG, as proposed by Chang et al. (2024), is a training-free RAG framework that leverages multiple LLM agents to collaboratively filter and score retrieved documents. This approach minimizes noise while maintaining high recall of relevant documents.

**VI. Applications of Agentic RAG**
-------------------------------

Agentic RAG has various applications, including knowledge-intensive tasks, question-answering systems, and automated optimization modeling.

In knowledge-intensive tasks, Agentic RAG can be used to enhance the reasoning capabilities of LLMs by integrating RAG and Knowledge Graphs (KGs) with an Incremental Learning (IL) approach.

In question-answering systems, Agentic RAG can be used to improve the accuracy and adaptability of RAG systems by dynamically managing retrieval strategies and refining contextual understanding.

In automated optimization modeling, Agentic RAG can be used to leverage LLM agents for automated optimization modeling for SASP problems.

**VII. Conclusion**
--------------

Agentic RAG is a novel approach that integrates autonomous AI agents into the RAG pipeline, enabling dynamic management of retrieval strategies and iterative refinement of contextual understanding. This approach has various applications, including knowledge-intensive tasks, question-answering systems, and automated optimization modeling. Future research directions include exploring the potential of Agentic RAG to revolutionize the field of artificial intelligence.

**Referências Bibliográficas**
-----------------------------

CHANG, C. Y.; JIANG, Z.; RAKESH, V. et al. MAIN-RAG: Multi-Agent Filtering Retrieval-Augmented Generation. 2024. Disponível em: <http://arxiv.org/abs/2501.00332v1>.

CHEN, Y.; YAN, L. et al. Improving Retrieval-Augmented Generation through Multi-Agent Reinforcement Learning. 2025. Disponível em: <http://arxiv.org/abs/2501.15228v1>.

JEONG, C. A Study on the Implementation Method of an Agent-Based Advanced RAG System Using Graph. 2024. Disponível em: <http://arxiv.org/abs/2407.19994v3>.

LI, X.; MEI, S. et al. RAG-DDR: Optimizing Retrieval-Augmented Generation Using Differentiable Data Rewards. 2024. Disponível em: <http://arxiv.org/abs/2410.13509v2>.

MOHAMMED, A. M.; MANSOOR, I. et al. Developing an Artificial Intelligence Tool for Personalized Breast Cancer Treatment Plans based on the NCCN Guidelines. 2025. Disponível em: <http://arxiv.org/abs/2502.15698v1>.

PAN, T.; PU, W. et al. Leveraging LLM Agents for Automated Optimization Modeling for SASP Problems: A Graph-RAG based Approach. 2025. Disponível em: <http://arxiv.org/abs/2501.18320v1>.

SINGH, A.; EHTESHAM, A. et al. Agentic Retrieval-Augmented Generation: A Survey on Agentic RAG. 2025. Disponível em: <http://arxiv.org/abs/2501.09136v3>.

XIONG, G.; JIN, Q. et al. RAG-Gym: Optimizing Reasoning and Search Agents with Process Supervision. 2025. Disponível em: <http://arxiv.org/abs/2502.13957v1>.

YU, H. Q.; MCQUADE, F. RAG-KG-IL: A Multi-Agent Hybrid Framework for Reducing Hallucinations and Enhancing LLM Reasoning through RAG and Incremental Knowledge Graph Learning Integration. 2025. Disponível em: <http://arxiv.org/abs/2503.13514v1>.