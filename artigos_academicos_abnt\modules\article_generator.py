"""
Módulo de geração de artigos para o aplicativo de artigos acadêmicos ABNT.
Implementa a geração de artigos usando a API Groq.
"""

import logging
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type, before_sleep_log

logger = logging.getLogger(__name__)

# Definição de exceções personalizadas
class RateLimitError(Exception):
    """Erro de limite de taxa (429 Too Many Requests)"""
    pass

class ArticleGenerator:
    """Classe para geração de artigos com Groq"""

    def __init__(self, api_key, model="llama3-70b-8192", max_retries=5, use_cache=True, cache=None):
        """
        Inicializa o gerador de artigos.

        Args:
            api_key (str): Chave da <PERSON> Groq.
            model (str): Modelo a ser usado.
            max_retries (int): Número máximo de tentativas em caso de erro.
            use_cache (bool): Se deve usar cache.
            cache (APICache, optional): Instância de cache a ser usada.
        """
        try:
            from groq import Groq
            self.client = Groq(api_key=api_key)
        except ImportError:
            logger.error("Biblioteca groq não encontrada. Instale com 'pip install groq'.")
            raise

        self.model = model
        self.max_retries = max_retries
        self.use_cache = use_cache
        self.cache = cache

    def _call_groq_api_with_retry(self, messages, temperature=0.7, max_tokens=1000):
        """Wrapper para aplicar o decorador retry dinamicamente com base no max_retries"""
        @retry(
            stop=stop_after_attempt(self.max_retries),
            wait=wait_exponential(multiplier=1, min=4, max=60),
            retry=retry_if_exception_type(RateLimitError),
            reraise=True,
            before_sleep=before_sleep_log(logger, logging.INFO)
        )
        def _call_api(messages, temperature, max_tokens):
            return self._call_groq_api_internal(messages, temperature, max_tokens)

        return _call_api(messages, temperature, max_tokens)

    def _call_groq_api_internal(self, messages, temperature=0.7, max_tokens=1000):
        """Chama a API Groq diretamente (sem retry)"""
        # Verificar cache primeiro
        if self.use_cache and self.cache:
            cached_response = self.cache.get(messages, self.model, temperature, max_tokens)
            if cached_response:
                logger.info("Usando resposta em cache")
                return cached_response

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens,
            )

            # Salvar no cache
            if self.use_cache and self.cache:
                self.cache.set(messages, self.model, temperature, max_tokens, response)

            return response
        except Exception as e:
            error_message = str(e)
            if "429" in error_message or "Too Many Requests" in error_message:
                logger.warning(f"Erro de limite de taxa (429). Tentando novamente após backoff...")
                raise RateLimitError("Limite de taxa excedido. Tentando novamente.")
            else:
                # Para outros erros, não fazemos retry
                raise

    def generate_outline(self, topic, research_results):
        """
        Gera um esboço para o artigo.

        Args:
            topic (str): Tópico do artigo.
            research_results (list): Resultados da pesquisa.

        Returns:
            str: Esboço gerado.
        """
        # Preparar o contexto com os resultados da pesquisa
        context = "Informações da pesquisa:\n\n"
        for i, result in enumerate(research_results, 1):
            context += f"{i}. {result['title']}\n"
            context += f"   Autores: {', '.join(result['authors'])}\n"
            context += f"   Publicado: {result['published']}\n"
            context += f"   Descrição: {result['description']}\n\n"

        # Criar o prompt para geração do esboço
        prompt = f"""Você é um especialista em criar esboços para artigos no estilo Wikipedia.
Com base nas informações de pesquisa fornecidas, crie um esboço detalhado para um artigo sobre "{topic}".
O esboço deve incluir:
1. Uma introdução
2. Seções principais (pelo menos 3-5)
3. Subseções para cada seção principal
4. Uma conclusão

{context}

Formate o esboço usando marcadores e numeração para indicar a hierarquia.
"""

        # Gerar o esboço com retry em caso de erro 429
        try:
            response = self._call_groq_api_with_retry(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=1000
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Erro ao gerar esboço: {str(e)}")
            # Retornar um esboço básico em caso de erro
            return f"# {topic}\n\n## Introdução\n\n## Desenvolvimento\n\n## Conclusão"

    def generate_article(self, topic, outline, research_results, article_format="standard", word_counts=None):
        """
        Gera um artigo completo.

        Args:
            topic (str): Tópico do artigo.
            outline (str): Esboço do artigo.
            research_results (list): Resultados da pesquisa.
            article_format (str): Formato do artigo ("standard" ou "academic").
            word_counts (dict, optional): Contagem de palavras para cada seção do artigo acadêmico.
                Exemplo: {"resumo": 200, "intro": 400, "metodo": 400, "desenv": 600, "result": 600, "concl": 200}

        Returns:
            str: Artigo gerado.
        """
        # Preparar o contexto com os resultados da pesquisa
        context = "Informações da pesquisa:\n\n"
        for i, result in enumerate(research_results, 1):
            context += f"{i}. {result['title']}\n"
            context += f"   Autores: {', '.join(result['authors'])}\n"
            context += f"   Publicado: {result['published']}\n"
            context += f"   ID: {result['url']}\n"
            context += f"   Descrição: {result['description']}\n\n"

        # Base do prompt para ambos os formatos
        base_prompt = f"""Você é um especialista em criar artigos acadêmicos seguindo as normas ABNT (Associação Brasileira de Normas Técnicas).
Com base no esboço e nas informações de pesquisa fornecidas, crie um artigo completo sobre "{topic}".

Esboço:
{outline}

{context}

O artigo deve:
1. Usar um tom neutro e informativo, como em publicações acadêmicas
2. Incluir informações relevantes das fontes de pesquisa
3. Ser bem estruturado com títulos, subtítulos e parágrafos
4. Ter aproximadamente 1500-2000 palavras
5. Incluir citações no formato ABNT ao longo do texto

Para citações no formato ABNT:
- Citação direta curta (até 3 linhas): use aspas e indique o autor, ano e página entre parênteses. Ex: "texto citado" (SOBRENOME, ano, p. XX).
- Citação indireta: apenas indique o autor e ano entre parênteses. Ex: (SOBRENOME, ano).
- Use o ID fornecido para identificar cada fonte.

Formate o artigo usando Markdown para os títulos e subtítulos.
Ao final do artigo, inclua uma seção de "Referências Bibliográficas" no formato ABNT."""

        # Adicionar instruções específicas para o formato acadêmico
        if article_format == "academic":
            # Definir contagens de palavras padrão se não forem fornecidas
            if not word_counts:
                word_counts = {
                    "resumo": 200,
                    "intro": 400,
                    "metodo": 400,
                    "desenv": 600,
                    "result": 600,
                    "concl": 200
                }

            # Calcular o total de palavras para o artigo
            total_words = sum(word_counts.values()) + word_counts["resumo"]  # Adicionar o abstract (mesmo tamanho do resumo)

            prompt = base_prompt + f"""\n\nImportante: Use o formato acadêmico estruturado com as seguintes seções nesta ordem exata, respeitando a contagem de palavras especificada para cada seção (total aproximado: {total_words} palavras):\n
# Título do Artigo

## Resumo
(Um parágrafo resumindo o artigo em português, com aproximadamente {word_counts['resumo']} palavras)

**Palavras-chave:** 3-5 palavras-chave separadas por ponto e vírgula

## Abstract
(Tradução do resumo para inglês, com aproximadamente {word_counts['resumo']} palavras)

**Keywords:** Tradução das palavras-chave para inglês

## 1. Introdução
(Contextualização, justificativa, objetivos e estrutura do artigo - aproximadamente {word_counts['intro']} palavras)

## 2. Metodologia, Material e Métodos
(Descrição detalhada dos métodos utilizados - aproximadamente {word_counts['metodo']} palavras)

## 3. Desenvolvimento
(Fundamentação teórica e desenvolvimento do tema - aproximadamente {word_counts['desenv']} palavras)

## 4. Resultados e Discussão
(Apresentação e análise dos resultados - aproximadamente {word_counts['result']} palavras)

## 5. Conclusões
(Síntese dos principais resultados e contribuições - aproximadamente {word_counts['concl']} palavras)

## Referências Bibliográficas
(Lista de referências no formato ABNT)
"""
        else:  # Formato padrão
            prompt = base_prompt + """\n\nSiga o esboço fornecido e organize o artigo no estilo Wikipedia, com introdução, desenvolvimento e conclusão."""


        # Gerar o artigo com retry em caso de erro 429
        try:
            response = self._call_groq_api_with_retry(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=3000
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Erro ao gerar artigo: {str(e)}")
            # Retornar um artigo básico em caso de erro
            return f"# {topic}\n\nNão foi possível gerar o artigo completo devido a limitações da API. Por favor, tente novamente mais tarde."

    def polish_article(self, article):
        """
        Realiza o polimento do artigo.

        Args:
            article (str): Artigo a ser polido.

        Returns:
            str: Artigo polido.
        """
        # Criar o prompt para polimento do artigo
        prompt = f"""Você é um editor profissional especializado em artigos acadêmicos no estilo Wikipedia, seguindo as normas ABNT.
Revise e aprimore o seguinte artigo, mantendo seu conteúdo, estrutura e citações ABNT, mas melhorando:
1. Clareza e fluidez
2. Gramática e pontuação
3. Consistência de estilo
4. Formatação Markdown
5. Consistência nas citações ABNT

Certifique-se de que todas as citações estão no formato ABNT correto:
- Citação direta curta (até 3 linhas): use aspas e indique o autor, ano e página entre parênteses. Ex: "texto citado" (SOBRENOME, ano, p. XX).
- Citação indireta: apenas indique o autor e ano entre parênteses. Ex: (SOBRENOME, ano).

Verifique se a seção de "Referências Bibliográficas" está formatada corretamente segundo as normas ABNT.

Artigo:
{article}

Retorne o artigo polido completo.
"""

        # Polir o artigo com retry em caso de erro 429
        try:
            response = self._call_groq_api_with_retry(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=3000
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Erro ao polir artigo: {str(e)}")
            # Retornar o artigo original em caso de erro
            return article
