{"version": "6_1_0", "md.comp.secondary-navigation-tab.active-indicator.color": "primary", "md.comp.secondary-navigation-tab.active-indicator.height": 2.0, "md.comp.secondary-navigation-tab.active.label-text.color": "onSurface", "md.comp.secondary-navigation-tab.container.color": "surface", "md.comp.secondary-navigation-tab.container.elevation": "md.sys.elevation.level0", "md.comp.secondary-navigation-tab.container.height": 48.0, "md.comp.secondary-navigation-tab.container.shadow-color": "shadow", "md.comp.secondary-navigation-tab.container.shape": "md.sys.shape.corner.none", "md.comp.secondary-navigation-tab.focus.indicator.color": "secondary", "md.comp.secondary-navigation-tab.focus.indicator.outline.offset": "md.sys.state.focus-indicator.inner-offset", "md.comp.secondary-navigation-tab.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.secondary-navigation-tab.focus.label-text.color": "onSurface", "md.comp.secondary-navigation-tab.focus.state-layer.color": "onSurface", "md.comp.secondary-navigation-tab.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.secondary-navigation-tab.hover.label-text.color": "onSurface", "md.comp.secondary-navigation-tab.hover.state-layer.color": "onSurface", "md.comp.secondary-navigation-tab.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.secondary-navigation-tab.inactive.label-text.color": "onSurfaceVariant", "md.comp.secondary-navigation-tab.label-text.text-style": "titleSmall", "md.comp.secondary-navigation-tab.pressed.label-text.color": "onSurface", "md.comp.secondary-navigation-tab.pressed.state-layer.color": "onSurface", "md.comp.secondary-navigation-tab.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.secondary-navigation-tab.with-icon.active.icon.color": "onSurface", "md.comp.secondary-navigation-tab.with-icon.focus.icon.color": "onSurface", "md.comp.secondary-navigation-tab.with-icon.hover.icon.color": "onSurface", "md.comp.secondary-navigation-tab.with-icon.icon.size": 24.0, "md.comp.secondary-navigation-tab.with-icon.inactive.icon.color": "onSurfaceVariant", "md.comp.secondary-navigation-tab.with-icon.pressed.icon.color": "onSurface"}