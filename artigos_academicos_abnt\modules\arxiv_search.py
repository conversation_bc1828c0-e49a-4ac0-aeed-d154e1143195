"""
Módulo de busca no Arxiv para o aplicativo de artigos acadêmicos ABNT.
Implementa a busca de artigos no arxiv.org.
"""

import logging
import arxiv

logger = logging.getLogger(__name__)

class ArxivSearch:
    """Classe para busca de artigos no arxiv.org"""
    
    def __init__(self, max_results=10):
        self.max_results = max_results
    
    def search(self, query, max_results=None):
        """
        Busca artigos no arxiv.org com base na consulta fornecida.
        
        Args:
            query (str): Consulta de busca.
            max_results (int, optional): Número máximo de resultados. Se None, usa o valor padrão.
            
        Returns:
            list: Lista de resultados formatados.
        """
        if max_results is None:
            max_results = self.max_results
            
        try:
            # Configurar o cliente do Arxiv
            client = arxiv.Client()
            
            # Criar a consulta
            search = arxiv.Search(
                query=query,
                max_results=max_results,
                sort_by=arxiv.SortCriterion.Relevance,
                sort_order=arxiv.SortOrder.Descending,
            )
            
            # Executar a consulta
            results = list(client.results(search))
            
            # Processar os resultados
            processed_results = []
            for result in results:
                # Extrair o resumo e limitar a 1000 caracteres
                summary = result.summary.replace('\n', ' ')
                if len(summary) > 1000:
                    summary = summary[:997] + '...'
                
                # Criar o documento
                document = {
                    "url": result.entry_id,
                    "title": result.title,
                    "description": summary,
                    "authors": [author.name for author in result.authors],
                    "published": result.published.strftime("%Y-%m-%d"),
                    "pdf_url": result.pdf_url,
                    "categories": result.categories
                }
                processed_results.append(document)
                
            return processed_results
                
        except Exception as e:
            logger.error(f"Erro ao buscar a consulta {query}: {e}")
            return []
