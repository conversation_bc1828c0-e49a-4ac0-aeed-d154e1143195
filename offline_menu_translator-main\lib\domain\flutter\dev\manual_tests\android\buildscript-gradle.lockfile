# This is a Gradle generated file for dependency locking.
# Manual edits can break the build and are not advised.
# This file is expected to be part of source control.
androidx.databinding:databinding-common:8.7.0=classpath
androidx.databinding:databinding-compiler-common:8.7.0=classpath
com.android.application:com.android.application.gradle.plugin:8.7.0=classpath
com.android.databinding:baseLibrary:8.7.0=classpath
com.android.tools.analytics-library:crash:31.7.0=classpath
com.android.tools.analytics-library:protos:31.7.0=classpath
com.android.tools.analytics-library:shared:31.7.0=classpath
com.android.tools.analytics-library:tracker:31.7.0=classpath
com.android.tools.build.jetifier:jetifier-core:1.0.0-beta10=classpath
com.android.tools.build.jetifier:jetifier-processor:1.0.0-beta10=classpath
com.android.tools.build:aapt2-proto:8.7.0-12006047=classpath
com.android.tools.build:aaptcompiler:8.7.0=classpath
com.android.tools.build:apksig:8.7.0=classpath
com.android.tools.build:apkzlib:8.7.0=classpath
com.android.tools.build:builder-model:8.7.0=classpath
com.android.tools.build:builder-test-api:8.7.0=classpath
com.android.tools.build:builder:8.7.0=classpath
com.android.tools.build:bundletool:1.17.1=classpath
com.android.tools.build:gradle-api:8.7.0=classpath
com.android.tools.build:gradle-settings-api:8.7.0=classpath
com.android.tools.build:gradle:8.7.0=classpath
com.android.tools.build:manifest-merger:31.7.0=classpath
com.android.tools.build:transform-api:2.0.0-deprecated-use-gradle-api=classpath
com.android.tools.ddms:ddmlib:31.7.0=classpath
com.android.tools.layoutlib:layoutlib-api:31.7.0=classpath
com.android.tools.lint:lint-model:31.7.0=classpath
com.android.tools.lint:lint-typedef-remover:31.7.0=classpath
com.android.tools.utp:android-device-provider-ddmlib-proto:31.7.0=classpath
com.android.tools.utp:android-device-provider-gradle-proto:31.7.0=classpath
com.android.tools.utp:android-device-provider-profile-proto:31.7.0=classpath
com.android.tools.utp:android-test-plugin-host-additional-test-output-proto:31.7.0=classpath
com.android.tools.utp:android-test-plugin-host-apk-installer-proto:31.7.0=classpath
com.android.tools.utp:android-test-plugin-host-coverage-proto:31.7.0=classpath
com.android.tools.utp:android-test-plugin-host-emulator-control-proto:31.7.0=classpath
com.android.tools.utp:android-test-plugin-host-logcat-proto:31.7.0=classpath
com.android.tools.utp:android-test-plugin-host-retention-proto:31.7.0=classpath
com.android.tools.utp:android-test-plugin-result-listener-gradle-proto:31.7.0=classpath
com.android.tools:annotations:31.7.0=classpath
com.android.tools:common:31.7.0=classpath
com.android.tools:dvlib:31.7.0=classpath
com.android.tools:repository:31.7.0=classpath
com.android.tools:sdk-common:31.7.0=classpath
com.android.tools:sdklib:31.7.0=classpath
com.android:signflinger:8.7.0=classpath
com.android:zipflinger:8.7.0=classpath
com.google.android:annotations:*******=classpath
com.google.api.grpc:proto-google-common-protos:2.17.0=classpath
com.google.auto.value:auto-value-annotations:1.6.2=classpath
com.google.code.findbugs:jsr305:3.0.2=classpath
com.google.code.gson:gson:2.10.1=classpath
com.google.crypto.tink:tink:1.7.0=classpath
com.google.dagger:dagger:2.28.3=classpath
com.google.errorprone:error_prone_annotations:2.18.0=classpath
com.google.flatbuffers:flatbuffers-java:1.12.0=classpath
com.google.guava:failureaccess:1.0.1=classpath
com.google.guava:guava:32.0.1-jre=classpath
com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava=classpath
com.google.j2objc:j2objc-annotations:2.8=classpath
com.google.jimfs:jimfs:1.1=classpath
com.google.protobuf:protobuf-java-util:3.22.3=classpath
com.google.protobuf:protobuf-java:3.22.3=classpath
com.google.testing.platform:core-proto:0.0.9-alpha02=classpath
com.googlecode.juniversalchardet:juniversalchardet:1.0.3=classpath
com.squareup:javapoet:1.10.0=classpath
com.squareup:javawriter:2.5.0=classpath
com.sun.activation:javax.activation:1.2.0=classpath
com.sun.istack:istack-commons-runtime:3.0.8=classpath
com.sun.xml.fastinfoset:FastInfoset:1.2.16=classpath
commons-codec:commons-codec:1.11=classpath
commons-io:commons-io:2.13.0=classpath
commons-logging:commons-logging:1.2=classpath
io.grpc:grpc-api:1.57.0=classpath
io.grpc:grpc-context:1.57.0=classpath
io.grpc:grpc-core:1.57.0=classpath
io.grpc:grpc-netty:1.57.0=classpath
io.grpc:grpc-protobuf-lite:1.57.0=classpath
io.grpc:grpc-protobuf:1.57.0=classpath
io.grpc:grpc-stub:1.57.0=classpath
io.netty:netty-buffer:4.1.93.Final=classpath
io.netty:netty-codec-http2:4.1.93.Final=classpath
io.netty:netty-codec-http:4.1.93.Final=classpath
io.netty:netty-codec-socks:4.1.93.Final=classpath
io.netty:netty-codec:4.1.93.Final=classpath
io.netty:netty-common:4.1.93.Final=classpath
io.netty:netty-handler-proxy:4.1.93.Final=classpath
io.netty:netty-handler:4.1.93.Final=classpath
io.netty:netty-resolver:4.1.93.Final=classpath
io.netty:netty-transport-native-unix-common:4.1.93.Final=classpath
io.netty:netty-transport:4.1.93.Final=classpath
io.perfmark:perfmark-api:0.26.0=classpath
jakarta.activation:jakarta.activation-api:1.2.1=classpath
jakarta.xml.bind:jakarta.xml.bind-api:2.3.2=classpath
javax.annotation:javax.annotation-api:1.3.2=classpath
javax.inject:javax.inject:1=classpath
net.java.dev.jna:jna-platform:5.6.0=classpath
net.java.dev.jna:jna:5.6.0=classpath
net.sf.jopt-simple:jopt-simple:4.9=classpath
net.sf.kxml:kxml2:2.3.0=classpath
org.apache.commons:commons-compress:1.21=classpath
org.apache.httpcomponents:httpclient:4.5.14=classpath
org.apache.httpcomponents:httpcore:4.4.16=classpath
org.apache.httpcomponents:httpmime:4.5.6=classpath
org.bitbucket.b_c:jose4j:0.9.5=classpath
org.bouncycastle:bcpkix-jdk18on:1.77=classpath
org.bouncycastle:bcprov-jdk18on:1.77=classpath
org.bouncycastle:bcutil-jdk18on:1.77=classpath
org.checkerframework:checker-qual:3.33.0=classpath
org.codehaus.mojo:animal-sniffer-annotations:1.23=classpath
org.glassfish.jaxb:jaxb-runtime:2.3.2=classpath
org.glassfish.jaxb:txw2:2.3.2=classpath
org.jdom:jdom2:2.0.6=classpath
org.jetbrains.intellij.deps:trove4j:1.0.20200330=classpath
org.jetbrains.kotlin.android:org.jetbrains.kotlin.android.gradle.plugin:1.8.10=classpath
org.jetbrains.kotlin:kotlin-android-extensions:1.8.10=classpath
org.jetbrains.kotlin:kotlin-annotation-processing-gradle:1.8.10=classpath
org.jetbrains.kotlin:kotlin-build-common:1.8.10=classpath
org.jetbrains.kotlin:kotlin-compiler-embeddable:1.8.10=classpath
org.jetbrains.kotlin:kotlin-compiler-runner:1.8.10=classpath
org.jetbrains.kotlin:kotlin-daemon-client:1.8.10=classpath
org.jetbrains.kotlin:kotlin-daemon-embeddable:1.8.10=classpath
org.jetbrains.kotlin:kotlin-gradle-plugin-api:1.8.10=classpath
org.jetbrains.kotlin:kotlin-gradle-plugin-idea-proto:1.8.10=classpath
org.jetbrains.kotlin:kotlin-gradle-plugin-idea:1.8.10=classpath
org.jetbrains.kotlin:kotlin-gradle-plugin-model:1.8.10=classpath
org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.10=classpath
org.jetbrains.kotlin:kotlin-klib-commonizer-api:1.8.10=classpath
org.jetbrains.kotlin:kotlin-native-utils:1.8.10=classpath
org.jetbrains.kotlin:kotlin-project-model:1.8.10=classpath
org.jetbrains.kotlin:kotlin-reflect:1.9.20=classpath
org.jetbrains.kotlin:kotlin-scripting-common:1.8.10=classpath
org.jetbrains.kotlin:kotlin-scripting-compiler-embeddable:1.8.10=classpath
org.jetbrains.kotlin:kotlin-scripting-compiler-impl-embeddable:1.8.10=classpath
org.jetbrains.kotlin:kotlin-scripting-jvm:1.8.10=classpath
org.jetbrains.kotlin:kotlin-stdlib-common:1.9.20=classpath
org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20=classpath
org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20=classpath
org.jetbrains.kotlin:kotlin-stdlib:1.9.20=classpath
org.jetbrains.kotlin:kotlin-tooling-core:1.8.10=classpath
org.jetbrains.kotlin:kotlin-util-io:1.8.10=classpath
org.jetbrains.kotlin:kotlin-util-klib:1.8.10=classpath
org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.5.0=classpath
org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.4.0=classpath
org.jetbrains.kotlinx:kotlinx-serialization-core:1.4.0=classpath
org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.4.0=classpath
org.jetbrains.kotlinx:kotlinx-serialization-json:1.4.0=classpath
org.jetbrains:annotations:23.0.0=classpath
org.jvnet.staxex:stax-ex:1.8.1=classpath
org.ow2.asm:asm-analysis:9.6=classpath
org.ow2.asm:asm-commons:9.6=classpath
org.ow2.asm:asm-tree:9.6=classpath
org.ow2.asm:asm-util:9.6=classpath
org.ow2.asm:asm:9.6=classpath
org.slf4j:slf4j-api:1.7.30=classpath
org.tensorflow:tensorflow-lite-metadata:0.1.0-rc2=classpath
empty=
