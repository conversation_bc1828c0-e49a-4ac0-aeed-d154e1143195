# Showcase complex end-to-end use cases

This folder houses the companion notebooks for the "Build with AI" video series, along with code examples from mobile app demos. Here you'll find practical examples and ready-to-run code to help you bring AI-powered features to life.

## Accompanying notebooks for the Build with AI video series
| Folder                                                      | Description |
| ----------------------------------------------------------- | ----------- |
| [Business email assistant](business-email-assistant/) | [Business Email AI Assistant with <PERSON>](https://www.youtube.com/watch?v=YxhzozLH1Dk)<br>This project tackles the specific problem of extracting order information from emails to a bakery into structured data, so it can be quickly added to an order handling system. |
| [Personal code assistant](personal-code-assistant/)   | [Personal AI Code Assistant with Gemma](https://www.youtube.com/watch?v=Zpo7UTvg_9E)<br>This project shows you how to set up your own web service for hosting <PERSON> and connecting it to a Microsoft Visual Studio Code extension, to make using the model more convenient while coding. |
| [Spoken language tasks](spoken-language-tasks/)       | [Spoken Language AI Assistant with <PERSON>](https://www.youtube.com/watch?v=M4HGJehH4r0)<br>Learn how to tune a model to perform specific tasks in a specific language, without requiring extensive data or training time. |

## Mobile App Demo
| Folder                                                      | Description |
| ----------------------------------------------------------- | ----------- |
| [Gemma on Android](Gemma-on-Android/)         | Android app to deploy fine-tuned Gemma-2B-it model using MediaPipe LLM Inference API. |
| [PaliGemma on Android](PaliGemma-on-Android/) | Inference PaliGemma on Android using Hugging Face and Gradio Client API for tasks like zero-shot object detection, image captioning, and visual question-answering. |

## Web App Demo
| Folder                                                      | Description |
| ----------------------------------------------------------- | ----------- |
| [PaliGemma 2 on Web](PaliGemma2-on-Web/)         | Inference PaliGemma 2 on the web using its ONNX weights and Transformers.js for tasks like object detection, image captioning, OCR, and visual Q&A. |


## Cloud Run Demo
| Folder                                                      | Description |
| ----------------------------------------------------------- | ----------- |
| [Gemma on Cloud run](Gemma-on-Cloudrun/)         | This folder contains a Dockerfile for building and deploying a Gemma-powered application on Cloud Run, featuring Gemini APIs. |

