"""
Script de teste para a exportação para PDF.
"""

import os
from modules.pdf_export import PDFExporter

def main():
    """Função principal para testar a exportação para PDF"""
    # Texto de exemplo em Markdown
    markdown_text = """# Título do Artigo

## Introdução

Este é um exemplo de artigo acadêmico gerado para testar a exportação para PDF.

### Objetivos

* Testar a exportação para PDF
* Verificar a formatação ABNT
* Garantir que não há erros

## Desenvolvimento

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget
aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.

> Esta é uma citação de exemplo que deve ser formatada de acordo com as normas ABNT.

### Resultados

Os resultados obtidos foram satisfatórios e demonstram a eficácia do método proposto.

## Conclusão

A exportação para PDF está funcionando corretamente e a formatação ABNT está sendo
aplicada conforme esperado.

## Referências Bibliográficas

1. SOBRENOME, Nome. **Título do Livro**. Cidade: Editora, 2023.
2. SOBRENOME, Nome; SOBRENOME, Nome. **Título do Artigo**. Nome da Revista, v. 1, n. 1, p. 1-10, 2023.
"""

    # Gerar o PDF
    print("Gerando PDF...")
    pdf_bytes = PDFExporter.markdown_to_pdf(markdown_text, title="Teste de Exportação para PDF")
    
    # Salvar o PDF
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, "teste.pdf")
    
    with open(output_path, "wb") as f:
        f.write(pdf_bytes)
    
    print(f"PDF gerado com sucesso: {output_path}")

if __name__ == "__main__":
    main()
