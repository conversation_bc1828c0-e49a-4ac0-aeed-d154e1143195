{"version": "6_1_0", "md.comp.outlined-segmented-button.container.height": 40.0, "md.comp.outlined-segmented-button.disabled.icon.color": "onSurface", "md.comp.outlined-segmented-button.disabled.icon.opacity": 0.38, "md.comp.outlined-segmented-button.disabled.label-text.color": "onSurface", "md.comp.outlined-segmented-button.disabled.label-text.opacity": 0.38, "md.comp.outlined-segmented-button.disabled.outline.color": "onSurface", "md.comp.outlined-segmented-button.disabled.outline.opacity": 0.12, "md.comp.outlined-segmented-button.focus.indicator.color": "secondary", "md.comp.outlined-segmented-button.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.outlined-segmented-button.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.outlined-segmented-button.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.outlined-segmented-button.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.outlined-segmented-button.label-text.text-style": "labelLarge", "md.comp.outlined-segmented-button.outline.color": "outline", "md.comp.outlined-segmented-button.outline.width": 1.0, "md.comp.outlined-segmented-button.pressed.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.outlined-segmented-button.selected.container.color": "secondaryContainer", "md.comp.outlined-segmented-button.selected.focus.icon.color": "onSecondaryContainer", "md.comp.outlined-segmented-button.selected.focus.label-text.color": "onSecondaryContainer", "md.comp.outlined-segmented-button.selected.focus.state-layer.color": "onSecondaryContainer", "md.comp.outlined-segmented-button.selected.hover.icon.color": "onSecondaryContainer", "md.comp.outlined-segmented-button.selected.hover.label-text.color": "onSecondaryContainer", "md.comp.outlined-segmented-button.selected.hover.state-layer.color": "onSecondaryContainer", "md.comp.outlined-segmented-button.selected.label-text.color": "onSecondaryContainer", "md.comp.outlined-segmented-button.selected.pressed.icon.color": "onSecondaryContainer", "md.comp.outlined-segmented-button.selected.pressed.label-text.color": "onSecondaryContainer", "md.comp.outlined-segmented-button.selected.pressed.state-layer.color": "onSecondaryContainer", "md.comp.outlined-segmented-button.selected.with-icon.icon.color": "onSecondaryContainer", "md.comp.outlined-segmented-button.shape": "md.sys.shape.corner.full", "md.comp.outlined-segmented-button.unselected.focus.icon.color": "onSurface", "md.comp.outlined-segmented-button.unselected.focus.label-text.color": "onSurface", "md.comp.outlined-segmented-button.unselected.focus.state-layer.color": "onSurface", "md.comp.outlined-segmented-button.unselected.hover.icon.color": "onSurface", "md.comp.outlined-segmented-button.unselected.hover.label-text.color": "onSurface", "md.comp.outlined-segmented-button.unselected.hover.state-layer.color": "onSurface", "md.comp.outlined-segmented-button.unselected.label-text.color": "onSurface", "md.comp.outlined-segmented-button.unselected.pressed.icon.color": "onSurface", "md.comp.outlined-segmented-button.unselected.pressed.label-text.color": "onSurface", "md.comp.outlined-segmented-button.unselected.pressed.state-layer.color": "onSurface", "md.comp.outlined-segmented-button.unselected.with-icon.icon.color": "onSurface", "md.comp.outlined-segmented-button.with-icon.icon.size": 18.0}