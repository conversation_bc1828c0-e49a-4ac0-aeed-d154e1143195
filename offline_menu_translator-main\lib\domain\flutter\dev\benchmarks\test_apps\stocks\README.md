# Stocks

Demo app for the Material Design widgets and other features provided by Flutter.

## Building

You can follow these instructions to build the stocks app
and install it onto your device.

### Prerequisites

If you are new to Flutter, please first follow
the [Flutter Setup](https://flutter.dev/setup/) guide.

### Building and installing the stocks demo app

* `cd $FLUTTER_ROOT/examples/stocks`
* `flutter pub get`
* `flutter run --release`

The `flutter run --release` command both builds and installs the Flutter app.

## Internationalization

This app has been internationalized (just enough to show how it's
done). It's an example of how one can do so with the gen_l10n tool.

See [regenerate.md](lib/i18n/regenerate.md) for an explanation for how
the tool is used to generate localizations for this app.

## Icon

The icon was created using Android Asset Studio:
https://romannurik.github.io/AndroidAssetStudio/icons-launcher.html#foreground.type=image&foreground.space.trim=0&foreground.space.pad=0&foreColor=607d8b%2C0&crop=0&backgroundShape=square&backColor=fff%2C100&effects=none

From this clipart:
https://openclipart.org/detail/30403/tango-go-up
Which is in the public domain.
