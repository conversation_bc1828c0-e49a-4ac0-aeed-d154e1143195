# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/common/config.gni")
import("//flutter/shell/config.gni")

source_set("context") {
  sources = [
    "android_context.cc",
    "android_context.h",
  ]

  public_configs = [ "//flutter:config" ]

  deps = [
    "//flutter/common",
    "//flutter/fml",
    "//flutter/impeller/renderer",
    "//flutter/skia",
  ]

  if (shell_enable_vulkan) {
    deps += [ "//flutter/vulkan" ]
  }
}
