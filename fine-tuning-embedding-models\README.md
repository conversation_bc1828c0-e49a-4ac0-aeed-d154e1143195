# Finetuning Embedding Models using Sentence Transformers

Learn how you can easily fine-tune an embedding model like `bert-base-uncased` from scratch using sentence transformers using datasets like `triplets` or `SNLI`

### [Read the blog!](https://www.ionio.ai/blog/fine-tuning-embedding-models-using-sentence-transformers-code-included)

## 💡 Final Results

Here is the comparison between base model and fine-tuned model with SNLI dataset

![](https://assets-global.website-files.com/62528d398a42420e66390ef9/662a156c41950a8b603b6104_image9.png)

Here is the comparison between base model and fine-tuned model with triplet dataset

![](https://assets-global.website-files.com/62528d398a42420e66390ef9/662a1582f2c08187d7fb93b7_image10.png)

## 🚀 Getting started

### Prerequisites

- Python and anaconda installed on your machine
- A little bit knowledge about LLMs and embedding models

### How to run?

- Clone the repository
- Open any jupyter notebook from repository and follow the instructions written in it
- Select your existing python environment or create one using anaconda
- Run the code
