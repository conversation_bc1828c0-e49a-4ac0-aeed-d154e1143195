# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//build/compiled_action.gni")
import("//flutter/impeller/tools/impeller.gni")
import("//flutter/testing/testing.gni")

if (enable_unittests) {
  test_shaders = [
    "blue_green_sampler.frag",
    "children_and_uniforms.frag",
    "functions.frag",
    "no_builtin_redefinition.frag",
    "no_uniforms.frag",
    "simple.frag",
    "uniforms.frag",
    "uniforms_sorted.frag",
    "uniform_ordering.frag",
    "uniform_arrays.frag",
    "filter_shader.frag",
    "missing_size.frag",
    "missing_texture.frag",
    "vec3_uniform.frag",
  ]

  group("general_shaders") {
    testonly = true
    deps = [ ":fixtures" ]
  }

  impellerc("compile_general_shaders") {
    shaders = test_shaders
    shader_target_flags = [ "--sksl" ]
    intermediates_subdir = "iplr"
    sl_file_extension = "iplr"
    iplr = true
  }

  test_fixtures("fixtures") {
    deps = [ ":compile_general_shaders" ]
    fixtures = get_target_outputs(":compile_general_shaders")
    dest = "$root_gen_dir/flutter/lib/ui"
  }
}
