{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to Automate Data Wrangling with AI\n", "\n", "In this tutorial, we will learn how to automate data wrangling with using the Data Wrangling Agent. The Data Wrangling Agent is a tool that uses AI to automate the data wrangling process. It is a powerful tool that can save you time and effort when working with many large datasets.\n", "\n", "### Want To Become A Full-Stack Generative AI Data Scientist?\n", "\n", "![Generative AI Data Scientist](../img/become_a_generative_ai_data_scientist.jpg)\n", "\n", "I teach Generative AI Data Science to help you build AI-powered data science apps. [**Register for my next Generative AI for Data Scientists workshop here.**](https://learn.business-science.io/ai-register)\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "bat"}}, "source": ["# Table of Contents\n", "\n", "1. [How to Automate Data Wrangling with AI](#how-to-automate-data-wrangling-with-ai)\n", "2. [Load Libraries](#load-libraries)\n", "3. [Setup AI and Logging](#setup-ai-and-logging)\n", "4. [Load a Dataset](#load-a-dataset)\n", "5. [Create The Agent](#create-the-agent)\n", "6. [Response](#response)\n", "    1. [The data wrangling steps that were performed](#the-data-wrangling-steps-that-were-performed)\n", "    2. [Data Wrangling Function](#data-wrangling-function)\n", "    3. [Wrangled Data As Pandas Data Frame](#wrangled-data-as-pandas-data-frame)\n", "7. [Free Generative AI Data Science Workshop](#free-generative-ai-data-science-workshop)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load Libraries"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# * Libraries\n", "\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "import yaml\n", "import pandas as pd\n", "from pprint import pprint\n", "\n", "from ai_data_science_team.agents import DataWranglingAgent"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setup AI and Logging\n", "\n", "This section of code sets up the LLM inputs and the logging information. Logging is used to store AI-generated code and files during the AI Data Science Teams processing of files. \n", "\n", "*Important Note:* This example uses OpenAI's API. But any LLM can be used such as Anthropic or local LLMs with Ollama."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatOpenAI(client=<openai.resources.chat.completions.Completions object at 0x7fc410c1cd30>, async_client=<openai.resources.chat.completions.AsyncCompletions object at 0x7fc410c1e0e0>, root_client=<openai.OpenAI object at 0x7fc3f926cd60>, root_async_client=<openai.AsyncOpenAI object at 0x7fc410c1ccd0>, model_name='gpt-4o-mini', model_kwargs={}, openai_api_key=SecretStr('**********'))"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# * Setup\n", "\n", "MODEL    = \"gpt-4o-mini\"\n", "LOG      = True\n", "LOG_PATH = os.path.join(os.getcwd(), \"logs/\")\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = yaml.safe_load(open('../credentials.yml'))['openai']\n", "\n", "llm = ChatOpenAI(model = MODEL)\n", "\n", "llm\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load a Dataset\n", "\n", "Imagine that we have a directory full of datasets that need to be merged. We can use the AI to generate the code to merge the datasets."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Simulate multiple CSV files with a common ID that can be joined\n", "\n", "df1 = pd.DataFrame({\n", "    \"ID\": [1, 2, 3, 4],\n", "    \"Name\": [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\"]\n", "})\n", "\n", "df2 = pd.DataFrame({\n", "    \"ID\": [1, 2, 3, 4],\n", "    \"Age\": [25, 30, 35, 40]\n", "})\n", "\n", "df3 = pd.DataFrame({\n", "    \"ID\": [1, 2, 3, 4],\n", "    \"Education\": [\"Bachelors\", \"Masters\", \"PhD\", \"MBA\"]\n", "})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Create The Agent\n", "\n", "Run this code to create the agent with `DataWranglingAgent()`."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<ai_data_science_team.agents.data_wrangling_agent.DataWranglingAgent object at 0x7fc410ab6230>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["data_wrangling_agent = DataWranglingAgent(\n", "    model = llm, \n", "    log=LOG, \n", "    log_path=LOG_PATH\n", ")\n", "\n", "data_wrangling_agent"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The main inputs to the Data Wrangling Agent are:\n", "\n", "- **user_instructions**: The agent will use these comments modify the data wrangling operations. \n", "- **data_raw**: The raw data to have data wrangling performed.\n", "  - **Dictionary**: Used for a singled dataset to be wrangled. \n", "  - **List of Dictionaries**: Used for multiple datasets that need to be merged or concatenated.\n", "- **max_retries**: Used to limit the number of attempts to fix the python code generated by the agent. Set this to 3 to limit to 3 attempts. \n", "- **retry_count**: Set this to 0. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---DATA WRANGLING AGENT----\n", "    * RECOMMEND WRANGLING STEPS\n", "    * CREATE DATA WRANGLER CODE\n", "      File saved to: /Users/<USER>/Desktop/course_code/ai-data-science-team/logs/data_wrangler.py\n", "    * EXECUTING AGENT CODE\n", "    * EXPLAIN AGENT CODE\n"]}], "source": ["data_wrangling_agent.invoke_agent(\n", "    data_raw= [df1, df2, df3],\n", "    user_instructions=\"Merge the data frames on the ID column\", \n", "    max_retries=3, \n", "    retry_count=0\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Response\n", "\n", "The response produced contains everything we need to understand the data wrangling decisions made and get the wrangled dataset. "]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["['messages',\n", " 'user_instructions',\n", " 'recommended_steps',\n", " 'data_raw',\n", " 'data_wrangled',\n", " 'all_datasets_summary',\n", " 'data_wrangler_function',\n", " 'data_wrangler_function_path',\n", " 'data_wrangler_function_name',\n", " 'data_wrangler_error',\n", " 'max_retries',\n", " 'retry_count']"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["response = data_wrangling_agent.get_response()\n", "\n", "list(response.keys())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Data Wrangled"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>Education</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>John</td>\n", "      <td>25</td>\n", "      <td>Bachelors</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Jane</td>\n", "      <td>30</td>\n", "      <td>Masters</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td><PERSON></td>\n", "      <td>35</td>\n", "      <td>PhD</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>Jill</td>\n", "      <td>40</td>\n", "      <td>MBA</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   ID  Name  Age  Education\n", "0   1  John   25  Bachelors\n", "1   2  <PERSON>   30    Masters\n", "2   3   <PERSON>   35        PhD\n", "3   4  Jill   40        MBA"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["data_wrangling_agent.get_data_wrangled()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Data Wrangling Function"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/markdown": ["```python\n", "# Disclaimer: This function was generated by AI. Please review before using.\n", "# Agent Name: data_wrangling_agent\n", "# Time Created: 2025-01-10 08:15:33\n", "\n", "def data_wrangler(data_list):\n", "    import pandas as pd\n", "    '''\n", "    Wrangle the data provided in data_list.\n", "    \n", "    data_list: A list of one or more pandas data frames containing the raw data to be wrangled.\n", "    '''\n", "\n", "    # Ensure that data_list is a list, if not, convert it to a list\n", "    if not isinstance(data_list, list):\n", "        data_list = [data_list]\n", "    \n", "    # Assume that the first three datasets in the list are dataset_1, dataset_2, and dataset_3\n", "    dataset_1 = data_list[0]\n", "    dataset_2 = data_list[1]\n", "    dataset_3 = data_list[2]\n", "    \n", "    # Step 2: Merge dataset_1 and dataset_2 on the 'ID' column\n", "    merged_1_2 = pd.merge(dataset_1, dataset_2, on='ID')\n", "    \n", "    # Step 3: Merge the result with dataset_3 on the 'ID' column\n", "    final_merged = pd.merge(merged_1_2, dataset_3, on='ID')\n", "    \n", "    # Step 4: Check the final DataFrame by returning it\n", "    return final_merged\n", "```"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["data_wrangling_agent.get_data_wrangler_function(markdown=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Want To Become A Full-Stack Generative AI Data Scientist?\n", "\n", "![Generative AI Data Scientist](../img/become_a_generative_ai_data_scientist.jpg)\n", "\n", "I teach Generative AI Data Science to help you build AI-powered data science apps. [**Register for my next Generative AI for Data Scientists workshop here.**](https://learn.business-science.io/ai-register)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "ds4b_301p_dev", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}