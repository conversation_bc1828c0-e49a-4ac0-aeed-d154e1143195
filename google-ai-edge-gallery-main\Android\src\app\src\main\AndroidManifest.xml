<?xml version="1.0" encoding="utf-8"?>
<!--
 Copyright 2025 Google LLC

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.google.ai.edge.gallery"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-sdk
        android:minSdkVersion="26"
        android:compileSdkVersion ="35"
        android:targetSdkVersion="35" />

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC"/>
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <application
        android:name="com.google.ai.edge.gallery.GalleryApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="Edge Gallery"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/Theme.Gallery"
        tools:targetApi="31">
        <!--
          android:configChanges="uiMode" tells the system don't destroy and
          recreate the activity when UI mode changes (e.g. setting dark mode).
          Instead, just recompose the view.
         -->
        <activity
            android:name="com.google.ai.edge.gallery.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.Gallery.SplashScreen"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize"
            android:configChanges="uiMode"
            tools:ignore="DiscouragedApi,LockedOrientationActivity">
            <!-- This is for putting the app into launcher -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <!-- This is for deep linking -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="com.google.ai.edge.gallery" />
            </intent-filter>
        </activity>

        <!-- Set themes for activities that are used for viewing open source licenses -->
        <activity
            android:name="com.google.android.gms.oss.licenses.OssLicensesMenuActivity"
            android:exported="true"
            android:theme="@style/Theme.Gallery.OssLicenses" />
        <activity
            android:name="com.google.android.gms.oss.licenses.OssLicensesActivity"
            android:exported="true"
            android:theme="@style/Theme.Gallery.OssLicenses" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:foregroundServiceType="dataSync"
            android:exported="false"
            tools:node="merge">
        </service>

        <!-- For Firebase Analytics. -->
        <receiver
            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
            android:enabled="true"
            android:exported="false" />
        <service android:name="com.google.android.gms.measurement.AppMeasurementService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />
    </application>

</manifest>
