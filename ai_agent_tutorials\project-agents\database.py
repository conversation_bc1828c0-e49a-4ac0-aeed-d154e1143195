import sqlite3
from datetime import datetime
from typing import Dict, Any, List, Optional
import json

class ArticleDatabase:
    def __init__(self, db_path: str = "articles.db"):
        self.db_path = db_path
        self._initialize_db()

    def _initialize_db(self):
        """Initialize the database with required tables."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create articles table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS articles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    topic TEXT NOT NULL,
                    content TEXT NOT NULL,
                    model_used TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metrics TEXT,
                    is_favorite BOOLEAN DEFAULT 0
                )
            """)
            
            # Create search_terms table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS search_terms (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    article_id INTEGER,
                    term TEXT NOT NULL,
                    FOREIGN KEY (article_id) REFERENCES articles(id)
                )
            """)
            
            # Create sources table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sources (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    article_id INTEGER,
                    url TEXT NOT NULL,
                    domain TEXT NOT NULL,
                    FOREIGN KEY (article_id) REFERENCES articles(id)
                )
            """)
            
            conn.commit()

    def save_article(
        self,
        topic: str,
        content: str,
        model_used: str,
        metrics: Optional[Dict[str, Any]] = None,
        search_terms: Optional[List[str]] = None,
        sources: Optional[List[Dict[str, str]]] = None
    ) -> int:
        """Save a generated article to the database."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Insert article
            cursor.execute("""
                INSERT INTO articles (topic, content, model_used, metrics)
                VALUES (?, ?, ?, ?)
            """, (topic, content, model_used, json.dumps(metrics) if metrics else None))
            
            article_id = cursor.lastrowid
            
            # Insert search terms if provided
            if search_terms:
                cursor.executemany("""
                    INSERT INTO search_terms (article_id, term)
                    VALUES (?, ?)
                """, [(article_id, term) for term in search_terms])
            
            # Insert sources if provided
            if sources:
                cursor.executemany("""
                    INSERT INTO sources (article_id, url, domain)
                    VALUES (?, ?, ?)
                """, [
                    (article_id, source['url'], source.get('domain', ''))
                    for source in sources
                ])
            
            conn.commit()
            return article_id

    def get_article(self, article_id: int) -> Optional[Dict[str, Any]]:
        """Retrieve an article by ID."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM articles WHERE id = ?
            """, (article_id,))
            
            article = cursor.fetchone()
            
            if article:
                return dict(article)
            return None

    def get_recent_articles(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get most recent articles."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, topic, model_used, created_at, is_favorite
                FROM articles
                ORDER BY created_at DESC
                LIMIT ?
            """, (limit,))
            
            return [dict(row) for row in cursor.fetchall()]

    def toggle_favorite(self, article_id: int) -> bool:
        """Toggle favorite status of an article."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE articles
                SET is_favorite = NOT is_favorite
                WHERE id = ?
            """, (article_id,))
            
            conn.commit()
            return cursor.rowcount > 0

    def get_article_metrics(self, article_id: int) -> Optional[Dict[str, Any]]:
        """Get metrics for a specific article."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT metrics FROM articles WHERE id = ?
            """, (article_id,))
            
            result = cursor.fetchone()
            if result and result[0]:
                return json.loads(result[0])
            return None