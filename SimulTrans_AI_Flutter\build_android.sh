#!/bin/bash

echo ""
echo "========================================"
echo "   SimulTrans AI - Build Android"
echo "========================================"
echo ""

# Verificar se Flutter está instalado
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter não encontrado! Instale o Flutter primeiro."
    echo "💡 https://flutter.dev/docs/get-started/install"
    exit 1
fi

echo "✅ Flutter encontrado!"
echo ""

# Verificar arquivo .env
if [ ! -f ".env" ]; then
    echo "⚠️  Arquivo .env não encontrado!"
    echo "💡 Crie o arquivo .env com sua GEMINI_API_KEY"
    echo ""
    echo "Exemplo:"
    echo "GEMINI_API_KEY=sua_chave_aqui"
    echo "GEMINI_MODEL_NAME=gemini-2.5-flash"
    echo "USE_GEMINI_API=true"
    exit 1
fi

echo "✅ Arquivo .env encontrado!"
echo ""

# Limpar builds anteriores
echo "🧹 Limpando builds anteriores..."
flutter clean
if [ $? -ne 0 ]; then
    echo "❌ Erro ao limpar projeto"
    exit 1
fi

echo "✅ Projeto limpo!"
echo ""

# Instalar dependências
echo "📦 Instalando dependências..."
flutter pub get
if [ $? -ne 0 ]; then
    echo "❌ Erro ao instalar dependências"
    exit 1
fi

echo "✅ Dependências instaladas!"
echo ""

# Verificar dispositivos Android
echo "📱 Verificando dispositivos Android..."
android_devices=$(flutter devices | grep "android")
if [ -z "$android_devices" ]; then
    echo "⚠️  Nenhum dispositivo Android encontrado!"
    echo ""
    echo "💡 Opções:"
    echo "1. Conecte um dispositivo Android via USB"
    echo "2. Inicie um emulador Android"
    echo "3. Execute: flutter emulators --launch nome_do_emulador"
    echo ""
    echo "Pressione Enter para continuar mesmo assim..."
    read
fi

echo ""
echo "🚀 Iniciando build para Android..."
echo ""

# Menu de opções
echo "Escolha o tipo de build:"
echo "1. Debug (desenvolvimento)"
echo "2. Release (otimizado)"
echo "3. Profile (análise de performance)"
echo ""
read -p "Digite sua escolha (1-3): " choice

case $choice in
    1)
        echo "🔧 Executando em modo DEBUG..."
        flutter run -d android --debug
        ;;
    2)
        echo "⚡ Executando em modo RELEASE..."
        flutter run -d android --release
        ;;
    3)
        echo "📊 Executando em modo PROFILE..."
        flutter run -d android --profile
        ;;
    *)
        echo "❌ Opção inválida! Executando em modo DEBUG..."
        flutter run -d android --debug
        ;;
esac

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ Erro ao executar no Android!"
    echo ""
    echo "💡 Possíveis soluções:"
    echo "1. Verifique se o dispositivo está conectado"
    echo "2. Verifique se o USB debugging está habilitado"
    echo "3. Execute: flutter doctor para diagnóstico"
    echo "4. Verifique se a GEMINI_API_KEY está configurada"
    echo ""
    exit 1
fi

echo ""
echo "✅ SimulTrans AI executando no Android!"
echo "🎉 Aproveite a tradução com Google Gemini AI!"
echo ""
