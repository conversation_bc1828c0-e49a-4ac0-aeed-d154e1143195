# This file was autogenerated by uv via the following command:
#    ./generate_requirements.sh
agno==1.1.6
    # via -r cookbook/examples/apps/tic_tac_toe/requirements.in
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anthropic==0.47.1
    # via -r cookbook/examples/apps/tic_tac_toe/requirements.in
anyio==4.8.0
    # via
    #   anthropic
    #   groq
    #   httpx
    #   openai
attrs==25.1.0
    # via
    #   jsonschema
    #   referencing
blinker==1.9.0
    # via streamlit
build==1.2.2.post1
    # via pip-tools
cachetools==5.5.2
    # via
    #   google-auth
    #   streamlit
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   pip-tools
    #   streamlit
    #   typer
distro==1.9.0
    # via
    #   anthropic
    #   groq
    #   openai
docstring-parser==0.16
    # via agno
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via
    #   agno
    #   streamlit
google-auth==2.38.0
    # via google-genai
google-genai==1.3.0
    # via -r cookbook/examples/apps/tic_tac_toe/requirements.in
groq==0.18.0
    # via -r cookbook/examples/apps/tic_tac_toe/requirements.in
h11==0.14.0
    # via httpcore
httpcore==1.0.7
    # via httpx
httpx==0.28.1
    # via
    #   agno
    #   anthropic
    #   google-genai
    #   groq
    #   ollama
    #   openai
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
jinja2==3.1.5
    # via
    #   altair
    #   pydeck
jiter==0.8.2
    # via
    #   anthropic
    #   openai
jsonschema==4.23.0
    # via altair
jsonschema-specifications==2024.10.1
    # via jsonschema
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via jinja2
mdurl==0.1.2
    # via markdown-it-py
narwhals==1.28.0
    # via altair
nest-asyncio==1.6.0
    # via -r cookbook/examples/apps/tic_tac_toe/requirements.in
numpy==2.2.3
    # via
    #   pandas
    #   pydeck
    #   streamlit
ollama==0.4.7
    # via -r cookbook/examples/apps/tic_tac_toe/requirements.in
openai==1.64.0
    # via -r cookbook/examples/apps/tic_tac_toe/requirements.in
packaging==24.2
    # via
    #   altair
    #   build
    #   streamlit
pandas==2.2.3
    # via streamlit
pathlib==1.0.1
    # via -r cookbook/examples/apps/tic_tac_toe/requirements.in
pillow==11.1.0
    # via
    #   -r cookbook/examples/apps/tic_tac_toe/requirements.in
    #   streamlit
pip==25.0.1
    # via pip-tools
pip-tools==7.4.1
    # via -r cookbook/examples/apps/tic_tac_toe/requirements.in
protobuf==5.29.3
    # via streamlit
pyarrow==19.0.1
    # via streamlit
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.1
    # via google-auth
pydantic==2.10.6
    # via
    #   agno
    #   anthropic
    #   google-genai
    #   groq
    #   ollama
    #   openai
    #   pydantic-settings
pydantic-core==2.27.2
    # via pydantic
pydantic-settings==2.8.0
    # via agno
pydeck==0.9.1
    # via streamlit
pygments==2.19.1
    # via rich
pyproject-hooks==1.2.0
    # via
    #   build
    #   pip-tools
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.0.1
    # via
    #   -r cookbook/examples/apps/tic_tac_toe/requirements.in
    #   agno
    #   pydantic-settings
python-multipart==0.0.20
    # via agno
pytz==2025.1
    # via pandas
pyyaml==6.0.2
    # via agno
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
requests==2.32.3
    # via
    #   google-genai
    #   streamlit
rich==13.9.4
    # via
    #   -r cookbook/examples/apps/tic_tac_toe/requirements.in
    #   agno
    #   streamlit
    #   typer
rpds-py==0.23.1
    # via
    #   jsonschema
    #   referencing
rsa==4.9
    # via google-auth
setuptools==75.8.0
    # via pip-tools
shellingham==1.5.4
    # via typer
six==1.17.0
    # via python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   groq
    #   openai
streamlit==1.42.2
    # via -r cookbook/examples/apps/tic_tac_toe/requirements.in
tenacity==9.0.0
    # via streamlit
toml==0.10.2
    # via streamlit
tomli==2.2.1
    # via agno
tornado==6.4.2
    # via streamlit
tqdm==4.67.1
    # via openai
typer==0.15.1
    # via agno
typing-extensions==4.12.2
    # via
    #   agno
    #   altair
    #   anthropic
    #   anyio
    #   google-genai
    #   groq
    #   openai
    #   pydantic
    #   pydantic-core
    #   referencing
    #   streamlit
    #   typer
tzdata==2025.1
    # via pandas
urllib3==2.3.0
    # via requests
websockets==14.2
    # via google-genai
wheel==0.45.1
    # via pip-tools
