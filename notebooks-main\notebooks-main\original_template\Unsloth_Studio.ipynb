{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "Nz4odU5XYDDw"}, "outputs": [], "source": ["# @title ↙️ Press ▶ to start 🦥 Unsloth Studio Chat for Llama-3.1 8b\n", "\n", "# Unsloth Studio\n", "# Copyright (C) 2024-present the Unsloth AI team. All rights reserved.\n", "\n", "# This program is free software: you can redistribute it and/or modify\n", "# it under the terms of the GNU Affero General Public License as published\n", "# by the Free Software Foundation, either version 3 of the License, or\n", "# (at your option) any later version.\n", "\n", "# This program is distributed in the hope that it will be useful,\n", "# but WITHOUT ANY WARRANTY; without even the implied warranty of\n", "# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n", "# GNU Affero General Public License for more details.\n", "\n", "# You should have received a copy of the GNU Affero General Public License\n", "# along with this program.  If not, see <https://www.gnu.org/licenses/>.\n", "!git clone https://github.com/unslothai/studio > /dev/null 2>&1\n", "with open(\"studio/unsloth_studio/chat.py\", \"r\") as chat_module:\n", "    code = chat_module.read()\n", "exec(code)"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}