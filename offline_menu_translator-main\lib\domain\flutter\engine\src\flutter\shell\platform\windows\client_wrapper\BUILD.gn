# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/build/zip_bundle.gni")
import("//flutter/shell/platform/common/client_wrapper/publish.gni")
import("//flutter/testing/testing.gni")

_wrapper_includes = [
  "include/flutter/dart_project.h",
  "include/flutter/flutter_engine.h",
  "include/flutter/flutter_view_controller.h",
  "include/flutter/flutter_view.h",
  "include/flutter/plugin_registrar_windows.h",
]

_wrapper_sources = [
  "flutter_engine.cc",
  "flutter_view_controller.cc",
]

# This code will be merged into .../common/client_wrapper for client use,
# so uses header paths that assume the merged state. Include the header
# directories of the core wrapper files so these includes will work.
config("relative_core_wrapper_headers") {
  include_dirs = [
    "//flutter/shell/platform/common/client_wrapper",
    "//flutter/shell/platform/common/client_wrapper/include/flutter",
  ]
}

# Windows client wrapper build for internal use by the shell implementation.
source_set("client_wrapper_windows") {
  sources = _wrapper_sources
  public = _wrapper_includes

  deps = [
    "//flutter/shell/platform/common:common_cpp_library_headers",
    "//flutter/shell/platform/common/client_wrapper:client_wrapper",
    "//flutter/shell/platform/windows:flutter_windows_headers",
  ]

  configs +=
      [ "//flutter/shell/platform/common:desktop_library_implementation" ]

  public_configs = [
    ":relative_core_wrapper_headers",
    "//flutter/shell/platform/common:relative_flutter_library_headers",
    "//flutter/shell/platform/windows:relative_flutter_windows_headers",
  ]
}

# Copies the Windows client wrapper code to the output directory.
publish_client_wrapper_extension("publish_wrapper_windows") {
  public = _wrapper_includes
  sources = _wrapper_sources
}

source_set("client_wrapper_library_stubs_windows") {
  sources = [
    "testing/stub_flutter_windows_api.cc",
    "testing/stub_flutter_windows_api.h",
  ]

  defines = [ "FLUTTER_DESKTOP_LIBRARY" ]

  public_deps = [ "//flutter/shell/platform/windows:flutter_windows_headers" ]
}

test_fixtures("client_wrapper_windows_fixtures") {
  fixtures = []
}

executable("client_wrapper_windows_unittests") {
  testonly = true

  sources = [
    "dart_project_unittests.cc",
    "flutter_engine_unittests.cc",
    "flutter_view_controller_unittests.cc",
    "flutter_view_unittests.cc",
    "plugin_registrar_windows_unittests.cc",
  ]

  # Set embedder.h to export, not import, since the stubs are locally defined.
  defines = [ "FLUTTER_DESKTOP_LIBRARY" ]

  deps = [
    ":client_wrapper_library_stubs_windows",
    ":client_wrapper_windows",
    ":client_wrapper_windows_fixtures",
    "//flutter/shell/platform/common/client_wrapper:client_wrapper_library_stubs",
    "//flutter/testing",

    # TODO(chunhtai): Consider refactoring flutter_root/testing so that there's a testing
    # target that doesn't require a Dart runtime to be linked in.
    # https://github.com/flutter/flutter/issues/41414.
    "$dart_src/runtime:libdart_jit",
  ]
}

client_wrapper_file_archive_list = [
  "core_implementations.cc",
  "engine_method_result.cc",
  "plugin_registrar.cc",
  "README",
  "standard_codec.cc",
]

win_client_wrapper_file_archive_list = [
  "flutter_engine.cc",
  "flutter_view_controller.cc",
]

zip_bundle("client_wrapper_archive") {
  output = "$full_target_platform_name/flutter-cpp-client-wrapper.zip"
  deps = [
    ":client_wrapper_windows",
    ":publish_wrapper_windows",
    "//flutter/shell/platform/common/client_wrapper:client_wrapper",
  ]
  tmp_files = []
  foreach(source_file, client_wrapper_file_archive_list) {
    tmp_files += [
      {
        source = "//flutter/shell/platform/common/client_wrapper/$source_file"
        destination = "cpp_client_wrapper/$source_file"
      },
    ]
  }

  # Windows specific source code files
  foreach(source_file, win_client_wrapper_file_archive_list) {
    tmp_files += [
      {
        source = "//flutter/shell/platform/windows/client_wrapper/$source_file"
        destination = "cpp_client_wrapper/$source_file"
      },
    ]
  }

  # Headers
  headers = core_cpp_client_wrapper_includes +
            core_cpp_client_wrapper_internal_headers
  foreach(header, headers) {
    rebased_path =
        rebase_path(header, "//flutter/shell/platform/common/client_wrapper")
    tmp_files += [
      {
        source = header
        destination = "cpp_client_wrapper/$rebased_path"
      },
    ]
  }

  # Wrapper includes
  foreach(header, _wrapper_includes) {
    rebased_path =
        rebase_path("//flutter/shell/platform/windows/client_wrapper/$header",
                    "//flutter/shell/platform/windows/client_wrapper")
    tmp_files += [
      {
        source = header
        destination = "cpp_client_wrapper/$rebased_path"
      },
    ]
  }
  files = tmp_files
}
