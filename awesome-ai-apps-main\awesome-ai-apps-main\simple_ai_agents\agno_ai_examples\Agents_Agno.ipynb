{"cells": [{"cell_type": "markdown", "metadata": {"id": "WX9g8Pv7X827"}, "source": ["# Agno Agents with Nebius AI and Couchbase\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "Sy3qDT4IX828"}, "source": ["In this notebook, we'll explore:\n", "1. Setting up a simple agent with web search capability\n", "2. Creating an agent with a knowledge base for specialized domains\n", "3. Building multi-agent systems where specialized agents work together"]}, {"cell_type": "markdown", "metadata": {"id": "fDDNzpIuX829"}, "source": ["## Install required packages for this notebook\n", "- agno: The agent framework we'll be using\n", "- duckduckgo-search: For web search capabilities\n", "- pypdf: For processing PDF documents  \n", "- Couchbase: For vector storage\n", "- yfinance: For financial data retrieval"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "TeFlV5AOX829"}, "outputs": [], "source": ["!pip install -qU agno duckduckgo-search pypdf couchbase yfinance openai"]}, {"cell_type": "markdown", "metadata": {"id": "1-7Af5QQX82-"}, "source": ["## Simple Agent\n", "\n", "Below we create a basic agent that can search the web to answer questions. This agent uses:\n", "- meta-llama/Llama-3.3-70B-Instruct\n", "- DuckDuckGo search as a tool to retrieve information from the web\n", "\n", "When we run this agent with a question about the NBA, it will search for recent information and provide an answer."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QpYHduUAX82-", "outputId": "e03bd3bc-826c-4265-bf6c-f462bacb1029", "colab": {"base_uri": "https://localhost:8080/", "height": 337, "referenced_widgets": ["672945fe311840648fb87576c950c83c", "b912414579114ca09c3c3eaf043e9981"]}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Output()"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "672945fe311840648fb87576c950c83c"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}}], "source": ["from agno.agent import Agent\n", "from agno.models.nebius import Nebius\n", "from agno.tools.duckduckgo import DuckDuckGoTools\n", "\n", "agent = Agent(\n", "    model=Nebius(\n", "        id=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        api_key=os.getenv(\"NEBIUS_API_KEY\")\n", "    ),\n", "    tools=[DuckDuckGoTools()],\n", "    markdown=True\n", ")\n", "\n", "agent.print_response(\"How are the golden state warriors doing this 2024-2025 season?\", stream=True)"]}, {"cell_type": "markdown", "metadata": {"id": "KbekSAFyX82-"}, "source": ["# Agent with Knowledge Base\n", "\n", "This example demonstrates how to create an agent with specialized knowledge. We'll build a Thai cuisine expert by:\n", "1. Loading a PDF containing Thai recipes into a knowledge base\n", "2. Setting up a vector database to store and retrieve this information efficiently\n", "3. Configuring the agent to prioritize its knowledge base while still being able to search the web\n", "\n", "This type of agent is ideal for specialized domains where you want to combine proprietary knowledge with the ability to find supplementary information."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QSR5Xhd5X82-", "outputId": "514d9932-fee2-400c-8141-970fdadc4693", "colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["1f6ad8fee3db4818b1869c3b87e5ccbf", "4c7e158ddf6b43658190fe1a2ad5d29b", "3e7896274da745788ac2c3fe7f1c4146", "0d05d0707f4d422dbd8c4b1ff6a39c0f"]}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: couchbase in /usr/local/lib/python3.11/dist-packages (4.3.6)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[34mINFO\u001b[0m Connected to Couchbase successfully.                                                                          \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO</span> Connected to Couchbase successfully.                                                                          \n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[34mINFO\u001b[0m Loading knowledge base                                                                                        \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO</span> Loading knowledge base                                                                                        \n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[34mINFO\u001b[0m Reading: \u001b[4;94mhttps://agno-public.s3.amazonaws.com/recipes/ThaiRecipes.pdf\u001b[0m                                         \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO</span> Reading: <span style=\"color: #0000ff; text-decoration-color: #0000ff; text-decoration: underline\">https://agno-public.s3.amazonaws.com/recipes/ThaiRecipes.pdf</span>                                         \n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[34mINF<PERSON>\u001b[0m Skipped \u001b[1;36m14\u001b[0m existing/duplicate documents.                                                                      \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO</span> Skipped <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">14</span> existing/duplicate documents.                                                                      \n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[34mINFO\u001b[0m Added \u001b[1;36m0\u001b[0m documents to knowledge base                                                                           \n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #000080; text-decoration-color: #000080\">INFO</span> Added <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span> documents to knowledge base                                                                           \n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Output()"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1f6ad8fee3db4818b1869c3b87e5ccbf"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Output()"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "3e7896274da745788ac2c3fe7f1c4146"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}}], "source": ["import os\n", "# Import additional components for knowledge base functionality\n", "from agno.agent import Agent\n", "from agno.models.nebius import Nebius\n", "from agno.embedder.openai import OpenAIEmbedder\n", "from agno.tools.duckduckgo import DuckDuckGoTools\n", "from agno.knowledge.pdf_url import PDFUrlKnowledgeBase\n", "from agno.vectordb.couchbase import CouchbaseSearch\n", "from couchbase.options import ClusterOptions, KnownConfigProfiles\n", "from couchbase.auth import PasswordAuthenticator\n", "\n", "\n", "# Couchbase connection settings\n", "username = os.getenv(\"COUCHBASE_USER\", \"\")\n", "password = os.getenv(\"COUCHBASE_PASSWORD\", \"\")\n", "connection_string = os.getenv(\"COUCHBASE_CONNECTION_STRING\", \"\")\n", "\n", "# Create cluster options with authentication\n", "auth = PasswordAuthenticator(username, password)\n", "cluster_options = ClusterOptions(auth)\n", "cluster_options.apply_profile(KnownConfigProfiles.WanDevelopment)\n", "\n", "# Create an agent with a specialized knowledge base for Thai recipes\n", "agent = Agent(\n", "    model=Nebius(\n", "        id=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        api_key=os.getenv(\"NEBIUS_API_KEY\")\n", "        ),\n", "    # Define the agent's role\n", "    description=\"You are a Thai cuisine expert!\",\n", "    # Give the agent specific instructions on how to use its knowledge\n", "    instructions=[\n", "        \"Search your knowledge base for Thai recipes.\",\n", "        \"If the question is better suited for the web, search the web to fill in gaps.\",\n", "        \"Prefer the information in your knowledge base over the web results.\"\n", "    ],\n", "    # Set up the knowledge base from a PDF URL\n", "    knowledge=PDFUrlKnowledgeBase(\n", "        urls=[\"https://agno-public.s3.amazonaws.com/recipes/ThaiRecipes.pdf\"],\n", "        # Configure the vector database for semantic search\n", "        vector_db=CouchbaseSearch(\n", "        bucket_name=\"recipe_bucket\",\n", "        scope_name=\"recipe_scope\",\n", "        collection_name=\"recipes\",\n", "        couchbase_connection_string=connection_string,\n", "        cluster_options=cluster_options,\n", "        search_index=\"vector_search_fts_index\",\n", "        embedder=OpenAIEmbedder(\n", "            id=\"text-embedding-3-large\",\n", "            dimensions=3072,\n", "            api_key=os.getenv(\"OPENAI_API_KEY\")\n", "        ),\n", "        wait_until_index_ready=60,\n", "        overwrite=True\n", "    ),\n", "    ),\n", "    # Add web search capability as a backup\n", "    tools=[DuckDuckGoTools()],\n", "    # Display tool usage in the output\n", "    show_tool_calls=True,\n", "    markdown=True\n", ")\n", "\n", "\n", "\n", "# Load the knowledge base (only needs to be done once)\n", "if agent.knowledge is not None:\n", "    agent.knowledge.load()\n", "\n", "agent.print_response(\"How do I make chicken and galangal in coconut milk soup\", stream=True)\n", "agent.print_response(\"What is the history of Thai curry?\", stream=True)"]}, {"cell_type": "markdown", "metadata": {"id": "pfsAEoNvX82-"}, "source": ["# Multi-Agent System\n", "\n", "This example demonstrates how to create a team of specialized agents that work together. We'll build:\n", "1. A web search agent for finding general information\n", "2. A finance agent for retrieving financial data\n", "3. A coordinator agent that delegates tasks to the specialized agents\n", "\n", "This approach allows us to create more powerful systems by combining specialized capabilities."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0m75XzYQX82-", "outputId": "f6b87e47-55ce-4afd-f91a-2a61835dd620", "colab": {"base_uri": "https://localhost:8080/", "height": 753, "referenced_widgets": ["********************************", "572d420abefe4953b799c4a9356f2811"]}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Output()"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "********************************"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": [], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"]}, "metadata": {}}], "source": ["from agno.agent import Agent\n", "from agno.tools.duckduckgo import DuckDuckGoTools\n", "from agno.models.nebius import Nebius\n", "from agno.tools.yfinance import YFinanceTools\n", "\n", "# Create a specialized agent for web searches\n", "web_agent = Agent(\n", "    name=\"Web Agent\",\n", "    role=\"Search the web for information\",\n", "    # Using the larger Llama 3.3 (70B) model for better performance\n", "    model=Nebius(\n", "        id=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        api_key=os.getenv(\"NEBIUS_API_KEY\")\n", "    ),\n", "    tools=[DuckDuckGoTools()],\n", "    instructions=\"Always include sources\",\n", "    show_tool_calls=True,\n", "    markdown=True,\n", ")\n", "\n", "# Create a specialized agent for financial data\n", "finance_agent = Agent(\n", "    name=\"Finance Agent\",\n", "    role=\"Get financial data\",\n", "    model=Nebius(\n", "        id=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        api_key=os.getenv(\"NEBIUS_API_KEY\")\n", "    ),\n", "    # Financial tools for stock data, analyst recommendations, and company info\n", "    tools=[YFinanceTools(stock_price=True, analyst_recommendations=True, company_info=True)],\n", "    instructions=\"Use tables to display data\",\n", "    show_tool_calls=True,\n", "    markdown=True,\n", ")\n", "\n", "# Create a coordinator agent that manages the team\n", "agent_team = Agent(\n", "    # Provide the specialized agents as a team\n", "    team=[web_agent, finance_agent],\n", "    # The coordinator also uses a powerful model\n", "    model=Nebius(\n", "        id=\"meta-llama/Llama-3.3-70B-Instruct\",\n", "        api_key=os.getenv(\"NEBIUS_API_KEY\")\n", "    ),\n", "    # Instructions for the final output\n", "    instructions=[\"Always include sources\", \"Use tables to display data\"],\n", "    show_tool_calls=True,\n", "    markdown=True,\n", ")\n", "\n", "agent_team.print_response(\"What's the market outlook and financial performance of AI semiconductor companies?\", stream=True)"]}], "metadata": {"kernelspec": {"display_name": "cookbook", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}, "colab": {"provenance": []}, "widgets": {"application/vnd.jupyter.widget-state+json": {"672945fe311840648fb87576c950c83c": {"model_module": "@jupyter-widgets/output", "model_name": "OutputModel", "model_module_version": "1.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_b912414579114ca09c3c3eaf043e9981", "msg_id": "", "outputs": [{"output_type": "display_data", "data": {"text/plain": "\u001b[32m▰▰▰▰▰▰▰\u001b[0m Thinking...\n\u001b[36m┏━\u001b[0m\u001b[36m Message \u001b[0m\u001b[36m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[36m━┓\u001b[0m\n\u001b[36m┃\u001b[0m                                                                                                                 \u001b[36m┃\u001b[0m\n\u001b[36m┃\u001b[0m \u001b[32mHow are the golden state warriors doing this 2024-2025 season?\u001b[0m                                                  \u001b[36m┃\u001b[0m\n\u001b[36m┃\u001b[0m                                                                                                                 \u001b[36m┃\u001b[0m\n\u001b[36m┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\u001b[0m\n\u001b[33m┏━\u001b[0m\u001b[33m Tool Calls \u001b[0m\u001b[33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[33m━┓\u001b[0m\n\u001b[33m┃\u001b[0m                                                                                                                 \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m • duckduckgo_search(query=Golden State Warriors 2024-2025 season, max_results=5)                                \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m                                                                                                                 \u001b[33m┃\u001b[0m\n\u001b[33m┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\u001b[0m\n\u001b[34m┏━\u001b[0m\u001b[34m Response (6.7s) \u001b[0m\u001b[34m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[34m━┓\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m Based on the search results, it appears that the Golden State Warriors are doing well in the 2024-2025 season.  \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m They have a strong schedule with 24 national TV appearances and have won some of their games. However, the      \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m exact details of their performance are not provided in the search results. To get a better understanding of     \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m their current standing, it would be best to check the latest news and updates on their official website or      \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m sports news websites.                                                                                           \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\u001b[0m\n", "text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">▰▰▰▰▰▰▰</span> Thinking...\n<span style=\"color: #008080; text-decoration-color: #008080\">┏━ Message ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┃</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">┃</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┃</span> <span style=\"color: #008000; text-decoration-color: #008000\">How are the golden state warriors doing this 2024-2025 season?</span>                                                  <span style=\"color: #008080; text-decoration-color: #008080\">┃</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┃</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">┃</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┏━ Tool Calls ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span> • duckduckgo_search(query=Golden State Warriors 2024-2025 season, max_results=5)                                <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┏━ Response (6.7s) ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> Based on the search results, it appears that the Golden State Warriors are doing well in the 2024-2025 season.  <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> They have a strong schedule with 24 national TV appearances and have won some of their games. However, the      <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> exact details of their performance are not provided in the search results. To get a better understanding of     <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> their current standing, it would be best to check the latest news and updates on their official website or      <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> sports news websites.                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛</span>\n</pre>\n"}, "metadata": {}}]}}, "b912414579114ca09c3c3eaf043e9981": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1f6ad8fee3db4818b1869c3b87e5ccbf": {"model_module": "@jupyter-widgets/output", "model_name": "OutputModel", "model_module_version": "1.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_4c7e158ddf6b43658190fe1a2ad5d29b", "msg_id": "", "outputs": [{"output_type": "display_data", "data": {"text/plain": "\u001b[32m▰▱▱▱▱▱▱\u001b[0m Thinking...\n\u001b[36m┏━\u001b[0m\u001b[36m Message \u001b[0m\u001b[36m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[36m━┓\u001b[0m\n\u001b[36m┃\u001b[0m                                                                                                                 \u001b[36m┃\u001b[0m\n\u001b[36m┃\u001b[0m \u001b[32mHow do I make chicken and galangal in coconut milk soup\u001b[0m                                                         \u001b[36m┃\u001b[0m\n\u001b[36m┃\u001b[0m                                                                                                                 \u001b[36m┃\u001b[0m\n\u001b[36m┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\u001b[0m\n\u001b[33m┏━\u001b[0m\u001b[33m Tool Calls \u001b[0m\u001b[33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[33m━┓\u001b[0m\n\u001b[33m┃\u001b[0m                                                                                                                 \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m • search_knowledge_base(query=chicken and galangal in coconut milk soup recipe)                                 \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m • duckduckgo_search(query=chicken and galangal in coconut milk soup recipe, max_results=5)                      \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m                                                                                                                 \u001b[33m┃\u001b[0m\n\u001b[33m┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\u001b[0m\n\u001b[34m┏━\u001b[0m\u001b[34m Response (14.6s) \u001b[0m\u001b[34m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[34m━┓\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m Here is a simple recipe for chicken and galangal in coconut milk soup:                                          \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0m150 grams chicken, cut into bite-size pieces                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0m50 grams sliced young galangal                                                                               \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0m100 grams lightly crushed lemongrass, julienned                                                              \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0m100 grams straw mushrooms                                                                                    \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0m250 grams coconut milk                                                                                       \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0m100 grams chicken stock                                                                                      \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0m3 tbsp lime juice                                                                                            \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0m3 tbsp fish sauce                                                                                            \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0m2 leaves kaffir lime, shredded                                                                               \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0m1-2 bird’s eye chilies, pounded                                                                              \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0m3 leaves coriander                                                                                           \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m 1 \u001b[0mBring the chicken stock and coconut milk to a slow boil. Add galangal, lemongrass, chicken and mushrooms.    \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m   \u001b[0mWhen the soup returns to a boil, season it with fish sauce.                                                  \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m 2 \u001b[0mWait until the chicken is cooked, and then add the kaffir lime leaves and bird’s eye chilies. Remove the pot \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m   \u001b[0mfrom heat and add lime juice.                                                                                \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m 3 \u001b[0mGarnish with coriander leaves.                                                                               \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m Tips:                                                                                                           \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0mKeep the heat low throughout the cooking process. High heat will make the oil in the coconut milk separate   \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m   \u001b[0mand rise to the top.                                                                                         \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0mIf you’re using mature galangal, reduce the amount.                                                          \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0mLime juice becomes more aromatic when it is added after the pot is removed from heat.                        \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m \u001b[1;33m • \u001b[0mReduce amount of chilies for a milder taste.                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\u001b[0m\n", "text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">▰▱▱▱▱▱▱</span> Thinking...\n<span style=\"color: #008080; text-decoration-color: #008080\">┏━ Message ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┃</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">┃</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┃</span> <span style=\"color: #008000; text-decoration-color: #008000\">How do I make chicken and galangal in coconut milk soup</span>                                                         <span style=\"color: #008080; text-decoration-color: #008080\">┃</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┃</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">┃</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┏━ Tool Calls ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span> • search_knowledge_base(query=chicken and galangal in coconut milk soup recipe)                                 <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span> • duckduckgo_search(query=chicken and galangal in coconut milk soup recipe, max_results=5)                      <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┏━ Response (14.6s) ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> Here is a simple recipe for chicken and galangal in coconut milk soup:                                          <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>150 grams chicken, cut into bite-size pieces                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>50 grams sliced young galangal                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>100 grams lightly crushed lemongrass, julienned                                                              <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>100 grams straw mushrooms                                                                                    <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>250 grams coconut milk                                                                                       <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>100 grams chicken stock                                                                                      <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>3 tbsp lime juice                                                                                            <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>3 tbsp fish sauce                                                                                            <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>2 leaves kaffir lime, shredded                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>1-2 bird’s eye chilies, pounded                                                                              <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>3 leaves coriander                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 1 </span>Bring the chicken stock and coconut milk to a slow boil. Add galangal, lemongrass, chicken and mushrooms.    <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>When the soup returns to a boil, season it with fish sauce.                                                  <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 2 </span>Wait until the chicken is cooked, and then add the kaffir lime leaves and bird’s eye chilies. Remove the pot <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>from heat and add lime juice.                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> 3 </span>Garnish with coriander leaves.                                                                               <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> Tips:                                                                                                           <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Keep the heat low throughout the cooking process. High heat will make the oil in the coconut milk separate   <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">   </span>and rise to the top.                                                                                         <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>If you’re using mature galangal, reduce the amount.                                                          <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Lime juice becomes more aromatic when it is added after the pot is removed from heat.                        <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> <span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\"> • </span>Reduce amount of chilies for a milder taste.                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛</span>\n</pre>\n"}, "metadata": {}}]}}, "4c7e158ddf6b43658190fe1a2ad5d29b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3e7896274da745788ac2c3fe7f1c4146": {"model_module": "@jupyter-widgets/output", "model_name": "OutputModel", "model_module_version": "1.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_0d05d0707f4d422dbd8c4b1ff6a39c0f", "msg_id": "", "outputs": [{"output_type": "display_data", "data": {"text/plain": "\u001b[32m▰▰▰▰▰▰▱\u001b[0m Thinking...\n\u001b[36m┏━\u001b[0m\u001b[36m Message \u001b[0m\u001b[36m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[36m━┓\u001b[0m\n\u001b[36m┃\u001b[0m                                                                                                                 \u001b[36m┃\u001b[0m\n\u001b[36m┃\u001b[0m \u001b[32mWhat is the history of Thai curry?\u001b[0m                                                                              \u001b[36m┃\u001b[0m\n\u001b[36m┃\u001b[0m                                                                                                                 \u001b[36m┃\u001b[0m\n\u001b[36m┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\u001b[0m\n\u001b[33m┏━\u001b[0m\u001b[33m Tool Calls \u001b[0m\u001b[33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[33m━┓\u001b[0m\n\u001b[33m┃\u001b[0m                                                                                                                 \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m • search_knowledge_base(query=history of Thai curry)                                                            \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m • duckduckgo_search(query=history of Thai curry, max_results=5)                                                 \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m                                                                                                                 \u001b[33m┃\u001b[0m\n\u001b[33m┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\u001b[0m\n\u001b[34m┏━\u001b[0m\u001b[34m Response (10.8s) \u001b[0m\u001b[34m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[34m━┓\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m The history of Thai curry is a rich and complex one, with influences from various cultures and cuisines. The    \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m term \"Thai curry\" was first coined in the 19th century, and since then, it has evolved into a distinct and      \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m popular dish in Thai cuisine. Thai curry is made from curry paste, coconut milk or water, meat, seafood,        \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m vegetables or fruit, and herbs, and is characterized by its use of fresh rhizomes, herbs, and aromatic leaves   \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m rather than a mix of dried spices. The dish has been popularized by culinary pioneers such as David <PERSON>    \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m and Khun Ying Supatra Singholaga, and has become a beloved global cuisine. There are various types of Thai      \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m curry, including red, yellow, and green curry, each with its own unique flavor and ingredients. Overall, the    \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m history of Thai curry is a story of cultural exchange, innovation, and delicious food.                          \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\u001b[0m\n", "text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">▰▰▰▰▰▰▱</span> Thinking...\n<span style=\"color: #008080; text-decoration-color: #008080\">┏━ Message ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┃</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">┃</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┃</span> <span style=\"color: #008000; text-decoration-color: #008000\">What is the history of Thai curry?</span>                                                                              <span style=\"color: #008080; text-decoration-color: #008080\">┃</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┃</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">┃</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┏━ Tool Calls ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span> • search_knowledge_base(query=history of Thai curry)                                                            <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span> • duckduckgo_search(query=history of Thai curry, max_results=5)                                                 <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┏━ Response (10.8s) ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> The history of Thai curry is a rich and complex one, with influences from various cultures and cuisines. The    <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> term \"Thai curry\" was first coined in the 19th century, and since then, it has evolved into a distinct and      <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> popular dish in Thai cuisine. Thai curry is made from curry paste, coconut milk or water, meat, seafood,        <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> vegetables or fruit, and herbs, and is characterized by its use of fresh rhizomes, herbs, and aromatic leaves   <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> rather than a mix of dried spices. The dish has been popularized by culinary pioneers such as David Thompson    <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> and Khun Ying Supatra Singholaga, and has become a beloved global cuisine. There are various types of Thai      <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> curry, including red, yellow, and green curry, each with its own unique flavor and ingredients. Overall, the    <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> history of Thai curry is a story of cultural exchange, innovation, and delicious food.                          <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛</span>\n</pre>\n"}, "metadata": {}}]}}, "0d05d0707f4d422dbd8c4b1ff6a39c0f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "********************************": {"model_module": "@jupyter-widgets/output", "model_name": "OutputModel", "model_module_version": "1.0.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/output", "_model_module_version": "1.0.0", "_model_name": "OutputModel", "_view_count": null, "_view_module": "@jupyter-widgets/output", "_view_module_version": "1.0.0", "_view_name": "OutputView", "layout": "IPY_MODEL_572d420abefe4953b799c4a9356f2811", "msg_id": "", "outputs": [{"output_type": "display_data", "data": {"text/plain": "\u001b[32m▰▰▰▰▰▱▱\u001b[0m Thinking...\n\u001b[36m┏━\u001b[0m\u001b[36m Message \u001b[0m\u001b[36m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[36m━┓\u001b[0m\n\u001b[36m┃\u001b[0m                                                                                                                 \u001b[36m┃\u001b[0m\n\u001b[36m┃\u001b[0m \u001b[32mWhat's the market outlook and financial performance of AI semiconductor companies?\u001b[0m                              \u001b[36m┃\u001b[0m\n\u001b[36m┃\u001b[0m                                                                                                                 \u001b[36m┃\u001b[0m\n\u001b[36m┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\u001b[0m\n\u001b[33m┏━\u001b[0m\u001b[33m Tool Calls \u001b[0m\u001b[33m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[33m━┓\u001b[0m\n\u001b[33m┃\u001b[0m                                                                                                                 \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m • transfer_task_to_web_agent(task_description=Find the market outlook and financial performance of AI           \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m semiconductor companies, expected_output=A summary of the market outlook and financial performance of AI        \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m semiconductor companies, additional_information=Look for recent news articles and financial reports from        \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m reputable sources such as Bloomberg, CNBC, and company websites)                                                \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m • transfer_task_to_finance_agent(task_description=Find the current stock price of NVIDIA, expected_output=The   \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m current stock price of NVIDIA, additional_information=Look for real-time stock price data from a reliable       \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m financial source such as Yahoo Finance or Bloomberg)                                                            \u001b[33m┃\u001b[0m\n\u001b[33m┃\u001b[0m                                                                                                                 \u001b[33m┃\u001b[0m\n\u001b[33m┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\u001b[0m\n\u001b[34m┏━\u001b[0m\u001b[34m Response (21.8s) \u001b[0m\u001b[34m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[34m━┓\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                     \u001b[1mMarket Outlook and Financial Performance of AI Semiconductor Companies\u001b[0m                      \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m The market outlook for AI semiconductor companies is expected to reach over $1 trillion by 2025, driven by the  \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m growth of artificial intelligence, electric vehicles, and other innovations.                                    \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m  \u001b[1m \u001b[0m\u001b[1mCategory\u001b[0m\u001b[1m   \u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mDescription\u001b[0m\u001b[1m                                                      \u001b[0m\u001b[1m \u001b[0m                              \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━                              \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m   Market Size   $1 trillion by 2025                                                                             \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m   Growth Rate   11% year-over-year in 2025                                                                      \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m   Drivers       Artificial intelligence, electric vehicles, and other innovations                               \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                               \u001b[1;2mCompany Performance\u001b[0m                                               \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m Some major AI semiconductor companies such as NVIDIA have seen significant growth in their stock prices.        \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m  \u001b[1m \u001b[0m\u001b[1mCompany\u001b[0m\u001b[1m \u001b[0m \u001b[1m \u001b[0m\u001b[1mCurrent Stock Price\u001b[0m\u001b[1m \u001b[0m                                                                                \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━                                                                                \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m   NVIDIA    $134.81                                                                                             \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m Overall, the outlook for AI semiconductor companies is positive, with increasing demand and investment in the   \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m industry. Sources: Bloomberg, CNBC, company websites.                                                           \u001b[34m┃\u001b[0m\n\u001b[34m┃\u001b[0m                                                                                                                 \u001b[34m┃\u001b[0m\n\u001b[34m┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛\u001b[0m\n", "text/html": "<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000\">▰▰▰▰▰▱▱</span> Thinking...\n<span style=\"color: #008080; text-decoration-color: #008080\">┏━ Message ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┃</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">┃</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┃</span> <span style=\"color: #008000; text-decoration-color: #008000\">What's the market outlook and financial performance of AI semiconductor companies?</span>                              <span style=\"color: #008080; text-decoration-color: #008080\">┃</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┃</span>                                                                                                                 <span style=\"color: #008080; text-decoration-color: #008080\">┃</span>\n<span style=\"color: #008080; text-decoration-color: #008080\">┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┏━ Tool Calls ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span> • transfer_task_to_web_agent(task_description=Find the market outlook and financial performance of AI           <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span> semiconductor companies, expected_output=A summary of the market outlook and financial performance of AI        <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span> semiconductor companies, additional_information=Look for recent news articles and financial reports from        <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span> reputable sources such as Bloomberg, CNBC, and company websites)                                                <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span> • transfer_task_to_finance_agent(task_description=Find the current stock price of NVIDIA, expected_output=The   <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span> current stock price of NVIDIA, additional_information=Look for real-time stock price data from a reliable       <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span> financial source such as Yahoo Finance or Bloomberg)                                                            <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┃</span>                                                                                                                 <span style=\"color: #808000; text-decoration-color: #808000\">┃</span>\n<span style=\"color: #808000; text-decoration-color: #808000\">┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┏━ Response (21.8s) ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                     <span style=\"font-weight: bold\">Market Outlook and Financial Performance of AI Semiconductor Companies</span>                      <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> The market outlook for AI semiconductor companies is expected to reach over $1 trillion by 2025, driven by the  <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> growth of artificial intelligence, electric vehicles, and other innovations.                                    <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>  <span style=\"font-weight: bold\"> Category    </span> <span style=\"font-weight: bold\"> Description                                                       </span>                              <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━                              <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>   Market Size   $1 trillion by 2025                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>   Growth Rate   11% year-over-year in 2025                                                                      <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>   Drivers       Artificial intelligence, electric vehicles, and other innovations                               <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                               <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f; font-weight: bold\">Company Performance</span>                                               <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> Some major AI semiconductor companies such as NVIDIA have seen significant growth in their stock prices.        <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>  <span style=\"font-weight: bold\"> Company </span> <span style=\"font-weight: bold\"> Current Stock Price </span>                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>  ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━                                                                                <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>   NVIDIA    $134.81                                                                                             <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> Overall, the outlook for AI semiconductor companies is positive, with increasing demand and investment in the   <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span> industry. Sources: Bloomberg, CNBC, company websites.                                                           <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┃</span>                                                                                                                 <span style=\"color: #000080; text-decoration-color: #000080\">┃</span>\n<span style=\"color: #000080; text-decoration-color: #000080\">┗━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┛</span>\n</pre>\n"}, "metadata": {}}]}}, "572d420abefe4953b799c4a9356f2811": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}}}}, "nbformat": 4, "nbformat_minor": 0}