# Pipeline ETL para Banco de Dados de Farmacogenética

## Visão Geral
Este repositório contém uma **pipeline ETL completa** para processamento do banco de dados `banco_farmacogeneticabrasil_processado.csv`. A pipeline realiza:

- **Carregamento** de dados brutos
- **Transformação** (renomeação de colunas, tipagem adequada, codificação categórica)
- **Tratamento condicional** de dados faltantes
- **Codificação** (one-hot e label encoding)
- **Validação** e salvamento dos dados processados

---

## Estrutura do Projeto

```
farmacogenetica_pipeline/
│
├── etl_pipeline.py          # Script principal da pipeline ETL
├── config.py                # Mapeamentos de renomeação, tipos e codificações
├── utils.py                 # Funções auxiliares (condicionais, imputação, codificação)
├── processed_data/          # Pasta para dados processados (saída)
└── README.md                # Documentação do projeto
```

---

## Requisitos

- Python 3.8+
- Pacotes:
  - pandas
  - numpy
  - scikit-learn (opcional para validações)

Instale dependências com:
```bash
pip install pandas numpy
```

---

## Como Usar

1. **Coloque o arquivo CSV** na raiz do projeto como `banco_farmacogeneticabrasil_processado.csv`
2. **Execute a pipeline**:
   ```bash
   python etl_pipeline.py
   ```
3. **Resultados salvos** em `processed_data/processed_data.csv`

---

## Documentação Técnica

### Transformações Realizadas

| Tipo de Transformação       | Detalhes                                                                 |
|----------------------------|--------------------------------------------------------------------------|
| **Renomeação de Colunas**  | Todas as colunas renomeadas para snake_case (ex: `sex` → `sex_feminino`) |
| **Tipagem Adequada**       | Conversão para `float64`, `category`, `datetime`, etc.                  |
| **Codificação Categórica** | Label Encoding para variáveis binárias (ex: `sex_feminino: 1/0`)        |
| **One-Hot Encoding**       | Aplicado a variáveis com múltiplas categorias (ex: `ethnicity_race`)     |
| **Regras Condicionais**    | Se `smoker == 0`, campos relacionados ao tabagismo são zerados           |

---

### Exemplo de Mapeamento de Colunas

| Nome Original         | Nome Processado                     | Observação                          |
|-----------------------|-------------------------------------|-------------------------------------|
| `sex`                 | `sex_feminino`                      | 1 = Feminino, 0 = Masculino         |
| `types_cigarette_1`   | `cigarette_type_industrialized`     | 1 = Sim, 0 = Não                    |
| `types_cigarette_2`   | `cigarette_type_straw`              | 1 = Sim, 0 = Não                    |
| `origin`              | `hospital_origin`                   | Codificação categórica              |
| `diabetes_2`          | `diabetes_type`                     | 0 = Não, 1 = Tipo 1, 2 = Tipo 2     |

---

### Dados Condicionais Tratados

A pipeline aplica lógica condicional para garantir consistência:

```python
if parent_col == 0:
    child_cols = [0]  # Zera campos dependentes
```

Exemplo:  
Se `current_smoker == 0`, então:  
- `cigarette_type_industrialized` → 0  
- `cigarettes_per_day_current` → 0  
- `years_smoking_current` → 0  

---

### Codificação de Variáveis

#### Label Encoding
```python
{
    'sex_feminino': {1: 1, 2: 0},
    'ethnicity_race': {
        1: 'black',
        2: 'pardo',
        3: 'yellow',
        4: 'indigenous',
        5: 'white',
        6: 'not_informed'
    }
}
```

#### One-Hot Encoding
Aplicado a variáveis como:
- `ethnicity_race`
- `hospital_origin`
- `diabetes_type`

---

## Notas Importantes

1. **Datas Ausentes**:  
   - Valores como `NA` ou vazios são tratados conforme regras condicionais.
   - Imputação por mediana para numéricos e `'missing'` para categóricos.

2. **Melhorias Futuras**:
   - Adicionar testes unitários para validação.
   - Implementar logs detalhados para auditoria.
   - Criar visualizações exploratórias (EDA) como parte do pipeline.

---

**Contato:**  
Para dúvidas ou contribuições, abra uma issue neste repositório.