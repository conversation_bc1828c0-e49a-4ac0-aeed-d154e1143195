steps:
  - id: 'Build gemma3 1b Image'
    name: 'gcr.io/cloud-builders/docker:latest'
    dir: 'Demos/Gemma-on-Cloudrun/' # in CI cloudbuild.yaml's context is the root of the repo. 
    env:
      - 'MODEL=gemma3:1b'
    script: |
      set -x
      docker build --pull --build-arg MODEL=${MODEL} . -t "${_LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_REPO}/gemma/gemma3-1b:latest"
  - id: 'Build gemma3 4b Image'
    name: 'gcr.io/cloud-builders/docker:latest'
    dir: 'Demos/Gemma-on-Cloudrun/' # in CI cloudbuild.yaml's context is the root of the repo. 
    env:
      - 'MODEL=gemma3:4b'
    script: |
      set -x
      docker build --pull --build-arg MODEL=${MODEL} . -t "${_LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_REPO}/gemma/gemma3-4b:latest"
  - id: 'Build gemma3 12b Image'
    name: 'gcr.io/cloud-builders/docker:latest'
    dir: 'Demos/Gemma-on-Cloudrun/' # in CI cloudbuild.yaml's context is the root of the repo. 
    env:
      - 'MODEL=gemma3:12b'
    script: |
      set -x
      docker build --pull --build-arg MODEL=${MODEL} . -t "${_LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_REPO}/gemma/gemma3-12b:latest"
  - id: 'Build gemma3 27b Image'
    name: 'gcr.io/cloud-builders/docker:latest'
    dir: 'Demos/Gemma-on-Cloudrun/' # in CI cloudbuild.yaml's context is the root of the repo. 
    env:
      - 'MODEL=gemma3:27b'
    script: |
      set -x
      docker build --pull --build-arg MODEL=${MODEL} . -t "${_LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_REPO}/gemma/gemma3-27b:latest"
  - id: 'Build gemma3n e2b Image'
    name: 'gcr.io/cloud-builders/docker:latest'
    dir: 'Demos/Gemma-on-Cloudrun/' # in CI cloudbuild.yaml's context is the root of the repo. 
    env:
      - 'MODEL=gemma3n:E2b'
    script: |
      set -x
      docker build --pull --build-arg MODEL=${MODEL} . -t "${_LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_REPO}/gemma/gemma3n-e2b:latest"
  - id: 'Build gemma3n e4b Image'
    name: 'gcr.io/cloud-builders/docker:latest'
    dir: 'Demos/Gemma-on-Cloudrun/' # in CI cloudbuild.yaml's context is the root of the repo. 
    env:
      - 'MODEL=gemma3n:E4b'
    script: |
      set -x
      docker build --pull --build-arg MODEL=${MODEL} . -t "${_LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_REPO}/gemma/gemma3n-e4b:latest"



images:
  # Push image with Cloud Build context.
  - ${_LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_REPO}/gemma/gemma3-1b:latest
  - ${_LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_REPO}/gemma/gemma3-4b:latest
  - ${_LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_REPO}/gemma/gemma3-12b:latest
  - ${_LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_REPO}/gemma/gemma3-27b:latest
  - ${_LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_REPO}/gemma/gemma3n-e2b:latest
  - ${_LOCATION}-docker.pkg.dev/${PROJECT_ID}/${_REPO}/gemma/gemma3n-e4b:latest


substitutions:
  _LOCATION: us
  _REPO: container
  

options:
  # Automatically make substitutions available as environment variables to scripts.
  # https://cloud.google.com/build/docs/configuring-builds/substitute-variable-values#mapping_substitutions_to_environment_variables
  automapSubstitutions: true
  machineType: 'E2_HIGHCPU_32'
  diskSizeGb: '500'