import os
import logging
import requests
from dotenv import load_dotenv
from typing import Optional, Dict

def setup_logging() -> logging.Logger:
    """Configure comprehensive logging."""
    log_dir = 'logs'
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(
                os.path.join(log_dir, 'article_generator.log'),
                encoding='utf-8',
                delay=True
            ),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    return logger

def load_env() -> str:
    """Load GROQ_API_KEY from environment."""
    load_dotenv()
    api_key = os.getenv("GROQ_API_KEY")
    
    if not api_key:
        raise ValueError(
            "GROQ_API_KEY not found in environment variables. "
            "Please create a .env file with your API key."
        )
    
    return api_key

def get_rate_limit_info() -> Optional[Dict[str, str]]:
    """Fetch current rate limit info from Groq API."""
    try:
        response = requests.get(
            "https://api.groq.com/openai/v1/models",
            headers={"Authorization": f"Bearer {load_env()}"},
            timeout=10
        )
        
        return {
            "limit-requests": response.headers.get("x-ratelimit-limit-requests"),
            "remaining-requests": response.headers.get("x-ratelimit-remaining-requests"),
            "reset-requests": response.headers.get("x-ratelimit-reset-requests"),
            "limit-tokens": response.headers.get("x-ratelimit-limit-tokens"),
            "remaining-tokens": response.headers.get("x-ratelimit-remaining-tokens"),
            "reset-tokens": response.headers.get("x-ratelimit-reset-tokens"),
        }
    except Exception as e:
        logging.error(f"Failed to fetch rate limits: {e}")
        return None