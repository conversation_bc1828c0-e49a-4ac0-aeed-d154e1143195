import 'dart:io';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'lib/core/services/real_translation_service.dart';

/// Teste simples para verificar se a tradução REAL está funcionando
void main() async {
  print('🧪 TESTE DE TRADUÇÃO REAL - SimulTrans AI');
  print('=' * 50);
  
  try {
    // Carregar variáveis de ambiente
    await dotenv.load(fileName: '.env');
    print('✅ Arquivo .env carregado');
    
    // Verificar se a API key está configurada
    final apiKey = dotenv.env['GEMINI_API_KEY'];
    if (apiKey == null || apiKey.isEmpty || apiKey == 'YOUR_GOOGLE_AI_API_KEY_HERE') {
      print('❌ ERRO: GEMINI_API_KEY não configurada no arquivo .env');
      print('💡 Configure sua API key do Google AI em .env');
      print('🔗 Obtenha em: https://makersuite.google.com/app/apikey');
      exit(1);
    }
    
    print('✅ API Key configurada: ${apiKey.substring(0, 10)}...');
    
    // Inicializar o serviço de tradução
    print('\n🚀 Inicializando serviço de tradução...');
    final translationService = RealTranslationService.instance;
    await translationService.initialize();
    print('✅ Serviço inicializado com sucesso!');
    
    // Teste 1: Tradução simples EN -> PT
    print('\n📝 TESTE 1: Inglês → Português');
    print('Texto: "Hello, how are you?"');
    
    final result1 = await translationService.translateText(
      text: 'Hello, how are you?',
      sourceLanguage: 'en',
      targetLanguage: 'pt',
    );
    
    print('✅ Tradução: "$result1"');
    
    // Teste 2: Tradução PT -> EN
    print('\n📝 TESTE 2: Português → Inglês');
    print('Texto: "Olá, como você está?"');
    
    final result2 = await translationService.translateText(
      text: 'Olá, como você está?',
      sourceLanguage: 'pt',
      targetLanguage: 'en',
    );
    
    print('✅ Tradução: "$result2"');
    
    // Teste 3: Tradução com contexto
    print('\n📝 TESTE 3: Com contexto (EN → PT)');
    print('Texto: "The bank is closed"');
    print('Contexto: "financial institution"');
    
    final result3 = await translationService.translateText(
      text: 'The bank is closed',
      sourceLanguage: 'en',
      targetLanguage: 'pt',
      context: 'financial institution',
    );
    
    print('✅ Tradução: "$result3"');
    
    // Teste 4: Auto-detecção de idioma
    print('\n📝 TESTE 4: Auto-detecção → Espanhol');
    print('Texto: "Good morning, everyone!"');
    
    final result4 = await translationService.translateText(
      text: 'Good morning, everyone!',
      sourceLanguage: 'auto',
      targetLanguage: 'es',
    );
    
    print('✅ Tradução: "$result4"');
    
    print('\n🎉 TODOS OS TESTES CONCLUÍDOS COM SUCESSO!');
    print('✅ A tradução REAL está funcionando perfeitamente!');
    print('🌐 Usando Google Gemini API para traduções autênticas');
    
  } catch (e) {
    print('\n❌ ERRO NO TESTE: $e');
    print('\n💡 Possíveis soluções:');
    print('1. Verifique se GEMINI_API_KEY está configurada no .env');
    print('2. Verifique sua conexão com a internet');
    print('3. Verifique se a API key é válida');
    print('4. Execute: flutter pub get');
    exit(1);
  }
}
