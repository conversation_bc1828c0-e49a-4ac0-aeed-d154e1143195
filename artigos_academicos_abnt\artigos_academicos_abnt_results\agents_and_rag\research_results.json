[{"url": "http://arxiv.org/abs/2501.09136v3", "title": "Agentic Retrieval-Augmented Generation: A Survey on Agentic RAG", "description": "Large Language Models (LLMs) have revolutionized artificial intelligence (AI) by enabling human like text generation and natural language understanding. However, their reliance on static training data limits their ability to respond to dynamic, real time queries, resulting in outdated or inaccurate outputs. Retrieval Augmented Generation (RAG) has emerged as a solution, enhancing LLMs by integrating real time data retrieval to provide contextually relevant and up-to-date responses. Despite its promise, traditional RAG systems are constrained by static workflows and lack the adaptability required for multistep reasoning and complex task management.   Agentic Retrieval-Augmented Generation (Agentic RAG) transcends these limitations by embedding autonomous AI agents into the RAG pipeline. These agents leverage agentic design patterns reflection, planning, tool use, and multiagent collaboration to dynamically manage retrieval strategies, iteratively refine contextual understanding, and ...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2025-01-15", "pdf_url": "http://arxiv.org/pdf/2501.09136v3", "categories": ["cs.AI", "cs.CL", "cs.IR"]}, {"url": "http://arxiv.org/abs/2410.13509v2", "title": "RAG-DDR: Optimizing Retrieval-Augmented Generation Using Differentiable Data Rewards", "description": "Retrieval-Augmented Generation (RAG) has proven its effectiveness in mitigating hallucinations in Large Language Models (LLMs) by retrieving knowledge from external resources. To adapt LLMs for the RAG systems, current approaches use instruction tuning to optimize LLMs, improving their ability to utilize retrieved knowledge. This supervised fine-tuning (SFT) approach focuses on equipping LLMs to handle diverse RAG tasks using different instructions. However, it trains RAG modules to overfit training signals and overlooks the varying data preferences among agents within the RAG system. In this paper, we propose a Differentiable Data Rewards (DDR) method, which end-to-end trains RAG systems by aligning data preferences between different RAG modules. DDR works by collecting the rewards to optimize each agent in the RAG system with the rollout method, which prompts agents to sample some potential responses as perturbations, evaluates the impact of these perturbations on the whole RAG sy...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Maosong Sun", "<PERSON><PERSON>"], "published": "2024-10-17", "pdf_url": "http://arxiv.org/pdf/2410.13509v2", "categories": ["cs.CL"]}, {"url": "http://arxiv.org/abs/2501.15228v1", "title": "Improving Retrieval-Augmented Generation through Multi-Agent Reinforcement Learning", "description": "Retrieval-augmented generation (RAG) is extensively utilized to incorporate external, current knowledge into large language models, thereby minimizing hallucinations. A standard RAG pipeline may comprise several components, such as query rewriting, document retrieval, document filtering, and answer generation. However, these components are typically optimized separately through supervised fine-tuning, which can lead to misalignments between the objectives of individual modules and the overarching aim of generating accurate answers in question-answering (QA) tasks. Although recent efforts have explored reinforcement learning (RL) to optimize specific RAG components, these approaches often focus on overly simplistic pipelines with only two components or do not adequately address the complex interdependencies and collaborative interactions among the modules. To overcome these challenges, we propose treating the RAG pipeline as a multi-agent cooperative task, with each component regarde...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yan", "Weiwei Sun", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "published": "2025-01-25", "pdf_url": "http://arxiv.org/pdf/2501.15228v1", "categories": ["cs.CL", "cs.IR"]}, {"url": "http://arxiv.org/abs/2502.13957v1", "title": "RAG-Gym: Optimizing Reasoning and Search Agents with Process Supervision", "description": "Retrieval-augmented generation (RAG) has shown great potential for knowledge-intensive tasks, but its traditional architectures rely on static retrieval, limiting their effectiveness for complex questions that require sequential information-seeking. While agentic reasoning and search offer a more adaptive approach, most existing methods depend heavily on prompt engineering. In this work, we introduce RAG-Gym, a unified optimization framework that enhances information-seeking agents through fine-grained process supervision at each search step. We also propose ReSearch, a novel agent architecture that synergizes answer reasoning and search query generation within the RAG-Gym framework. Experiments on four challenging datasets show that RAG-Gym improves performance by up to 25.6\\% across various agent architectures, with ReSearch consistently outperforming existing baselines. Further analysis highlights the effectiveness of advanced LLMs as process reward judges and the transferability...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Zhiyong Lu", "<PERSON><PERSON>"], "published": "2025-02-19", "pdf_url": "http://arxiv.org/pdf/2502.13957v1", "categories": ["cs.CL", "cs.AI"]}, {"url": "http://arxiv.org/abs/2503.13514v1", "title": "RAG-KG-IL: A Multi-Agent Hybrid Framework for Reducing Hallucinations and Enhancing LLM Reasoning through RAG and Incremental Knowledge Graph Learning Integration", "description": "This paper presents RAG-KG-IL, a novel multi-agent hybrid framework designed to enhance the reasoning capabilities of Large Language Models (LLMs) by integrating Retrieval-Augmented Generation (RAG) and Knowledge Graphs (KGs) with an Incremental Learning (IL) approach. Despite recent advancements, LLMs still face significant challenges in reasoning with structured data, handling dynamic knowledge evolution, and mitigating hallucinations, particularly in mission-critical domains. Our proposed RAG-KG-IL framework addresses these limitations by employing a multi-agent architecture that enables continuous knowledge updates, integrates structured knowledge, and incorporates autonomous agents for enhanced explainability and reasoning. The framework utilizes RAG to ensure the generated responses are grounded in verifiable information, while KGs provide structured domain knowledge for improved consistency and depth of understanding. The Incremental Learning approach allows for dynamic updat...", "authors": ["Hong Qing Yu", "<PERSON>"], "published": "2025-03-14", "pdf_url": "http://arxiv.org/pdf/2503.13514v1", "categories": ["cs.CL", "cs.AI", "cs.IR"]}, {"url": "http://arxiv.org/abs/2407.19994v3", "title": "A Study on the Implementation Method of an Agent-Based Advanced RAG System Using Graph", "description": "This study aims to improve knowledge-based question-answering (QA) systems by overcoming the limitations of existing Retrieval-Augmented Generation (RAG) models and implementing an advanced RAG system based on Graph technology to develop high-quality generative AI services. While existing RAG models demonstrate high accuracy and fluency by utilizing retrieved information, they may suffer from accuracy degradation as they generate responses using pre-loaded knowledge without reprocessing. Additionally, they cannot incorporate real-time data after the RAG configuration stage, leading to issues with contextual understanding and biased information. To address these limitations, this study implemented an enhanced RAG system utilizing Graph technology. This system is designed to efficiently search and utilize information. Specifically, it employs LangGraph to evaluate the reliability of retrieved information and synthesizes diverse data to generate more accurate and enhanced responses. Fu...", "authors": ["<PERSON><PERSON><PERSON>"], "published": "2024-07-29", "pdf_url": "http://arxiv.org/pdf/2407.19994v3", "categories": ["cs.AI"]}, {"url": "http://arxiv.org/abs/2502.15698v1", "title": "Developing an Artificial Intelligence Tool for Personalized Breast Cancer Treatment Plans based on the NCCN Guidelines", "description": "Cancer treatments require personalized approaches based on a patient's clinical condition, medical history, and evidence-based guidelines. The National Comprehensive Cancer Network (NCCN) provides frequently updated, complex guidelines through visuals like flowcharts and diagrams, which can be time consuming for oncologists to stay current with treatment protocols. This study presents an AI (Artificial Intelligence)-driven methodology to accurately automate treatment regimens following NCCN guidelines for breast cancer patients.   We proposed two AI-driven methods: Agentic-RAG (Retrieval-Augmented Generation) and Graph-RAG. Agentic-RAG used a three-step Large Language Model (LLM) process to select clinical titles from NCCN guidelines, retrieve matching JSON content, and iteratively refine recommendations based on insufficiency checks. Graph-RAG followed a Microsoft-developed framework with proprietary prompts, where JSON data was converted to text via an LLM, summarized, and mapped ...", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "published": "2025-01-06", "pdf_url": "http://arxiv.org/pdf/2502.15698v1", "categories": ["cs.IR"]}, {"url": "http://arxiv.org/abs/2501.18320v1", "title": "Leveraging LLM Agents for Automated Optimization Modeling for SASP Problems: A Graph-RAG based Approach", "description": "Automated optimization modeling (AOM) has evoked considerable interest with the rapid evolution of large language models (LLMs). Existing approaches predominantly rely on prompt engineering, utilizing meticulously designed expert response chains or structured guidance. However, prompt-based techniques have failed to perform well in the sensor array signal processing (SASP) area due the lack of specific domain knowledge. To address this issue, we propose an automated modeling approach based on retrieval-augmented generation (RAG) technique, which consists of two principal components: a multi-agent (MA) structure and a graph-based RAG (Graph-RAG) process. The MA structure is tailored for the architectural AOM process, with each agent being designed based on principles of human modeling procedure. The Graph-RAG process serves to match user query with specific SASP modeling knowledge, thereby enhancing the modeling result. Results on ten classical signal processing problems demonstrate ...", "authors": ["<PERSON><PERSON><PERSON>g Pan", "<PERSON><PERSON><PERSON>", "Licheng Zhao", "<PERSON><PERSON>"], "published": "2025-01-30", "pdf_url": "http://arxiv.org/pdf/2501.18320v1", "categories": ["cs.AI", "eess.SP"]}, {"url": "http://arxiv.org/abs/2501.00332v1", "title": "MAIN-RAG: Multi-Agent Filtering Retrieval-Augmented Generation", "description": "Large Language Models (LLMs) are becoming essential tools for various natural language processing tasks but often suffer from generating outdated or incorrect information. Retrieval-Augmented Generation (RAG) addresses this issue by incorporating external, real-time information retrieval to ground LLM responses. However, the existing RAG systems frequently struggle with the quality of retrieval documents, as irrelevant or noisy documents degrade performance, increase computational overhead, and undermine response reliability. To tackle this problem, we propose Multi-Agent Filtering Retrieval-Augmented Generation (MAIN-RAG), a training-free RAG framework that leverages multiple LLM agents to collaboratively filter and score retrieved documents. Specifically, MAIN-RAG introduces an adaptive filtering mechanism that dynamically adjusts the relevance filtering threshold based on score distributions, effectively minimizing noise while maintaining high recall of relevant documents. The pr...", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mingzhi Hu", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "published": "2024-12-31", "pdf_url": "http://arxiv.org/pdf/2501.00332v1", "categories": ["cs.CL", "cs.IR"]}, {"url": "http://arxiv.org/abs/2408.05933v1", "title": "Optimizing RAG Techniques for Automotive Industry PDF Chatbots: A Case Study with Locally Deployed Ollama Models", "description": "With the growing demand for offline PDF chatbots in automotive industrial production environments, optimizing the deployment of large language models (LLMs) in local, low-performance settings has become increasingly important. This study focuses on enhancing Retrieval-Augmented Generation (RAG) techniques for processing complex automotive industry documents using locally deployed Ollama models. Based on the Langchain framework, we propose a multi-dimensional optimization approach for Ollama's local RAG implementation. Our method addresses key challenges in automotive document processing, including multi-column layouts and technical specifications. We introduce improvements in PDF processing, retrieval mechanisms, and context compression, tailored to the unique characteristics of automotive industry documents. Additionally, we design custom classes supporting embedding pipelines and an agent supporting self-RAG based on LangGraph best practices. To evaluate our approach, we construct...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Xing Han"], "published": "2024-08-12", "pdf_url": "http://arxiv.org/pdf/2408.05933v1", "categories": ["cs.IR", "cs.AI", "cs.MA"]}]