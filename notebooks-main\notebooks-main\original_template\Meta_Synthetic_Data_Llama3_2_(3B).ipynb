{"cells": [{"cell_type": "markdown", "metadata": {"id": "Om2qjxs5PSr0"}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {"id": "RCa86oMuPSr0"}, "source": ["Placehoder"]}, {"cell_type": "markdown", "metadata": {"id": "imwJhjjYPSr0"}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Placeholder"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Placeholder"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Primary Goal\n", "Our goal is to make Llama 3.2 3B understand the \"Byte Latent Transformer: Patches Scale Better Than Tokens\" [research paper](https://ai.meta.com/research/publications/byte-latent-transformer-patches-scale-better-than-tokens/) that was published in December 2024.\n", "\n", "We'll use https://github.com/meta-llama/synthetic-data-kit to generate question and answer pairs **fully locally** which will be used for finetuning Llama 3.2 3B!"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 937, "referenced_widgets": ["7ba927b6ec4e41c39cb445443c301c57", "81c79f2262d04a8aba854e679f5e1dac", "734ba48df6c34b8fb20e92f99c4e4aa2", "fde296d28c5d4c61b384a4553b499d69", "442f7ccc30314a58b1aabc91cf4dbe48", "1f65e57e365e47eeb83cb259efbd698b", "772f1cc7afea484bbb3cb1cc6aa7576f", "0d66ab586e7f4f588b3e70bb0d6191a3", "d527203d8b414db08f40dd6be3235c5c", "50f52e4c2e5e4513b0d1fb23c39657f8", "73d0263833c147fc99cc5be1ce5357e9", "09b1ea46e7d4437bac1539aeb1a47fbe", "ecfbc357537f493e83f9a84606581f4c", "425f26b7325541569144cd46eaefcdc0", "1739bb52ba624c67804927dd372649e7", "33262ee83efa4523b434a4c456067d18", "8e20e88da0f34b6fadf118a0443674c6", "d5500717a6534c768be6a07913b6a339", "7f246d9fe6e9415596645f736027d246", "1d95662d1741434581d501e83b99c778", "ed0ba5d3437b492dbdb9980ae677c35c", "122c08b183f0425f881acef17f5daf08", "5454423e6291484bb63b310699f5d645", "7e23ee6479ee484e90b93c572848a2b8", "cea072caa7694eeb8b858ea0624e88fa", "812390f2da734ba9b516af73ddb8f452", "f83ddfe2510447d0a6c08b109cf42dbf", "cab7a4bf98cf42c69f81b9c4b7b4b21d", "1a155e85dd674f468d8b240481413722", "1b032fc19dfa46a5add673aa9c6aeda5", "df624458fe5a4a21b58602af80fa0a1c", "bf983c3e807d4e25ba8c2a079aea34bf", "315f16beade4418390ad511458013b81", "9a62a83412df4453840f06c97ebcc216", "6b9895bccc364862a39b1c70d9612ba5", "2eee87d6fc0445038e9ccf896cfa3d77", "5c022f8ebf4c44baa2195ca706b687e7", "834de6e5d5504ba794c43d186d6de8a7", "f4c156d84ae041339c5fef3c9ce6ba30", "6b9d7dd7465a4fbfbfbca7cc5dfc2c23", "2f5974e37f7847efa368f06b96ae9481", "97ff30809ee4484988b6d0de7acd5c04", "b0c12a5f8a9a4a7c8a5584b54acb6adb", "5738348f2e70424babbd91c44680b87a"]}, "id": "Ym8UA1PRiXsa", "outputId": "1dc83bf1-adf6-47d8-fdea-6a4c2f26ffb2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "🦥 Unsloth Zoo will now patch everything to make training faster!\n", "INFO 05-01 14:15:23 [__init__.py:239] Automatically detected platform cuda.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7ba927b6ec4e41c39cb445443c301c57", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/890 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "09b1ea46e7d4437bac1539aeb1a47fbe", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/54.7k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5454423e6291484bb63b310699f5d645", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/17.2M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9a62a83412df4453840f06c97ebcc216", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/454 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Using dtype = torch.float16 for vLLM.\n", "Unsloth: vLLM loading unsloth/Llama-3.2-3B-Instruct with actual GPU utilization = 89.39%\n", "Unsloth: Your GPU has CUDA compute capability 7.5 with VRAM = 14.74 GB.\n", "Unsloth: Using conservativeness = 1.0. Chunked prefill tokens = 2048. Num Sequences = 192.\n", "Unsloth: vLLM's KV Cache can use up to 7.19 GB. Also swap space = 2 GB.\n", "vLLM STDOUT: INFO 05-01 14:15:38 [__init__.py:239] Automatically detected platform cuda.\n", "vLLM STDOUT: INFO 05-01 14:15:39 [api_server.py:981] vLLM API server version 0.8.2\n", "vLLM STDOUT: INFO 05-01 14:15:39 [api_server.py:982] args: Namespace(subparser='serve', model_tag='unsloth/Llama-3.2-3B-Instruct', config='', host=None, port=8000, uvicorn_log_level='info', disable_uvicorn_access_log=False, allow_credentials=False, allowed_origins=['*'], allowed_methods=['*'], allowed_headers=['*'], api_key=None, lora_modules=None, prompt_adapters=None, chat_template=None, chat_template_content_format='auto', response_role='assistant', ssl_keyfile=None, ssl_certfile=None, ssl_ca_certs=None, enable_ssl_refresh=False, ssl_cert_reqs=0, root_path=None, middleware=[], return_tokens_as_token_ids=False, disable_frontend_multiprocessing=False, enable_request_id_headers=False, enable_auto_tool_choice=False, tool_call_parser=None, tool_parser_plugin='', model='unsloth/Llama-3.2-3B-Instruct', task='auto', tokenizer=None, hf_config_path=None, skip_tokenizer_init=False, revision=None, code_revision=None, tokenizer_revision=None, tokenizer_mode='auto', trust_remote_code=False, allowed_local_media_path=None, download_dir=None, load_format='auto', config_format=<ConfigFormat.AUTO: 'auto'>, dtype='float16', kv_cache_dtype='auto', max_model_len=2048, guided_decoding_backend='xgrammar', logits_processor_pattern=None, model_impl='auto', distributed_executor_backend=None, pipeline_parallel_size=1, tensor_parallel_size=1, enable_expert_parallel=False, max_parallel_loading_workers=None, ray_workers_use_nsight=False, block_size=None, enable_prefix_caching=True, disable_sliding_window=False, use_v2_block_manager=True, num_lookahead_slots=0, seed=0, swap_space=2.0, cpu_offload_gb=0, gpu_memory_utilization=0.8938626454842437, num_gpu_blocks_override=None, max_num_batched_tokens=2048, max_num_partial_prefills=1, max_long_partial_prefills=1, long_prefill_token_threshold=0, max_num_seqs=192, max_logprobs=0, disable_log_stats=True, quantization=None, rope_scaling=None, rope_theta=None, hf_overrides=None, enforce_eager=False, max_seq_len_to_capture=2304, disable_custom_all_reduce=False, tokenizer_pool_size=0, tokenizer_pool_type='ray', tokenizer_pool_extra_config=None, limit_mm_per_prompt=None, mm_processor_kwargs=None, disable_mm_preprocessor_cache=False, enable_lora=False, enable_lora_bias=False, max_loras=1, max_lora_rank=16, lora_extra_vocab_size=256, lora_dtype='auto', long_lora_scaling_factors=None, max_cpu_loras=None, fully_sharded_loras=False, enable_prompt_adapter=False, max_prompt_adapters=1, max_prompt_adapter_token=0, device='auto', num_scheduler_steps=1, use_tqdm_on_load=True, multi_step_stream_outputs=True, scheduler_delay_factor=0.0, enable_chunked_prefill=None, speculative_config=None, speculative_model=None, speculative_model_quantization=None, num_speculative_tokens=None, speculative_disable_mqa_scorer=False, speculative_draft_tensor_parallel_size=None, speculative_max_model_len=None, speculative_disable_by_batch_size=None, ngram_prompt_lookup_max=None, ngram_prompt_lookup_min=None, spec_decoding_acceptance_method='rejection_sampler', typical_acceptance_sampler_posterior_threshold=None, typical_acceptance_sampler_posterior_alpha=None, disable_logprobs_during_spec_decoding=None, model_loader_extra_config=None, ignore_patterns=[], preemption_mode=None, served_model_name=None, qlora_adapter_name_or_path=None, show_hidden_metrics_for_version=None, otlp_traces_endpoint=None, collect_detailed_traces=None, disable_async_output_proc=False, scheduling_policy='fcfs', scheduler_cls='vllm.core.scheduler.Scheduler', override_neuron_config=None, override_pooler_config=None, compilation_config={\"level\":3,\"splitting_ops\":[]}, kv_transfer_config=None, worker_cls='auto', worker_extension_cls='', generation_config='auto', override_generation_config=None, enable_sleep_mode=False, calculate_kv_scales=False, additional_config=None, enable_reasoning=False, reasoning_parser=None, disable_cascade_attn=False, disable_log_requests=False, max_log_len=None, disable_fastapi_docs=False, enable_prompt_tokens_details=False, enable_server_load_tracking=False, dispatch_function=<function ServeSubcommand.cmd at 0x7fb5b68e1a80>)\n", "vLLM STDOUT: WARNING 05-01 14:15:40 [config.py:2614] Casting torch.bfloat16 to torch.float16.\n", "vLLM STDOUT: INFO 05-01 14:15:54 [config.py:585] This model supports multiple tasks: {'classify', 'embed', 'generate', 'reward', 'score'}. Defaulting to 'generate'.\n", "vLLM STDOUT: WARNING 05-01 14:15:54 [arg_utils.py:1854] Compute Capability < 8.0 is not supported by the V1 Engine. Falling back to V0.\n", "vLLM STDOUT: INFO 05-01 14:15:54 [api_server.py:241] Started engine process with PID 2108\n", "vLLM STDOUT: INFO 05-01 14:16:02 [__init__.py:239] Automatically detected platform cuda.\n", "vLLM STDOUT: INFO 05-01 14:16:03 [llm_engine.py:241] Initializing a V0 LLM engine (v0.8.2) with config: model='unsloth/Llama-3.2-3B-Instruct', speculative_config=None, tokenizer='unsloth/Llama-3.2-3B-Instruct', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=2048, download_dir=None, load_format=LoadFormat.AUTO, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto,  device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='xgrammar', reasoning_backend=None), observability_config=ObservabilityConfig(show_hidden_metrics=False, otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=unsloth/Llama-3.2-3B-Instruct, num_scheduler_steps=1, multi_step_stream_outputs=True, enable_prefix_caching=True, chunked_prefill_enabled=False, use_async_output_proc=True, disable_mm_preprocessor_cache=False, mm_processor_kwargs=None, pooler_config=None, compilation_config={\"level\":3,\"splitting_ops\":[],\"compile_sizes\":[],\"cudagraph_capture_sizes\":[192,184,176,168,160,152,144,136,128,120,112,104,96,88,80,72,64,56,48,40,32,24,16,8,4,2,1],\"max_capture_size\":192}, use_cached_outputs=True,\n", "vLLM STDOUT: INFO 05-01 14:16:06 [cuda.py:239] Cannot use FlashAttention-2 backend for Volta and Turing GPUs.\n", "vLLM STDOUT: INFO 05-01 14:16:06 [cuda.py:288] Using XFormers backend.\n", "vLLM STDOUT: INFO 05-01 14:16:07 [parallel_state.py:954] rank 0 in world size 1 is assigned as DP rank 0, PP rank 0, TP rank 0\n", "vLLM STDOUT: INFO 05-01 14:16:07 [model_runner.py:1110] Starting to load model unsloth/Llama-3.2-3B-Instruct...\n", "vLLM STDOUT: INFO 05-01 14:16:08 [weight_utils.py:265] Using model weights format ['*.safetensors']\n", "vLLM STDOUT: INFO 05-01 14:25:37 [weight_utils.py:281] Time spent downloading weights for unsloth/Llama-3.2-3B-Instruct: 569.324218 seconds\n", "vLLM STDOUT: INFO 05-01 14:26:04 [loader.py:447] Loading weights took 27.20 seconds\n", "vLLM STDOUT: INFO 05-01 14:26:05 [model_runner.py:1146] Model loading took 6.0160 GB and 597.363926 seconds\n", "vLLM STDOUT: INFO 05-01 14:26:17 [backends.py:415] Using cache directory: /root/.cache/vllm/torch_compile_cache/3fbc507988/rank_0_0 for vLLM's torch.compile\n", "vLLM STDOUT: INFO 05-01 14:26:17 [backends.py:425] Dynamo bytecode transform time: 11.58 s\n", "vLLM STDOUT: INFO 05-01 14:26:58 [backends.py:132] Cache the graph of shape None for later use\n", "vLLM STDOUT: INFO 05-01 14:26:58 [backends.py:144] Compiling a graph for general shape takes 36.18 s\n", "vLLM STDOUT: INFO 05-01 14:26:59 [monitor.py:33] torch.compile takes 47.76 s in total\n", "vLLM STDOUT: INFO 05-01 14:27:02 [worker.py:267] Memory profiling takes 56.21 seconds\n", "vLLM STDOUT: INFO 05-01 14:27:02 [worker.py:267] the current vLLM instance can use total_gpu_memory (14.74GiB) x gpu_memory_utilization (0.89) = 13.18GiB\n", "vLLM STDOUT: INFO 05-01 14:27:02 [worker.py:267] model weights take 6.02GiB; non_torch_memory takes 0.05GiB; PyTorch activation peak memory takes 0.90GiB; the rest of the memory reserved for KV Cache is 6.22GiB.\n", "vLLM STDOUT: INFO 05-01 14:27:02 [executor_base.py:111] # cuda blocks: 3637, # CPU blocks: 1170\n", "vLLM STDOUT: INFO 05-01 14:27:02 [executor_base.py:116] Maximum concurrency for 2048 tokens per request: 28.41x\n", "vLLM STDOUT: INFO 05-01 14:27:05 [model_runner.py:1442] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI. If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.\n", "vLLM STDOUT: INFO 05-01 14:27:46 [model_runner.py:1570] Graph capturing finished in 41 secs, took 0.17 GiB\n", "vLLM STDOUT: INFO 05-01 14:27:46 [llm_engine.py:447] init engine (profile, create kv cache, warmup model) took 100.89 seconds\n", "vLLM STDOUT: WARNING 05-01 14:27:47 [config.py:1028] Default sampling parameters have been overridden by the model's Hugging Face generation config recommended from the model creator. If this is not intended, please relaunch vLLM instance with `--generation-config vllm`.\n", "vLLM STDOUT: INFO 05-01 14:27:47 [serving_chat.py:115] Using default chat sampling params from model: {'temperature': 0.6, 'top_p': 0.9}\n", "vLLM STDOUT: INFO 05-01 14:27:47 [serving_completion.py:61] Using default completion sampling params from model: {'temperature': 0.6, 'top_p': 0.9}\n", "vLLM STDOUT: INFO 05-01 14:27:47 [api_server.py:1028] Starting vLLM API server on http://0.0.0.0:8000\n", "\n", "--- vLLM Server Ready (Detected: 'Starting vLLM API server on') ---\n"]}], "source": ["from unsloth.dataprep import SyntheticDataKit\n", "\n", "generator = SyntheticDataKit.from_pretrained(\n", "    # Choose any model from https://huggingface.co/unsloth\n", "    model_name = \"unsloth/Llama-3.2-3B-Instruct\",\n", "    max_seq_length = 2048, # Longer sequence lengths will be slower!\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "Ef_MnK575tr2"}, "source": ["## Generate QA Pairs + Auto clean data\n", "We now use synthetic data kit for question answer pair generation:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "q487TNby-nwT"}, "outputs": [], "source": ["generator.prepare_qa_generation(\n", "    output_folder = \"data\", # Output location of synthetic data\n", "    temperature = 0.7, # Higher temp makes more diverse datases\n", "    top_p = 0.95,\n", "    overlap = 64, # Overlap portion during chunking\n", "    max_generation_tokens = 512, # Can increase for longer QA pairs\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "yV7DyufR51IN"}, "source": ["Check if it succeeded:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "C2gQZcr_Wp94", "outputId": "6c1fc4cb-de58-4573-a8c4-7c7d05f4f07f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[?25l\u001b[32m VLLM server is running at \u001b[0m\u001b[4;94mhttp://localhost:8000/v1\u001b[0m\n", "\u001b[32m⠋\u001b[0m\u001b[32m Checking VLLM server at http://localhost:8000/v1...\u001b[0m\r\u001b[2KAvailable models: \u001b[1m{\u001b[0m\u001b[32m'object'\u001b[0m: \u001b[32m'list'\u001b[0m, \u001b[32m'data'\u001b[0m: \u001b[1m[\u001b[0m\u001b[1m{\u001b[0m\u001b[32m'id'\u001b[0m: \n", "\u001b[32m'unsloth/Llama-3.2-3B-Instruct'\u001b[0m, \u001b[32m'object'\u001b[0m: \u001b[32m'model'\u001b[0m, \u001b[32m'created'\u001b[0m: \u001b[1;36m1746109670\u001b[0m, \n", "\u001b[32m'owned_by'\u001b[0m: \u001b[32m'vllm'\u001b[0m, \u001b[32m'root'\u001b[0m: \u001b[32m'unsloth/Llama-3.2-3B-Instruct'\u001b[0m, \u001b[32m'parent'\u001b[0m: \u001b[3;35mNone\u001b[0m, \n", "\u001b[32m'max_model_len'\u001b[0m: \u001b[1;36m2048\u001b[0m, \u001b[32m'permission'\u001b[0m: \u001b[1m[\u001b[0m\u001b[1m{\u001b[0m\u001b[32m'id'\u001b[0m: \n", "\u001b[32m'modelperm-4b22dda9834e436cb033f1d00ee42ee8'\u001b[0m, \u001b[32m'object'\u001b[0m: \u001b[32m'model_permission'\u001b[0m, \n", "\u001b[32m'created'\u001b[0m: \u001b[1;36m1746109670\u001b[0m, \u001b[32m'allow_create_engine'\u001b[0m: \u001b[3;91mFalse\u001b[0m, \u001b[32m'allow_sampling'\u001b[0m: \u001b[3;92mTrue\u001b[0m, \n", "\u001b[32m'allow_logprobs'\u001b[0m: \u001b[3;92mTrue\u001b[0m, \u001b[32m'allow_search_indices'\u001b[0m: \u001b[3;91mFalse\u001b[0m, \u001b[32m'allow_view'\u001b[0m: \u001b[3;92mTrue\u001b[0m, \n", "\u001b[32m'allow_fine_tuning'\u001b[0m: \u001b[3;91mFalse\u001b[0m, \u001b[32m'organization'\u001b[0m: \u001b[32m'*'\u001b[0m, \u001b[32m'group'\u001b[0m: \u001b[3;35mNone\u001b[0m, \u001b[32m'is_blocking'\u001b[0m: \n", "\u001b[3;91mFalse\u001b[0m\u001b[1m}\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\u001b[1m]\u001b[0m\u001b[1m}\u001b[0m\n", "\u001b[32m⠋\u001b[0m Checking VLLM server at http://localhost:8000/v1...\r\u001b[2K\u001b[32m⠋\u001b[0m Checking VLLM server at http://localhost:8000/v1...\n", "\u001b[?25h\r\u001b[1A\u001b[2K"]}], "source": ["!synthetic-data-kit system-check"]}, {"cell_type": "markdown", "metadata": {"id": "xdl7aPFK55M1"}, "source": ["## Document Parsing (PDF, CSV, HTML etc.)\n", "Now, let's take the Byte Latent Transformer: Patches Scale Better Than Tokens research paper found at https://arxiv.org/abs/2412.09871 and covert it to Q&A pairs in order to finetune Llama 3.2!"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "8BN1yrPGmANA", "outputId": "18ad218b-7977-4daf-9662-ddfb32e9972e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2K\u001b[32m⠴\u001b[0m Processing https://arxiv.org/html/2412.09871v1...\n", "\u001b[1A\u001b[2K\u001b[32m Text successfully extracted to \u001b[0m\u001b[1;32mdata/output/arxiv_org.txt\u001b[0m\n", "37 ['data/output/arxiv_org_0.txt', 'data/output/arxiv_org_1.txt', 'data/output/arxiv_org_2.txt']\n"]}], "source": ["# Byte Latent Transformer: Patches Scale Better Than Tokens paper in HTML format\n", "!synthetic-data-kit \\\n", "    -c synthetic_data_kit_config.yaml \\\n", "    ingest \"https://arxiv.org/html/2412.09871v1\"\n", "\n", "# Truncate document\n", "filenames = generator.chunk_data(\"data/output/arxiv_org.txt\")\n", "print(len(filenames), filenames[:3])"]}, {"cell_type": "markdown", "metadata": {"id": "nGdAXafV6S2M"}, "source": ["We see around 37 chunks of data. We now call synthetic-data-kit to create some pairs of data for 3 of our chunks.\n", "\n", "You can process more chunks, but it'll be much slower!\n", "\n", "Using `--num-pairs` will generate **approximately** that many QA pairs. However it might be shorter or longer depending on the `max_seq_length` of the loaded up model. So if you specify 100, you might only get 10 since the model's max sequence length is capped."]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pYYYlMJ7ZtT7", "outputId": "d83c7691-944c-45ab-9fc9-19434a34cf2a"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2KProcessing 1 chunks to generate QA pairs...\n", "\u001b[2KBatch processing complete.\n", "\u001b[2KGenerated 10 QA pairs total\n", "\u001b[2KSaving result to data/generated/arxiv_org_0_qa_pairs.json\n", "\u001b[2KSuccessfully wrote test file to data/generated/test_write.json\n", "\u001b[2KSuccessfully wrote result to data/generated/arxiv_org_0_qa_pairs.json\n", "\u001b[2K\u001b[32m⠇\u001b[0m Generating qa content from data/output/arxiv_org_0.txt...\n", "\u001b[1A\u001b[2K\u001b[32m Content saved to \u001b[0m\u001b[1;32mdata/generated/arxiv_org_0_qa_pairs.json\u001b[0m\n", "\u001b[2KProcessing 1 chunks to generate QA pairs...\n", "\u001b[2KBatch processing complete.\n", "\u001b[2KGenerated 12 QA pairs total\n", "\u001b[2KSaving result to data/generated/arxiv_org_1_qa_pairs.json\n", "\u001b[2KSuccessfully wrote test file to data/generated/test_write.json\n", "\u001b[2KSuccessfully wrote result to data/generated/arxiv_org_1_qa_pairs.json\n", "\u001b[2K\u001b[32m⠧\u001b[0m Generating qa content from data/output/arxiv_org_1.txt...\n", "\u001b[1A\u001b[2K\u001b[32m Content saved to \u001b[0m\u001b[1;32mdata/generated/arxiv_org_1_qa_pairs.json\u001b[0m\n", "\u001b[2KProcessing 1 chunks to generate QA pairs...\n", "\u001b[2KBatch processing complete.\n", "\u001b[2KGenerated 13 QA pairs total\n", "\u001b[2KSaving result to data/generated/arxiv_org_2_qa_pairs.json\n", "\u001b[2KSuccessfully wrote test file to data/generated/test_write.json\n", "\u001b[2KSuccessfully wrote result to data/generated/arxiv_org_2_qa_pairs.json\n", "\u001b[2K\u001b[32m⠴\u001b[0m Generating qa content from data/output/arxiv_org_2.txt...\n", "\u001b[1A\u001b[2K\u001b[32m Content saved to \u001b[0m\u001b[1;32mdata/generated/arxiv_org_2_qa_pairs.json\u001b[0m\n"]}], "source": ["import time\n", "# Process 3 chunks for now -> can increase but slower!\n", "for filename in filenames[:3]:\n", "    !synthetic-data-kit \\\n", "        -c synthetic_data_kit_config.yaml \\\n", "        create {filename} \\\n", "        --num-pairs 25 \\\n", "        --type \"qa\"\n", "    time.sleep(2) # Sleep some time to leave some room for processing"]}, {"cell_type": "markdown", "metadata": {"id": "cNkxxvBx7Csp"}, "source": ["Optionally, you can clean up the data via pruning \"bad\" or low quality examples and convert the rest to JSON format for finetuning!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "HMD-izj5OiAK"}, "outputs": [], "source": ["# !synthetic-data-kit \\\n", "#     -c synthetic_data_kit_config.yaml \\\n", "#     curate --threshold 0.0 \\\n", "#     \"data/generated/arxiv_org_0_qa_pairs.json\""]}, {"cell_type": "markdown", "metadata": {"id": "AScJ5-vAOjYj"}, "source": ["We now convert the generated datasets into QA formats so we can load it for finetuning:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "J9Um4Z8SqUTB", "outputId": "d7880fb6-7548-45f0-9112-9837094285b9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[?25l\u001b[32m⠋\u001b[0m Converting data/generated/arxiv_org_0_qa_pairs.json to ft format with json \n", "storage...\n", "\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[32m Converted to ft format and saved to \u001b[0m\u001b[1;32mdata/final/arxiv_org_0_qa_pairs_ft.json\u001b[0m\n", "\u001b[?25l\u001b[32m⠋\u001b[0m Converting data/generated/arxiv_org_1_qa_pairs.json to ft format with json \n", "storage...\n", "\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[32m Converted to ft format and saved to \u001b[0m\u001b[1;32mdata/final/arxiv_org_1_qa_pairs_ft.json\u001b[0m\n", "\u001b[?25l\u001b[32m⠋\u001b[0m Converting data/generated/arxiv_org_2_qa_pairs.json to ft format with json \n", "storage...\n", "\u001b[1A\u001b[2K\u001b[1A\u001b[2K\u001b[32m Converted to ft format and saved to \u001b[0m\u001b[1;32mdata/final/arxiv_org_2_qa_pairs_ft.json\u001b[0m\n"]}], "source": ["qa_pairs_filenames = [\n", "    f\"data/generated/arxiv_org_{i}_qa_pairs.json\"\n", "    for i in range(len(filenames[:3]))\n", "]\n", "for filename in qa_pairs_filenames:\n", "    !synthetic-data-kit \\\n", "        -c synthetic_data_kit_config.yaml \\\n", "        save-as {filename} -f ft"]}, {"cell_type": "markdown", "metadata": {"id": "6dVK-qza7rPB"}, "source": ["Let's load up the data and see what the synthetic data looks like!"]}, {"cell_type": "code", "execution_count": 25, "metadata": {"id": "VrBwG2KT7dam"}, "outputs": [], "source": ["from datasets import Dataset\n", "import pandas as pd\n", "final_filenames = [\n", "    f\"data/final/arxiv_org_{i}_qa_pairs_ft.json\"\n", "    for i in range(len(filenames[:3]))\n", "]\n", "conversations = pd.concat([\n", "    pd.read_json(name) for name in final_filenames\n", "]).reset_index(drop = True)\n", "\n", "dataset = Dataset.from_pandas(conversations)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "jaZ3tRP8frSn", "outputId": "c1ec0e11-0a8e-4969-bb9d-c4496822ba9d"}, "outputs": [{"data": {"text/plain": ["{'messages': [{'content': 'You are a helpful assistant.', 'role': 'system'},\n", "  {'content': 'What is the primary unit of computation in the Byte Latent Transformer (BLT) architecture?',\n", "   'role': 'user'},\n", "  {'content': '<PERSON><PERSON>', 'role': 'assistant'}]}"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[0]"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "504n46Sxfruu", "outputId": "815928be-e07f-4257-ddd7-751cf8008cdb"}, "outputs": [{"data": {"text/plain": ["{'messages': [{'content': 'You are a helpful assistant.', 'role': 'system'},\n", "  {'content': 'How do patches in BLT architecture get segmented?',\n", "   'role': 'user'},\n", "  {'content': 'Based on the entropy of the next byte', 'role': 'assistant'}]}"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[1]"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1BVBp9YXRw_o", "outputId": "6c7fcf69-7de3-4bf7-8037-cb0477bce3b7"}, "outputs": [{"data": {"text/plain": ["{'messages': [{'content': 'You are a helpful assistant.', 'role': 'system'},\n", "  {'content': 'What is the main purpose of the formal definition of the patching function?',\n", "   'role': 'user'},\n", "  {'content': 'to formally describe the process of segmenting bytes into patches',\n", "   'role': 'assistant'}]}"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[-1]"]}, {"cell_type": "markdown", "metadata": {"id": "aO9qePmP7yaY"}, "source": ["Finally free vLLM process to save memory and to allow for finetuning!"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "F8qgTjywzgl6", "outputId": "f54b04cd-9586-42ff-8276-c4c54496560b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to terminate the VLLM server gracefully...\n", "Server terminated gracefully.\n"]}], "source": ["generator.cleanup()"]}, {"cell_type": "markdown", "metadata": {"id": "EQo2PR7oqDQE"}, "source": ["### Fine-tuning Synthetic Dataset with <PERSON>sloth"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 282, "referenced_widgets": ["fc9c4d66a2fa41a393525a04ced011f3", "0365c9b5e9a44318a70109069135b7fe", "5759ecdb02714a9f82892e12c6b9ae46", "85ff0a11606e466ebd7f7b3b6ed3048e", "cae045b72aca476a9c16c39778be53a0", "c0cf2ea32d7e42d2b6cb00ba5b128e6d", "5856dc5484e54deaae2c0ea0703885cb", "f229d574614945029666403a10bb761e", "b6da3300ba7e42888add16741bafd614", "5d6cb85f619b439db3544414b732607c", "9f083cc2ddb64b6196cc2d170affabae", "3ca561741e5f4b59b796b074cac64d80", "15c3082a57e04b0198a8232f135290ef", "7cd6c8ef7ec9451c90560208277975c5", "f631738fd3014d13a7e6a50a66eaf92a", "f8b4855c3f0146dd8d3d0876f3a12c08", "a87d29f844c449119a95f5548c0af0c7", "abe93b4cbe524be08d08ff75f22d0c08", "9f60d2b4aa054b82bcef0b49cd4014f3", "27be088be2de4517964a8e5217641fd9", "7f616bf83de54cfeaabda52b3f592e4c", "2f822636140c45e8a9713b711d057005", "d944a4b11b614896bdb6af583faed134", "83657ceb17f1454ca7e33a5fcc5ad5f7", "c6f86936f86347819002d6bbf50ec484", "cf6e3f6267cb49d5ac4575d1d1e41d10", "2d0a363ce9c54665811b8cd0a8fdb42b", "bbc163a35ec54087bf0c56cc332df0b4", "c901f4517b474511b6dd880e63f25b58", "74806db2db6e4085a28dceca4fbeee28", "e2f989df5f494351a5b242d2430c2917", "ef715f56f3824a64949be1f2bb05296b", "cd3f2a8158124c6b87be72e959a455d5", "62e6190f733844319221ce6eb7a7928c", "acd616b0a9574855a3d4525e72a6513a", "9b27fcca9ec64b81952560f38080abc6", "2f88451af2cd48aa93da32431e5de139", "55e82538888940c1b754fa02d29e965c", "2b15c1d19cf84911bee54a6d113eed1a", "cfdfb65ccad04899881976d93945bf0d", "e3ab144489504058b7c9009d867bef95", "943c7280c19948999aa663ca6736d698", "47d0de835e7d48e39161a7f038167653", "6956008e9def426b82617536b015dd38", "4952048f852a4593af48573a163a052f", "a6e9f1533f83416c8b512f31b099254c", "937a90e4c4dc41b195d24fa1694c9157", "b82aae8041fa462d88467a02fa252af7", "e7d6be17a7bb4d5e8b248d7f5dee9647", "fc915d911ee144bea96b1885f5eb7191", "caed1189f10140549fe2b445dd7dbd16", "8c6845e9bd81418e8c02d66c32046129", "2fb3b417c2404e8a9ad83b1a4114174c", "e22b473dd97446298b42987e36d945ec", "9a18c25e8d694fff905917e7ce2931dd"]}, "id": "QmUBVEnvCDJv", "outputId": "b094477b-ebf4-4980-934b-686f135e1347"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==((====))==  Unsloth 2025.4.3: Fast Llama patching. Transformers: 4.51.3. vLLM: 0.8.2.\n", "   \\\\   /|    Tesla T4. Num GPUs = 1. Max memory: 14.741 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.6.0+cu124. CUDA: 7.5. CUDA Toolkit: 12.4. Triton: 3.2.0\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.29.post3. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fc9c4d66a2fa41a393525a04ced011f3", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/2.35G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "3ca561741e5f4b59b796b074cac64d80", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/234 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d944a4b11b614896bdb6af583faed134", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/54.7k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "62e6190f733844319221ce6eb7a7928c", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/17.2M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4952048f852a4593af48573a163a052f", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/454 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "\n", "fourbit_models = [\n", "    # 4bit dynamic quants for superior accuracy and low memory use\n", "    \"unsloth/gemma-3-4b-it-unsloth-bnb-4bit\",\n", "    \"unsloth/gemma-3-12b-it-unsloth-bnb-4bit\",\n", "    \"unsloth/gemma-3-27b-it-unsloth-bnb-4bit\",\n", "    # Qwen3 new models\n", "    \"unsloth/Qwen3-4B-unsloth-bnb-4bit\",\n", "    \"unsloth/Qwen3-8B-unsloth-bnb-4bit\",\n", "    # Other very popular models!\n", "    \"unsloth/Llama-3.1-8B\",\n", "    \"unsloth/Llama-3.2-3B\",\n", "    \"unsloth/Llama-3.3-70B\",\n", "    \"unsloth/mistral-7b-instruct-v0.3\",\n", "    \"unsloth/Phi-4\",\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/Llama-3.2-3B-Instruct\",\n", "    max_seq_length = 2048, # Choose any for long context!\n", "    load_in_4bit = True,  # 4 bit quantization to reduce memory\n", "    load_in_8bit = False, # [NEW!] A bit more accurate, uses 2x memory\n", "    full_finetuning = False, # [NEW!] We have full finetuning now!\n", "    # token = \"hf_...\", # use one if using gated models\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "SXd9bTZd1aaL"}, "source": ["We now add LoRA adapters so we only need to update 1 to 10% of all parameters!"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6bZsfBuZDeCL", "outputId": "a8a510bf-226e-4baf-b333-0cbc58cf93a7"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth 2025.4.3 patched 28 layers with 28 QKV layers, 28 O layers and 28 MLP layers.\n"]}], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 16, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 16,\n", "    lora_dropout = 0, # Supports any, but = 0 is optimized\n", "    bias = \"none\",    # Supports any, but = \"none\" is optimized\n", "    # [NEW] \"unsloth\" uses 30% less VRAM, fits 2x larger batch sizes!\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for very long context\n", "    random_state = 3407,\n", "    use_rslora = False,  # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "vITh0KVJ10qX"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We now use the `Llama-3.2` format for conversation style finetunes. The chat template renders conversations like below: (Cutting Knowledge Date is by default there!)\n", "\n", "```\n", "<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n", "\n", "Cutting Knowledge Date: December 2023\n", "Today Date: 01 May 2025\n", "\n", "You are a helpful assistant.<|eot_id|><|start_header_id|>user<|end_header_id|>\n", "\n", "What is 1+1?<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n", "\n", "2<|eot_id|>\n", "```"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 49, "referenced_widgets": ["c7787082fecf4df0ae58e67f46e42589", "60c5e826831c4afbb869a1429d3a372e", "3fdc25451ed543d288d89df799883b3c", "079705095aa14fa0bd5af53459ad5bfb", "1082d64463a94f6282c1754720c702cd", "137ca939b43b4bc7b41da9005e28b646", "390ed406aa524d0c91e11ef74d01d700", "21ba911bfeb2439aa0ca135c299de1ba", "91ae4592c2d545bf9df31983257b6907", "1e5ffbbd4d954281af45f8d0f2488de7", "6751a852357447259e610430fdbb444b"]}, "id": "LjY75GoYUCB8", "outputId": "a8b657b0-e534-44a1-89a7-c7962e764ad2"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c7787082fecf4df0ae58e67f46e42589", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/35 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def formatting_prompts_func(examples):\n", "    convos = examples[\"messages\"]\n", "    texts = [tokenizer.apply_chat_template(convo, tokenize = False, add_generation_prompt = False) for convo in convos]\n", "    return { \"text\" : texts, }\n", "pass\n", "\n", "# Get our previous dataset and format it:\n", "dataset = dataset.map(formatting_prompts_func, batched = True,)"]}, {"cell_type": "markdown", "metadata": {"id": "YKA0VEF4CfCB"}, "source": ["See result of the first row:"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "0usAI0M40hpT", "outputId": "d9debc89-361b-4fb8-cf56-77acf55195cd"}, "outputs": [{"data": {"text/plain": ["{'messages': [{'content': 'You are a helpful assistant.', 'role': 'system'},\n", "  {'content': 'What is the primary unit of computation in the Byte Latent Transformer (BLT) architecture?',\n", "   'role': 'user'},\n", "  {'content': '<PERSON><PERSON>', 'role': 'assistant'}],\n", " 'text': '<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\nCutting Knowledge Date: December 2023\\nToday Date: 01 May 2025\\n\\nYou are a helpful assistant.<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\nWhat is the primary unit of computation in the Byte Latent Transformer (BLT) architecture?<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\nPatches<|eot_id|>'}"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[0]"]}, {"cell_type": "markdown", "metadata": {"id": "idAEIeSQ3xdS"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `SFTTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 60 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`."]}, {"cell_type": "code", "execution_count": 54, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 49, "referenced_widgets": ["cfd9f69db23f4b86a42179ac0ac91a8c", "e624589b592d43239e812e02fd3ab244", "e0311985f098497e821f7c8720457384", "7c05acca6aed46d49663afe10ebf6d9b", "8d38463677324bd79c193798157a1568", "b554950c61ae436dbb7772e7de173a81", "ab2bdd97242243d7aadfe932c72c39a2", "7be62d010b564972924fea1d8343398c", "14b6b0568a074f8eb20cb57ffbcbb0f3", "644e720f44d24d159133bfa515482054", "50066adf2c2d4f6abe16456cb3cee737"]}, "id": "95_Nn-89DhsL", "outputId": "6ee189f7-91bf-4064-faf5-c6a8a96823f5"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cfd9f69db23f4b86a42179ac0ac91a8c", "version_major": 2, "version_minor": 0}, "text/plain": ["Unsloth: Tokenizing [\"text\"] (num_proc=2):   0%|          | 0/35 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from trl import SFTTrainer, SFTConfig\n", "\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = dataset,\n", "    eval_dataset = None, # Can set up evaluation!\n", "    args = SFTConfig(\n", "        dataset_text_field = \"text\",\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4, # Use GA to mimic batch size!\n", "        warmup_steps = 5,\n", "        max_steps = 60,\n", "        learning_rate = 2e-4,\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "2ejIt2xSNKKp", "outputId": "11f7102e-4aed-4389-aab4-35aa346c9df6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU = Tesla T4. Max memory = 14.741 GB.\n", "3.441 GB of memory reserved.\n"]}], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "yqxqAZ7KJ4oL", "outputId": "1965bdc3-3caa-4d5d-8dab-aa93fc64fa39"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 35 | Num Epochs = 15 | Total steps = 60\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient accumulation steps = 4\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (2 x 4 x 1) = 8\n", " \"-____-\"     Trainable parameters = 24,313,856/3,000,000,000 (0.81% trained)\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='60' max='60' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [60/60 01:47, Epoch 12/15]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>1.770500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>1.901100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>1.770800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>1.705000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>1.940100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>1.610800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>1.617000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>1.426600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>1.255000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>1.410500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>1.328100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>1.193000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>1.215000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>1.027200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.933600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.976800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.928700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.865900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.956300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.925800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.853600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.663600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.772600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.609900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.634200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.556100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.489200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.523400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.465100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.545400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.373400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.358500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.344900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.302400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.400300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.294800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.272400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.264200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.233400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.244500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.212200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.192300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.177500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.175400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.176100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.146400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.142300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.133200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.132700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.147300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>0.118800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>0.116800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>0.106100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>0.101200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>0.101300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>0.096300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>0.103200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>0.099000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>0.107300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>0.105100</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": 42, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "pCqnaKmlO1U9", "outputId": "7d2e126d-42d9-4175-d909-31fe15a4193e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["110.0157 seconds used for training.\n", "1.83 minutes used for training.\n", "Peak reserved memory = 3.441 GB.\n", "Peak reserved memory for training = 0.0 GB.\n", "Peak reserved memory % of max memory = 23.343 %.\n", "Peak reserved memory for training % of max memory = 0.0 %.\n"]}], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {"id": "ekOmTR1hSNcr"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model! Use `apply_chat_template` with `add_generation_prompt` set to `True` for inference."]}, {"cell_type": "code", "execution_count": 43, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kR3gIAX-SM2q", "outputId": "76e23150-16b3-4ef9-9cf8-8c494bc0f1ad"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["The attention mask is not set and cannot be inferred from input because pad token is same as eos token. As a consequence, you may observe unexpected behavior. Please pass your input's `attention_mask` to obtain reliable results.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["A novel architecture that uses patches to encode bytes<|eot_id|>\n"]}], "source": ["messages = [\n", "    {\"role\": \"user\", \"content\": \"What is the Byte Latent Transformer?\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = True,\n", "    add_generation_prompt = True, # Must add for generation\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(input_ids = inputs, streamer = text_streamer,\n", "                   max_new_tokens = 256, temperature = 0.1)"]}, {"cell_type": "markdown", "metadata": {"id": "bRrr--20Udm9"}, "source": ["The model learns about the research paper!!"]}, {"cell_type": "code", "execution_count": 45, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "2strt31SUc5W", "outputId": "3359dc6a-f533-4960-a875-400af205e20d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Improved inference capabilities, enhanced model robustness, and the ability to scale while maintaining a fixed-inference budget<|eot_id|>\n"]}], "source": ["messages = [\n", "    {\"role\": \"user\", \"content\": \"What are some benefits of the BLT?\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = True,\n", "    add_generation_prompt = True, # Must add for generation\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(input_ids = inputs, streamer = text_streamer,\n", "                   max_new_tokens = 256, temperature = 0.1)"]}, {"cell_type": "markdown", "metadata": {"id": "uMuVrWbjAzhc"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": 46, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "upcOlWe7A1vc", "outputId": "b74c98e2-3909-46ff-f4c2-9ad838c7021b"}, "outputs": [{"data": {"text/plain": ["('lora_model/tokenizer_config.json',\n", " 'lora_model/special_tokens_map.json',\n", " 'lora_model/tokenizer.json')"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "AEEcJ4qfC7Lp"}, "source": ["Now if you want to load the LoRA adapters we just saved for inference, set `False` to `True`:"]}, {"cell_type": "code", "execution_count": 49, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MKX_XKs_BNZR", "outputId": "93cafd03-c9f7-4fdf-d031-f81e3497a839"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["It dynamically groups bytes into patches preserving access to the byte-level information<|eot_id|>\n"]}], "source": ["if False:\n", "    from unsloth import FastLanguageModel\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name = \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        max_seq_length = max_seq_length,\n", "        dtype = dtype,\n", "        load_in_4bit = load_in_4bit,\n", "    )\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"What is so special about BLT's tokenization?\"},\n", "]\n", "inputs = tokenizer.apply_chat_template(\n", "    messages,\n", "    tokenize = True,\n", "    add_generation_prompt = True, # Must add for generation\n", "    return_tensors = \"pt\",\n", ").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer, skip_prompt = True)\n", "_ = model.generate(input_ids = inputs, streamer = text_streamer,\n", "                   max_new_tokens = 256, temperature = 0.1)"]}, {"cell_type": "markdown", "metadata": {"id": "f422JgM9sdVT"}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16 or `merged_4bit` for int4. We also allow `lora` adapters as a fallback. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": 52, "metadata": {"id": "iHjt_SMYsd3P"}, "outputs": [], "source": ["# Merge to 16bit\n", "if False:\n", "    model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_16bit\",)\n", "if False: # Change to True to upload finetune\n", "    model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_16bit\", token = \"\")\n", "\n", "# Merge to 4bit\n", "if False:\n", "    model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_4bit\",)\n", "if False: # Change to True to upload finetune\n", "    model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_4bit\", token = \"\")\n", "\n", "# Just LoRA adapters\n", "if False:\n", "    model.save_pretrained_merged(\"model\", tokenizer, save_method = \"lora\",)\n", "if False: # Change to True to upload finetune\n", "    model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"lora\", token = \"\")"]}, {"cell_type": "markdown", "metadata": {"id": "TCv4vXHd61i7"}, "source": ["### GGUF / llama.cpp Conversion\n", "To save to `GGUF` / `llama.cpp`, we support it natively now! We clone `llama.cpp` and we default save it to `q8_0`. We allow all methods like `q4_k_m`. Use `save_pretrained_gguf` for local saving and `push_to_hub_gguf` for uploading to HF.\n", "\n", "Some supported quant methods (full list on our [Wiki page](https://github.com/unslothai/unsloth/wiki#gguf-quantization-options)):\n", "* `q8_0` - Fast conversion. High resource use, but generally acceptable.\n", "* `q4_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q4_K.\n", "* `q5_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q5_K."]}, {"cell_type": "code", "execution_count": 53, "metadata": {"id": "FqfebeAdT073"}, "outputs": [], "source": ["# Save to 8bit Q8_0\n", "if False:\n", "    model.save_pretrained_gguf(\"model\", tokenizer,)\n", "if False: # Change to True to upload finetune\n", "    model.push_to_hub_gguf(\"hf/model\", tokenizer, token = \"\")\n", "\n", "# Save to 16bit GGUF\n", "if False:\n", "    model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"f16\")\n", "if False: # Change to True to upload finetune\n", "    model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"f16\", token = \"\")\n", "\n", "# Save to q4_k_m GGUF\n", "if False:\n", "    model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"q4_k_m\")\n", "if False: # Change to True to upload finetune\n", "    model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"q4_k_m\", token = \"\")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "include_colab_link": true, "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"0365c9b5e9a44318a70109069135b7fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c0cf2ea32d7e42d2b6cb00ba5b128e6d", "placeholder": "​", "style": "IPY_MODEL_5856dc5484e54deaae2c0ea0703885cb", "value": "model.safetensors: 100%"}}, "079705095aa14fa0bd5af53459ad5bfb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1e5ffbbd4d954281af45f8d0f2488de7", "placeholder": "​", "style": "IPY_MODEL_6751a852357447259e610430fdbb444b", "value": " 35/35 [00:00&lt;00:00, 435.88 examples/s]"}}, "09b1ea46e7d4437bac1539aeb1a47fbe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ecfbc357537f493e83f9a84606581f4c", "IPY_MODEL_425f26b7325541569144cd46eaefcdc0", "IPY_MODEL_1739bb52ba624c67804927dd372649e7"], "layout": "IPY_MODEL_33262ee83efa4523b434a4c456067d18"}}, "0d66ab586e7f4f588b3e70bb0d6191a3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1082d64463a94f6282c1754720c702cd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "122c08b183f0425f881acef17f5daf08": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "137ca939b43b4bc7b41da9005e28b646": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "14b6b0568a074f8eb20cb57ffbcbb0f3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "15c3082a57e04b0198a8232f135290ef": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a87d29f844c449119a95f5548c0af0c7", "placeholder": "​", "style": "IPY_MODEL_abe93b4cbe524be08d08ff75f22d0c08", "value": "generation_config.json: 100%"}}, "1739bb52ba624c67804927dd372649e7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ed0ba5d3437b492dbdb9980ae677c35c", "placeholder": "​", "style": "IPY_MODEL_122c08b183f0425f881acef17f5daf08", "value": " 54.7k/54.7k [00:00&lt;00:00, 5.38MB/s]"}}, "1a155e85dd674f468d8b240481413722": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1b032fc19dfa46a5add673aa9c6aeda5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1d95662d1741434581d501e83b99c778": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1e5ffbbd4d954281af45f8d0f2488de7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1f65e57e365e47eeb83cb259efbd698b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "21ba911bfeb2439aa0ca135c299de1ba": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "27be088be2de4517964a8e5217641fd9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2b15c1d19cf84911bee54a6d113eed1a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2d0a363ce9c54665811b8cd0a8fdb42b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2eee87d6fc0445038e9ccf896cfa3d77": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2f5974e37f7847efa368f06b96ae9481", "max": 454, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_97ff30809ee4484988b6d0de7acd5c04", "value": 454}}, "2f5974e37f7847efa368f06b96ae9481": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2f822636140c45e8a9713b711d057005": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2f88451af2cd48aa93da32431e5de139": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_47d0de835e7d48e39161a7f038167653", "placeholder": "​", "style": "IPY_MODEL_6956008e9def426b82617536b015dd38", "value": " 17.2M/17.2M [00:00&lt;00:00, 34.6MB/s]"}}, "2fb3b417c2404e8a9ad83b1a4114174c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "315f16beade4418390ad511458013b81": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "33262ee83efa4523b434a4c456067d18": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "390ed406aa524d0c91e11ef74d01d700": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3ca561741e5f4b59b796b074cac64d80": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_15c3082a57e04b0198a8232f135290ef", "IPY_MODEL_7cd6c8ef7ec9451c90560208277975c5", "IPY_MODEL_f631738fd3014d13a7e6a50a66eaf92a"], "layout": "IPY_MODEL_f8b4855c3f0146dd8d3d0876f3a12c08"}}, "3fdc25451ed543d288d89df799883b3c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_21ba911bfeb2439aa0ca135c299de1ba", "max": 35, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_91ae4592c2d545bf9df31983257b6907", "value": 35}}, "425f26b7325541569144cd46eaefcdc0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7f246d9fe6e9415596645f736027d246", "max": 54670, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1d95662d1741434581d501e83b99c778", "value": 54670}}, "442f7ccc30314a58b1aabc91cf4dbe48": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "47d0de835e7d48e39161a7f038167653": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4952048f852a4593af48573a163a052f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a6e9f1533f83416c8b512f31b099254c", "IPY_MODEL_937a90e4c4dc41b195d24fa1694c9157", "IPY_MODEL_b82aae8041fa462d88467a02fa252af7"], "layout": "IPY_MODEL_e7d6be17a7bb4d5e8b248d7f5dee9647"}}, "50066adf2c2d4f6abe16456cb3cee737": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "50f52e4c2e5e4513b0d1fb23c39657f8": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5454423e6291484bb63b310699f5d645": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_7e23ee6479ee484e90b93c572848a2b8", "IPY_MODEL_cea072caa7694eeb8b858ea0624e88fa", "IPY_MODEL_812390f2da734ba9b516af73ddb8f452"], "layout": "IPY_MODEL_f83ddfe2510447d0a6c08b109cf42dbf"}}, "55e82538888940c1b754fa02d29e965c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5738348f2e70424babbd91c44680b87a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5759ecdb02714a9f82892e12c6b9ae46": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f229d574614945029666403a10bb761e", "max": 2354805470, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b6da3300ba7e42888add16741bafd614", "value": 2354805470}}, "5856dc5484e54deaae2c0ea0703885cb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5c022f8ebf4c44baa2195ca706b687e7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b0c12a5f8a9a4a7c8a5584b54acb6adb", "placeholder": "​", "style": "IPY_MODEL_5738348f2e70424babbd91c44680b87a", "value": " 454/454 [00:00&lt;00:00, 34.4kB/s]"}}, "5d6cb85f619b439db3544414b732607c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "60c5e826831c4afbb869a1429d3a372e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_137ca939b43b4bc7b41da9005e28b646", "placeholder": "​", "style": "IPY_MODEL_390ed406aa524d0c91e11ef74d01d700", "value": "Map: 100%"}}, "62e6190f733844319221ce6eb7a7928c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_acd616b0a9574855a3d4525e72a6513a", "IPY_MODEL_9b27fcca9ec64b81952560f38080abc6", "IPY_MODEL_2f88451af2cd48aa93da32431e5de139"], "layout": "IPY_MODEL_55e82538888940c1b754fa02d29e965c"}}, "644e720f44d24d159133bfa515482054": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6751a852357447259e610430fdbb444b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6956008e9def426b82617536b015dd38": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6b9895bccc364862a39b1c70d9612ba5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f4c156d84ae041339c5fef3c9ce6ba30", "placeholder": "​", "style": "IPY_MODEL_6b9d7dd7465a4fbfbfbca7cc5dfc2c23", "value": "special_tokens_map.json: 100%"}}, "6b9d7dd7465a4fbfbfbca7cc5dfc2c23": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "734ba48df6c34b8fb20e92f99c4e4aa2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_0d66ab586e7f4f588b3e70bb0d6191a3", "max": 890, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d527203d8b414db08f40dd6be3235c5c", "value": 890}}, "73d0263833c147fc99cc5be1ce5357e9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "74806db2db6e4085a28dceca4fbeee28": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "772f1cc7afea484bbb3cb1cc6aa7576f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7ba927b6ec4e41c39cb445443c301c57": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_81c79f2262d04a8aba854e679f5e1dac", "IPY_MODEL_734ba48df6c34b8fb20e92f99c4e4aa2", "IPY_MODEL_fde296d28c5d4c61b384a4553b499d69"], "layout": "IPY_MODEL_442f7ccc30314a58b1aabc91cf4dbe48"}}, "7be62d010b564972924fea1d8343398c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7c05acca6aed46d49663afe10ebf6d9b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_644e720f44d24d159133bfa515482054", "placeholder": "​", "style": "IPY_MODEL_50066adf2c2d4f6abe16456cb3cee737", "value": " 35/35 [00:02&lt;00:00, 19.01 examples/s]"}}, "7cd6c8ef7ec9451c90560208277975c5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9f60d2b4aa054b82bcef0b49cd4014f3", "max": 234, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_27be088be2de4517964a8e5217641fd9", "value": 234}}, "7e23ee6479ee484e90b93c572848a2b8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_cab7a4bf98cf42c69f81b9c4b7b4b21d", "placeholder": "​", "style": "IPY_MODEL_1a155e85dd674f468d8b240481413722", "value": "tokenizer.json: 100%"}}, "7f246d9fe6e9415596645f736027d246": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7f616bf83de54cfeaabda52b3f592e4c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "812390f2da734ba9b516af73ddb8f452": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bf983c3e807d4e25ba8c2a079aea34bf", "placeholder": "​", "style": "IPY_MODEL_315f16beade4418390ad511458013b81", "value": " 17.2M/17.2M [00:00&lt;00:00, 23.6MB/s]"}}, "81c79f2262d04a8aba854e679f5e1dac": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1f65e57e365e47eeb83cb259efbd698b", "placeholder": "​", "style": "IPY_MODEL_772f1cc7afea484bbb3cb1cc6aa7576f", "value": "config.json: 100%"}}, "834de6e5d5504ba794c43d186d6de8a7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "83657ceb17f1454ca7e33a5fcc5ad5f7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bbc163a35ec54087bf0c56cc332df0b4", "placeholder": "​", "style": "IPY_MODEL_c901f4517b474511b6dd880e63f25b58", "value": "tokenizer_config.json: 100%"}}, "85ff0a11606e466ebd7f7b3b6ed3048e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5d6cb85f619b439db3544414b732607c", "placeholder": "​", "style": "IPY_MODEL_9f083cc2ddb64b6196cc2d170affabae", "value": " 2.35G/2.35G [02:28&lt;00:00, 48.6MB/s]"}}, "8c6845e9bd81418e8c02d66c32046129": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8d38463677324bd79c193798157a1568": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8e20e88da0f34b6fadf118a0443674c6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "91ae4592c2d545bf9df31983257b6907": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "937a90e4c4dc41b195d24fa1694c9157": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8c6845e9bd81418e8c02d66c32046129", "max": 454, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_2fb3b417c2404e8a9ad83b1a4114174c", "value": 454}}, "943c7280c19948999aa663ca6736d698": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "97ff30809ee4484988b6d0de7acd5c04": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9a18c25e8d694fff905917e7ce2931dd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9a62a83412df4453840f06c97ebcc216": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6b9895bccc364862a39b1c70d9612ba5", "IPY_MODEL_2eee87d6fc0445038e9ccf896cfa3d77", "IPY_MODEL_5c022f8ebf4c44baa2195ca706b687e7"], "layout": "IPY_MODEL_834de6e5d5504ba794c43d186d6de8a7"}}, "9b27fcca9ec64b81952560f38080abc6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e3ab144489504058b7c9009d867bef95", "max": 17209920, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_943c7280c19948999aa663ca6736d698", "value": 17209920}}, "9f083cc2ddb64b6196cc2d170affabae": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9f60d2b4aa054b82bcef0b49cd4014f3": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a6e9f1533f83416c8b512f31b099254c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fc915d911ee144bea96b1885f5eb7191", "placeholder": "​", "style": "IPY_MODEL_caed1189f10140549fe2b445dd7dbd16", "value": "special_tokens_map.json: 100%"}}, "a87d29f844c449119a95f5548c0af0c7": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ab2bdd97242243d7aadfe932c72c39a2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "abe93b4cbe524be08d08ff75f22d0c08": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "acd616b0a9574855a3d4525e72a6513a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_2b15c1d19cf84911bee54a6d113eed1a", "placeholder": "​", "style": "IPY_MODEL_cfdfb65ccad04899881976d93945bf0d", "value": "tokenizer.json: 100%"}}, "b0c12a5f8a9a4a7c8a5584b54acb6adb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b554950c61ae436dbb7772e7de173a81": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b6da3300ba7e42888add16741bafd614": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b82aae8041fa462d88467a02fa252af7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e22b473dd97446298b42987e36d945ec", "placeholder": "​", "style": "IPY_MODEL_9a18c25e8d694fff905917e7ce2931dd", "value": " 454/454 [00:00&lt;00:00, 29.8kB/s]"}}, "bbc163a35ec54087bf0c56cc332df0b4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bf983c3e807d4e25ba8c2a079aea34bf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c0cf2ea32d7e42d2b6cb00ba5b128e6d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c6f86936f86347819002d6bbf50ec484": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_74806db2db6e4085a28dceca4fbeee28", "max": 54674, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_e2f989df5f494351a5b242d2430c2917", "value": 54674}}, "c7787082fecf4df0ae58e67f46e42589": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_60c5e826831c4afbb869a1429d3a372e", "IPY_MODEL_3fdc25451ed543d288d89df799883b3c", "IPY_MODEL_079705095aa14fa0bd5af53459ad5bfb"], "layout": "IPY_MODEL_1082d64463a94f6282c1754720c702cd"}}, "c901f4517b474511b6dd880e63f25b58": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cab7a4bf98cf42c69f81b9c4b7b4b21d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cae045b72aca476a9c16c39778be53a0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "caed1189f10140549fe2b445dd7dbd16": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cd3f2a8158124c6b87be72e959a455d5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "cea072caa7694eeb8b858ea0624e88fa": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1b032fc19dfa46a5add673aa9c6aeda5", "max": 17209920, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_df624458fe5a4a21b58602af80fa0a1c", "value": 17209920}}, "cf6e3f6267cb49d5ac4575d1d1e41d10": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ef715f56f3824a64949be1f2bb05296b", "placeholder": "​", "style": "IPY_MODEL_cd3f2a8158124c6b87be72e959a455d5", "value": " 54.7k/54.7k [00:00&lt;00:00, 5.79MB/s]"}}, "cfd9f69db23f4b86a42179ac0ac91a8c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e624589b592d43239e812e02fd3ab244", "IPY_MODEL_e0311985f098497e821f7c8720457384", "IPY_MODEL_7c05acca6aed46d49663afe10ebf6d9b"], "layout": "IPY_MODEL_8d38463677324bd79c193798157a1568"}}, "cfdfb65ccad04899881976d93945bf0d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d527203d8b414db08f40dd6be3235c5c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d5500717a6534c768be6a07913b6a339": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d944a4b11b614896bdb6af583faed134": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_83657ceb17f1454ca7e33a5fcc5ad5f7", "IPY_MODEL_c6f86936f86347819002d6bbf50ec484", "IPY_MODEL_cf6e3f6267cb49d5ac4575d1d1e41d10"], "layout": "IPY_MODEL_2d0a363ce9c54665811b8cd0a8fdb42b"}}, "df624458fe5a4a21b58602af80fa0a1c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e0311985f098497e821f7c8720457384": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7be62d010b564972924fea1d8343398c", "max": 35, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_14b6b0568a074f8eb20cb57ffbcbb0f3", "value": 35}}, "e22b473dd97446298b42987e36d945ec": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e2f989df5f494351a5b242d2430c2917": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "e3ab144489504058b7c9009d867bef95": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e624589b592d43239e812e02fd3ab244": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b554950c61ae436dbb7772e7de173a81", "placeholder": "​", "style": "IPY_MODEL_ab2bdd97242243d7aadfe932c72c39a2", "value": "Unsloth: Tokenizing [&quot;text&quot;] (num_proc=2): 100%"}}, "e7d6be17a7bb4d5e8b248d7f5dee9647": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ecfbc357537f493e83f9a84606581f4c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8e20e88da0f34b6fadf118a0443674c6", "placeholder": "​", "style": "IPY_MODEL_d5500717a6534c768be6a07913b6a339", "value": "tokenizer_config.json: 100%"}}, "ed0ba5d3437b492dbdb9980ae677c35c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ef715f56f3824a64949be1f2bb05296b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f229d574614945029666403a10bb761e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f4c156d84ae041339c5fef3c9ce6ba30": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f631738fd3014d13a7e6a50a66eaf92a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7f616bf83de54cfeaabda52b3f592e4c", "placeholder": "​", "style": "IPY_MODEL_2f822636140c45e8a9713b711d057005", "value": " 234/234 [00:00&lt;00:00, 25.2kB/s]"}}, "f83ddfe2510447d0a6c08b109cf42dbf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f8b4855c3f0146dd8d3d0876f3a12c08": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fc915d911ee144bea96b1885f5eb7191": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fc9c4d66a2fa41a393525a04ced011f3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_0365c9b5e9a44318a70109069135b7fe", "IPY_MODEL_5759ecdb02714a9f82892e12c6b9ae46", "IPY_MODEL_85ff0a11606e466ebd7f7b3b6ed3048e"], "layout": "IPY_MODEL_cae045b72aca476a9c16c39778be53a0"}}, "fde296d28c5d4c61b384a4553b499d69": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_50f52e4c2e5e4513b0d1fb23c39657f8", "placeholder": "​", "style": "IPY_MODEL_73d0263833c147fc99cc5be1ce5357e9", "value": " 890/890 [00:00&lt;00:00, 95.0kB/s]"}}}}}, "nbformat": 4, "nbformat_minor": 0}