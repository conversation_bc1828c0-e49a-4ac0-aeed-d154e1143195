#!/usr/bin/env python3
"""
Test script to verify all dependencies are properly installed
"""

def test_imports():
    """Test all required imports"""
    print("Testing imports...")
    
    try:
        # Standard library
        import os
        import json
        print("✅ Standard library imports OK")
        
        # Environment
        from dotenv import load_dotenv
        print("✅ python-dotenv OK")
        
        # LangGraph
        from langgraph.graph import START, StateGraph, MessagesState
        from langgraph.prebuilt import tools_condition, ToolNode
        print("✅ langgraph OK")
        
        # LangChain providers
        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_groq import ChatGroq
        from langchain_huggingface import ChatHuggingFace, HuggingFaceEndpoint, HuggingFaceEmbeddings
        print("✅ LangChain providers OK")
        
        # LangChain community
        from langchain_community.tools.tavily_search import TavilySearchResults
        from langchain_community.document_loaders import WikipediaLoader, ArxivLoader
        from langchain_community.vectorstores import SupabaseVectorStore, FAISS
        print("✅ LangChain community OK")
        
        # LangChain core
        from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
        from langchain_core.tools import tool
        from langchain.tools.retriever import create_retriever_tool
        from langchain.schema import Document
        print("✅ LangChain core OK")
        
        # Supabase
        from supabase.client import Client, create_client
        print("✅ Supabase OK")
        
        # FAISS (new dependency)
        import faiss
        print("✅ FAISS OK")
        
        # Sentence transformers (new dependency)
        from sentence_transformers import SentenceTransformer
        print("✅ sentence-transformers OK")
        
        # NumPy
        import numpy as np
        print("✅ numpy OK")
        
        print("\n🎉 All dependencies imported successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_faiss_functionality():
    """Test basic FAISS functionality"""
    print("\nTesting FAISS functionality...")
    
    try:
        import faiss
        import numpy as np
        
        # Create a simple index
        dimension = 128
        index = faiss.IndexFlatL2(dimension)
        
        # Add some vectors
        vectors = np.random.random((10, dimension)).astype('float32')
        index.add(vectors)
        
        # Search
        query = np.random.random((1, dimension)).astype('float32')
        distances, indices = index.search(query, 3)
        
        print(f"✅ FAISS functionality test passed - found {len(indices[0])} results")
        return True
        
    except Exception as e:
        print(f"❌ FAISS functionality test failed: {e}")
        return False

def test_sentence_transformers():
    """Test sentence transformers functionality"""
    print("\nTesting sentence-transformers functionality...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        # This will download the model if not present
        print("Loading sentence transformer model...")
        model = SentenceTransformer('all-MiniLM-L6-v2')
        
        # Test encoding
        sentences = ["This is a test sentence", "This is another test"]
        embeddings = model.encode(sentences)
        
        print(f"✅ sentence-transformers test passed - generated embeddings shape: {embeddings.shape}")
        return True
        
    except Exception as e:
        print(f"❌ sentence-transformers test failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("DEPENDENCY TEST SCRIPT")
    print("=" * 50)
    
    success = True
    
    # Test imports
    success &= test_imports()
    
    # Test FAISS functionality
    success &= test_faiss_functionality()
    
    # Test sentence transformers (optional - requires download)
    # success &= test_sentence_transformers()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("Your environment is ready to run the agent.")
    else:
        print("❌ SOME TESTS FAILED!")
        print("Please check the error messages above and install missing dependencies.")
    print("=" * 50)
