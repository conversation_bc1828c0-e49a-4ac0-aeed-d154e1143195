// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef FLUTTER_SHELL_VERSION_VERSION_H_
#define FLUTTER_SHELL_VERSION_VERSION_H_

namespace flutter {

const char* GetFlutterEngineVersion();

const char* GetSkiaVersion();

const char* GetDartVersion();

}  // namespace flutter

#endif  // FLUTTER_SHELL_VERSION_VERSION_H_
