# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/impeller/tools/impeller.gni")

group("backend") {
  public_deps = []

  if (impeller_enable_metal) {
    public_deps += [ "metal" ]
  }

  if (impeller_enable_opengles) {
    public_deps += [ "gles" ]
  }

  if (impeller_enable_vulkan) {
    public_deps += [ "vulkan" ]
  }
}
