{"cells": [{"cell_type": "markdown", "metadata": {"id": "A33W4HbMoXsl"}, "source": ["# Como automatizar a limpeza de dados com IA <a id=\"make-a-data-cleaning-agent\"></a>\n", "\n", "### Quer se tornar um cientista de dados de IA generativa full-stack?\n", "\n", "[Cientista de dados de IA generativa]\n", "\n", "Eu ensino Ciência de dados de IA generativa para ajudar você a criar aplicativos de ciência de dados com tecnologia de IA. [**Registre-se para meu próximo workshop de IA generativa para cientistas de dados aqui.**](https://learn.business-science.io/ai-register)\n"]}, {"cell_type": "markdown", "metadata": {"id": "P-l7LT-MoXsn", "vscode": {"languageId": "bat"}}, "source": ["# Índice\n", "\n", "1. [<PERSON>ria<PERSON> um agente de limpeza de dados](#make-a-data-cleaning-agent)\n", "2. [Carregar bibliotecas](#load-libraries)\n", "3. [Configurar IA e registro](#setup-ai-and-logging)\n", "4. [<PERSON><PERSON><PERSON> um conjunto de dados](#load-a-dataset)\n", "5. [<PERSON><PERSON><PERSON> o agente](#create-the-agent)\n", "6. [<PERSON><PERSON><PERSON><PERSON>](#response)\n", "7. [A receita de limpeza](#the-cleaning-recipe)\n", "8. [Função de limpeza de dados](#data-cleaner-function)\n", "9. [Dados limpos como quadro de dados Pandas](#cleaned-data-as-pandas-data-frame)\n", "10. [Workshop gratuito de ciência de dados de IA generativa](#free-generative-ai-data-science-workshop)"]}, {"cell_type": "markdown", "metadata": {"id": "HmubvlNvoXso"}, "source": ["### Load Libraries <a id=\"load-libraries\"></a>"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "TW9JBGt7oXso"}, "outputs": [], "source": ["# * Libraries\n", "\n", "# from langchain_openai import ChatOpenAI\n", "from langchain_groq import ChatGroq\n", "import os\n", "import yaml\n", "import pandas as pd\n", "from pprint import pprint\n", "from dotenv import load_dotenv\n", "from ai_data_science_team.agents import DataCleaningAgent"]}, {"cell_type": "markdown", "metadata": {"id": "KeYz894MoXsp"}, "source": ["### Configurar IA e registro <a id=\"setup-ai-and-logging\"></a>\n", "\n", "Esta seção de código configura as entradas do LLM e as informações de registro. O registro é usado para armazenar código e arquivos gerados por IA durante o processamento de arquivos pelas equipes de ciência de dados de IA.\n", "\n", "*Observação importante:* Este exemplo usa a API do OpenAI. Mas qualquer LLM pode ser usado, como LLMs locais ou antrópicos com Ollama."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: deepseek-r1-distill-llama-70b\n"]}], "source": ["import os\n", "from dotenv import load_dotenv\n", "\n", "# * Setup\n", "MODEL    = \"deepseek-r1-distill-llama-70b\"\n", "LOG      = True\n", "LOG_PATH = os.path.join(os.getcwd(), \"logs/\")\n", "\n", "load_dotenv()\n", "\n", "llm = ChatGroq(model_name=MODEL)\n", "\n", "print(f\"Model: {llm.model_name}\")"]}, {"cell_type": "markdown", "metadata": {"id": "VT6nIgjtoXsr"}, "source": ["### <PERSON>egar um conjunto de dados <a id=\"load-a-dataset\"></a>\n", "\n", "Em seguida, vamos carregar um conjunto de dados de rotatividade de clientes que iremos limpar."]}, {"cell_type": "code", "execution_count": 19, "metadata": {"id": "j9AiAGADoXsr", "outputId": "1e226748-1615-40b5-c94c-e63a6506b090"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>customerID</th>\n", "      <th>gender</th>\n", "      <th>SeniorCitizen</th>\n", "      <th>Partner</th>\n", "      <th>Dependents</th>\n", "      <th>tenure</th>\n", "      <th>PhoneService</th>\n", "      <th>MultipleLines</th>\n", "      <th>InternetService</th>\n", "      <th>OnlineSecurity</th>\n", "      <th>...</th>\n", "      <th>DeviceProtection</th>\n", "      <th>TechSupport</th>\n", "      <th>StreamingTV</th>\n", "      <th>StreamingMovies</th>\n", "      <th>Contract</th>\n", "      <th>PaperlessBilling</th>\n", "      <th>PaymentMethod</th>\n", "      <th>MonthlyCharges</th>\n", "      <th>TotalCharges</th>\n", "      <th><PERSON>rn</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7590-VHVEG</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>1</td>\n", "      <td>No</td>\n", "      <td>No phone service</td>\n", "      <td>DSL</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>29.85</td>\n", "      <td>29.85</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5575-GNVDE</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>34</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>One year</td>\n", "      <td>No</td>\n", "      <td>Mailed check</td>\n", "      <td>56.95</td>\n", "      <td>1889.5</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3668-QPYBK</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>53.85</td>\n", "      <td>108.15</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7795-CFOCW</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>45</td>\n", "      <td>No</td>\n", "      <td>No phone service</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>One year</td>\n", "      <td>No</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>42.30</td>\n", "      <td>1840.75</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>9237-HQITU</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>2</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Fiber optic</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>70.70</td>\n", "      <td>151.65</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7038</th>\n", "      <td>6840-RESVB</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>24</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>One year</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>84.80</td>\n", "      <td>1990.5</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7039</th>\n", "      <td>2234-XADUH</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>72</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Fiber optic</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>One year</td>\n", "      <td>Yes</td>\n", "      <td>Credit card (automatic)</td>\n", "      <td>103.20</td>\n", "      <td>7362.9</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7040</th>\n", "      <td>4801-JZAZL</td>\n", "      <td>Female</td>\n", "      <td>0</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>11</td>\n", "      <td>No</td>\n", "      <td>No phone service</td>\n", "      <td>DSL</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Electronic check</td>\n", "      <td>29.60</td>\n", "      <td>346.45</td>\n", "      <td>No</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7041</th>\n", "      <td>8361-LTMKD</td>\n", "      <td>Male</td>\n", "      <td>1</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>4</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Fiber optic</td>\n", "      <td>No</td>\n", "      <td>...</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>Month-to-month</td>\n", "      <td>Yes</td>\n", "      <td>Mailed check</td>\n", "      <td>74.40</td>\n", "      <td>306.6</td>\n", "      <td>Yes</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7042</th>\n", "      <td>3186-AJIEK</td>\n", "      <td>Male</td>\n", "      <td>0</td>\n", "      <td>No</td>\n", "      <td>No</td>\n", "      <td>66</td>\n", "      <td>Yes</td>\n", "      <td>No</td>\n", "      <td>Fiber optic</td>\n", "      <td>Yes</td>\n", "      <td>...</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Yes</td>\n", "      <td>Two year</td>\n", "      <td>Yes</td>\n", "      <td>Bank transfer (automatic)</td>\n", "      <td>105.65</td>\n", "      <td>6844.5</td>\n", "      <td>No</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7043 rows × 21 columns</p>\n", "</div>"], "text/plain": ["      customerID  gender  SeniorCitizen Partner Dependents  tenure  \\\n", "0     7590-VHVEG  Female              0     Yes         No       1   \n", "1     5575-GNVDE    Male              0      No         No      34   \n", "2     3668-QPYBK    Male              0      No         No       2   \n", "3     7795-CFOCW    Male              0      No         No      45   \n", "4     9237-HQITU  Female              0      No         No       2   \n", "...          ...     ...            ...     ...        ...     ...   \n", "7038  6840-RESVB    Male              0     Yes        Yes      24   \n", "7039  2234-XADUH  Female              0     Yes        Yes      72   \n", "7040  4801-JZAZL  Female              0     Yes        Yes      11   \n", "7041  8361-LTMKD    Male              1     Yes         No       4   \n", "7042  3186-AJIEK    Male              0      No         No      66   \n", "\n", "     PhoneService     MultipleLines InternetService OnlineSecurity  ...  \\\n", "0              No  No phone service             DSL             No  ...   \n", "1             Yes                No             DSL            Yes  ...   \n", "2             Yes                No             DSL            Yes  ...   \n", "3              No  No phone service             DSL            Yes  ...   \n", "4             Yes                No     Fiber optic             No  ...   \n", "...           ...               ...             ...            ...  ...   \n", "7038          Yes               Yes             DSL            Yes  ...   \n", "7039          Yes               Yes     Fiber optic             No  ...   \n", "7040           No  No phone service             DSL            Yes  ...   \n", "7041          Yes               Yes     Fiber optic             No  ...   \n", "7042          Yes                No     Fiber optic            Yes  ...   \n", "\n", "     DeviceProtection TechSupport StreamingTV StreamingMovies        Contract  \\\n", "0                  No          No          No              No  Month-to-month   \n", "1                 Yes          No          No              No        One year   \n", "2                  No          No          No              No  Month-to-month   \n", "3                 Yes         Yes          No              No        One year   \n", "4                  No          No          No              No  Month-to-month   \n", "...               ...         ...         ...             ...             ...   \n", "7038              Yes         Yes         Yes             Yes        One year   \n", "7039              Yes          No         Yes             Yes        One year   \n", "7040               No          No          No              No  Month-to-month   \n", "7041               No          No          No              No  Month-to-month   \n", "7042              Yes         Yes         Yes             Yes        Two year   \n", "\n", "     PaperlessBilling              PaymentMethod MonthlyCharges  TotalCharges  \\\n", "0                 Yes           Electronic check          29.85         29.85   \n", "1                  No               Mailed check          56.95        1889.5   \n", "2                 Yes               Mailed check          53.85        108.15   \n", "3                  No  Bank transfer (automatic)          42.30       1840.75   \n", "4                 Yes           Electronic check          70.70        151.65   \n", "...               ...                        ...            ...           ...   \n", "7038              Yes               Mailed check          84.80        1990.5   \n", "7039              Yes    Credit card (automatic)         103.20        7362.9   \n", "7040              Yes           Electronic check          29.60        346.45   \n", "7041              Yes               Mailed check          74.40         306.6   \n", "7042              Yes  Bank transfer (automatic)         105.65        6844.5   \n", "\n", "     Churn  \n", "0       No  \n", "1       No  \n", "2      Yes  \n", "3       No  \n", "4      Yes  \n", "...    ...  \n", "7038    No  \n", "7039    No  \n", "7040    No  \n", "7041   Yes  \n", "7042    No  \n", "\n", "[7043 rows x 21 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv(\"https://raw.githubusercontent.com/business-science/ai-data-science-team/refs/heads/master/data/churn_data.csv\")\n", "df"]}, {"cell_type": "markdown", "metadata": {"id": "Bfma8jqvoXss"}, "source": ["### <PERSON><PERSON> o agente <a id=\"create-the-agent\"></a>\n", "\n", "Execute este código para criar um agente com `make_data_cleaning_agent()`."]}, {"cell_type": "code", "execution_count": 20, "metadata": {"id": "qZny43ploXss", "outputId": "e64410ab-537f-4aff-f119-43cebbb703a3"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<ai_data_science_team.agents.data_cleaning_agent.DataCleaningAgent object at 0x000002E65AC400B0>"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["data_cleaning_agent = DataCleaningAgent(\n", "    model = llm,\n", "    log=LOG,\n", "    log_path=LOG_PATH\n", ")\n", "\n", "data_cleaning_agent"]}, {"cell_type": "markdown", "metadata": {"id": "F1Z8hFQkoXss"}, "source": ["<PERSON>so cria um `app`, que é um agente langgraph com as principais entradas:\n", "\n", "- **user_instructions**: O agente de limpeza de dados usará esses comentários para modificar a \"receita padrão\"\n", "- Receita padrão: A receita de limpeza padrão que inclui a remoção de colunas com mais de 40% de valores ausentes, imputando valores ausentes usando média (numérica) ou modo (categórico), removendo linhas duplicadas e removendo outliers.\n", "- **data_raw**: Os dados brutos a serem limpos\n", "- **max_retries**: Usado para limitar o número de tentativas de corrigir o código python gerado pelo agente. Defina como 3 para limitar a 3 tentativas.\n", "- **retry_count**: Defina como 0."]}, {"cell_type": "code", "execution_count": 21, "metadata": {"id": "5yxgG091oXss", "outputId": "16474214-7e2b-4369-925e-83a18f212dda"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---DATA CLEANING AGENT----\n", "    * RECOMMEND CLEANING STEPS\n", "    * CREATE DATA CLEANER CODE\n", "      File saved to: C:\\Users\\<USER>\\downloads\\ai-data-science-team\\examples\\logs/data_cleaner.py\n", "    * EXECUTING AGENT CODE\n", "    * REPORT AGENT OUTPUTS\n"]}], "source": ["data_cleaning_agent.invoke_agent(\n", "    data_raw=df,\n", "    user_instructions=\"Don't remove outliers when cleaning the data.\",\n", "    max_retries=3,\n", "    retry_count=0\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "kOuoiQuSoXst"}, "source": ["### Resposta <a id=\"response\"></a>\n", "\n", "A resposta produzida contém tudo o que precisamos para entender as decisões de limpeza de dados tomadas e obter o conjunto de dados limpo."]}, {"cell_type": "code", "execution_count": 22, "metadata": {"id": "Ee_Tlex_oXst", "outputId": "bd7763ab-5805-4e70-b6f4-4a9c94d860fc"}, "outputs": [{"data": {"text/plain": ["['messages',\n", " 'user_instructions',\n", " 'recommended_steps',\n", " 'data_raw',\n", " 'data_cleaned',\n", " 'all_datasets_summary',\n", " 'data_cleaner_function',\n", " 'data_cleaner_function_path',\n", " 'data_cleaner_file_name',\n", " 'data_cleaner_function_name',\n", " 'data_cleaner_error',\n", " 'max_retries',\n", " 'retry_count']"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["response = data_cleaning_agent.get_response()\n", "\n", "list(response.keys())"]}, {"cell_type": "markdown", "metadata": {"id": "y8G32_JLoXst"}, "source": ["#### Dados limpos como dataframe Pandas <a id=\"cleaned-data-as-pandas-data-frame\"></a>"]}, {"cell_type": "markdown", "metadata": {"id": "ZClyjKB6oXst"}, "source": ["Use o método `get_data_cleaned()` para obter os dados limpos como um quadro de dados do pandas."]}, {"cell_type": "code", "execution_count": 23, "metadata": {"id": "w52-cHjPoXst", "outputId": "087ce6ed-1ebf-44d5-82ed-af31bfcd715d"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SeniorCitizen</th>\n", "      <th>tenure</th>\n", "      <th>MonthlyCharges</th>\n", "      <th>TotalCharges</th>\n", "      <th>gender_Female</th>\n", "      <th>gender_Male</th>\n", "      <th>Partner_No</th>\n", "      <th>Partner_Yes</th>\n", "      <th>Dependents_No</th>\n", "      <th>Dependents_Yes</th>\n", "      <th>...</th>\n", "      <th>Contract_Month-to-month</th>\n", "      <th>Contract_One year</th>\n", "      <th>Contract_Two year</th>\n", "      <th>PaperlessBilling_No</th>\n", "      <th>PaperlessBilling_Yes</th>\n", "      <th>PaymentMethod_Bank transfer (automatic)</th>\n", "      <th>PaymentMethod_Credit card (automatic)</th>\n", "      <th>PaymentMethod_Electronic check</th>\n", "      <th>PaymentMethod_Mailed check</th>\n", "      <th><PERSON>rn</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-0.439916</td>\n", "      <td>-1.277445</td>\n", "      <td>-1.160323</td>\n", "      <td>-0.994194</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-0.439916</td>\n", "      <td>0.066327</td>\n", "      <td>-0.259629</td>\n", "      <td>-0.173740</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-0.439916</td>\n", "      <td>-1.236724</td>\n", "      <td>-0.362660</td>\n", "      <td>-0.959649</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-0.439916</td>\n", "      <td>0.514251</td>\n", "      <td>-0.746535</td>\n", "      <td>-0.195248</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-0.439916</td>\n", "      <td>-1.236724</td>\n", "      <td>0.197365</td>\n", "      <td>-0.940457</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7038</th>\n", "      <td>-0.439916</td>\n", "      <td>-0.340876</td>\n", "      <td>0.665992</td>\n", "      <td>-0.129180</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7039</th>\n", "      <td>-0.439916</td>\n", "      <td>1.613701</td>\n", "      <td>1.277533</td>\n", "      <td>2.241056</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7040</th>\n", "      <td>-0.439916</td>\n", "      <td>-0.870241</td>\n", "      <td>-1.168632</td>\n", "      <td>-0.854514</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7041</th>\n", "      <td>2.273159</td>\n", "      <td>-1.155283</td>\n", "      <td>0.320338</td>\n", "      <td>-0.872095</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7042</th>\n", "      <td>-0.439916</td>\n", "      <td>1.369379</td>\n", "      <td>1.358961</td>\n", "      <td>2.012344</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7043 rows × 46 columns</p>\n", "</div>"], "text/plain": ["      SeniorCitizen    tenure  MonthlyCharges  TotalCharges  gender_Female  \\\n", "0         -0.439916 -1.277445       -1.160323     -0.994194           True   \n", "1         -0.439916  0.066327       -0.259629     -0.173740          False   \n", "2         -0.439916 -1.236724       -0.362660     -0.959649          False   \n", "3         -0.439916  0.514251       -0.746535     -0.195248          <PERSON><PERSON>e   \n", "4         -0.439916 -1.236724        0.197365     -0.940457           True   \n", "...             ...       ...             ...           ...            ...   \n", "7038      -0.439916 -0.340876        0.665992     -0.129180          False   \n", "7039      -0.439916  1.613701        1.277533      2.241056           True   \n", "7040      -0.439916 -0.870241       -1.168632     -0.854514           True   \n", "7041       2.273159 -1.155283        0.320338     -0.872095          False   \n", "7042      -0.439916  1.369379        1.358961      2.012344          False   \n", "\n", "      gender_Male  Partner_No  Partner_Yes  Dependents_No  Dependents_Yes  \\\n", "0           False       False         True           True           False   \n", "1            True        True        False           True           False   \n", "2            True        True        False           True           False   \n", "3            True        True        False           True           False   \n", "4           False        True        False           True           False   \n", "...           ...         ...          ...            ...             ...   \n", "7038         True       False         True          False            True   \n", "7039        False       False         True          False            True   \n", "7040        False       False         True          False            True   \n", "7041         True       False         True           True           False   \n", "7042         True        True        False           True           False   \n", "\n", "      ...  Contract_Month-to-month  Contract_One year  Contract_Two year  \\\n", "0     ...                     True              False              False   \n", "1     ...                    False               True              False   \n", "2     ...                     True              False              False   \n", "3     ...                    False               True              False   \n", "4     ...                     True              False              False   \n", "...   ...                      ...                ...                ...   \n", "7038  ...                    False               True              False   \n", "7039  ...                    False               True              False   \n", "7040  ...                     True              False              False   \n", "7041  ...                     True              False              False   \n", "7042  ...                    False              False               True   \n", "\n", "      PaperlessBilling_No  PaperlessBilling_Yes  \\\n", "0                   False                  True   \n", "1                    True                 False   \n", "2                   False                  True   \n", "3                    True                 False   \n", "4                   False                  True   \n", "...                   ...                   ...   \n", "7038                False                  True   \n", "7039                False                  True   \n", "7040                False                  True   \n", "7041                False                  True   \n", "7042                False                  True   \n", "\n", "      PaymentMethod_Bank transfer (automatic)  \\\n", "0                                       False   \n", "1                                       False   \n", "2                                       False   \n", "3                                        True   \n", "4                                       False   \n", "...                                       ...   \n", "7038                                    False   \n", "7039                                    False   \n", "7040                                    False   \n", "7041                                    False   \n", "7042                                     True   \n", "\n", "      PaymentMethod_Credit card (automatic)  PaymentMethod_Electronic check  \\\n", "0                                     False                            True   \n", "1                                     False                           False   \n", "2                                     False                           False   \n", "3                                     False                           False   \n", "4                                     False                            True   \n", "...                                     ...                             ...   \n", "7038                                  False                           False   \n", "7039                                   True                           False   \n", "7040                                  False                            True   \n", "7041                                  False                           False   \n", "7042                                  False                           False   \n", "\n", "      PaymentMethod_Mailed check  Churn  \n", "0                          False      0  \n", "1                           True      0  \n", "2                           True      1  \n", "3                          False      0  \n", "4                          False      1  \n", "...                          ...    ...  \n", "7038                        True      0  \n", "7039                       False      0  \n", "7040                       False      0  \n", "7041                        True      1  \n", "7042                       False      0  \n", "\n", "[7043 rows x 46 columns]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["data_cleaning_agent.get_data_cleaned()"]}, {"cell_type": "markdown", "metadata": {"id": "3RPiz3r3oXst"}, "source": ["#### Data Cleaner Function <a id=\"data-cleaner-function\"></a>"]}, {"cell_type": "markdown", "metadata": {"id": "WefDqF4voXst"}, "source": ["Podemos usar o método `get_data_cleaner_function()` para obter o pipeline da função de limpeza de dados.\n", "\n", "- Em Jupyter Notebooks, definir `markdown=True` retornará a função como código markdown.\n", "- Em aplicativos Streamlit, é recomendado definir `markdown=False`."]}, {"cell_type": "code", "execution_count": 24, "metadata": {"id": "nOJs9HEUoXsu", "outputId": "e24e67d8-76d3-4d7e-94df-b3b5a64fcadf"}, "outputs": [{"data": {"text/markdown": ["```python\n", "# Disclaimer: This function was generated by AI. Please review before using.\n", "# Agent Name: data_cleaning_agent\n", "# Time Created: 2025-01-30 17:44:32\n", "\n", "def data_cleaner(data_raw):\n", "    import pandas as pd\n", "    import numpy as np\n", "    from sklearn.preprocessing import StandardScaler\n", "\n", "\n", "\n", "    # Convert 'TotalCharges' to numeric type\n", "    data_raw['TotalCharges'] = pd.to_numeric(data_raw['TotalCharges'], errors='coerce')\n", "    \n", "    # Drop the 'customerID' column\n", "    data_raw = data_raw.drop(['customerID'], axis=1)\n", "    \n", "    # Separate numerical and categorical columns\n", "    numerical_cols = ['SeniorCitizen', 'tenure', 'MonthlyCharges', 'TotalCharges']\n", "    categorical_cols = [col for col in data_raw.columns if col not in numerical_cols and col not in ['Churn']]\n", "    \n", "    # One-hot encoding for categorical columns\n", "    data_raw = pd.get_dummies(data_raw, columns=categorical_cols)\n", "    \n", "    # Separate features and target\n", "    X = data_raw.drop('Churn', axis=1)\n", "    y = data_raw['Churn']\n", "    \n", "    # Encode the target variable\n", "    y = pd.Categorical(y).codes\n", "    \n", "    # Apply StandardScaler to numerical features\n", "    scaler = StandardScaler()\n", "    X[numerical_cols] = scaler.fit_transform(X[numerical_cols])\n", "    \n", "    # Combine features and target\n", "    data_cleaned = X\n", "    data_cleaned['Churn'] = y\n", "    \n", "    return data_cleaned\n", "```"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["data_cleaning_agent.get_data_cleaner_function(markdown=True)"]}, {"cell_type": "markdown", "metadata": {"id": "VKwNmmq5oXsu"}, "source": ["#### Etapas recomendadas\n", "\n", "Para obter as etapas recomendadas durante a análise de dados (antes da codificação), execute o método `get_recommended_steps()`."]}, {"cell_type": "code", "execution_count": 25, "metadata": {"id": "B39NWMtPoXsu", "outputId": "d7aaa7e5-3806-4f10-c4f9-84d7ad1082ec"}, "outputs": [{"data": {"text/markdown": ["# Recommended Data Cleaning Steps:\n", "<think>\n", "Okay, so I'm trying to figure out the best steps to clean and preprocess this dataset. Let me start by looking at the information provided.\n", "\n", "First, the dataset has 7043 rows and 21 columns. The columns are a mix of object, int64, and float64 types. The user mentioned that there are no missing values in any of the columns, which is great because that means I don't have to worry about handling missing data through removal or imputation. That's a relief because missing value handling can be tricky and time-consuming.\n", "\n", "Next, I notice that all the columns have zero missing values, so I can skip steps like removing columns with high missing percentages or imputing values. That's one less thing to do, which simplifies the process.\n", "\n", "Looking at the data types, I see that 'TotalCharges' is an object type, but when I look at the data sample, it has values like 29.85, 1889.5, etc. These look like numeric values, so I think 'TotalCharges' should be converted to a float. This is important because if we leave it as an object, any numeric operations on it might cause errors or unexpected behavior. So, converting this column will make it consistent with 'MonthlyCharges' which is already a float.\n", "\n", "Now, the user specifically instructed not to remove outliers. That means I shouldn't perform any steps that filter out extreme values based on IQR or other methods. This is a crucial point because outliers can significantly affect model performance, but since the user doesn't want them removed, I have to respect that instruction.\n", "\n", "Another thing I notice is the presence of categorical variables. Columns like 'gender', 'Partner', 'Dependents', etc., are all object types and have a limited number of unique values. These will need to be converted into numerical representations because most machine learning algorithms can't process categorical data directly. Common methods include one-hot encoding or label encoding. I'm leaning towards one-hot encoding here because it can capture the nominal nature of these categories without implying any order.\n", "\n", "There are a couple of columns that might need special attention. 'customerID' seems to be a unique identifier for each customer, which isn't useful for analysis. It's better to drop this column to avoid any potential issues with model training, like the curse of dimensionality or overfitting. Also, 'Churn' is the target variable, so I should consider it as the dependent variable and might need to handle it differently, like stratifying splits or using it as a target in encoding schemes.\n", "\n", "The 'tenure' and 'MonthlyCharges' columns are numerical, but I should check if they need scaling or normalization. However, since this is a cleaning step, I might hold off on scaling until after preprocessing, depending on the model requirements.\n", "\n", "I also need to ensure that all the categorical variables are properly encoded. For example, 'gender' has two categories, which is straightforward. But other columns like 'PaymentMethod' have four categories, so one-hot encoding will create four new columns. It's essential to handle this correctly to prevent data leakage or incorrect model assumptions.\n", "\n", "In summary, the main steps I need to take are:\n", "\n", "1. Convert 'TotalCharges' to float to ensure correct data type.\n", "2. Drop the 'customerID' column as it's unnecessary.\n", "3. Use one-hot encoding on all categorical columns to convert them into numerical features.\n", "4. Ensure that 'Churn' is treated as the target variable and might need special handling during encoding.\n", "\n", "I think these steps will prepare the dataset adequately for further analysis or modeling without removing any data points or outliers, as per the user's instructions.\n", "</think>\n", "\n", "### Step-by-Step Data Cleaning and Preprocessing Recommendations\n", "\n", "1. **Convert 'TotalCharges' to Numeric Type**\n", "   - **Action:** Convert the 'TotalCharges' column from object type to float.\n", "   - **Reason:** The values in 'TotalCharges' are numeric but stored as strings. Converting ensures proper numeric operations.\n", "   - **Code Snippet:** `df['TotalCharges'] = pd.to_numeric(df['TotalCharges'])`\n", "\n", "2. **Drop 'customerID' Column**\n", "   - **Action:** Remove the 'customerID' column.\n", "   - **Reason:** 'customerID' is a unique identifier that doesn't provide meaningful information for analysis.\n", "   - **Code Snippet:** `df = df.drop(['customerID'], axis=1)`\n", "\n", "3. **One-Hot Encoding for Categorical Variables**\n", "   - **Action:** Apply one-hot encoding to all categorical columns.\n", "   - **Reason:** Convert categorical data into a numerical format suitable for machine learning models.\n", "   - **Code Snippet:** `df = pd.get_dummies(df, columns=['gender', 'Partner', 'Dependents', 'PhoneService', 'MultipleLines', 'InternetService', 'OnlineSecurity', 'OnlineBackup', 'DeviceProtection', 'TechSupport', 'StreamingTV', 'StreamingMovies', 'Contract', 'PaperlessBilling', 'PaymentMethod'])`\n", "\n", "4. **Handle the Target Variable 'Churn'**\n", "   - **Action:** Ensure 'Churn' is treated as the target variable, potentially using appropriate encoding.\n", "   - **Reason:** 'Churn' is the target variable and may require specific handling, such as stratified sampling or target encoding.\n", "   - **Code Snippet:** `target = df['Churn']` followed by appropriate encoding if necessary.\n", "\n", "These steps prepare the dataset by correcting data types, removing unnecessary identifiers, and converting categorical variables into a usable format for analysis."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["data_cleaning_agent.get_recommended_cleaning_steps(markdown=True)"]}, {"cell_type": "markdown", "metadata": {"id": "24GmkKbeoXsu"}, "source": ["### Want To Become A Full-Stack Generative AI Data Scientist?\n", "\n", "![Generative AI Data Scientist](../img/become_a_generative_ai_data_scientist.jpg)\n", "\n", "I teach Generative AI Data Science to help you build AI-powered data science apps. [**Register for my next Generative AI for Data Scientists workshop here.**](https://learn.business-science.io/ai-register)"]}, {"cell_type": "markdown", "metadata": {"id": "Jwh89gMwoXsu"}, "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 4}