============================= test session starts ==============================
platform darwin -- Python 3.13.2, pytest-8.4.1, pluggy-1.6.0
rootdir: /Users/<USER>/code/baml-kuzu-demo
configfile: pyproject.toml
plugins: anyio-4.9.0
collected 20 items

tests/test_agent_endpoint.py ..........                                  [ 50%]
tests/test_vanilla_graphrag.py ....FFFFFF                                [100%]

=================================== FAILURES ===================================
_ TestAnswerQuestion.test_answer_question_contains_expected_values[test_case4] _

self = <tests.test_vanilla_graphrag.TestAnswerQuestion object at 0x13be332f0>
graph_rag = <helpers.GraphRAG object at 0x13beccd70>
test_case = {'expected_values': ['diazepam', 'morphine', 'oxycodone'], 'question': 'What drugs can cause sleepiness as a side effect?'}

    @pytest.mark.parametrize("test_case", test_cases)
    def test_answer_question_contains_expected_values(self, graph_rag, test_case):
        """Test that the answer_question method returns responses containing expected values."""
    
        question = test_case["question"]
        expected_values = test_case["expected_values"]
    
        # First retrieve the data
        result = graph_rag.retrieve(question)
    
        # Then generate the natural language answer
        answer = graph_rag.answer_question(result)
    
        print(f"\nQuestion: {question}")
        print(f"Generated Cypher: {result['cypher']}")
        print(f"Raw response: {result['response']}")
        print(f"Natural language answer: {answer}")
    
        # Assert that the answer contains the expected values
        answer_lower = answer.lower()
        for expected_value in expected_values:
>           assert expected_value.lower() in answer_lower, f"Expected '{expected_value}' not found in answer: {answer}"
E           AssertionError: Expected 'diazepam' not found in answer: I don't have enough information to answer the question about which drugs can cause sleepiness as a side effect.
E           assert 'diazepam' in "i don't have enough information to answer the question about which drugs can cause sleepiness as a side effect."
E            +  where 'diazepam' = <built-in method lower of str object at 0x10303ddf0>()
E            +    where <built-in method lower of str object at 0x10303ddf0> = 'diazepam'.lower

tests/test_vanilla_graphrag.py:48: AssertionError
----------------------------- Captured stdout call -----------------------------

Question: What drugs can cause sleepiness as a side effect?
Generated Cypher: MATCH (d:DrugGeneric)-[:CAN_CAUSE]->(s:Symptom) WHERE LOWER(s.name) = 'sleepiness' RETURN d.name
Raw response: 
Natural language answer: I don't have enough information to answer the question about which drugs can cause sleepiness as a side effect.
_ TestAnswerQuestion.test_answer_question_contains_expected_values[test_case5] _

self = <tests.test_vanilla_graphrag.TestAnswerQuestion object at 0x13bec48d0>
graph_rag = <helpers.GraphRAG object at 0x13beccd70>
test_case = {'expected_values': ['L4D8Z'], 'question': 'Which patients experience sleepiness as a side effect?'}

    @pytest.mark.parametrize("test_case", test_cases)
    def test_answer_question_contains_expected_values(self, graph_rag, test_case):
        """Test that the answer_question method returns responses containing expected values."""
    
        question = test_case["question"]
        expected_values = test_case["expected_values"]
    
        # First retrieve the data
        result = graph_rag.retrieve(question)
    
        # Then generate the natural language answer
        answer = graph_rag.answer_question(result)
    
        print(f"\nQuestion: {question}")
        print(f"Generated Cypher: {result['cypher']}")
        print(f"Raw response: {result['response']}")
        print(f"Natural language answer: {answer}")
    
        # Assert that the answer contains the expected values
        answer_lower = answer.lower()
        for expected_value in expected_values:
>           assert expected_value.lower() in answer_lower, f"Expected '{expected_value}' not found in answer: {answer}"
E           AssertionError: Expected 'L4D8Z' not found in answer: I don't have enough information to answer the question about which patients experience sleepiness as a side effect.
E           assert 'l4d8z' in "i don't have enough information to answer the question about which patients experience sleepiness as a side effect."
E            +  where 'l4d8z' = <built-in method lower of str object at 0x102a40c30>()
E            +    where <built-in method lower of str object at 0x102a40c30> = 'L4D8Z'.lower

tests/test_vanilla_graphrag.py:48: AssertionError
----------------------------- Captured stdout call -----------------------------

Question: Which patients experience sleepiness as a side effect?
Generated Cypher: MATCH (p:Patient)-[:EXPERIENCES]->(s:Symptom) WHERE LOWER(s.name) = 'sleepiness' RETURN p.patient_id
Raw response: 
Natural language answer: I don't have enough information to answer the question about which patients experience sleepiness as a side effect.
_ TestAnswerQuestion.test_answer_question_contains_expected_values[test_case6] _

self = <tests.test_vanilla_graphrag.TestAnswerQuestion object at 0x13bec4c00>
graph_rag = <helpers.GraphRAG object at 0x13beccd70>
test_case = {'expected_values': ['yes'], 'question': 'Can Vancomycin cause vomiting as a side effect?'}

    @pytest.mark.parametrize("test_case", test_cases)
    def test_answer_question_contains_expected_values(self, graph_rag, test_case):
        """Test that the answer_question method returns responses containing expected values."""
    
        question = test_case["question"]
        expected_values = test_case["expected_values"]
    
        # First retrieve the data
        result = graph_rag.retrieve(question)
    
        # Then generate the natural language answer
        answer = graph_rag.answer_question(result)
    
        print(f"\nQuestion: {question}")
        print(f"Generated Cypher: {result['cypher']}")
        print(f"Raw response: {result['response']}")
        print(f"Natural language answer: {answer}")
    
        # Assert that the answer contains the expected values
        answer_lower = answer.lower()
        for expected_value in expected_values:
>           assert expected_value.lower() in answer_lower, f"Expected '{expected_value}' not found in answer: {answer}"
E           AssertionError: Expected 'yes' not found in answer: I don't have enough information to answer the question about whether Vancomycin can cause vomiting as a side effect.
E           assert 'yes' in "i don't have enough information to answer the question about whether vancomycin can cause vomiting as a side effect."
E            +  where 'yes' = <built-in method lower of str object at 0x10089d560>()
E            +    where <built-in method lower of str object at 0x10089d560> = 'yes'.lower

tests/test_vanilla_graphrag.py:48: AssertionError
----------------------------- Captured stdout call -----------------------------

Question: Can Vancomycin cause vomiting as a side effect?
Generated Cypher: MATCH (d:DrugGeneric)-[:CAN_CAUSE]->(s:Symptom) WHERE LOWER(d.name) = 'vancomycin' AND LOWER(s.name) = 'vomiting' RETURN d.name AS drug, s.name AS side_effect
Raw response: 
Natural language answer: I don't have enough information to answer the question about whether Vancomycin can cause vomiting as a side effect.
_ TestAnswerQuestion.test_answer_question_contains_expected_values[test_case7] _

self = <tests.test_vanilla_graphrag.TestAnswerQuestion object at 0x13bbf7d50>
graph_rag = <helpers.GraphRAG object at 0x13beccd70>
test_case = {'expected_values': ['upset stomach', 'headache', 'muscle pain'], 'question': 'What are the side effects of drugs for conditions related to lowering cholestrol?'}

    @pytest.mark.parametrize("test_case", test_cases)
    def test_answer_question_contains_expected_values(self, graph_rag, test_case):
        """Test that the answer_question method returns responses containing expected values."""
    
        question = test_case["question"]
        expected_values = test_case["expected_values"]
    
        # First retrieve the data
        result = graph_rag.retrieve(question)
    
        # Then generate the natural language answer
        answer = graph_rag.answer_question(result)
    
        print(f"\nQuestion: {question}")
        print(f"Generated Cypher: {result['cypher']}")
        print(f"Raw response: {result['response']}")
        print(f"Natural language answer: {answer}")
    
        # Assert that the answer contains the expected values
        answer_lower = answer.lower()
        for expected_value in expected_values:
>           assert expected_value.lower() in answer_lower, f"Expected '{expected_value}' not found in answer: {answer}"
E           AssertionError: Expected 'upset stomach' not found in answer: I don't have enough information to answer the question about the side effects of drugs for conditions related to lowering cholesterol.
E           assert 'upset stomach' in "i don't have enough information to answer the question about the side effects of drugs for conditions related to lowering cholesterol."
E            +  where 'upset stomach' = <built-in method lower of str object at 0x10303dcb0>()
E            +    where <built-in method lower of str object at 0x10303dcb0> = 'upset stomach'.lower

tests/test_vanilla_graphrag.py:48: AssertionError
----------------------------- Captured stdout call -----------------------------

Question: What are the side effects of drugs for conditions related to lowering cholestrol?
Generated Cypher: MATCH (c:Condition)-[:IS_TREATED_BY]->(d:DrugGeneric)-[:CAN_CAUSE]->(s:Symptom) WHERE LOWER(c.name) CONTAINS 'cholestrol' RETURN DISTINCT s.name
Raw response: 
Natural language answer: I don't have enough information to answer the question about the side effects of drugs for conditions related to lowering cholesterol.
_ TestAnswerQuestion.test_answer_question_contains_expected_values[test_case8] _

self = <tests.test_vanilla_graphrag.TestAnswerQuestion object at 0x13bbf7b50>
graph_rag = <helpers.GraphRAG object at 0x13beccd70>
test_case = {'expected_values': ['L4D8Z'], 'question': 'Which patients experience sleepiness as a side effect?'}

    @pytest.mark.parametrize("test_case", test_cases)
    def test_answer_question_contains_expected_values(self, graph_rag, test_case):
        """Test that the answer_question method returns responses containing expected values."""
    
        question = test_case["question"]
        expected_values = test_case["expected_values"]
    
        # First retrieve the data
        result = graph_rag.retrieve(question)
    
        # Then generate the natural language answer
        answer = graph_rag.answer_question(result)
    
        print(f"\nQuestion: {question}")
        print(f"Generated Cypher: {result['cypher']}")
        print(f"Raw response: {result['response']}")
        print(f"Natural language answer: {answer}")
    
        # Assert that the answer contains the expected values
        answer_lower = answer.lower()
        for expected_value in expected_values:
>           assert expected_value.lower() in answer_lower, f"Expected '{expected_value}' not found in answer: {answer}"
E           AssertionError: Expected 'L4D8Z' not found in answer: I don't have enough information to answer the question about which patients experience sleepiness as a side effect.
E           assert 'l4d8z' in "i don't have enough information to answer the question about which patients experience sleepiness as a side effect."
E            +  where 'l4d8z' = <built-in method lower of str object at 0x102a40c30>()
E            +    where <built-in method lower of str object at 0x102a40c30> = 'L4D8Z'.lower

tests/test_vanilla_graphrag.py:48: AssertionError
----------------------------- Captured stdout call -----------------------------

Question: Which patients experience sleepiness as a side effect?
Generated Cypher: MATCH (p:Patient)-[:EXPERIENCES]->(s:Symptom) WHERE LOWER(s.name) = 'sleepiness' RETURN p.patient_id
Raw response: 
Natural language answer: I don't have enough information to answer the question about which patients experience sleepiness as a side effect.
_ TestAnswerQuestion.test_answer_question_contains_expected_values[test_case9] _

self = <tests.test_vanilla_graphrag.TestAnswerQuestion object at 0x10af63980>
graph_rag = <helpers.GraphRAG object at 0x13beccd70>
test_case = {'expected_values': ['Digitek', 'Cordarone', 'Inderal', 'Pacerone', 'Lanoxin'], 'question': 'What drug brands treat the condition of irregular heart rhythm?'}

    @pytest.mark.parametrize("test_case", test_cases)
    def test_answer_question_contains_expected_values(self, graph_rag, test_case):
        """Test that the answer_question method returns responses containing expected values."""
    
        question = test_case["question"]
        expected_values = test_case["expected_values"]
    
        # First retrieve the data
        result = graph_rag.retrieve(question)
    
        # Then generate the natural language answer
        answer = graph_rag.answer_question(result)
    
        print(f"\nQuestion: {question}")
        print(f"Generated Cypher: {result['cypher']}")
        print(f"Raw response: {result['response']}")
        print(f"Natural language answer: {answer}")
    
        # Assert that the answer contains the expected values
        answer_lower = answer.lower()
        for expected_value in expected_values:
>           assert expected_value.lower() in answer_lower, f"Expected '{expected_value}' not found in answer: {answer}"
E           AssertionError: Expected 'Digitek' not found in answer: I don't have enough information to answer the question about which drug brands treat the condition of irregular heart rhythm.
E           assert 'digitek' in "i don't have enough information to answer the question about which drug brands treat the condition of irregular heart rhythm."
E            +  where 'digitek' = <built-in method lower of str object at 0x102a40c60>()
E            +    where <built-in method lower of str object at 0x102a40c60> = 'Digitek'.lower

tests/test_vanilla_graphrag.py:48: AssertionError
----------------------------- Captured stdout call -----------------------------

Question: What drug brands treat the condition of irregular heart rhythm?
Generated Cypher: MATCH (c:Condition)-[:IS_TREATED_BY]->(dg:DrugGeneric)-[:HAS_BRAND]->(db:DrugBrand) WHERE LOWER(c.name) = LOWER('irregular heart rhythm') RETURN db.name
Raw response: 
Natural language answer: I don't have enough information to answer the question about which drug brands treat the condition of irregular heart rhythm.
=========================== short test summary info ============================
FAILED tests/test_vanilla_graphrag.py::TestAnswerQuestion::test_answer_question_contains_expected_values[test_case4]
FAILED tests/test_vanilla_graphrag.py::TestAnswerQuestion::test_answer_question_contains_expected_values[test_case5]
FAILED tests/test_vanilla_graphrag.py::TestAnswerQuestion::test_answer_question_contains_expected_values[test_case6]
FAILED tests/test_vanilla_graphrag.py::TestAnswerQuestion::test_answer_question_contains_expected_values[test_case7]
FAILED tests/test_vanilla_graphrag.py::TestAnswerQuestion::test_answer_question_contains_expected_values[test_case8]
FAILED tests/test_vanilla_graphrag.py::TestAnswerQuestion::test_answer_question_contains_expected_values[test_case9]
=================== 6 failed, 14 passed in 91.22s (0:01:31) ====================
