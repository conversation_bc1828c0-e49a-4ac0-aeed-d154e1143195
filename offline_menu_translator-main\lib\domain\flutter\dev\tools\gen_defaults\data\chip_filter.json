{"version": "6_1_0", "md.comp.filter-chip.container.height": 32.0, "md.comp.filter-chip.container.shape": "md.sys.shape.corner.small", "md.comp.filter-chip.disabled.label-text.color": "onSurface", "md.comp.filter-chip.disabled.label-text.opacity": 0.38, "md.comp.filter-chip.dragged.container.elevation": "md.sys.elevation.level4", "md.comp.filter-chip.elevated.container.elevation": "md.sys.elevation.level1", "md.comp.filter-chip.elevated.container.shadow-color": "shadow", "md.comp.filter-chip.elevated.disabled.container.color": "onSurface", "md.comp.filter-chip.elevated.disabled.container.elevation": "md.sys.elevation.level0", "md.comp.filter-chip.elevated.disabled.container.opacity": 0.12, "md.comp.filter-chip.elevated.focus.container.elevation": "md.sys.elevation.level1", "md.comp.filter-chip.elevated.hover.container.elevation": "md.sys.elevation.level2", "md.comp.filter-chip.elevated.pressed.container.elevation": "md.sys.elevation.level1", "md.comp.filter-chip.elevated.selected.container.color": "secondaryContainer", "md.comp.filter-chip.elevated.unselected.container.color": "surfaceContainerLow", "md.comp.filter-chip.flat.container.elevation": "md.sys.elevation.level0", "md.comp.filter-chip.flat.disabled.selected.container.color": "onSurface", "md.comp.filter-chip.flat.disabled.selected.container.opacity": 0.12, "md.comp.filter-chip.flat.disabled.unselected.outline.color": "onSurface", "md.comp.filter-chip.flat.disabled.unselected.outline.opacity": 0.12, "md.comp.filter-chip.flat.selected.container.color": "secondaryContainer", "md.comp.filter-chip.flat.selected.focus.container.elevation": "md.sys.elevation.level0", "md.comp.filter-chip.flat.selected.hover.container.elevation": "md.sys.elevation.level1", "md.comp.filter-chip.flat.selected.outline.width": 0.0, "md.comp.filter-chip.flat.selected.pressed.container.elevation": "md.sys.elevation.level0", "md.comp.filter-chip.flat.unselected.focus.container.elevation": "md.sys.elevation.level0", "md.comp.filter-chip.flat.unselected.focus.outline.color": "onSurfaceVariant", "md.comp.filter-chip.flat.unselected.hover.container.elevation": "md.sys.elevation.level0", "md.comp.filter-chip.flat.unselected.outline.color": "outlineVariant", "md.comp.filter-chip.flat.unselected.outline.width": 1.0, "md.comp.filter-chip.flat.unselected.pressed.container.elevation": "md.sys.elevation.level0", "md.comp.filter-chip.focus.indicator.color": "secondary", "md.comp.filter-chip.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.filter-chip.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.filter-chip.label-text.text-style": "labelLarge", "md.comp.filter-chip.selected.dragged.label-text.color": "onSecondaryContainer", "md.comp.filter-chip.selected.dragged.state-layer.color": "onSecondaryContainer", "md.comp.filter-chip.selected.dragged.state-layer.opacity": "md.sys.state.dragged.state-layer-opacity", "md.comp.filter-chip.selected.focus.label-text.color": "onSecondaryContainer", "md.comp.filter-chip.selected.focus.state-layer.color": "onSecondaryContainer", "md.comp.filter-chip.selected.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.filter-chip.selected.hover.label-text.color": "onSecondaryContainer", "md.comp.filter-chip.selected.hover.state-layer.color": "onSecondaryContainer", "md.comp.filter-chip.selected.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.filter-chip.selected.label-text.color": "onSecondaryContainer", "md.comp.filter-chip.selected.pressed.label-text.color": "onSecondaryContainer", "md.comp.filter-chip.selected.pressed.state-layer.color": "onSurfaceVariant", "md.comp.filter-chip.selected.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.filter-chip.unselected.dragged.label-text.color": "onSurfaceVariant", "md.comp.filter-chip.unselected.dragged.state-layer.color": "onSurfaceVariant", "md.comp.filter-chip.unselected.dragged.state-layer.opacity": "md.sys.state.dragged.state-layer-opacity", "md.comp.filter-chip.unselected.focus.label-text.color": "onSurfaceVariant", "md.comp.filter-chip.unselected.focus.state-layer.color": "onSurfaceVariant", "md.comp.filter-chip.unselected.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.filter-chip.unselected.hover.label-text.color": "onSurfaceVariant", "md.comp.filter-chip.unselected.hover.state-layer.color": "onSurfaceVariant", "md.comp.filter-chip.unselected.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.filter-chip.unselected.label-text.color": "onSurfaceVariant", "md.comp.filter-chip.unselected.pressed.label-text.color": "onSurfaceVariant", "md.comp.filter-chip.unselected.pressed.state-layer.color": "onSecondaryContainer", "md.comp.filter-chip.unselected.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.filter-chip.with-icon.icon.size": 18.0, "md.comp.filter-chip.with-leading-icon.disabled.leading-icon.color": "onSurface", "md.comp.filter-chip.with-leading-icon.disabled.leading-icon.opacity": 0.38, "md.comp.filter-chip.with-leading-icon.selected.dragged.leading-icon.color": "onSecondaryContainer", "md.comp.filter-chip.with-leading-icon.selected.focus.leading-icon.color": "onSecondaryContainer", "md.comp.filter-chip.with-leading-icon.selected.hover.leading-icon.color": "onSecondaryContainer", "md.comp.filter-chip.with-leading-icon.selected.leading-icon.color": "onSecondaryContainer", "md.comp.filter-chip.with-leading-icon.selected.pressed.leading-icon.color": "onSecondaryContainer", "md.comp.filter-chip.with-leading-icon.unselected.dragged.leading-icon.color": "primary", "md.comp.filter-chip.with-leading-icon.unselected.focus.leading-icon.color": "primary", "md.comp.filter-chip.with-leading-icon.unselected.hover.leading-icon.color": "primary", "md.comp.filter-chip.with-leading-icon.unselected.leading-icon.color": "primary", "md.comp.filter-chip.with-leading-icon.unselected.pressed.leading-icon.color": "primary", "md.comp.filter-chip.with-trailing-icon.disabled.trailing-icon.color": "onSurface", "md.comp.filter-chip.with-trailing-icon.disabled.trailing-icon.opacity": 0.38, "md.comp.filter-chip.with-trailing-icon.selected.dragged.trailing-icon.color": "onSecondaryContainer", "md.comp.filter-chip.with-trailing-icon.selected.focus.trailing-icon.color": "onSecondaryContainer", "md.comp.filter-chip.with-trailing-icon.selected.hover.trailing-icon.color": "onSecondaryContainer", "md.comp.filter-chip.with-trailing-icon.selected.pressed.trailing-icon.color": "onSecondaryContainer", "md.comp.filter-chip.with-trailing-icon.selected.trailing-icon.color": "onSecondaryContainer", "md.comp.filter-chip.with-trailing-icon.unselected.dragged.trailing-icon.color": "onSurfaceVariant", "md.comp.filter-chip.with-trailing-icon.unselected.focus.trailing-icon.color": "onSurfaceVariant", "md.comp.filter-chip.with-trailing-icon.unselected.hover.trailing-icon.color": "onSurfaceVariant", "md.comp.filter-chip.with-trailing-icon.unselected.pressed.trailing-icon.color": "onSurfaceVariant", "md.comp.filter-chip.with-trailing-icon.unselected.trailing-icon.color": "onSurfaceVariant"}