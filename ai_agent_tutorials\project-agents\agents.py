from dataclasses import dataclass
from typing import List, Dict, Optional
from agno.agent import Agent
from agno.tools.duckduckgo import DuckDuckGoTools
from agno.tools.newspaper4k import Newspaper4kTools

@dataclass
class SearchResult:
    terms: List[str]
    urls: List[Dict[str, str]]

def create_searcher_agent() -> Agent:
    """Create an agent for searching relevant URLs."""
    return Agent(
        name="Searcher",
        role="Searches the top URLs for a topic",
        instructions=[
            "Given a topic, first generate a list of 3 search terms related to that topic.",
            "For each search term, search the web and analyze the results.",
            "Return the 10 most relevant URLs to the topic.",
            "For each URL, include the domain name for attribution.",
            "You are writing for the New York Times, so the quality of the sources is important.",
            "Prioritize authoritative sources like academic journals, government sites, and reputable news outlets.",
        ],
        tools=[DuckDuckGoTools()],
        add_datetime_to_instructions=True,
        expected_output=SearchResult,
    )

def create_writer_agent() -> Agent:
    """Create an agent for writing high-quality articles."""
    return Agent(
        name="Writer",
        role="Writes a high-quality article",
        description=(
            "You are a senior writer for the New York Times. Given a topic and a list of URLs, "
            "your goal is to write a high-quality NYT-worthy article on the topic."
        ),
        instructions=[
            "First read all URLs using `read_article`.",
            "Then write a high-quality NYT-worthy article on the topic.",
            "The article should be well-structured with an engaging introduction, "
            "informative body, and thoughtful conclusion.",
            "Use subheadings to organize sections when appropriate.",
            "Ensure the length is at least 15 paragraphs.",
            "Provide a nuanced and balanced opinion, quoting facts where possible.",
            "Include proper attribution for all facts and quotes.",
            "Write in a formal but engaging style suitable for the New York Times.",
            "Never make up facts or plagiarize. Always cite your sources.",
        ],
        tools=[Newspaper4kTools()],
        add_datetime_to_instructions=True,
    )