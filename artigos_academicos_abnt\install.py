"""
Script de instalação para o aplicativo de artigos acadêmicos ABNT.
Instala todas as dependências necessárias.
"""

import os
import sys
import subprocess

def main():
    """Função principal para instalar as dependências"""
    print("Instalando dependências para o aplicativo de artigos acadêmicos ABNT...")
    
    # Obter o diretório do script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Caminho para o arquivo de requisitos
    requirements_file = os.path.join(script_dir, "requirements.txt")
    
    # Verificar se o arquivo de requisitos existe
    if not os.path.exists(requirements_file):
        print(f"Erro: Arquivo de requisitos não encontrado em {requirements_file}")
        return 1
    
    # Instalar as dependências
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", requirements_file])
        print("Dependências instaladas com sucesso!")
        
        # Verificar se o diretório .streamlit existe
        streamlit_dir = os.path.join(script_dir, ".streamlit")
        secrets_file = os.path.join(streamlit_dir, "secrets.toml")
        
        if not os.path.exists(streamlit_dir):
            os.makedirs(streamlit_dir)
        
        # Verificar se o arquivo secrets.toml existe
        if not os.path.exists(secrets_file):
            # Solicitar a chave da API Groq
            print("\nChave da API Groq não encontrada.")
            api_key = input("Digite sua chave da API Groq: ")
            
            # Criar o arquivo secrets.toml
            with open(secrets_file, "w") as f:
                f.write(f'GROQ_API_KEY = "{api_key}"\n')
            
            print(f"Arquivo de segredos criado em {secrets_file}")
        
        print("\nInstalação concluída com sucesso!")
        print("Para executar o aplicativo, use o comando:")
        print(f"python {os.path.join(script_dir, 'run.py')}")
        
        return 0
    except subprocess.CalledProcessError as e:
        print(f"Erro ao instalar dependências: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
