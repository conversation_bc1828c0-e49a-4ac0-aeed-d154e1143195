name: simul_trans_ai
description: "Tradutor Simultâneo Multimodal Avançado com Google Gemma 3N - Suporte a 140+ idiomas com processamento offline"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: '>=3.8.0'

dependencies:
  flutter:
    sdk: flutter

  # Internationalization
  flutter_localizations:
    sdk: flutter
  intl: any

  # Core UI
  cupertino_icons: ^1.0.6
  material_design_icons_flutter: ^7.0.7296

  # Online AI Model Support (Web + Mobile compatible)
  google_generative_ai: ^0.4.7

  # UI & UX
  lottie: ^3.3.1
  flutter_staggered_animations: ^1.1.1

  # Multimodal Processing
  file_picker: ^10.2.0
  image_picker: ^1.0.4

  # Audio Recording & Playback
  record: ^5.0.4
  audioplayers: ^6.0.0
  permission_handler: ^11.3.1

  # Video Recording & Playback
  camera: ^0.10.5+9
  video_player: ^2.8.2
  chewie: ^1.7.5

  # Storage & Cache
  shared_preferences: ^2.2.2
  path_provider: ^2.1.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Network & API
  http: ^1.1.0

  # Utils
  package_info_plus: ^8.3.0
  device_info_plus: ^11.5.0

  # State Management
  flutter_riverpod: ^2.4.9

  # JSON Serialization
  json_annotation: ^4.8.1

  # HTTP client for Ollama communication
  # google_generative_ai: ^0.2.2  # REMOVED - Using Ollama exclusively

  # Cache and crypto for Gemma-3N optimizations
  crypto: ^3.0.3

  # Environment variables
  flutter_dotenv: ^5.1.0

  # Image handling
  image: ^4.1.7

  # Additional utilities
  uuid: ^4.3.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  build_runner: ^2.4.7
  hive_generator: ^2.0.1
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/animations/
    - assets/icons/
    - assets/models/
    - assets/sounds/
    - .env
