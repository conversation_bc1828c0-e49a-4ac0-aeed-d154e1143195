name: Bug Report
description: Create a report to help us improve
title: "[BUG] "
labels: [bug]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
        Please provide as much detail as possible to help us understand and fix the issue.

  - type: textarea
    id: bug-description
    attributes:
      label: Bug Description
      description: Provide a clear and concise description of the bug
      placeholder: "Describe the bug in detail..."
    validations:
      required: true

  - type: input
    id: project-name
    attributes:
      label: Project Name
      description: Name of the project where the bug was found
      placeholder: "e.g., job_searching_agent"
    validations:
      required: true

  - type: dropdown
    id: project-directory
    attributes:
      label: Project Directory
      description: Select the directory where the bug was found
      options:
        - advance_ai_agents
        - mcp_ai_agents
        - rag_apps
        - starter_ai_agents
    validations:
      required: true

  - type: textarea
    id: current-behavior
    attributes:
      label: Current Behavior
      description: Describe what is currently happening
      placeholder: "What happens when you encounter the bug?"
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected Behavior
      description: Describe what should happen
      placeholder: "What did you expect to happen?"
    validations:
      required: true

  - type: textarea
    id: reproduction-steps
    attributes:
      label: Steps to Reproduce
      description: Provide step-by-step instructions to reproduce the bug
      placeholder: |
        1. 
        2. 
        3.
      render: bash
    validations:
      required: true

  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: What operating system are you using?
      options:
        - macOS
        - Windows
        - Linux
        - Other
    validations:
      required: true

  - type: input
    id: python-version
    attributes:
      label: Python Version
      description: What version of Python are you using?
      placeholder: "e.g., 3.8.0"
    validations:
      required: true

  - type: textarea
    id: dependencies
    attributes:
      label: Dependencies
      description: List relevant dependencies and their versions
      placeholder: |
        package1==1.0.0
        package2==2.0.0
    validations:
      required: true

  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: If applicable, add screenshots to help explain the bug
      placeholder: "Add screenshots here..."

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context about the bug here
      placeholder: "Any additional information that might be helpful..."
