[{"id": "65a3965f-5b24-4301-9850-134fcb35888b", "title": "how to fine-tune retrieval and reranking models using your documents", "sanitized_title": "how_to_fine_tune_retrieval_and_reranking_models_using_your_documents", "path": "artigos_academicos_abnt_results\\how_to_fine_tune_retrieval_and_reranking_models_using_your_documents", "summary": "De acordo com resultados de pesquisa, a seleção cuidadosa de documentos e um pré-processamento adequado são fundamentais para o sucesso do fine-tuning (RESULTADOS DA PESQUISA, 2023a).", "sources_count": 20, "has_polished": true, "created_at": "2025-04-14T11:14:17.407170", "final_article_path": "artigos_academicos_abnt_results\\how_to_fine_tune_retrieval_and_reranking_models_using_your_documents\\polished_article.txt", "article_format": "standard", "timestamp": "2025-04-14T11:14:17.408166", "tags": [], "category": "Sem categoria"}, {"id": "d849cec0-0849-4e5e-a4d0-ec9a5ac43538", "title": "Synthetic Data", "sanitized_title": "synthetic_data", "path": "artigos_academicos_abnt_results\\synthetic_data", "summary": "ILYAS, <PERSON><PERSON> et al. Synthetic Data for Machine Learning. 2020. Disponível em: <https://arxiv.org/abs/2003.08505>. Acesso em: 2023.", "sources_count": 10, "has_polished": true, "created_at": "2025-04-14T10:26:32.372053", "final_article_path": "artigos_academicos_abnt_results\\synthetic_data\\polished_article.txt", "article_format": "standard", "timestamp": "2025-04-14T10:26:32.372053", "tags": [], "category": "Sem categoria"}, {"id": "052838a9-76d0-438c-86a5-282714a078ab", "title": "Multimodal Agents and Teams of Agents", "sanitized_title": "multimodal_agents_and_teams_of_agents", "path": "artigos_academicos_abnt_results\\multimodal_agents_and_teams_of_agents", "summary": "AUTOR DESCONHECIDO. Multimodal Agents and Teams of Agents: Pesquisa Acadêmica. Data desconhecida. Disponível em: <source_2>. Acesso em: 20 fev. 2023.", "sources_count": 2, "has_polished": true, "created_at": "2025-04-13T22:47:24.197407", "final_article_path": "artigos_academicos_abnt_results\\multimodal_agents_and_teams_of_agents\\polished_article.txt", "article_format": "academic", "word_counts": {"resumo": 200, "intro": 400, "metodo": 400, "desenv": 600, "result": 600, "concl": 200}, "timestamp": "2025-04-13T22:47:24.198405", "tags": ["Agents"], "category": "Equipes de Agentes"}, {"id": "af2c71a1-8727-4a0f-ab1b-5a3c27560047", "title": "multi agent llm", "sanitized_title": "multi_agent_llm", "path": "artigos_academicos_abnt_results\\multi_agent_llm", "summary": "**Palavras-chave:** sistemas multi-agentes; modelos de linguagem grande; inteligência artificial; segurança; confiabilidade", "sources_count": 10, "has_polished": true, "created_at": "2025-04-13T16:29:40.099660", "final_article_path": "artigos_academicos_abnt_results\\multi_agent_llm\\polished_article.txt", "article_format": "academic", "timestamp": "2025-04-13T16:29:40.099660"}, {"id": "14695153-6889-4b73-82ca-15cdc28415b7", "title": "Vector Store", "sanitized_title": "vector_store", "path": "artigos_academicos_abnt_results\\vector_store", "summary": "ISSEN, A.; FURON, T.; GRIPON, V.; RABBAT, M.; JEGOU, H. Memory vectors for similarity search in high-dimensional spaces. 2014. Disponível em: <http://arxiv.org/abs/1412.3328v7>. Acesso em: 10 fev. 202...", "sources_count": 10, "has_polished": true, "created_at": "2025-04-13T16:16:50.079668", "final_article_path": "artigos_academicos_abnt_results\\vector_store\\polished_article.txt", "article_format": "academic", "word_counts": {"resumo": 200, "intro": 1000, "metodo": 600, "desenv": 1600, "result": 600, "concl": 350}, "timestamp": "2025-04-13T16:16:50.079668"}, {"id": "4e1ce109-2a4a-4847-9f82-12e38b511174", "title": "Ancient Egypt", "sanitized_title": "ancient_egypt", "path": "artigos_academicos_abnt_results\\ancient_egypt", "summary": "DE GREGORIO, <PERSON><PERSON> et al. A New Framework for Error Analysis in Computational Paleographic Dating of Greek Papyri. 2024. Disponível em: <http://arxiv.org/abs/2408.07779v1>. Acesso em: 10 set. 2022.", "sources_count": 10, "has_polished": true, "created_at": "2025-04-13T15:52:13.459531", "final_article_path": "artigos_academicos_abnt_results\\ancient_egypt\\polished_article.txt", "article_format": "academic", "word_counts": {"resumo": 200, "intro": 1000, "metodo": 600, "desenv": 1600, "result": 600, "concl": 350}, "timestamp": "2025-04-13T15:52:13.459531"}, {"id": "dc92f3d9-981b-4e68-8d53-02aae88604ee", "title": "llms", "sanitized_title": "llms", "path": "artigos_academicos_abnt_results\\llms", "summary": "**Palavras-chave:** Modelos de Linguagem de Grande Escala; Inteligência Artificial; Processamento de Linguagem Natural", "sources_count": 15, "has_polished": true, "created_at": "2025-04-13T15:38:19.730656", "final_article_path": "artigos_academicos_abnt_results\\llms\\polished_article.txt", "article_format": "academic", "timestamp": "2025-04-13T15:38:19.730656"}, {"id": "cb170b58-ac8a-4d7b-abaa-2117b5270d2d", "title": "retrieval augmented generation with agents", "sanitized_title": "retrieval_augmented_generation_with_agents", "path": "artigos_academicos_abnt_results\\retrieval_augmented_generation_with_agents", "summary": "<think>\nOkay, so I'm trying to understand this article about Retrieval-Augmented Generation (RAG) with agents. The introduction says RAG combines text generation from large language models (LLMs) with...", "sources_count": 10, "has_polished": true, "created_at": "2025-04-13T15:21:34.372673", "final_article_path": "artigos_academicos_abnt_results\\retrieval_augmented_generation_with_agents\\polished_article.txt", "article_format": "academic", "timestamp": "2025-04-13T15:21:34.372673"}, {"id": "a0f03898-bfe6-486d-beab-92d51f88c1a4", "title": "retrieval augmented generation with reranker", "sanitized_title": "retrieval_augmented_generation_with_reranker", "path": "artigos_academicos_abnt_results\\retrieval_augmented_generation_with_reranker", "summary": "**Palavras-chave:** Retrieval Augmented Generation; Reranker; Processamento de Linguagem Natural; Modelos de Linguagem.", "sources_count": 10, "has_polished": true, "created_at": "2025-04-13T15:15:12.980246", "final_article_path": "artigos_academicos_abnt_results\\retrieval_augmented_generation_with_reranker\\polished_article.txt", "article_format": "academic", "timestamp": "2025-04-13T15:15:12.980246"}, {"id": "000598c9-d34d-46cf-ae76-7371d4851a06", "title": "RAG and llms agents in retrieving information from physiotherapy patient records", "sanitized_title": "rag_and_llms_agents_in_retrieving_information_from_physiotherapy_patient_records", "path": "artigos_academicos_abnt_results\\rag_and_llms_agents_in_retrieving_information_from_physiotherapy_patient_records", "summary": "A importância da utilização de técnicas de inteligência artificial para melhorar a recuperação de informações em registros de pacientes de fisioterapia se deve ao fato de que esses registros contêm um...", "sources_count": 10, "has_polished": true, "created_at": "2025-04-13T14:13:04.630956", "final_article_path": "artigos_academicos_abnt_results\\rag_and_llms_agents_in_retrieving_information_from_physiotherapy_patient_records\\polished_article.txt", "timestamp": "2025-04-13T14:13:04.631953"}, {"id": "b9d878a3-7e38-45fd-9115-aec519f20678", "title": "retrieval augmented generation with agents", "sanitized_title": "retrieval_augmented_generation_with_agents", "path": "artigos_academicos_abnt_results\\retrieval_augmented_generation_with_agents", "summary": "Aqui está o artigo revisado e aprimorado, mantendo seu conteúdo, estrutura e citações ABNT:", "sources_count": 10, "has_polished": true, "created_at": "2025-04-12T23:49:50.263176", "final_article_path": "artigos_academicos_abnt_results\\retrieval_augmented_generation_with_agents\\polished_article.txt", "timestamp": "2025-04-12T23:49:50.263176"}, {"id": "985345b8-b26a-4283-b769-aaa78f8a8d17", "title": "agents and rag", "sanitized_title": "agents_and_rag", "path": "artigos_academicos_abnt_results\\agents_and_rag", "summary": "**Agents and RAG: A Novel Approach to Enhance Large Language Models**\n===========================================================", "sources_count": 10, "has_polished": true, "created_at": "2025-04-12T23:33:42.996058", "final_article_path": "artigos_academicos_abnt_results\\agents_and_rag\\polished_article.txt", "timestamp": "2025-04-12T23:33:42.997056"}]