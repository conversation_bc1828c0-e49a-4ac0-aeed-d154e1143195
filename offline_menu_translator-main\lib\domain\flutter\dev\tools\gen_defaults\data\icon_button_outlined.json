{"version": "6_1_0", "md.comp.outlined-icon-button.container.height": 40.0, "md.comp.outlined-icon-button.container.shape": "md.sys.shape.corner.full", "md.comp.outlined-icon-button.container.width": 40.0, "md.comp.outlined-icon-button.disabled.icon.color": "onSurface", "md.comp.outlined-icon-button.disabled.icon.opacity": 0.38, "md.comp.outlined-icon-button.disabled.selected.container.color": "onSurface", "md.comp.outlined-icon-button.disabled.selected.container.opacity": 0.12, "md.comp.outlined-icon-button.disabled.unselected.outline.color": "onSurface", "md.comp.outlined-icon-button.disabled.unselected.outline.opacity": 0.12, "md.comp.outlined-icon-button.focus.indicator.color": "secondary", "md.comp.outlined-icon-button.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.outlined-icon-button.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.outlined-icon-button.focus.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.outlined-icon-button.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.outlined-icon-button.icon.size": 24.0, "md.comp.outlined-icon-button.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.outlined-icon-button.selected.container.color": "inverseSurface", "md.comp.outlined-icon-button.selected.focus.icon.color": "onInverseSurface", "md.comp.outlined-icon-button.selected.focus.state-layer.color": "onInverseSurface", "md.comp.outlined-icon-button.selected.hover.icon.color": "onInverseSurface", "md.comp.outlined-icon-button.selected.hover.state-layer.color": "onInverseSurface", "md.comp.outlined-icon-button.selected.icon.color": "onInverseSurface", "md.comp.outlined-icon-button.selected.pressed.icon.color": "onInverseSurface", "md.comp.outlined-icon-button.selected.pressed.state-layer.color": "onInverseSurface", "md.comp.outlined-icon-button.unselected.focus.icon.color": "onSurfaceVariant", "md.comp.outlined-icon-button.unselected.focus.state-layer.color": "onSurfaceVariant", "md.comp.outlined-icon-button.unselected.hover.icon.color": "onSurfaceVariant", "md.comp.outlined-icon-button.unselected.hover.state-layer.color": "onSurfaceVariant", "md.comp.outlined-icon-button.unselected.icon.color": "onSurfaceVariant", "md.comp.outlined-icon-button.unselected.outline.color": "outline", "md.comp.outlined-icon-button.unselected.outline.width": 1.0, "md.comp.outlined-icon-button.unselected.pressed.icon.color": "onSurface", "md.comp.outlined-icon-button.unselected.pressed.state-layer.color": "onSurface"}