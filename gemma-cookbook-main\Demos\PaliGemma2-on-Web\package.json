{"name": "paligemma2-onnx-transformers.js", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "MIT", "description": "Inference PaliGemma 2 on the browser using ONNX weights, and Transformers.js.", "repository": {"type": "git", "url": "git+https://github.com/NSTiwari/PaliGemma2-ONNX-Transformers.js.git"}, "bugs": {"url": "https://github.com/NSTiwari/PaliGemma2-ONNX-Transformers.js/issues"}, "homepage": "https://github.com/NSTiwari/PaliGemma2-ONNX-Transformers.js#readme", "dependencies": {"@huggingface/transformers": "^3.3.3", "canvas": "^3.1.0", "express": "^4.21.2", "server.js": "^1.0.0"}}