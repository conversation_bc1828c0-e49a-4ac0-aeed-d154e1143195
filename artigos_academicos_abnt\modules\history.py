"""
Módulo de histórico e favoritos para o aplicativo de artigos acadêmicos ABNT.
Implementa o gerenciamento de histórico de artigos gerados e favoritos.
"""

import os
import json
import uuid
import shutil
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class HistoryManager:
    """Gerencia o histórico de artigos gerados e favoritos"""

    def __init__(self, history_dir=".history"):
        """Inicializa o gerenciador de histórico"""
        self.history_dir = history_dir
        self.favorites_file = os.path.join(history_dir, "favorites.json")
        self.history_file = os.path.join(history_dir, "history.json")

        # Criar diretório de histórico se não existir
        os.makedirs(history_dir, exist_ok=True)

        # Inicializar arquivos se não existirem
        if not os.path.exists(self.favorites_file):
            self._save_json(self.favorites_file, [])

        if not os.path.exists(self.history_file):
            self._save_json(self.history_file, [])

    def _load_json(self, file_path):
        """Carrega dados de um arquivo JSON"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Erro ao carregar {file_path}: {e}")
            return []

    def _save_json(self, file_path, data):
        """Salva dados em um arquivo JSON"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"Erro ao salvar {file_path}: {e}")
            return False

    def add_to_history(self, article_info):
        """Adiciona um artigo ao histórico"""
        # Carregar histórico atual
        history = self._load_json(self.history_file)

        # Adicionar ID único e timestamp se não existirem
        if "id" not in article_info:
            article_info["id"] = str(uuid.uuid4())
        if "timestamp" not in article_info:
            article_info["timestamp"] = datetime.now().isoformat()

        # Adicionar campos de tags e categoria se não existirem
        if "tags" not in article_info:
            article_info["tags"] = []
        if "category" not in article_info:
            article_info["category"] = "Sem categoria"

        # Adicionar ao início da lista (mais recente primeiro)
        history.insert(0, article_info)

        # Limitar o histórico a 100 itens
        if len(history) > 100:
            history = history[:100]

        # Salvar histórico atualizado
        return self._save_json(self.history_file, history)

    def get_history(self, limit=None):
        """Obtém o histórico de artigos"""
        history = self._load_json(self.history_file)
        if limit and limit > 0:
            return history[:limit]
        return history

    def add_to_favorites(self, article_id):
        """Adiciona um artigo aos favoritos"""
        # Carregar favoritos atuais
        favorites = self._load_json(self.favorites_file)

        # Verificar se o artigo já está nos favoritos
        if article_id in favorites:
            return True

        # Adicionar aos favoritos
        favorites.append(article_id)

        # Salvar favoritos atualizados
        return self._save_json(self.favorites_file, favorites)

    def remove_from_favorites(self, article_id):
        """Remove um artigo dos favoritos"""
        # Carregar favoritos atuais
        favorites = self._load_json(self.favorites_file)

        # Remover o artigo dos favoritos
        if article_id in favorites:
            favorites.remove(article_id)

            # Salvar favoritos atualizados
            return self._save_json(self.favorites_file, favorites)

        return True

    def get_favorites(self):
        """Obtém a lista de artigos favoritos"""
        # Carregar favoritos e histórico
        favorites_ids = self._load_json(self.favorites_file)
        history = self._load_json(self.history_file)

        # Filtrar artigos favoritos do histórico
        favorites = [article for article in history if article.get("id") in favorites_ids]

        return favorites

    def is_favorite(self, article_id):
        """Verifica se um artigo está nos favoritos"""
        favorites = self._load_json(self.favorites_file)
        return article_id in favorites

    def clear_history(self):
        """Limpa o histórico de artigos"""
        return self._save_json(self.history_file, [])

    def export_article(self, article_id, export_dir):
        """Exporta um artigo para um diretório específico"""
        # Carregar histórico
        history = self._load_json(self.history_file)

        # Encontrar o artigo pelo ID
        article = next((a for a in history if a.get("id") == article_id), None)
        if not article or "path" not in article:
            return False

        # Verificar se o diretório de origem existe
        source_dir = article["path"]
        if not os.path.exists(source_dir) or not os.path.isdir(source_dir):
            return False

        # Criar diretório de destino
        os.makedirs(export_dir, exist_ok=True)

        # Nome do diretório de destino
        dest_dir = os.path.join(export_dir, os.path.basename(source_dir))

        try:
            # Copiar o diretório
            shutil.copytree(source_dir, dest_dir, dirs_exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Erro ao exportar artigo: {e}")
            return False

    def add_tag(self, article_id, tag):
        """Adiciona uma tag a um artigo"""
        # Encontrar o artigo no histórico
        history = self._load_json(self.history_file)
        for article in history:
            if article.get("id") == article_id:
                # Inicializar a lista de tags se não existir
                if "tags" not in article:
                    article["tags"] = []

                # Adicionar a tag se ainda não estiver presente
                if tag not in article["tags"]:
                    article["tags"].append(tag)
                    self._save_json(self.history_file, history)
                return True
        return False

    def remove_tag(self, article_id, tag):
        """Remove uma tag de um artigo"""
        # Encontrar o artigo no histórico
        history = self._load_json(self.history_file)
        for article in history:
            if article.get("id") == article_id:
                # Remover a tag se estiver presente
                if "tags" in article and tag in article["tags"]:
                    article["tags"].remove(tag)
                    self._save_json(self.history_file, history)
                return True
        return False

    def set_category(self, article_id, category):
        """Define a categoria de um artigo"""
        # Encontrar o artigo no histórico
        history = self._load_json(self.history_file)
        for article in history:
            if article.get("id") == article_id:
                article["category"] = category
                self._save_json(self.history_file, history)
                return True
        return False

    def get_all_tags(self):
        """Retorna todas as tags usadas nos artigos"""
        history = self._load_json(self.history_file)
        all_tags = set()
        for article in history:
            if "tags" in article:
                all_tags.update(article["tags"])
        return sorted(list(all_tags))

    def get_all_categories(self):
        """Retorna todas as categorias usadas nos artigos"""
        history = self._load_json(self.history_file)
        all_categories = set()
        for article in history:
            if "category" in article:
                all_categories.add(article["category"])
        return sorted(list(all_categories))

    def filter_by_tag(self, tag):
        """Filtra artigos por tag"""
        history = self._load_json(self.history_file)
        return [article for article in history if "tags" in article and tag in article["tags"]]

    def filter_by_category(self, category):
        """Filtra artigos por categoria"""
        history = self._load_json(self.history_file)
        return [article for article in history if "category" in article and article["category"] == category]

    def delete_article(self, article_id):
        """Deleta um artigo do histórico e seus arquivos"""
        try:
            # Carregar histórico
            history = self._load_json(self.history_file)

            # Encontrar o artigo
            article_to_delete = None
            for article in history:
                if article.get("id") == article_id:
                    article_to_delete = article
                    break

            if not article_to_delete:
                logger.error(f"Artigo com ID {article_id} não encontrado.")
                return False

            # Remover o artigo do histórico
            history = [article for article in history if article.get("id") != article_id]
            self._save_json(self.history_file, history)

            # Remover o artigo dos favoritos, se estiver lá
            favorites = self._load_json(self.favorites_file)
            favorites = [fav for fav in favorites if fav != article_id]
            self._save_json(self.favorites_file, favorites)

            # Remover o diretório do artigo
            article_dir = article_to_delete.get("directory")
            if article_dir and os.path.exists(article_dir):
                shutil.rmtree(article_dir)
                logger.info(f"Diretório do artigo removido: {article_dir}")

            logger.info(f"Artigo com ID {article_id} deletado com sucesso.")
            return True
        except Exception as e:
            logger.error(f"Erro ao deletar artigo: {e}")
            return False