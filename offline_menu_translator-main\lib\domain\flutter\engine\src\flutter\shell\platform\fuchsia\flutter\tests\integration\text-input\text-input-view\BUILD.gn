# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/tools/fuchsia/dart/dart_library.gni")
import("//flutter/tools/fuchsia/flutter/flutter_component.gni")
import("//flutter/tools/fuchsia/gn-sdk/src/component.gni")
import("//flutter/tools/fuchsia/gn-sdk/src/gn_configs.gni")
import("//flutter/tools/fuchsia/gn-sdk/src/package.gni")

dart_library("lib") {
  testonly = true
  package_name = "text-input-view"
  sources = [ "text_input_view.dart" ]
  deps = [ "//flutter/shell/platform/fuchsia/dart:args" ]
}

flutter_component("component") {
  testonly = true
  component_name = "text-input-view"
  manifest = rebase_path("meta/text-input-view.cml")
  main_package = "text-input-view"
  main_dart = "text_input_view.dart"
  deps = [ ":lib" ]
}

fuchsia_package("package") {
  testonly = true
  package_name = "text-input-view"
  deps = [ ":component" ]
}
