{"version": "6_1_0", "md.comp.radio-button.disabled.selected.icon.color": "onSurface", "md.comp.radio-button.disabled.selected.icon.opacity": 0.38, "md.comp.radio-button.disabled.unselected.icon.color": "onSurface", "md.comp.radio-button.disabled.unselected.icon.opacity": 0.38, "md.comp.radio-button.icon.size": 20.0, "md.comp.radio-button.selected.focus.icon.color": "primary", "md.comp.radio-button.selected.focus.state-layer.color": "primary", "md.comp.radio-button.selected.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.radio-button.selected.hover.icon.color": "primary", "md.comp.radio-button.selected.hover.state-layer.color": "primary", "md.comp.radio-button.selected.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.radio-button.selected.icon.color": "primary", "md.comp.radio-button.selected.pressed.icon.color": "primary", "md.comp.radio-button.selected.pressed.state-layer.color": "onSurface", "md.comp.radio-button.selected.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.radio-button.state-layer.size": 40.0, "md.comp.radio-button.unselected.focus.icon.color": "onSurface", "md.comp.radio-button.unselected.focus.state-layer.color": "onSurface", "md.comp.radio-button.unselected.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.radio-button.unselected.hover.icon.color": "onSurface", "md.comp.radio-button.unselected.hover.state-layer.color": "onSurface", "md.comp.radio-button.unselected.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.radio-button.unselected.icon.color": "onSurfaceVariant", "md.comp.radio-button.unselected.pressed.icon.color": "onSurface", "md.comp.radio-button.unselected.pressed.state-layer.color": "primary", "md.comp.radio-button.unselected.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity"}