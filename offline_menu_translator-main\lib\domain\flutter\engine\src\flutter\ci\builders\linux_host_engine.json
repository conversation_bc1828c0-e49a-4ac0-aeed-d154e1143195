{"_comment": ["The builds defined in this file should not contain tests, ", "and the file should not contain builds that are essentially tests. ", "The only builds in this file should be the builds necessary to produce ", "release artifacts. ", "Tests to run on linux hosts should go in one of the other linux_ build ", "definition files."], "luci_flags": {"delay_collect_builds": true, "parallel_download_builds": true}, "builds": [{"archives": [{"name": "ci/host_debug", "base_path": "out/ci/host_debug/zip_archives/", "type": "gcs", "include_paths": ["out/ci/host_debug/zip_archives/dart-sdk-linux-x64.zip", "out/ci/host_debug/zip_archives/flutter_patched_sdk.zip", "out/ci/host_debug/zip_archives/linux-x64/artifacts.zip", "out/ci/host_debug/zip_archives/linux-x64/font-subset.zip", "out/ci/host_debug/zip_archives/linux-x64/impeller_sdk.zip", "out/ci/host_debug/zip_archives/linux-x64/linux-x64-embedder.zip"], "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Linux"], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/host_debug", "--runtime-mode", "debug", "--prebuilt-dart-sdk", "--rbe", "--no-goma"], "name": "ci/host_debug", "description": "Produces debug mode Linux host-side tooling.", "ninja": {"config": "ci/host_debug", "targets": ["flutter/build/archives:dart_sdk_archive", "flutter/build/archives:flutter_patched_sdk", "flutter/build/archives:artifacts", "flutter/tools/font_subset", "flutter/impeller/toolkit/interop:sdk", "flutter/build/archives:embedder"]}}, {"archives": [{"name": "ci/host_release", "base_path": "out/ci/host_release/zip_archives/", "type": "gcs", "include_paths": ["out/ci/host_release/zip_archives/flutter_patched_sdk_product.zip"], "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Linux"], "dependencies": [{"dependency": "goldctl", "version": "git_revision:720a542f6fe4f92922c3b8f0fdcc4d2ac6bb83cd"}], "gclient_variables": {"download_android_deps": false, "download_jdk": false, "use_rbe": true}, "gn": ["--target-dir", "ci/host_release", "--runtime-mode", "release", "--prebuilt-dart-sdk", "--rbe", "--no-goma"], "name": "ci/host_release", "description": "Produces flutter_patched_sdk_product.zip shared by all Flutter release builds.", "ninja": {"config": "ci/host_release", "targets": ["flutter/build/archives:flutter_patched_sdk"]}}], "generators": {"tasks": [{"name": "Verify-export-symbols-release-binaries", "parameters": ["src/out/ci", "src/flutter/buildtools"], "script": "flutter/testing/symbols/verify_exported.dart", "language": "dart"}, {"name": "api-documentation", "parameters": ["out/docs"], "script": "flutter/tools/gen_docs.py"}]}, "archives": [{"source": "out/docs/ios-docs.zip", "destination": "ios-docs.zip", "realm": "production"}, {"source": "out/docs/macos-docs.zip", "destination": "macos-docs.zip", "realm": "production"}, {"source": "out/docs/linux-docs.zip", "destination": "linux-docs.zip", "realm": "production"}, {"source": "out/docs/windows-docs.zip", "destination": "windows-docs.zip", "realm": "production"}, {"source": "out/docs/impeller-docs.zip", "destination": "impeller-docs.zip", "realm": "production"}]}