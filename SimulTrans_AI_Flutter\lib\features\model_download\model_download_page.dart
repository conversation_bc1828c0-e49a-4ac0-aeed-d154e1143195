import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../../core/services/local_gemma_translation_service.dart';
import '../../core/services/gemma_downloader_service.dart';

/// Page for downloading and managing the local Gemma-3N model
/// Based on the flutter_gemma package example
class ModelDownloadPage extends StatefulWidget {
  const ModelDownloadPage({super.key});

  @override
  State<ModelDownloadPage> createState() => _ModelDownloadPageState();
}

class _ModelDownloadPageState extends State<ModelDownloadPage> {
  final LocalGemmaTranslationService _translationService = LocalGemmaTranslationService.instance;
  final GemmaDownloaderService _downloader = GemmaDownloaderService.instance;
  
  bool _isModelDownloaded = false;
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  String _statusMessage = 'Checking model status...';
  Map<String, dynamic>? _modelInfo;

  @override
  void initState() {
    super.initState();
    _checkModelStatus();
  }

  Future<void> _checkModelStatus() async {
    try {
      final isDownloaded = await _downloader.checkModelExistence();
      final modelInfo = _downloader.getModelInfo();
      
      setState(() {
        _isModelDownloaded = isDownloaded;
        _modelInfo = modelInfo;
        _statusMessage = isDownloaded 
            ? 'Gemma-3N model is ready for offline translation!'
            : 'Gemma-3N model needs to be downloaded for offline translation';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error checking model status: $e';
      });
    }
  }

  Future<void> _downloadModel() async {
    if (_isDownloading) return;

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
      _statusMessage = 'Starting model download...';
    });

    try {
      final success = await _translationService.downloadModelIfNeeded(
        onProgress: (progress) {
          setState(() {
            _downloadProgress = progress;
            _statusMessage = 'Downloading Gemma-3N model: ${(progress * 100).toStringAsFixed(1)}%';
          });
        },
      );

      if (success) {
        setState(() {
          _isModelDownloaded = true;
          _statusMessage = 'Gemma-3N model downloaded successfully!';
        });
        await _checkModelStatus();
      } else {
        setState(() {
          _statusMessage = 'Failed to download model. Please check your Hugging Face token.';
        });
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Download failed: $e';
      });
    } finally {
      setState(() {
        _isDownloading = false;
      });
    }
  }

  Future<void> _deleteModel() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Model'),
        content: const Text('Are you sure you want to delete the Gemma-3N model? This will require re-downloading for offline translation.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await _downloader.deleteModel();
      if (success) {
        setState(() {
          _isModelDownloaded = false;
          _statusMessage = 'Model deleted successfully';
        });
        await _checkModelStatus();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Local AI Model'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _isModelDownloaded ? Icons.check_circle : Icons.download,
                          color: _isModelDownloaded ? Colors.green : Colors.orange,
                          size: 32,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Gemma-3N Local AI Model',
                                style: Theme.of(context).textTheme.headlineSmall,
                              ),
                              Text(
                                _statusMessage,
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),

            // Model Information
            if (_modelInfo != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Model Information',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      _buildInfoRow('Model Name', _modelInfo!['modelName']),
                      _buildInfoRow('Filename', _modelInfo!['modelFilename']),
                      _buildInfoRow('Downloaded', _modelInfo!['isDownloaded'] ? 'Yes' : 'No'),
                      if (_modelInfo!['isDownloaded']) ...[
                        _buildInfoRow('Path', _modelInfo!['modelPath'] ?? 'Unknown'),
                      ],
                    ],
                  ),
                ),
              ),
              
              const SizedBox(height: 16),

              // Capabilities
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Capabilities',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      ...(_modelInfo!['capabilities'] as List<String>).map(
                        (capability) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Row(
                            children: [
                              const Icon(Icons.check, size: 16, color: Colors.green),
                              const SizedBox(width: 8),
                              Text(capability),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],

            const Spacer(),

            // Download Progress
            if (_isDownloading) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      LinearProgressIndicator(value: _downloadProgress),
                      const SizedBox(height: 8),
                      Text('${(_downloadProgress * 100).toStringAsFixed(1)}%'),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Action Buttons
            SizedBox(
              width: double.infinity,
              child: Column(
                children: [
                  if (!_isModelDownloaded && !_isDownloading)
                    ElevatedButton.icon(
                      onPressed: _downloadModel,
                      icon: const Icon(Icons.download),
                      label: const Text('Download Gemma-3N Model'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.all(16),
                      ),
                    ),
                  
                  if (_isDownloading)
                    ElevatedButton.icon(
                      onPressed: null,
                      icon: const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      label: const Text('Downloading...'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.all(16),
                      ),
                    ),
                  
                  if (_isModelDownloaded) ...[
                    ElevatedButton.icon(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.check),
                      label: const Text('Model Ready - Continue'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.all(16),
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextButton.icon(
                      onPressed: _deleteModel,
                      icon: const Icon(Icons.delete),
                      label: const Text('Delete Model'),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
        ],
      ),
    );
  }
}
