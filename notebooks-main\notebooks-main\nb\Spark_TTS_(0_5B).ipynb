{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Unsloth now supports Text-to-Speech (TTS) models. Read our [guide here](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning).\n", "\n", "Read our **[Qwen3 Guide](https://docs.unsloth.ai/basics/qwen3-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "%%capture\nimport os\nif \"COLAB_\" not in \"\".join(os.environ.keys()):\n    !pip install unsloth\nelse:\n    # Do this only in Colab notebooks! Otherwise use pip install unsloth\n    !pip install --no-deps bitsandbytes accelerate xformers==0.0.29.post3 peft trl triton cut_cross_entropy unsloth_zoo\n    !pip install sentencepiece protobuf \"datasets>=3.4.1\" huggingface_hub hf_transfer\n    !pip install --no-deps unsloth\n!git clone https://github.com/SparkAudio/Spark-TTS\n!pip install omegaconf einx"}, {"cell_type": "markdown", "metadata": {"id": "AkWYsztAs9Ky"}, "source": ["### <PERSON><PERSON><PERSON><PERSON>\n", "\n", "`FastModel` supports loading nearly any model now! This includes Vision and Text models!\n", "\n", "Thank you to [<PERSON><PERSON><PERSON>](https://huggingface.co/E<PERSON>ll) for creating this notebook!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2025-03-22T00:48:54.511089Z", "iopub.status.busy": "2025-03-22T00:48:54.510770Z", "iopub.status.idle": "2025-03-22T00:51:37.363415Z", "shell.execute_reply": "2025-03-22T00:51:37.362696Z", "shell.execute_reply.started": "2025-03-22T00:48:54.511053Z"}, "id": "QmUBVEnvCDJv", "outputId": "72b5e851-edaa-4e2a-aad4-d6ce3a74ecfd", "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==((====))==  Unsloth 2025.3.19: Fast Qwen2 patching. Transformers: 4.51.3.\n", "   \\\\   /|    Tesla T4. Num GPUs = 1. Max memory: 14.741 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.6.0+cu124. CUDA: 7.5. CUDA Toolkit: 12.4. Triton: 3.2.0\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.29.post3. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n", "Unsloth: Float16 full finetuning uses more memory since we upcast weights to float32.\n"]}], "source": ["from unsloth import FastModel\n", "import torch\n", "from huggingface_hub import snapshot_download\n", "\n", "max_seq_length = 2048 # Choose any for long context!\n", "\n", "fourbit_models = [\n", "    # 4bit dynamic quants for superior accuracy and low memory use\n", "    \"unsloth/gemma-3-4b-it-unsloth-bnb-4bit\",\n", "    \"unsloth/gemma-3-12b-it-unsloth-bnb-4bit\",\n", "    \"unsloth/gemma-3-27b-it-unsloth-bnb-4bit\",\n", "    # Qwen3 new models\n", "    \"unsloth/Qwen3-4B-unsloth-bnb-4bit\",\n", "    \"unsloth/Qwen3-8B-unsloth-bnb-4bit\",\n", "    # Other very popular models!\n", "    \"unsloth/Llama-3.1-8B\",\n", "    \"unsloth/Llama-3.2-3B\",\n", "    \"unsloth/Llama-3.3-70B\",\n", "    \"unsloth/mistral-7b-instruct-v0.3\",\n", "    \"unsloth/Phi-4\",\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "# Download model and code\n", "snapshot_download(\"unsloth/Spark-TTS-0.5B\", local_dir = \"Spark-TTS-0.5B\")\n", "\n", "model, tokenizer = FastModel.from_pretrained(\n", "    model_name = f\"Spark-TTS-0.5B/LLM\",\n", "    max_seq_length = max_seq_length,\n", "    dtype = torch.float32, # <PERSON>rk seems to only work on float32 for now\n", "    full_finetuning = True, # We support full finetuning now!\n", "    load_in_4bit = False,\n", "    #token = \"hf_...\", # use one if using gated models like meta-llama/Llama-2-7b-hf\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "SXd9bTZd1aaL"}, "source": ["We now add LoRA adapters so we only need to update 1 to 10% of all parameters!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "execution": {"iopub.execute_input": "2025-03-22T00:51:37.365079Z", "iopub.status.busy": "2025-03-22T00:51:37.364731Z", "iopub.status.idle": "2025-03-22T00:51:44.221612Z", "shell.execute_reply": "2025-03-22T00:51:44.220949Z", "shell.execute_reply.started": "2025-03-22T00:51:37.365045Z"}, "id": "6bZsfBuZDeCL", "outputId": "6a2417b5-8199-4696-96e8-8772afe54d11", "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Unsloth: Making `model.base_model.model.model` require gradients\n"]}], "source": ["#LoRA does not work with float32 only works with bfloat16 !!!\n", "model = FastModel.get_peft_model(\n", "    model,\n", "    r = 128, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 128,\n", "    lora_dropout = 0, # Supports any, but = 0 is optimized\n", "    bias = \"none\",    # Supports any, but = \"none\" is optimized\n", "    # [NEW] \"unsloth\" uses 30% less VRAM, fits 2x larger batch sizes!\n", "    use_gradient_checkpointing = \"unsloth\", # True or \"unsloth\" for very long context\n", "    random_state = 3407,\n", "    use_rslora = False,  # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "vITh0KVJ10qX"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep  \n", "\n", "We will use the `MrDragonFox/Elise`, which is designed for training TTS models. Ensure that your dataset follows the required format: **text, audio** for single-speaker models or **source, text, audio** for multi-speaker models. You can modify this section to accommodate your own dataset, but maintaining the correct structure is essential for optimal training."]}, {"cell_type": "code", "execution_count": null, "metadata": {"execution": {"iopub.execute_input": "2025-03-22T00:51:44.222880Z", "iopub.status.busy": "2025-03-22T00:51:44.222617Z", "iopub.status.idle": "2025-03-22T00:52:16.516878Z", "shell.execute_reply": "2025-03-22T00:52:16.516033Z", "shell.execute_reply.started": "2025-03-22T00:51:44.222848Z"}, "id": "LjY75GoYUCB8", "trusted": true}, "outputs": [], "source": ["from datasets import load_dataset\n", "dataset = load_dataset(\"MrDragonFox/Elise\", split = \"train\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/", "height": 160, "referenced_widgets": ["0e415b591c0847ce9f46abfeafed9b2f", "9e775c9dc5894820988dabb266b73c60", "f26be09cfc434a92a6af2ed117bbc4e8", "575346aa27f944d58ed836b4032b2549", "09b4241e6b4d44a49802c19a78709736", "a61b2d8a90124b398b65480c90863580", "1aef9071b9ab408186c1087080e5e09c", "5e3d7ee9c5594e95b1a2443e9ab7542e", "cac4bb193c824a718907d73896d0b4b7", "834172bcf69140ed8698b5bd67384912", "a6d277168aca43b0bf234692fc193a37"]}, "execution": {"iopub.execute_input": "2025-03-22T00:52:16.518175Z", "iopub.status.busy": "2025-03-22T00:52:16.517841Z", "iopub.status.idle": "2025-03-22T00:52:35.039329Z", "shell.execute_reply": "2025-03-22T00:52:35.038356Z", "shell.execute_reply.started": "2025-03-22T00:52:16.518146Z"}, "id": "zK94B-Pfioto", "outputId": "036ffc1d-16e2-406f-f5a6-ea42ce93249b", "trusted": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.11/dist-packages/torch/nn/utils/weight_norm.py:143: FutureWarning: `torch.nn.utils.weight_norm` is deprecated in favor of `torch.nn.utils.parametrizations.weight_norm`.\n", "  WeightNorm.apply(module, name, dim)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Missing tensor: mel_transformer.spectrogram.window\n", "Missing tensor: mel_transformer.mel_scale.fb\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0e415b591c0847ce9f46abfeafed9b2f", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/1195 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Moving Bicodec model and Wav2Vec2Model to cpu.\n"]}], "source": ["#@title Tokenization Function\n", "\n", "import locale\n", "import torchaudio.transforms as T\n", "import os\n", "import torch\n", "import sys\n", "import numpy as np\n", "sys.path.append('Spark-TTS')\n", "from sparktts.models.audio_tokenizer import BiCodecTokenizer\n", "from sparktts.utils.audio import audio_volume_normalize\n", "\n", "audio_tokenizer = BiCodecTokenizer(\"Spark-TTS-0.5B\", \"cuda\")\n", "def extract_wav2vec2_features( wavs: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"extract wav2vec2 features\"\"\"\n", "\n", "        if wavs.shape[0] != 1:\n", "\n", "             raise ValueError(f\"Expected batch size 1, but got shape {wavs.shape}\")\n", "        wav_np = wavs.squeeze(0).cpu().numpy()\n", "\n", "        processed = audio_tokenizer.processor(\n", "            wav_np,\n", "            sampling_rate=16000,\n", "            return_tensors=\"pt\",\n", "            padding=True,\n", "        )\n", "        input_values = processed.input_values\n", "\n", "        input_values = input_values.to(audio_tokenizer.feature_extractor.device)\n", "\n", "        model_output = audio_tokenizer.feature_extractor(\n", "            input_values,\n", "        )\n", "\n", "\n", "        if model_output.hidden_states is None:\n", "             raise ValueError(\"Wav2Vec2Model did not return hidden states. Ensure config `output_hidden_states=True`.\")\n", "\n", "        num_layers = len(model_output.hidden_states)\n", "        required_layers = [11, 14, 16]\n", "        if any(l >= num_layers for l in required_layers):\n", "             raise IndexError(f\"Requested hidden state indices {required_layers} out of range for model with {num_layers} layers.\")\n", "\n", "        feats_mix = (\n", "            model_output.hidden_states[11] + model_output.hidden_states[14] + model_output.hidden_states[16]\n", "        ) / 3\n", "\n", "        return feats_mix\n", "def formatting_audio_func(example):\n", "    text = f\"{example['source']}: {example['text']}\" if \"source\" in example else example[\"text\"]\n", "    audio_array = example[\"audio\"][\"array\"]\n", "    sampling_rate = example[\"audio\"][\"sampling_rate\"]\n", "\n", "    target_sr = audio_tokenizer.config['sample_rate']\n", "\n", "    if sampling_rate != target_sr:\n", "        resampler = T.Resample(orig_freq=sampling_rate, new_freq=target_sr)\n", "        audio_tensor_temp = torch.from_numpy(audio_array).float()\n", "        audio_array = resampler(audio_tensor_temp).numpy()\n", "\n", "    if audio_tokenizer.config[\"volume_normalize\"]:\n", "        audio_array = audio_volume_normalize(audio_array)\n", "\n", "    ref_wav_np = audio_tokenizer.get_ref_clip(audio_array)\n", "\n", "    audio_tensor = torch.from_numpy(audio_array).unsqueeze(0).float().to(audio_tokenizer.device)\n", "    ref_wav_tensor = torch.from_numpy(ref_wav_np).unsqueeze(0).float().to(audio_tokenizer.device)\n", "\n", "\n", "    feat = extract_wav2vec2_features(audio_tensor)\n", "\n", "    batch = {\n", "\n", "        \"wav\": audio_tensor,\n", "        \"ref_wav\": ref_wav_tensor,\n", "        \"feat\": feat.to(audio_tokenizer.device),\n", "    }\n", "\n", "\n", "    semantic_token_ids, global_token_ids = audio_tokenizer.model.tokenize(batch)\n", "\n", "    global_tokens = \"\".join(\n", "        [f\"<|bicodec_global_{i}|>\" for i in global_token_ids.squeeze().cpu().numpy()] # Squeeze batch dim\n", "    )\n", "    semantic_tokens = \"\".join(\n", "        [f\"<|bicodec_semantic_{i}|>\" for i in semantic_token_ids.squeeze().cpu().numpy()] # Squeeze batch dim\n", "    )\n", "\n", "    inputs = [\n", "        \"<|task_tts|>\",\n", "        \"<|start_content|>\",\n", "        text,\n", "        \"<|end_content|>\",\n", "        \"<|start_global_token|>\",\n", "        global_tokens,\n", "        \"<|end_global_token|>\",\n", "        \"<|start_semantic_token|>\",\n", "        semantic_tokens,\n", "        \"<|end_semantic_token|>\",\n", "        \"<|im_end|>\"\n", "    ]\n", "    inputs = \"\".join(inputs)\n", "    return {\"text\": inputs}\n", "\n", "\n", "dataset = dataset.map(formatting_audio_func, remove_columns=[\"audio\"])\n", "print(\"Moving Bicodec model and Wav2Vec2Model to cpu.\")\n", "audio_tokenizer.model.cpu()\n", "audio_tokenizer.feature_extractor.cpu()\n", "torch.cuda.empty_cache()"]}, {"cell_type": "markdown", "metadata": {"id": "idAEIeSQ3xdS"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `SFTTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 60 steps to speed things up, but you can set `num_train_epochs=1` for a full run, and turn off `max_steps=None`. We also support TRL's `DPOTrainer`!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 49, "referenced_widgets": ["eb056c71d466466eaac36c4759f503f1", "32fa9340bcb4445f8599b17ceac2bdeb", "3bd7943ac4f14168ae39b65d226f150e", "769b14233c8e46dfa78caeaafd6e70ba", "a24bec4896be4065b5a0e6172c1b0d8d", "77595f6a6d1a4f1f8373dfb2edc120e4", "395c431c29b64e35bc74b35f12e37b79", "f5ba36e8df4a48d2ae5db0c766f92638", "18b768aeb5c44318a65782c396b32cfe", "b5f52f5cebdf439ab85b58b811050dd2", "2ca9d23efc04455ba0fc1903b6a4d980"]}, "execution": {"iopub.execute_input": "2025-03-22T00:34:09.688959Z", "iopub.status.busy": "2025-03-22T00:34:09.688649Z", "iopub.status.idle": "2025-03-22T00:34:09.729661Z", "shell.execute_reply": "2025-03-22T00:34:09.729001Z", "shell.execute_reply.started": "2025-03-22T00:34:09.688939Z"}, "id": "95_Nn-89DhsL", "outputId": "3710ae35-f834-463c-e988-015ab40e193b", "trusted": true}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "eb056c71d466466eaac36c4759f503f1", "version_major": 2, "version_minor": 0}, "text/plain": ["Unsloth: Tokenizing [\"text\"] (num_proc=2):   0%|          | 0/1195 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from trl import SFTConfig, SFTTrainer\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = dataset,\n", "    dataset_text_field = \"text\",\n", "    max_seq_length = max_seq_length,\n", "    dataset_num_proc = 2,\n", "    packing = False, # Can make training 5x faster for short sequences.\n", "    args = SFTConfig(\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4,\n", "        warmup_steps = 5,\n", "        # num_train_epochs = 1, # Set this for 1 full training run.\n", "        max_steps = 60,\n", "        learning_rate = 2e-4,\n", "        fp16 = False, # We're doing full float32 s disable mixed precision\n", "        bf16 = False, # We're doing full float32 s disable mixed precision\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.01,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "2ejIt2xSNKKp", "outputId": "29905f3f-2821-4a42-ea31-9c9b0a20a2a9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU = Tesla T4. Max memory = 14.741 GB.\n", "5.713 GB of memory reserved.\n"]}], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "execution": {"iopub.execute_input": "2025-03-22T00:34:12.049152Z", "iopub.status.busy": "2025-03-22T00:34:12.048862Z", "iopub.status.idle": "2025-03-22T00:34:14.404349Z", "shell.execute_reply": "2025-03-22T00:34:14.403239Z", "shell.execute_reply.started": "2025-03-22T00:34:12.049130Z"}, "id": "yqxqAZ7KJ4oL", "outputId": "82cbfc2a-3a90-4081-f446-315778142e5d", "trusted": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth - 2x faster free finetuning | Num GPUs used = 1\n", "   \\\\   /|    Num examples = 1,195 | Num Epochs = 1 | Total steps = 149\n", "O^O/ \\_/ \\    Batch size per device = 2 | Gradient accumulation steps = 4\n", "\\        /    Data Parallel GPUs = 1 | Total batch size (2 x 4 x 1) = 8\n", " \"-____-\"     Trainable parameters = 506,634,112/506,634,112 (100.00% trained)\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='149' max='149' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [149/149 21:17, Epoch 0/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>5.868800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>5.792300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>5.939600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>5.846100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>5.780900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>5.738200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>5.934800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>6.004500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>6.105400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>5.610200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>5.690500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>5.512300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>5.617500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>5.568700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>5.604100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>5.921400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>5.729400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>4.932300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>5.715700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>5.584700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>5.711500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>5.512400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>5.831400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>5.493800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>5.716400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>5.720300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>5.387800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>5.400100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>5.321100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>5.098000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>5.654800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>5.495400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>5.780300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>5.558500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>5.260000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>5.525800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>5.348300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>5.468600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>5.273500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>5.562800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>5.414300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>4.744300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>5.345500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>5.320100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>5.338900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>5.279000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>5.586400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>5.141600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>5.598900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>5.040700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>5.456500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>5.639700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>5.239900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>5.038500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>5.562700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>5.528500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>5.356100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>5.383900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>5.442800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>5.115200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>61</td>\n", "      <td>5.439700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>62</td>\n", "      <td>5.583700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>63</td>\n", "      <td>5.415000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>64</td>\n", "      <td>5.420900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>65</td>\n", "      <td>5.142300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>66</td>\n", "      <td>5.319900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>67</td>\n", "      <td>5.639600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>68</td>\n", "      <td>5.340700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>69</td>\n", "      <td>5.478500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>70</td>\n", "      <td>5.365500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>71</td>\n", "      <td>5.242400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>72</td>\n", "      <td>5.128900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>73</td>\n", "      <td>4.848800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>74</td>\n", "      <td>5.248300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>75</td>\n", "      <td>5.146500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>76</td>\n", "      <td>5.371800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>77</td>\n", "      <td>5.365300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>78</td>\n", "      <td>5.356300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>79</td>\n", "      <td>5.252000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>80</td>\n", "      <td>5.390100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>81</td>\n", "      <td>5.328500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>82</td>\n", "      <td>5.336200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>83</td>\n", "      <td>5.224200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>84</td>\n", "      <td>4.503600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>85</td>\n", "      <td>5.624900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>86</td>\n", "      <td>5.553200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>87</td>\n", "      <td>5.436100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>88</td>\n", "      <td>5.374400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>89</td>\n", "      <td>4.612000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>90</td>\n", "      <td>5.257400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>91</td>\n", "      <td>5.149700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>92</td>\n", "      <td>4.779200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>93</td>\n", "      <td>5.006900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>94</td>\n", "      <td>5.139600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>95</td>\n", "      <td>5.173300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>96</td>\n", "      <td>5.386400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>97</td>\n", "      <td>5.535300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>98</td>\n", "      <td>5.394300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>99</td>\n", "      <td>4.882800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>100</td>\n", "      <td>5.336000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>101</td>\n", "      <td>5.218300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>102</td>\n", "      <td>5.223400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>103</td>\n", "      <td>5.059000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>104</td>\n", "      <td>5.133800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>105</td>\n", "      <td>5.040500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>106</td>\n", "      <td>4.963700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>107</td>\n", "      <td>5.412100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>108</td>\n", "      <td>5.533600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>109</td>\n", "      <td>5.098500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>110</td>\n", "      <td>5.434100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>111</td>\n", "      <td>5.303500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>112</td>\n", "      <td>5.072300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>113</td>\n", "      <td>5.071400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>114</td>\n", "      <td>5.415200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>115</td>\n", "      <td>5.452400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>116</td>\n", "      <td>5.375400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>117</td>\n", "      <td>5.083500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>118</td>\n", "      <td>4.943900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>119</td>\n", "      <td>4.962200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>120</td>\n", "      <td>5.180200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>121</td>\n", "      <td>5.464400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>122</td>\n", "      <td>5.258200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>123</td>\n", "      <td>5.415000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>124</td>\n", "      <td>5.046600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>125</td>\n", "      <td>5.102400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>126</td>\n", "      <td>5.456000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>127</td>\n", "      <td>4.989200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>128</td>\n", "      <td>5.324000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>129</td>\n", "      <td>4.852200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>130</td>\n", "      <td>5.406600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>131</td>\n", "      <td>5.225800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>132</td>\n", "      <td>5.343500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>133</td>\n", "      <td>5.255500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>134</td>\n", "      <td>5.095600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>135</td>\n", "      <td>5.212300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>136</td>\n", "      <td>5.137300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>137</td>\n", "      <td>5.073700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>138</td>\n", "      <td>5.009600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>139</td>\n", "      <td>5.116400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>140</td>\n", "      <td>5.271000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>141</td>\n", "      <td>5.238400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>142</td>\n", "      <td>5.375600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>143</td>\n", "      <td>5.315500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>144</td>\n", "      <td>4.150000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>145</td>\n", "      <td>5.190200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>146</td>\n", "      <td>4.896300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>147</td>\n", "      <td>4.962800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>148</td>\n", "      <td>5.134400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>149</td>\n", "      <td>4.816800</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "pCqnaKmlO1U9"}, "outputs": [], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {"id": "ekOmTR1hSNcr"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model! You can change the prompts\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "apUdB40Ep6Ki"}, "outputs": [], "source": ["input_text = \"Hey there my name is <PERSON>, <giggles> and I'm a speech generation model that can sound like a person.\"\n", "\n", "chosen_voice = None # None for single-speaker"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/", "height": 220}, "execution": {"iopub.execute_input": "2025-03-22T00:52:35.040842Z", "iopub.status.busy": "2025-03-22T00:52:35.040125Z", "iopub.status.idle": "2025-03-22T00:52:35.050560Z", "shell.execute_reply": "2025-03-22T00:52:35.049663Z", "shell.execute_reply.started": "2025-03-22T00:52:35.040818Z"}, "id": "krYI8PrRJ6MX", "outputId": "6f13a1ad-65cf-450b-d225-afed44880694", "trusted": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating speech for: 'Hey there my name is <PERSON>, <giggles> and I'm a speech generation model that can sound like a person.'\n", "Generating token sequence...\n", "Token sequence generated.\n", "Found 348 semantic tokens.\n", "Found 32 global tokens.\n", "Detokenizing audio tokens...\n", "Detokenization complete.\n", "Audio saved to generated_speech_controllable.wav\n"]}, {"data": {"text/html": ["\n", "                <audio  controls=\"controls\" >\n", "                    <source src=\"data:audio/wav;base64,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\" type=\"audio/wav\" />\n", "                    Your browser does not support the audio element.\n", "                </audio>\n", "              "], "text/plain": ["<IPython.lib.display.Audio object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["#@title Run Inference\n", "\n", "import torch\n", "import re\n", "import numpy as np\n", "from typing import Dict, Any\n", "import torchaudio.transforms as T\n", "\n", "FastModel.for_inference(model) # Enable native 2x faster inference\n", "\n", "@torch.inference_mode()\n", "def generate_speech_from_text(\n", "    text: str,\n", "    temperature: float = 0.8,   # Generation temperature\n", "    top_k: int = 50,            # Generation top_k\n", "    top_p: float = 1,        # Generation top_p\n", "    max_new_audio_tokens: int = 2048, # Max tokens for audio part\n", "    device: torch.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", ") -> np.ndarray:\n", "    \"\"\"\n", "    Generates speech audio from text using default voice control parameters.\n", "\n", "    Args:\n", "        text (str): The text input to be converted to speech.\n", "        temperature (float): Sampling temperature for generation.\n", "        top_k (int): Top-k sampling parameter.\n", "        top_p (float): Top-p (nucleus) sampling parameter.\n", "        max_new_audio_tokens (int): Max number of new tokens to generate (limits audio length).\n", "        device (torch.device): Device to run inference on.\n", "\n", "    Returns:\n", "        np.ndarray: Generated waveform as a NumPy array.\n", "    \"\"\"\n", "\n", "    prompt = \"\".join([\n", "        \"<|task_tts|>\",\n", "        \"<|start_content|>\",\n", "        text,\n", "        \"<|end_content|>\",\n", "        \"<|start_global_token|>\"\n", "    ])\n", "\n", "    model_inputs = tokenizer([prompt], return_tensors=\"pt\").to(device)\n", "\n", "    print(\"Generating token sequence...\")\n", "    generated_ids = model.generate(\n", "        **model_inputs,\n", "        max_new_tokens=max_new_audio_tokens, # Limit generation length\n", "        do_sample=True,\n", "        temperature=temperature,\n", "        top_k=top_k,\n", "        top_p=top_p,\n", "        eos_token_id=tokenizer.eos_token_id, # Stop token\n", "        pad_token_id=tokenizer.pad_token_id # Use models pad token id\n", "    )\n", "    print(\"Token sequence generated.\")\n", "\n", "\n", "    generated_ids_trimmed = generated_ids[:, model_inputs.input_ids.shape[1]:]\n", "\n", "\n", "    predicts_text = tokenizer.batch_decode(generated_ids_trimmed, skip_special_tokens=False)[0]\n", "    # print(f\"\\nGenerated Text (for parsing):\\n{predicts_text}\\n\") # Debugging\n", "\n", "    # Extract semantic token IDs using regex\n", "    semantic_matches = re.findall(r\"<\\|bicodec_semantic_(\\d+)\\|>\", predicts_text)\n", "    if not semantic_matches:\n", "        print(\"Warning: No semantic tokens found in the generated output.\")\n", "        # Handle appropriately - perhaps return silence or raise error\n", "        return np.array([], dtype=np.float32)\n", "\n", "    pred_semantic_ids = torch.tensor([int(token) for token in semantic_matches]).long().unsqueeze(0) # Add batch dim\n", "\n", "    # Extract global token IDs using regex (assuming controllable mode also generates these)\n", "    global_matches = re.findall(r\"<\\|bicodec_global_(\\d+)\\|>\", predicts_text)\n", "    if not global_matches:\n", "         print(\"Warning: No global tokens found in the generated output (controllable mode). Might use defaults or fail.\")\n", "         pred_global_ids = torch.zeros((1, 1), dtype=torch.long)\n", "    else:\n", "         pred_global_ids = torch.tensor([int(token) for token in global_matches]).long().unsqueeze(0) # Add batch dim\n", "\n", "    pred_global_ids = pred_global_ids.unsqueeze(0) # Shape becomes (1, 1, N_global)\n", "\n", "    print(f\"Found {pred_semantic_ids.shape[1]} semantic tokens.\")\n", "    print(f\"Found {pred_global_ids.shape[2]} global tokens.\")\n", "\n", "\n", "    # 5. Detokenize using BiCodecTokenizer\n", "    print(\"Detokenizing audio tokens...\")\n", "    # Ensure audio_tokenizer and its internal model are on the correct device\n", "    audio_tokenizer.device = device\n", "    audio_tokenizer.model.to(device)\n", "    # Squeeze the extra dimension from global tokens as seen in SparkTTS example\n", "    wav_np = audio_tokenizer.detokenize(\n", "        pred_global_ids.to(device).squeeze(0), # Shape (1, N_global)\n", "        pred_semantic_ids.to(device)           # Shape (1, N_semantic)\n", "    )\n", "    print(\"Detokenization complete.\")\n", "\n", "    return wav_np\n", "\n", "if __name__ == \"__main__\":\n", "    print(f\"Generating speech for: '{input_text}'\")\n", "    text = f\"{chosen_voice}: \" + input_text if chosen_voice else input_text\n", "    generated_waveform = generate_speech_from_text(input_text)\n", "\n", "    if generated_waveform.size > 0:\n", "        import soundfile as sf\n", "        output_filename = \"generated_speech_controllable.wav\"\n", "        sample_rate = audio_tokenizer.config.get(\"sample_rate\", 16000)\n", "        sf.write(output_filename, generated_waveform, sample_rate)\n", "        print(f\"Audio saved to {output_filename}\")\n", "\n", "        # Optional: Play in notebook\n", "        from IPython.display import Audio, display\n", "        display(Audio(generated_waveform, rate=sample_rate))\n", "    else:\n", "        print(\"Audio generation failed (no tokens found?).\")"]}, {"cell_type": "markdown", "metadata": {"id": "uMuVrWbjAzhc"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "upcOlWe7A1vc", "outputId": "c2a40f71-fbb8-461a-8b82-b138c09e7a60"}, "outputs": [{"data": {"text/plain": ["('lora_model/tokenizer_config.json',\n", " 'lora_model/special_tokens_map.json',\n", " 'lora_model/vocab.json',\n", " 'lora_model/merges.txt',\n", " 'lora_model/added_tokens.json',\n", " 'lora_model/tokenizer.json')"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "f422JgM9sdVT"}, "source": ["### Saving to float16\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16 or `merged_4bit` for int4. We also allow `lora` adapters as a fallback. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "iHjt_SMYsd3P", "outputId": "bd8cccb7-6b95-45bf-80da-de120988447e"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth: You have 1 CPUs. Using `safe_serialization` is 10x slower.\n", "We shall switch to Pytorch saving, which might take 3 minutes and not 30 minutes.\n", "To force `safe_serialization`, set it to `None` instead.\n", "Unsloth: <PERSON><PERSON>/Colab has limited disk space. We need to delete the downloaded\n", "model which will save 4-16GB of disk space, allowing you to save on Kaggle/Colab.\n", "Unsloth: Will remove a cached repo with size 15.1G\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Merging 4bit and LoRA weights to 16bit...\n", "Unsloth: Will use up to 3.99 out of 12.67 RAM for saving.\n", "Unsloth: Saving model... This might take 5 minutes ...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 28/28 [00:01<00:00, 27.83it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Unsloth: Saving tokenizer... Done.\n", "Unsloth: Saving model/pytorch_model-00001-of-00002.bin...\n", "Unsloth: Saving model/pytorch_model-00002-of-00002.bin...\n", "Done.\n"]}], "source": ["# Merge to 16bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_16bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_16bit\", token = \"\")\n", "\n", "# Merge to 4bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_4bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_4bit\", token = \"\")\n", "\n", "# Just LoRA adapters\n", "if False:\n", "    model.save_pretrained(\"model\")\n", "    tokenizer.save_pretrained(\"model\")\n", "if False:\n", "    model.push_to_hub(\"hf/model\", token = \"\")\n", "    tokenizer.push_to_hub(\"hf/model\", token = \"\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "include_colab_link": true, "provenance": []}, "kaggle": {"accelerator": "nvidiaTeslaT4", "dataSources": [], "dockerImageVersionId": 30919, "isGpuEnabled": true, "isInternetEnabled": true, "language": "python", "sourceType": "notebook"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"09b4241e6b4d44a49802c19a78709736": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0e415b591c0847ce9f46abfeafed9b2f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_9e775c9dc5894820988dabb266b73c60", "IPY_MODEL_f26be09cfc434a92a6af2ed117bbc4e8", "IPY_MODEL_575346aa27f944d58ed836b4032b2549"], "layout": "IPY_MODEL_09b4241e6b4d44a49802c19a78709736"}}, "18b768aeb5c44318a65782c396b32cfe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "1aef9071b9ab408186c1087080e5e09c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2ca9d23efc04455ba0fc1903b6a4d980": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "32fa9340bcb4445f8599b17ceac2bdeb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_77595f6a6d1a4f1f8373dfb2edc120e4", "placeholder": "​", "style": "IPY_MODEL_395c431c29b64e35bc74b35f12e37b79", "value": "Unsloth: Tokenizing [&quot;text&quot;] (num_proc=2): 100%"}}, "395c431c29b64e35bc74b35f12e37b79": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3bd7943ac4f14168ae39b65d226f150e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f5ba36e8df4a48d2ae5db0c766f92638", "max": 1195, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_18b768aeb5c44318a65782c396b32cfe", "value": 1195}}, "575346aa27f944d58ed836b4032b2549": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_834172bcf69140ed8698b5bd67384912", "placeholder": "​", "style": "IPY_MODEL_a6d277168aca43b0bf234692fc193a37", "value": " 1195/1195 [02:33&lt;00:00,  8.03 examples/s]"}}, "5e3d7ee9c5594e95b1a2443e9ab7542e": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "769b14233c8e46dfa78caeaafd6e70ba": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b5f52f5cebdf439ab85b58b811050dd2", "placeholder": "​", "style": "IPY_MODEL_2ca9d23efc04455ba0fc1903b6a4d980", "value": " 1195/1195 [00:07&lt;00:00, 203.72 examples/s]"}}, "77595f6a6d1a4f1f8373dfb2edc120e4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "834172bcf69140ed8698b5bd67384912": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9e775c9dc5894820988dabb266b73c60": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a61b2d8a90124b398b65480c90863580", "placeholder": "​", "style": "IPY_MODEL_1aef9071b9ab408186c1087080e5e09c", "value": "Map: 100%"}}, "a24bec4896be4065b5a0e6172c1b0d8d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a61b2d8a90124b398b65480c90863580": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a6d277168aca43b0bf234692fc193a37": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b5f52f5cebdf439ab85b58b811050dd2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cac4bb193c824a718907d73896d0b4b7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "eb056c71d466466eaac36c4759f503f1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_32fa9340bcb4445f8599b17ceac2bdeb", "IPY_MODEL_3bd7943ac4f14168ae39b65d226f150e", "IPY_MODEL_769b14233c8e46dfa78caeaafd6e70ba"], "layout": "IPY_MODEL_a24bec4896be4065b5a0e6172c1b0d8d"}}, "f26be09cfc434a92a6af2ed117bbc4e8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5e3d7ee9c5594e95b1a2443e9ab7542e", "max": 1195, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_cac4bb193c824a718907d73896d0b4b7", "value": 1195}}, "f5ba36e8df4a48d2ae5db0c766f92638": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}