{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To run this, press \"*Runtime*\" and press \"*Run all*\" on a **free** Tesla T4 Google Colab instance!\n", "<div class=\"align-center\">\n", "<a href=\"https://unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "<a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord button.png\" width=\"145\"></a>\n", "<a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a></a> Join Disco<PERSON> if you need help + ⭐ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐\n", "</div>\n", "\n", "To install Unsloth on your own computer, follow the installation instructions on our Github page [here](https://docs.unsloth.ai/get-started/installing-+-updating).\n", "\n", "You will learn how to do [data prep](#Data), how to [train](#Train), how to [run the model](#Inference), & [how to save it](#Save)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### News"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Unsloth now supports Text-to-Speech (TTS) models. Read our [guide here](https://docs.unsloth.ai/basics/text-to-speech-tts-fine-tuning).\n", "\n", "Read our **[Qwen3 Guide](https://docs.unsloth.ai/basics/qwen3-how-to-run-and-fine-tune)** and check out our new **[Dynamic 2.0](https://docs.unsloth.ai/basics/unsloth-dynamic-2.0-ggufs)** quants which outperforms other quantization methods!\n", "\n", "Visit our docs for all our [model uploads](https://docs.unsloth.ai/get-started/all-our-models) and [notebooks](https://docs.unsloth.ai/get-started/unsloth-notebooks).\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "%%capture\nimport os\nif \"COLAB_\" not in \"\".join(os.environ.keys()):\n    !pip install unsloth\nelse:\n    # Do this only in Colab notebooks! Otherwise use pip install unsloth\n    !pip install --no-deps bitsandbytes accelerate xformers==0.0.29.post3 peft trl triton cut_cross_entropy unsloth_zoo\n    !pip install sentencepiece protobuf \"datasets>=3.4.1\" huggingface_hub hf_transfer\n    !pip install --no-deps unsloth"}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 474, "referenced_widgets": ["188ee79c6ef44ae896a93c0184780f41", "155663a36c2d4949a6bba5ee2b4224b1", "5e48035e021b40aea0317f856b842379", "d9a92404c9f74425ac0cff862001b168", "56d1a29a8e724aa98b0e9b2d8c995713", "94a7e3827ab241dbb75d3d816354c694", "c21c74c7956d425c9049fb1cdc8b7d07", "49de6fa3e53e4259b87caabcb1cc50b2", "b53a34cdb16a4b8eb482b91f407b688a", "982fd1d338e148b2ba66050e03897dbb", "2be4a7d986c44fd4a222dc2f1d7b9918", "c8fd063c9d9c4270b3080677fdd92632", "cd700122445641279b905116cc3b73a1", "e2901aa31b3e4ba39ba138468af5c95b", "20fcb167b6c04792b627f7d13da5db94", "2c35f51a2e9e439f9757d27a4e8cfb5a", "4b7057f7b1aa4e9ea73e2aff19d5d7f5", "66917e3680214990a27525ee10ef0259", "6cdb22652f1f47648c8dfe632e7e87b5", "02b75597dc7c4d1cbf3c51b1fddd3ec6", "008b398b553c47e1a3c9bedac5644642", "78ef61071c244b86b20227e775267aa0", "e517fa5b72d2455eb39101c368165b38", "d532a4ba00d7488ead1366f690a28c41", "ff667ca8001e44b0a6146daf3de94e55", "49ac5705579c4035b864db8d06a3efd6", "800461f0e86545e6a99fbb868e028a27", "70df82f25eeb4f9f83778c3c4cab7ddd", "9986a56c71b4451ea852e49e34ebd2fd", "ed980dda0e504e29ae8ca06e1535073b", "680c3c2c0ea24be0b7a29ad770f28abd", "112fd48addfb42cba27b1939375d5e4c", "08ddb073da9b4a4981e5452df0e80c5a", "5579f451f9bc490fa924455dcf6351f0", "92d80dfad00e4b0abfa3054752385c21", "95ed136481974ccd84eb0488ed5c328d", "d52459dc6f224b169a49d1cbd2d92f50", "e73ae12084e848a8aa82e73c5efa9d85", "6324e9afb3b64beaa84a80622e4aa4af", "fe63f948843240c184084e159a8feef3", "78dcd43379e64fb994fc5d8ad392cd32", "6d258790472640f493b26790298aa366", "9eb629533a45449ea819ed32c2a35a41", "5adbfe3e9c424429851258c009d72f26", "5e683f7a34e74032a2543ce2db6d9ceb", "5d20dbac1cd74e378902f7f6ef71e1f5", "323cca9bfd6a4342b614043b95b8f722", "b792c64d4aed4f90938c58344961d32c", "56109c78c5454216af1f1e1910bee352", "a8a15bf2150246999bf268f0731f9e43", "6ffc5ef1a24249bfbaea21fbc8990298", "254643fe17ec4613bc26de5b3330c7f1", "9725fce622b845b192fbb319f01f56d8", "ee2af32e73d542f9b089d761eed9d801", "138e959064c04ff6a2b6d3a4eb0f6359", "083e0fb519b843e48d283a638e8a6dcb", "56bc8dce7dfb44789f0edb09df8dd450", "64d035fbc33f417d970f39c5aa14c93f", "437886691cdf40d4861575b7b91feb89", "8809b53bd5f74f228c0ab6df9a918796", "c2628e20f1154acd94bbcb282fd6554d", "8680fbf7a67940e7ac6f6d59873ef4e9", "12634ac7d05e490493d5c068087ff978", "0d950e93a67e4402949fce40ba6097b6", "103ed45882c3450a8bd9e55940d88c50", "6abff53f5ed74b8ab7bfe981547d44b4", "acedd5f384554c108b806e7ceef84382", "78f3ba544cdb4e36b76f3a0fe4d79075", "876f9871caac4fba8c6a0f86f98e2314", "4b2bf0e58403475a8b201e87cf3efb4b", "68a8b3c46c15441d9a67d1b578c64f1d", "a06afe98f7fa4d6a99a2defe9c7828cc", "063e74555fbb4ab381cdcbcabc37461e", "1cffaffc53624ffab7ce7a5830fcad07", "47255152beba4690b21cce344d30a548", "4e7766fa9a1048bcacaf4cea16f24d90", "ffa5224eb65142d3a3cd053fd43c14b7"]}, "id": "QmUBVEnvCDJv", "outputId": "532355ad-a380-4841-d692-c69e9dd97c90"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/unsloth/__init__.py:67: UserWarning: CUDA is not linked properly.\n", "We shall run `ldconfig /usr/lib64-nvidia` to try to fix it.\n", "  warnings.warn(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "188ee79c6ef44ae896a93c0184780f41", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/1.09k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["==((====))==  Unsloth: Fast Llama patching release 2024.1\n", "   \\\\   /|    GPU: Tesla T4. Max memory: 14.748 GB\n", "O^O/ \\_/ \\    CUDA capability = 7.5. Xformers = 0.0.22.post7. FA = False.\n", "\\        /    Pytorch version: 2.1.0+cu121. CUDA Toolkit = 12.1\n", " \"-____-\"     bfloat16 = FALSE. Platform = Linux\n", "\n", "Unsloth: unsloth/tinyllama-bnb-4bit can only handle sequence lengths of at most 2048.\n", "But with kaiokendev's RoPE scaling of 2.0, it can be magically be extended to 4096!\n", "You passed `quantization_config` to `from_pretrained` but the model you're loading already has a `quantization_config` attribute. The `quantization_config` attribute will be overwritten with the one you passed to `from_pretrained`.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c8fd063c9d9c4270b3080677fdd92632", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/762M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e517fa5b72d2455eb39101c368165b38", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/129 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5579f451f9bc490fa924455dcf6351f0", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/894 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5e683f7a34e74032a2543ce2db6d9ceb", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.model:   0%|          | 0.00/500k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "083e0fb519b843e48d283a638e8a6dcb", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/1.84M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "acedd5f384554c108b806e7ceef84382", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/438 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "max_seq_length = 4096 # Choose any! We auto support RoPE Scaling internally!\n", "dtype = None # None for auto detection. Float16 for Tesla T4, V100, Bfloat16 for Ampere+\n", "load_in_4bit = True # Use 4bit quantization to reduce memory usage. Can be False.\n", "\n", "# 4bit pre quantized models we support for 4x faster downloading + no OOMs.\n", "fourbit_models = [\n", "    \"unsloth/mistral-7b-bnb-4bit\",\n", "    \"unsloth/mistral-7b-instruct-v0.2-bnb-4bit\",\n", "    \"unsloth/llama-2-7b-bnb-4bit\",\n", "    \"unsloth/llama-2-13b-bnb-4bit\",\n", "    \"unsloth/codellama-34b-bnb-4bit\",\n", "    \"unsloth/tinyllama-bnb-4bit\",\n", "    \"unsloth/gemma-7b-bnb-4bit\", # New Google 6 trillion tokens model 2.5x faster!\n", "    \"unsloth/gemma-2b-bnb-4bit\",\n", "] # More models at https://huggingface.co/unsloth\n", "\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name = \"unsloth/tinyllama-bnb-4bit\", # \"unsloth/tinyllama\" for 16bit loading\n", "    max_seq_length = max_seq_length,\n", "    dtype = dtype,\n", "    load_in_4bit = load_in_4bit,\n", "    # token = \"hf_...\", # use one if using gated models like meta-llama/Llama-2-7b-hf\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "S3xvsMEWyJbZ"}, "source": ["**[NOTE]** TinyLlama's internal maximum sequence length is 2048. We use RoPE Scaling to extend it to 4096 with <PERSON><PERSON>loth!"]}, {"cell_type": "markdown", "metadata": {"id": "SXd9bTZd1aaL"}, "source": ["We now add LoRA adapters so we only need to update 1 to 10% of all parameters!\n", "\n", "**[NOTE]** We set `gradient_checkpointing=False` ONLY for TinyLlama since Unsloth saves tonnes of memory usage. This does NOT work for `llama-2-7b` or `mistral-7b` since the memory usage will still exceed Tesla T4's 15GB. GC recomputes the forward pass during the backward pass, saving loads of memory.\n", "\n", "`**[IF YOU GET OUT OF MEMORY]**` set `gradient_checkpointing` to `True`."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6bZsfBuZDeCL", "outputId": "3de492b6-0bb6-4fd1-a0e5-f5e0ef36dcd5"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unsloth 2024.1 patched 22 layers with 22 QKV layers, 22 O layers and 22 MLP layers.\n"]}], "source": ["model = FastLanguageModel.get_peft_model(\n", "    model,\n", "    r = 32, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128\n", "    target_modules = [\"q_proj\", \"k_proj\", \"v_proj\", \"o_proj\",\n", "                      \"gate_proj\", \"up_proj\", \"down_proj\",],\n", "    lora_alpha = 32,\n", "    lora_dropout = 0, # Currently only supports dropout = 0\n", "    bias = \"none\",    # Currently only supports bias = \"none\"\n", "    use_gradient_checkpointing = False, # @@@ IF YOU GET OUT OF MEMORY - set to True @@@\n", "    random_state = 3407,\n", "    use_rslora = False,  # We support rank stabilized LoRA\n", "    loftq_config = None, # And LoftQ\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "vITh0KVJ10qX"}, "source": ["<a name=\"Data\"></a>\n", "### Data Prep\n", "We now use the Alpaca dataset from [yahma](https://huggingface.co/datasets/yahma/alpaca-cleaned), which is a filtered version of 52K of the original [Alpaca dataset](https://crfm.stanford.edu/2023/03/13/alpaca.html). You can replace this code section with your own data prep.\n", "\n", "**[NOTE]** To train only on completions (ignoring the user's input) read TRL's docs [here](https://huggingface.co/docs/trl/sft_trainer#train-on-completions-only).\n", "\n", "**[NOTE]** Remember to add the **EOS_TOKEN** to the tokenized output!! Otherwise you'll get infinite generations!\n", "\n", "If you want to use the `llama-3` template for ShareGPT datasets, try our conversational [notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Mistral_v0.3_(7B)-Conversational.ipynb)\n", "\n", "For text completions like novel writing, try this [notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Mistral_(7B)-Text_Completion.ipynb)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 145, "referenced_widgets": ["a681577f4a00440496f2ee70e2f1198b", "c64f215f76c345c2aa1b98d23364ece6", "d32436f7cb7c49a88c2fa867add54367", "17e0b9c3b30e4d9ab34f684b0f0c8865", "93e320b8f6f242d3805ef65c81f33718", "d930f897415947ada08ce063f9cb8bed", "54e400aa31c0492fbbfd0d291eb9dd7f", "594b86fdbc6c4ac582aee52d0099a590", "d6b6c9420c644d42b314ac7599bd75b8", "f68e5c7237cd4260be1644f4a33ba4b9", "f5fb96b55ae943d5b0a12cef560be493", "de996974999a4ebbb8a11a8889b2c683", "030a45a01bab49d29a6058e4f1f3f9ba", "b4b50aea923441d88530edd555673a0e", "e138d8341f094d60a05f2124f4d9fc50", "2ffed33dbe52437591ed87c34f87eb61", "d3498bc5c493472e9d785129ca8293de", "08f540a5520b4fa5811a6c229231ab5f", "7806f5a3a9404bbcbcedfaf8d827a35c", "1223ef92bfd542aaa7f789b8bd799609", "f5ee29b85e664ef180df1d1a60db8223", "b4ac02ab0c814609824a3405d6e31206", "8375b238b9084bceb04c8d67e148f9dc", "a07890ec0fd740d3a84a8aae4979355a", "204dcfe6d8404137b8cb0ac57f82cb08", "becf56b2d70542b1bc330f09bb8c6174", "4dcaad14afbf4a2791d230239ff01b3c", "b51201dc59ab47aea72930e60625d2cb", "553c2e573f5947b4b5bdcb8b2168f016", "4172136737eb4cf48ce6ee5aa566224a", "6d3d3a38139147dfae6ce54b2adcbbf6", "5a55d70b2d0543d082603c586787b9ac", "b815a7d8a9c84a088537c8f3549e8930", "12f020de361f41128fac402f89be26d8", "4eca6c289693485ba95ee9badb47389f", "83736d38ebea43e3b9c58ce36ef1351e", "385e918a99614ca5af1f3a058aae0d31", "ece8d196afd24a0cae98b6233f7e13cf", "af8a211d04f647bfb90cd72ed16bed05", "22e26e8fcc344cd9a9a76a1e2c8bbf53", "f097c1e989394c7f8a226f08ff7a6e9a", "12e46295371a46bc94e7632a49917e60", "82b55fbb2ba74150900dddf1143e54f0", "c6e634b3cd924881a2ac9745fe32cd2f"]}, "id": "LjY75GoYUCB8", "outputId": "3970582b-742e-41c7-813d-27b978b49c29"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a681577f4a00440496f2ee70e2f1198b", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading readme:   0%|          | 0.00/11.6k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "de996974999a4ebbb8a11a8889b2c683", "version_major": 2, "version_minor": 0}, "text/plain": ["Downloading data:   0%|          | 0.00/44.3M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8375b238b9084bceb04c8d67e148f9dc", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split: 0 examples [00:00, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "12f020de361f41128fac402f89be26d8", "version_major": 2, "version_minor": 0}, "text/plain": ["Map:   0%|          | 0/51760 [00:00<?, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["alpaca_prompt = \"\"\"Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\n", "\n", "### Instruction:\n", "{}\n", "\n", "### Input:\n", "{}\n", "\n", "### Response:\n", "{}\"\"\"\n", "\n", "EOS_TOKEN = tokenizer.eos_token\n", "def formatting_prompts_func(examples):\n", "    instructions = examples[\"instruction\"]\n", "    inputs       = examples[\"input\"]\n", "    outputs      = examples[\"output\"]\n", "    texts = []\n", "    for instruction, input, output in zip(instructions, inputs, outputs):\n", "        # Must add EOS_TOKEN, otherwise your generation will go on forever!\n", "        text = alpaca_prompt.format(instruction, input, output) + EOS_TOKEN\n", "        texts.append(text)\n", "    return { \"text\" : texts, }\n", "pass\n", "\n", "from datasets import load_dataset\n", "dataset = load_dataset(\"yahma/alpaca-cleaned\", split = \"train\")\n", "dataset = dataset.map(formatting_prompts_func, batched = True,)"]}, {"cell_type": "markdown", "metadata": {"id": "idAEIeSQ3xdS"}, "source": ["<a name=\"<PERSON>\"></a>\n", "### Train the model\n", "Now let's use Huggingface TRL's `SFTTrainer`! More docs here: [TRL SFT docs](https://huggingface.co/docs/trl/sft_trainer). We do 1 full epoch which makes Alpaca run in 80ish minutes! We also support TRL's `DPOTrainer`! See our DPO tutorial on a free Google Colab instance [here](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Zephyr_(7B)-DPO.ipynb)."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 211, "referenced_widgets": ["47b91d628c824007ba2a899908543fc4", "544c9ffdfd6346d19865bf34d43e26a3", "0a88fd16d72442198803c4c082bdd5ab", "e245c09fe14a49929a8274fdc356fd25", "5cb07c2ef3be4a9f90cac8a53565040f", "93cfec82c4e24cd8bbf6d0044c92f9a4", "7aea5b2b4cb944efae3dc1de39b72f83", "444156136b864dd4896c9f83135772f6", "0da54be740b943c38a4aa805692b35f2", "3e5a6d4cd4bb45879ed67ab7d9cbcc16", "22a3640c1e344b5c922e488a9a2feaca"]}, "id": "95_Nn-89DhsL", "outputId": "0be04edc-0aff-481c-a52f-fce8283ac502"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["PyTorch: setting up devices\n", "The default value for the training argument `--report_to` will change in v5 (from all installed integrations to none). In v5, you will need to use `--report_to all` to get the same behavior as now. You should start updating your code and make this info disappear :-).\n", "loading file tokenizer.model from cache at /root/.cache/huggingface/hub/models--unsloth--tinyllama-bnb-4bit/snapshots/fc56510003ea9d49362400b8a362345150802c31/tokenizer.model\n", "loading file tokenizer.json from cache at /root/.cache/huggingface/hub/models--unsloth--tinyllama-bnb-4bit/snapshots/fc56510003ea9d49362400b8a362345150802c31/tokenizer.json\n", "loading file added_tokens.json from cache at None\n", "loading file special_tokens_map.json from cache at /root/.cache/huggingface/hub/models--unsloth--tinyllama-bnb-4bit/snapshots/fc56510003ea9d49362400b8a362345150802c31/special_tokens_map.json\n", "loading file tokenizer_config.json from cache at /root/.cache/huggingface/hub/models--unsloth--tinyllama-bnb-4bit/snapshots/fc56510003ea9d49362400b8a362345150802c31/tokenizer_config.json\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "47b91d628c824007ba2a899908543fc4", "version_major": 2, "version_minor": 0}, "text/plain": ["Generating train split: 0 examples [00:00, ? examples/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Using auto half precision backend\n"]}], "source": ["from trl import SFTConfig, SFTTrainer\n", "trainer = SFTT<PERSON>er(\n", "    model = model,\n", "    tokenizer = tokenizer,\n", "    train_dataset = dataset,\n", "    dataset_text_field = \"text\",\n", "    max_seq_length = max_seq_length,\n", "    dataset_num_proc = 2,\n", "    packing = True, # Packs short sequences together to save time!\n", "    args = SFTConfig(\n", "        per_device_train_batch_size = 2,\n", "        gradient_accumulation_steps = 4,\n", "        warmup_ratio = 0.1,\n", "        num_train_epochs = 1,\n", "        learning_rate = 2e-5,\n", "        logging_steps = 1,\n", "        optim = \"adamw_8bit\",\n", "        weight_decay = 0.1,\n", "        lr_scheduler_type = \"linear\",\n", "        seed = 3407,\n", "        output_dir = \"outputs\",\n", "        report_to = \"none\", # Use this for WandB etc\n", "    ),\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "2ejIt2xSNKKp", "outputId": "b58a5395-e0e3-43eb-b5dc-fd656d571a3d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPU = Tesla T4. Max memory = 14.748 GB.\n", "0.879 GB of memory reserved.\n"]}], "source": ["# @title Show current memory stats\n", "gpu_stats = torch.cuda.get_device_properties(0)\n", "start_gpu_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "max_memory = round(gpu_stats.total_memory / 1024 / 1024 / 1024, 3)\n", "print(f\"GPU = {gpu_stats.name}. Max memory = {max_memory} GB.\")\n", "print(f\"{start_gpu_memory} GB of memory reserved.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "yqxqAZ7KJ4oL", "outputId": "985d469c-eff6-4d90-b0df-7e187efc0cf7"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["***** Running training *****\n", "  Num examples = 3,000\n", "  Num Epochs = 1\n", "  Instantaneous batch size per device = 2\n", "  Total train batch size (w. parallel, distributed & accumulation) = 8\n", "  Gradient Accumulation steps = 4\n", "  Total optimization steps = 375\n", "  Number of trainable parameters = 25,231,360\n", "You're using a LlamaTokenizerFast tokenizer. Please note that with a fast tokenizer, using the `__call__` method is faster than using a method to encode the text followed by a call to the `pad` method to get a padded encoding.\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='375' max='375' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [375/375 1:23:39, Epoch 1/1]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Step</th>\n", "      <th>Training Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>2.280100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>2.243700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>2.245500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>2.243600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>2.302000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>2.287400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>2.283900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>2.144600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>2.237200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>2.245700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>2.286200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>2.281900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>2.204400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>2.158000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>2.278200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>2.168600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>2.147400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>2.119500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>2.122100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>2.099000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>2.072200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>2.066000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>2.073800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>2.000600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>2.043400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>1.992100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>2.020800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>1.972900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>1.984700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>1.991100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>1.973500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>1.964000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>1.896200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>1.872200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>1.862700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>1.855800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>1.792600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>1.849300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>1.745700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>1.734800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>1.646200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>1.701200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>1.705900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>1.702300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>1.650300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>1.601100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>1.639200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>1.633000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>1.612800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>1.638400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>1.593000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>1.549500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>1.500600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>1.511000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>1.489900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>1.526300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>1.447300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>1.454200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>1.454100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>1.482700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>61</td>\n", "      <td>1.403600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>62</td>\n", "      <td>1.416300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>63</td>\n", "      <td>1.422400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>64</td>\n", "      <td>1.380600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>65</td>\n", "      <td>1.425800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>66</td>\n", "      <td>1.379000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>67</td>\n", "      <td>1.404900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>68</td>\n", "      <td>1.415200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>69</td>\n", "      <td>1.362400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>70</td>\n", "      <td>1.368600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>71</td>\n", "      <td>1.412700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>72</td>\n", "      <td>1.388100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>73</td>\n", "      <td>1.352500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>74</td>\n", "      <td>1.365600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>75</td>\n", "      <td>1.332100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>76</td>\n", "      <td>1.363200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>77</td>\n", "      <td>1.379200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>78</td>\n", "      <td>1.328400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>79</td>\n", "      <td>1.289600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>80</td>\n", "      <td>1.340600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>81</td>\n", "      <td>1.325800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>82</td>\n", "      <td>1.277400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>83</td>\n", "      <td>1.294000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>84</td>\n", "      <td>1.292500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>85</td>\n", "      <td>1.285600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>86</td>\n", "      <td>1.272400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>87</td>\n", "      <td>1.223900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>88</td>\n", "      <td>1.296300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>89</td>\n", "      <td>1.313700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>90</td>\n", "      <td>1.285300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>91</td>\n", "      <td>1.290900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>92</td>\n", "      <td>1.232500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>93</td>\n", "      <td>1.242800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>94</td>\n", "      <td>1.240500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>95</td>\n", "      <td>1.227000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>96</td>\n", "      <td>1.198800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>97</td>\n", "      <td>1.224400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>98</td>\n", "      <td>1.271700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>99</td>\n", "      <td>1.205700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>100</td>\n", "      <td>1.251400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>101</td>\n", "      <td>1.207000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>102</td>\n", "      <td>1.249500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>103</td>\n", "      <td>1.225200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>104</td>\n", "      <td>1.228000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>105</td>\n", "      <td>1.191200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>106</td>\n", "      <td>1.255500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>107</td>\n", "      <td>1.194300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>108</td>\n", "      <td>1.184900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>109</td>\n", "      <td>1.182600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>110</td>\n", "      <td>1.191200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>111</td>\n", "      <td>1.250900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>112</td>\n", "      <td>1.213200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>113</td>\n", "      <td>1.146200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>114</td>\n", "      <td>1.177700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>115</td>\n", "      <td>1.217800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>116</td>\n", "      <td>1.245500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>117</td>\n", "      <td>1.154900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>118</td>\n", "      <td>1.205400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>119</td>\n", "      <td>1.155000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>120</td>\n", "      <td>1.176500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>121</td>\n", "      <td>1.152200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>122</td>\n", "      <td>1.203300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>123</td>\n", "      <td>1.194100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>124</td>\n", "      <td>1.222000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>125</td>\n", "      <td>1.153100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>126</td>\n", "      <td>1.172800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>127</td>\n", "      <td>1.191600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>128</td>\n", "      <td>1.215200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>129</td>\n", "      <td>1.207100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>130</td>\n", "      <td>1.200800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>131</td>\n", "      <td>1.177500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>132</td>\n", "      <td>1.140600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>133</td>\n", "      <td>1.141500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>134</td>\n", "      <td>1.146600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>135</td>\n", "      <td>1.122800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>136</td>\n", "      <td>1.152900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>137</td>\n", "      <td>1.190700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>138</td>\n", "      <td>1.154700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>139</td>\n", "      <td>1.183800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>140</td>\n", "      <td>1.160500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>141</td>\n", "      <td>1.096000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>142</td>\n", "      <td>1.124700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>143</td>\n", "      <td>1.121000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>144</td>\n", "      <td>1.182000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>145</td>\n", "      <td>1.144500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>146</td>\n", "      <td>1.182200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>147</td>\n", "      <td>1.151000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>148</td>\n", "      <td>1.152600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>149</td>\n", "      <td>1.224500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>150</td>\n", "      <td>1.116600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>151</td>\n", "      <td>1.149500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>152</td>\n", "      <td>1.162200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>153</td>\n", "      <td>1.099100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>154</td>\n", "      <td>1.119100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>155</td>\n", "      <td>1.142200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>156</td>\n", "      <td>1.188800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>157</td>\n", "      <td>1.135000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>158</td>\n", "      <td>1.159000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>159</td>\n", "      <td>1.125200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>160</td>\n", "      <td>1.183500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>161</td>\n", "      <td>1.123200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>162</td>\n", "      <td>1.139300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>163</td>\n", "      <td>1.129700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>164</td>\n", "      <td>1.111700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>165</td>\n", "      <td>1.093400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>166</td>\n", "      <td>1.139300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>167</td>\n", "      <td>1.125600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>168</td>\n", "      <td>1.100800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>169</td>\n", "      <td>1.137200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>170</td>\n", "      <td>1.087700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>171</td>\n", "      <td>1.052200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>172</td>\n", "      <td>1.153600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>173</td>\n", "      <td>1.132200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>174</td>\n", "      <td>1.127100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>175</td>\n", "      <td>1.125800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>176</td>\n", "      <td>1.120600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>177</td>\n", "      <td>1.123200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>178</td>\n", "      <td>1.148700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>179</td>\n", "      <td>1.128300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>180</td>\n", "      <td>1.154800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>181</td>\n", "      <td>1.101900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>182</td>\n", "      <td>1.150900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>183</td>\n", "      <td>1.085300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>184</td>\n", "      <td>1.152900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>185</td>\n", "      <td>1.141800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>186</td>\n", "      <td>1.090200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>187</td>\n", "      <td>1.167600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>188</td>\n", "      <td>1.109800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>189</td>\n", "      <td>1.059100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>190</td>\n", "      <td>1.071300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>191</td>\n", "      <td>1.111100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>192</td>\n", "      <td>1.146600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>193</td>\n", "      <td>1.125800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>194</td>\n", "      <td>1.082200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>195</td>\n", "      <td>1.112300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>196</td>\n", "      <td>1.159800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>197</td>\n", "      <td>1.097600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>198</td>\n", "      <td>1.107900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>199</td>\n", "      <td>1.114800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>200</td>\n", "      <td>1.103600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>201</td>\n", "      <td>1.082200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>202</td>\n", "      <td>1.080200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>203</td>\n", "      <td>1.103900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>204</td>\n", "      <td>1.120600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>205</td>\n", "      <td>1.106400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>206</td>\n", "      <td>1.123900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>207</td>\n", "      <td>1.118700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>208</td>\n", "      <td>1.070800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>209</td>\n", "      <td>1.096500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>210</td>\n", "      <td>1.107800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>211</td>\n", "      <td>1.084000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>212</td>\n", "      <td>1.138900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>213</td>\n", "      <td>1.082100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>214</td>\n", "      <td>1.101900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>215</td>\n", "      <td>1.080700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>216</td>\n", "      <td>1.124300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>217</td>\n", "      <td>1.082500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>218</td>\n", "      <td>1.098300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>219</td>\n", "      <td>1.089400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>220</td>\n", "      <td>1.090600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>221</td>\n", "      <td>1.109700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>222</td>\n", "      <td>1.084900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>223</td>\n", "      <td>1.062900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>224</td>\n", "      <td>1.090400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>225</td>\n", "      <td>1.119900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>226</td>\n", "      <td>1.122600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>227</td>\n", "      <td>1.106500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>228</td>\n", "      <td>1.068300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>229</td>\n", "      <td>1.148000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>230</td>\n", "      <td>1.120300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>231</td>\n", "      <td>1.051000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>232</td>\n", "      <td>1.115600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>233</td>\n", "      <td>1.070800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>234</td>\n", "      <td>1.124800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>235</td>\n", "      <td>1.071500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>236</td>\n", "      <td>1.083000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>237</td>\n", "      <td>1.081800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>238</td>\n", "      <td>1.045200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>239</td>\n", "      <td>1.127600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>240</td>\n", "      <td>1.120100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>241</td>\n", "      <td>1.089800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>242</td>\n", "      <td>1.173400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>243</td>\n", "      <td>1.100400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>244</td>\n", "      <td>1.107100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>245</td>\n", "      <td>1.121700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>246</td>\n", "      <td>1.037800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>247</td>\n", "      <td>1.103900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>248</td>\n", "      <td>1.112700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>249</td>\n", "      <td>1.134600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>250</td>\n", "      <td>1.108800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>251</td>\n", "      <td>1.100000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>252</td>\n", "      <td>1.056000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>253</td>\n", "      <td>1.089300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>254</td>\n", "      <td>1.078600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>255</td>\n", "      <td>1.084300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>256</td>\n", "      <td>1.073000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>257</td>\n", "      <td>1.062500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>258</td>\n", "      <td>1.110800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>259</td>\n", "      <td>1.074600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>260</td>\n", "      <td>1.151400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>261</td>\n", "      <td>1.104700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>262</td>\n", "      <td>1.099700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>263</td>\n", "      <td>1.084100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>264</td>\n", "      <td>1.077900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>265</td>\n", "      <td>1.078400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>266</td>\n", "      <td>1.046300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>267</td>\n", "      <td>1.042800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>268</td>\n", "      <td>1.090200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>269</td>\n", "      <td>1.042600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>270</td>\n", "      <td>1.112700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>271</td>\n", "      <td>1.115300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>272</td>\n", "      <td>1.125800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>273</td>\n", "      <td>1.092600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>274</td>\n", "      <td>1.115700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>275</td>\n", "      <td>1.101700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>276</td>\n", "      <td>1.081200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>277</td>\n", "      <td>1.094400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>278</td>\n", "      <td>1.057900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>279</td>\n", "      <td>1.060400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>280</td>\n", "      <td>1.133200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>281</td>\n", "      <td>1.053900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>282</td>\n", "      <td>1.102300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>283</td>\n", "      <td>1.075500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>284</td>\n", "      <td>1.115100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>285</td>\n", "      <td>1.029300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>286</td>\n", "      <td>1.038500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>287</td>\n", "      <td>1.055000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>288</td>\n", "      <td>1.110800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>289</td>\n", "      <td>1.116200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>290</td>\n", "      <td>1.050500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>291</td>\n", "      <td>1.073800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>292</td>\n", "      <td>1.075900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>293</td>\n", "      <td>1.131600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>294</td>\n", "      <td>1.141400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>295</td>\n", "      <td>1.140300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>296</td>\n", "      <td>1.096600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>297</td>\n", "      <td>1.079900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>298</td>\n", "      <td>1.061700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>299</td>\n", "      <td>1.091000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>300</td>\n", "      <td>1.075700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>301</td>\n", "      <td>1.123300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>302</td>\n", "      <td>1.098200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>303</td>\n", "      <td>1.118200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>304</td>\n", "      <td>1.089600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>305</td>\n", "      <td>1.102400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>306</td>\n", "      <td>1.078900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>307</td>\n", "      <td>1.074900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>308</td>\n", "      <td>1.037700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>309</td>\n", "      <td>1.069100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>310</td>\n", "      <td>1.071500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>311</td>\n", "      <td>1.088100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>312</td>\n", "      <td>1.074900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>313</td>\n", "      <td>1.101600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>314</td>\n", "      <td>1.097200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>315</td>\n", "      <td>1.041500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>316</td>\n", "      <td>1.117300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>317</td>\n", "      <td>1.065400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>318</td>\n", "      <td>1.090700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>319</td>\n", "      <td>1.095100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>320</td>\n", "      <td>1.108600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>321</td>\n", "      <td>1.089400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>322</td>\n", "      <td>1.083800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>323</td>\n", "      <td>1.059400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>324</td>\n", "      <td>1.091200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>325</td>\n", "      <td>1.063700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>326</td>\n", "      <td>1.045400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>327</td>\n", "      <td>1.042900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>328</td>\n", "      <td>1.136300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>329</td>\n", "      <td>1.086600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>330</td>\n", "      <td>1.067300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>331</td>\n", "      <td>1.053600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>332</td>\n", "      <td>1.096500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>333</td>\n", "      <td>1.104300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>334</td>\n", "      <td>1.040800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>335</td>\n", "      <td>1.090800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>336</td>\n", "      <td>1.069800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>337</td>\n", "      <td>1.025400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>338</td>\n", "      <td>1.030300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>339</td>\n", "      <td>1.049500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>340</td>\n", "      <td>1.081800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>341</td>\n", "      <td>1.090200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>342</td>\n", "      <td>1.065400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>343</td>\n", "      <td>1.068600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>344</td>\n", "      <td>1.111300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>345</td>\n", "      <td>1.091700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>346</td>\n", "      <td>1.062700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>347</td>\n", "      <td>1.090900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>348</td>\n", "      <td>1.099900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>349</td>\n", "      <td>1.037900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>350</td>\n", "      <td>1.108200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>351</td>\n", "      <td>1.102100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>352</td>\n", "      <td>1.085800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>353</td>\n", "      <td>1.054900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>354</td>\n", "      <td>1.087700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>355</td>\n", "      <td>1.103500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>356</td>\n", "      <td>1.063600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>357</td>\n", "      <td>1.080600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>358</td>\n", "      <td>1.095600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>359</td>\n", "      <td>1.054600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>360</td>\n", "      <td>1.056100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>361</td>\n", "      <td>1.093400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>362</td>\n", "      <td>1.044400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>363</td>\n", "      <td>1.052700</td>\n", "    </tr>\n", "    <tr>\n", "      <td>364</td>\n", "      <td>1.051300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>365</td>\n", "      <td>1.060500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>366</td>\n", "      <td>1.063400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>367</td>\n", "      <td>1.044900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>368</td>\n", "      <td>1.108100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>369</td>\n", "      <td>1.074500</td>\n", "    </tr>\n", "    <tr>\n", "      <td>370</td>\n", "      <td>1.038300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>371</td>\n", "      <td>1.047300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>372</td>\n", "      <td>1.072900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>373</td>\n", "      <td>1.064600</td>\n", "    </tr>\n", "    <tr>\n", "      <td>374</td>\n", "      <td>1.104100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>375</td>\n", "      <td>1.074300</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["\n", "\n", "Training completed. Do not forget to share your model on huggingface.co/models =)\n", "\n", "\n"]}], "source": ["trainer_stats = trainer.train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "colab": {"base_uri": "https://localhost:8080/"}, "id": "pCqnaKmlO1U9", "outputId": "ecb21ea9-b0f5-48ab-9145-5baedb68a203"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5034.6413 seconds used for training.\n", "83.91 minutes used for training.\n", "Peak reserved memory = 13.508 GB.\n", "Peak reserved memory for training = 12.629 GB.\n", "Peak reserved memory % of max memory = 91.592 %.\n", "Peak reserved memory for training % of max memory = 85.632 %.\n"]}], "source": ["# @title Show final memory and time stats\n", "used_memory = round(torch.cuda.max_memory_reserved() / 1024 / 1024 / 1024, 3)\n", "used_memory_for_lora = round(used_memory - start_gpu_memory, 3)\n", "used_percentage = round(used_memory / max_memory * 100, 3)\n", "lora_percentage = round(used_memory_for_lora / max_memory * 100, 3)\n", "print(f\"{trainer_stats.metrics['train_runtime']} seconds used for training.\")\n", "print(\n", "    f\"{round(trainer_stats.metrics['train_runtime']/60, 2)} minutes used for training.\"\n", ")\n", "print(f\"Peak reserved memory = {used_memory} GB.\")\n", "print(f\"Peak reserved memory for training = {used_memory_for_lora} GB.\")\n", "print(f\"Peak reserved memory % of max memory = {used_percentage} %.\")\n", "print(f\"Peak reserved memory for training % of max memory = {lora_percentage} %.\")"]}, {"cell_type": "markdown", "metadata": {"id": "ekOmTR1hSNcr"}, "source": ["<a name=\"Inference\"></a>\n", "### Inference\n", "Let's run the model! You can change the instruction and input - leave the output blank!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "kR3gIAX-SM2q", "outputId": "2283bb0c-d568-4c92-8352-094ba12eaf06"}, "outputs": [{"data": {"text/plain": ["['<s> Below is an instruction that describes a task, paired with an input that provides further context. Write a response that appropriately completes the request.\\n\\n### Instruction:\\nContinue the fi<PERSON><PERSON><PERSON> sequence.\\n\\n### Input:\\n1, 1, 2, 3, 5, 8\\n\\n### Response:\\nThe fibonacci sequence is a sequence of numbers that can be generated by adding the previous two numbers and then subtracting the previous number from the previous number. The first number in the sequence is 1, and the second number is 1. The third number in the sequence is 1 + 1 = 2, the fourth number is 1 + 2 = 3, and so on. The sequence can be continued by adding 1 + 1 = 2, 1 + 2 = 3, and so on.</s>']"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# alpaca_prompt = Copied from above\n", "FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "inputs = tokenizer(\n", "[\n", "    alpaca_prompt.format(\n", "        \"Continue the fi<PERSON><PERSON><PERSON> sequence.\", # instruction\n", "        \"1, 1, 2, 3, 5, 8\", # input\n", "        \"\", # output - leave this blank for generation!\n", "    )\n", "], return_tensors = \"pt\").to(\"cuda\")\n", "\n", "outputs = model.generate(**inputs, max_new_tokens = 64, use_cache = True)\n", "tokenizer.batch_decode(outputs)"]}, {"cell_type": "markdown", "metadata": {"id": "V2otZJcevdpZ"}, "source": [" You can also use a `TextStreamer` for continuous inference - so you can see the generation token by token, instead of waiting the whole time!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QYvyvuj5vd7H"}, "outputs": [], "source": ["# alpaca_prompt = Copied from above\n", "FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "inputs = tokenizer(\n", "[\n", "    alpaca_prompt.format(\n", "        \"Continue the fi<PERSON><PERSON><PERSON> sequence.\", # instruction\n", "        \"1, 1, 2, 3, 5, 8\", # input\n", "        \"\", # output - leave this blank for generation!\n", "    )\n", "], return_tensors = \"pt\").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer)\n", "_ = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 128)"]}, {"cell_type": "markdown", "metadata": {"id": "uMuVrWbjAzhc"}, "source": ["<a name=\"Save\"></a>\n", "### Saving, loading finetuned models\n", "To save the final model as LoRA adapters, either use Huggingface's `push_to_hub` for an online save or `save_pretrained` for a local save.\n", "\n", "**[NOTE]** This ONLY saves the LoRA adapters, and not the full model. To save to 16bit or GGUF, scroll down!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upcOlWe7A1vc"}, "outputs": [], "source": ["model.save_pretrained(\"lora_model\")  # Local saving\n", "tokenizer.save_pretrained(\"lora_model\")\n", "# model.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving\n", "# tokenizer.push_to_hub(\"your_name/lora_model\", token = \"...\") # Online saving"]}, {"cell_type": "markdown", "metadata": {"id": "3CgqR2B0vmCt"}, "source": ["Now if you want to load the LoRA adapters we just saved for inference, set `False` to `True`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Yle1gGB3vmWK"}, "outputs": [], "source": ["if False:\n", "    from unsloth import FastLanguageModel\n", "    model, tokenizer = FastLanguageModel.from_pretrained(\n", "        model_name = \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        max_seq_length = max_seq_length,\n", "        dtype = dtype,\n", "        load_in_4bit = load_in_4bit,\n", "    )\n", "    FastLanguageModel.for_inference(model) # Enable native 2x faster inference\n", "\n", "# alpaca_prompt = You MUST copy from above!\n", "\n", "inputs = tokenizer(\n", "[\n", "    alpaca_prompt.format(\n", "        \"What is a famous tall tower in Paris?\", # instruction\n", "        \"\", # input\n", "        \"\", # output - leave this blank for generation!\n", "    )\n", "], return_tensors = \"pt\").to(\"cuda\")\n", "\n", "from transformers import TextStreamer\n", "text_streamer = TextStreamer(tokenizer)\n", "_ = model.generate(**inputs, streamer = text_streamer, max_new_tokens = 64)"]}, {"cell_type": "markdown", "metadata": {"id": "8m76iItmvni0"}, "source": ["You can also use Hugging Face's `AutoModelForPeftCausalLM`. Only use this if you do not have `unsloth` installed. It can be hopelessly slow, since `4bit` model downloading is not supported, and Unsloth's **inference is 2x faster**."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "wcMqKxzcvouj"}, "outputs": [], "source": ["if False:\n", "    # I highly do NOT suggest - use Unsloth if possible\n", "    from peft import AutoPeftModelForCausalLM\n", "    from transformers import AutoTokenizer\n", "    model = AutoPeftModelForCausalLM.from_pretrained(\n", "        \"lora_model\", # YOUR MODEL YOU USED FOR TRAINING\n", "        load_in_4bit = load_in_4bit,\n", "    )\n", "    tokenizer = AutoTokenizer.from_pretrained(\"lora_model\")"]}, {"cell_type": "markdown", "metadata": {"id": "xwCTbEUavpoC"}, "source": ["### Saving to float16 for VLLM\n", "\n", "We also support saving to `float16` directly. Select `merged_16bit` for float16 or `merged_4bit` for int4. We also allow `lora` adapters as a fallback. Use `push_to_hub_merged` to upload to your Hugging Face account! You can go to https://huggingface.co/settings/tokens for your personal tokens."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gJKx0osWvqzz"}, "outputs": [], "source": ["# Merge to 16bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_16bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_16bit\", token = \"\")\n", "\n", "# Merge to 4bit\n", "if False: model.save_pretrained_merged(\"model\", tokenizer, save_method = \"merged_4bit\",)\n", "if False: model.push_to_hub_merged(\"hf/model\", tokenizer, save_method = \"merged_4bit\", token = \"\")\n", "\n", "# Just LoRA adapters\n", "if False:\n", "    model.save_pretrained(\"model\")\n", "    tokenizer.save_pretrained(\"model\")\n", "if False:\n", "    model.push_to_hub(\"hf/model\", token = \"\")\n", "    tokenizer.push_to_hub(\"hf/model\", token = \"\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "mhc9u6HAvr3b"}, "source": ["### GGUF / llama.cpp Conversion\n", "To save to `GGUF` / `llama.cpp`, we support it natively now! We clone `llama.cpp` and we default save it to `q8_0`. We allow all methods like `q4_k_m`. Use `save_pretrained_gguf` for local saving and `push_to_hub_gguf` for uploading to HF.\n", "\n", "Some supported quant methods (full list on our [Wiki page](https://github.com/unslothai/unsloth/wiki#gguf-quantization-options)):\n", "* `q8_0` - Fast conversion. High resource use, but generally acceptable.\n", "* `q4_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q4_K.\n", "* `q5_k_m` - Recommended. Uses Q6_K for half of the attention.wv and feed_forward.w2 tensors, else Q5_K."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2_TmxAoavvYW"}, "outputs": [], "source": ["# Save to 8bit Q8_0\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer,)\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, token = \"\")\n", "\n", "# Save to 16bit GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"f16\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"f16\", token = \"\")\n", "\n", "# Save to q4_k_m GGUF\n", "if False: model.save_pretrained_gguf(\"model\", tokenizer, quantization_method = \"q4_k_m\")\n", "if False: model.push_to_hub_gguf(\"hf/model\", tokenizer, quantization_method = \"q4_k_m\", token = \"\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, use the `model-unsloth.gguf` file or `model-unsloth-Q4_K_M.gguf` file in llama.cpp or a UI based system like Jan or Open WebUI. You can install Jan [here](https://github.com/janhq/jan) and Open WebUI [here](https://github.com/open-webui/open-webui)\n", "\n", "And we're done! If you have any questions on Unsloth, we have a [Discord](https://discord.gg/unsloth) channel! If you find any bugs or want to keep updated with the latest LLM stuff, or need help, join projects etc, feel free to join our Discord!\n", "\n", "Some other links:\n", "1. Train your own reasoning model - Llama GRPO notebook [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.1_(8B)-GRPO.ipynb)\n", "2. Saving finetunes to Ollama. [Free notebook](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3_(8B)-Ollama.ipynb)\n", "3. Llama 3.2 Vision finetuning - Radiography use case. [Free Colab](https://colab.research.google.com/github/unslothai/notebooks/blob/main/nb/Llama3.2_(11B)-Vision.ipynb)\n", "6. See notebooks for DPO, ORPO, Continued pretraining, conversational finetuning and more on our [documentation](https://docs.unsloth.ai/get-started/unsloth-notebooks)!\n", "\n", "<div class=\"align-center\">\n", "  <a href=\"https://unsloth.ai\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/unsloth%20new%20logo.png\" width=\"115\"></a>\n", "  <a href=\"https://discord.gg/unsloth\"><img src=\"https://github.com/unslothai/unsloth/raw/main/images/Discord.png\" width=\"145\"></a>\n", "  <a href=\"https://docs.unsloth.ai/\"><img src=\"https://github.com/unslothai/unsloth/blob/main/images/documentation%20green%20button.png?raw=true\" width=\"125\"></a>\n", "\n", "  Join Discord if you need help + ⭐️ <i>Star us on <a href=\"https://github.com/unslothai/unsloth\">Github</a> </i> ⭐️\n", "</div>\n"]}], "metadata": {"accelerator": "GPU", "colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"008b398b553c47e1a3c9bedac5644642": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "02b75597dc7c4d1cbf3c51b1fddd3ec6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "030a45a01bab49d29a6058e4f1f3f9ba": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d3498bc5c493472e9d785129ca8293de", "placeholder": "​", "style": "IPY_MODEL_08f540a5520b4fa5811a6c229231ab5f", "value": "Downloading data: 100%"}}, "063e74555fbb4ab381cdcbcabc37461e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "083e0fb519b843e48d283a638e8a6dcb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_56bc8dce7dfb44789f0edb09df8dd450", "IPY_MODEL_64d035fbc33f417d970f39c5aa14c93f", "IPY_MODEL_437886691cdf40d4861575b7b91feb89"], "layout": "IPY_MODEL_8809b53bd5f74f228c0ab6df9a918796"}}, "08ddb073da9b4a4981e5452df0e80c5a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "08f540a5520b4fa5811a6c229231ab5f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "0a88fd16d72442198803c4c082bdd5ab": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_444156136b864dd4896c9f83135772f6", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_0da54be740b943c38a4aa805692b35f2", "value": 1}}, "0d950e93a67e4402949fce40ba6097b6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "0da54be740b943c38a4aa805692b35f2": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "103ed45882c3450a8bd9e55940d88c50": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "112fd48addfb42cba27b1939375d5e4c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1223ef92bfd542aaa7f789b8bd799609": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "12634ac7d05e490493d5c068087ff978": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "12e46295371a46bc94e7632a49917e60": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "12f020de361f41128fac402f89be26d8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_4eca6c289693485ba95ee9badb47389f", "IPY_MODEL_83736d38ebea43e3b9c58ce36ef1351e", "IPY_MODEL_385e918a99614ca5af1f3a058aae0d31"], "layout": "IPY_MODEL_ece8d196afd24a0cae98b6233f7e13cf"}}, "138e959064c04ff6a2b6d3a4eb0f6359": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "155663a36c2d4949a6bba5ee2b4224b1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_94a7e3827ab241dbb75d3d816354c694", "placeholder": "​", "style": "IPY_MODEL_c21c74c7956d425c9049fb1cdc8b7d07", "value": "config.json: 100%"}}, "17e0b9c3b30e4d9ab34f684b0f0c8865": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f68e5c7237cd4260be1644f4a33ba4b9", "placeholder": "​", "style": "IPY_MODEL_f5fb96b55ae943d5b0a12cef560be493", "value": " 11.6k/11.6k [00:00&lt;00:00, 266kB/s]"}}, "188ee79c6ef44ae896a93c0184780f41": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_155663a36c2d4949a6bba5ee2b4224b1", "IPY_MODEL_5e48035e021b40aea0317f856b842379", "IPY_MODEL_d9a92404c9f74425ac0cff862001b168"], "layout": "IPY_MODEL_56d1a29a8e724aa98b0e9b2d8c995713"}}, "1cffaffc53624ffab7ce7a5830fcad07": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "204dcfe6d8404137b8cb0ac57f82cb08": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4172136737eb4cf48ce6ee5aa566224a", "max": 1, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6d3d3a38139147dfae6ce54b2adcbbf6", "value": 1}}, "20fcb167b6c04792b627f7d13da5db94": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_008b398b553c47e1a3c9bedac5644642", "placeholder": "​", "style": "IPY_MODEL_78ef61071c244b86b20227e775267aa0", "value": " 762M/762M [00:09&lt;00:00, 81.8MB/s]"}}, "22a3640c1e344b5c922e488a9a2feaca": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "22e26e8fcc344cd9a9a76a1e2c8bbf53": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "254643fe17ec4613bc26de5b3330c7f1": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2be4a7d986c44fd4a222dc2f1d7b9918": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2c35f51a2e9e439f9757d27a4e8cfb5a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2ffed33dbe52437591ed87c34f87eb61": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "323cca9bfd6a4342b614043b95b8f722": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_254643fe17ec4613bc26de5b3330c7f1", "max": 499723, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_9725fce622b845b192fbb319f01f56d8", "value": 499723}}, "385e918a99614ca5af1f3a058aae0d31": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_82b55fbb2ba74150900dddf1143e54f0", "placeholder": "​", "style": "IPY_MODEL_c6e634b3cd924881a2ac9745fe32cd2f", "value": " 51760/51760 [00:00&lt;00:00, 53379.28 examples/s]"}}, "3e5a6d4cd4bb45879ed67ab7d9cbcc16": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4172136737eb4cf48ce6ee5aa566224a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "437886691cdf40d4861575b7b91feb89": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_103ed45882c3450a8bd9e55940d88c50", "placeholder": "​", "style": "IPY_MODEL_6abff53f5ed74b8ab7bfe981547d44b4", "value": " 1.84M/1.84M [00:00&lt;00:00, 6.90MB/s]"}}, "444156136b864dd4896c9f83135772f6": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "47255152beba4690b21cce344d30a548": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "47b91d628c824007ba2a899908543fc4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_544c9ffdfd6346d19865bf34d43e26a3", "IPY_MODEL_0a88fd16d72442198803c4c082bdd5ab", "IPY_MODEL_e245c09fe14a49929a8274fdc356fd25"], "layout": "IPY_MODEL_5cb07c2ef3be4a9f90cac8a53565040f"}}, "49ac5705579c4035b864db8d06a3efd6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_112fd48addfb42cba27b1939375d5e4c", "placeholder": "​", "style": "IPY_MODEL_08ddb073da9b4a4981e5452df0e80c5a", "value": " 129/129 [00:00&lt;00:00, 3.71kB/s]"}}, "49de6fa3e53e4259b87caabcb1cc50b2": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4b2bf0e58403475a8b201e87cf3efb4b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4e7766fa9a1048bcacaf4cea16f24d90", "placeholder": "​", "style": "IPY_MODEL_ffa5224eb65142d3a3cd053fd43c14b7", "value": " 438/438 [00:00&lt;00:00, 7.57kB/s]"}}, "4b7057f7b1aa4e9ea73e2aff19d5d7f5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4dcaad14afbf4a2791d230239ff01b3c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4e7766fa9a1048bcacaf4cea16f24d90": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "4eca6c289693485ba95ee9badb47389f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_af8a211d04f647bfb90cd72ed16bed05", "placeholder": "​", "style": "IPY_MODEL_22e26e8fcc344cd9a9a76a1e2c8bbf53", "value": "Map: 100%"}}, "544c9ffdfd6346d19865bf34d43e26a3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_93cfec82c4e24cd8bbf6d0044c92f9a4", "placeholder": "​", "style": "IPY_MODEL_7aea5b2b4cb944efae3dc1de39b72f83", "value": "Generating train split: "}}, "54e400aa31c0492fbbfd0d291eb9dd7f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "553c2e573f5947b4b5bdcb8b2168f016": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5579f451f9bc490fa924455dcf6351f0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_92d80dfad00e4b0abfa3054752385c21", "IPY_MODEL_95ed136481974ccd84eb0488ed5c328d", "IPY_MODEL_d52459dc6f224b169a49d1cbd2d92f50"], "layout": "IPY_MODEL_e73ae12084e848a8aa82e73c5efa9d85"}}, "56109c78c5454216af1f1e1910bee352": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "56bc8dce7dfb44789f0edb09df8dd450": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_c2628e20f1154acd94bbcb282fd6554d", "placeholder": "​", "style": "IPY_MODEL_8680fbf7a67940e7ac6f6d59873ef4e9", "value": "tokenizer.json: 100%"}}, "56d1a29a8e724aa98b0e9b2d8c995713": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "594b86fdbc6c4ac582aee52d0099a590": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5a55d70b2d0543d082603c586787b9ac": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5adbfe3e9c424429851258c009d72f26": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "5cb07c2ef3be4a9f90cac8a53565040f": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5d20dbac1cd74e378902f7f6ef71e1f5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a8a15bf2150246999bf268f0731f9e43", "placeholder": "​", "style": "IPY_MODEL_6ffc5ef1a24249bfbaea21fbc8990298", "value": "tokenizer.model: 100%"}}, "5e48035e021b40aea0317f856b842379": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_49de6fa3e53e4259b87caabcb1cc50b2", "max": 1089, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_b53a34cdb16a4b8eb482b91f407b688a", "value": 1089}}, "5e683f7a34e74032a2543ce2db6d9ceb": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_5d20dbac1cd74e378902f7f6ef71e1f5", "IPY_MODEL_323cca9bfd6a4342b614043b95b8f722", "IPY_MODEL_b792c64d4aed4f90938c58344961d32c"], "layout": "IPY_MODEL_56109c78c5454216af1f1e1910bee352"}}, "6324e9afb3b64beaa84a80622e4aa4af": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "64d035fbc33f417d970f39c5aa14c93f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_12634ac7d05e490493d5c068087ff978", "max": 1842767, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_0d950e93a67e4402949fce40ba6097b6", "value": 1842767}}, "66917e3680214990a27525ee10ef0259": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "680c3c2c0ea24be0b7a29ad770f28abd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "68a8b3c46c15441d9a67d1b578c64f1d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6abff53f5ed74b8ab7bfe981547d44b4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6cdb22652f1f47648c8dfe632e7e87b5": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6d258790472640f493b26790298aa366": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6d3d3a38139147dfae6ce54b2adcbbf6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "6ffc5ef1a24249bfbaea21fbc8990298": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "70df82f25eeb4f9f83778c3c4cab7ddd": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7806f5a3a9404bbcbcedfaf8d827a35c": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "78dcd43379e64fb994fc5d8ad392cd32": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "78ef61071c244b86b20227e775267aa0": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "78f3ba544cdb4e36b76f3a0fe4d79075": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a06afe98f7fa4d6a99a2defe9c7828cc", "placeholder": "​", "style": "IPY_MODEL_063e74555fbb4ab381cdcbcabc37461e", "value": "special_tokens_map.json: 100%"}}, "7aea5b2b4cb944efae3dc1de39b72f83": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "800461f0e86545e6a99fbb868e028a27": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "82b55fbb2ba74150900dddf1143e54f0": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "83736d38ebea43e3b9c58ce36ef1351e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f097c1e989394c7f8a226f08ff7a6e9a", "max": 51760, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_12e46295371a46bc94e7632a49917e60", "value": 51760}}, "8375b238b9084bceb04c8d67e148f9dc": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_a07890ec0fd740d3a84a8aae4979355a", "IPY_MODEL_204dcfe6d8404137b8cb0ac57f82cb08", "IPY_MODEL_becf56b2d70542b1bc330f09bb8c6174"], "layout": "IPY_MODEL_4dcaad14afbf4a2791d230239ff01b3c"}}, "8680fbf7a67940e7ac6f6d59873ef4e9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "876f9871caac4fba8c6a0f86f98e2314": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1cffaffc53624ffab7ce7a5830fcad07", "max": 438, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_47255152beba4690b21cce344d30a548", "value": 438}}, "8809b53bd5f74f228c0ab6df9a918796": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "92d80dfad00e4b0abfa3054752385c21": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6324e9afb3b64beaa84a80622e4aa4af", "placeholder": "​", "style": "IPY_MODEL_fe63f948843240c184084e159a8feef3", "value": "tokenizer_config.json: 100%"}}, "93cfec82c4e24cd8bbf6d0044c92f9a4": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "93e320b8f6f242d3805ef65c81f33718": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "94a7e3827ab241dbb75d3d816354c694": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "95ed136481974ccd84eb0488ed5c328d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_78dcd43379e64fb994fc5d8ad392cd32", "max": 894, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_6d258790472640f493b26790298aa366", "value": 894}}, "9725fce622b845b192fbb319f01f56d8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "982fd1d338e148b2ba66050e03897dbb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9986a56c71b4451ea852e49e34ebd2fd": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9eb629533a45449ea819ed32c2a35a41": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a06afe98f7fa4d6a99a2defe9c7828cc": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a07890ec0fd740d3a84a8aae4979355a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b51201dc59ab47aea72930e60625d2cb", "placeholder": "​", "style": "IPY_MODEL_553c2e573f5947b4b5bdcb8b2168f016", "value": "Generating train split: "}}, "a681577f4a00440496f2ee70e2f1198b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c64f215f76c345c2aa1b98d23364ece6", "IPY_MODEL_d32436f7cb7c49a88c2fa867add54367", "IPY_MODEL_17e0b9c3b30e4d9ab34f684b0f0c8865"], "layout": "IPY_MODEL_93e320b8f6f242d3805ef65c81f33718"}}, "a8a15bf2150246999bf268f0731f9e43": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "acedd5f384554c108b806e7ceef84382": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_78f3ba544cdb4e36b76f3a0fe4d79075", "IPY_MODEL_876f9871caac4fba8c6a0f86f98e2314", "IPY_MODEL_4b2bf0e58403475a8b201e87cf3efb4b"], "layout": "IPY_MODEL_68a8b3c46c15441d9a67d1b578c64f1d"}}, "af8a211d04f647bfb90cd72ed16bed05": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b4ac02ab0c814609824a3405d6e31206": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b4b50aea923441d88530edd555673a0e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7806f5a3a9404bbcbcedfaf8d827a35c", "max": 44307561, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_1223ef92bfd542aaa7f789b8bd799609", "value": 44307561}}, "b51201dc59ab47aea72930e60625d2cb": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b53a34cdb16a4b8eb482b91f407b688a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b792c64d4aed4f90938c58344961d32c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ee2af32e73d542f9b089d761eed9d801", "placeholder": "​", "style": "IPY_MODEL_138e959064c04ff6a2b6d3a4eb0f6359", "value": " 500k/500k [00:00&lt;00:00, 13.0MB/s]"}}, "b815a7d8a9c84a088537c8f3549e8930": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "becf56b2d70542b1bc330f09bb8c6174": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5a55d70b2d0543d082603c586787b9ac", "placeholder": "​", "style": "IPY_MODEL_b815a7d8a9c84a088537c8f3549e8930", "value": " 51760/0 [00:00&lt;00:00, 78727.68 examples/s]"}}, "c21c74c7956d425c9049fb1cdc8b7d07": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c2628e20f1154acd94bbcb282fd6554d": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c64f215f76c345c2aa1b98d23364ece6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d930f897415947ada08ce063f9cb8bed", "placeholder": "​", "style": "IPY_MODEL_54e400aa31c0492fbbfd0d291eb9dd7f", "value": "Downloading readme: 100%"}}, "c6e634b3cd924881a2ac9745fe32cd2f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c8fd063c9d9c4270b3080677fdd92632": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_cd700122445641279b905116cc3b73a1", "IPY_MODEL_e2901aa31b3e4ba39ba138468af5c95b", "IPY_MODEL_20fcb167b6c04792b627f7d13da5db94"], "layout": "IPY_MODEL_2c35f51a2e9e439f9757d27a4e8cfb5a"}}, "cd700122445641279b905116cc3b73a1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_4b7057f7b1aa4e9ea73e2aff19d5d7f5", "placeholder": "​", "style": "IPY_MODEL_66917e3680214990a27525ee10ef0259", "value": "model.safetensors: 100%"}}, "d32436f7cb7c49a88c2fa867add54367": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_594b86fdbc6c4ac582aee52d0099a590", "max": 11610, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d6b6c9420c644d42b314ac7599bd75b8", "value": 11610}}, "d3498bc5c493472e9d785129ca8293de": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d52459dc6f224b169a49d1cbd2d92f50": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9eb629533a45449ea819ed32c2a35a41", "placeholder": "​", "style": "IPY_MODEL_5adbfe3e9c424429851258c009d72f26", "value": " 894/894 [00:00&lt;00:00, 23.8kB/s]"}}, "d532a4ba00d7488ead1366f690a28c41": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_70df82f25eeb4f9f83778c3c4cab7ddd", "placeholder": "​", "style": "IPY_MODEL_9986a56c71b4451ea852e49e34ebd2fd", "value": "generation_config.json: 100%"}}, "d6b6c9420c644d42b314ac7599bd75b8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d930f897415947ada08ce063f9cb8bed": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d9a92404c9f74425ac0cff862001b168": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_982fd1d338e148b2ba66050e03897dbb", "placeholder": "​", "style": "IPY_MODEL_2be4a7d986c44fd4a222dc2f1d7b9918", "value": " 1.09k/1.09k [00:00&lt;00:00, 36.5kB/s]"}}, "de996974999a4ebbb8a11a8889b2c683": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_030a45a01bab49d29a6058e4f1f3f9ba", "IPY_MODEL_b4b50aea923441d88530edd555673a0e", "IPY_MODEL_e138d8341f094d60a05f2124f4d9fc50"], "layout": "IPY_MODEL_2ffed33dbe52437591ed87c34f87eb61"}}, "e138d8341f094d60a05f2124f4d9fc50": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f5ee29b85e664ef180df1d1a60db8223", "placeholder": "​", "style": "IPY_MODEL_b4ac02ab0c814609824a3405d6e31206", "value": " 44.3M/44.3M [00:05&lt;00:00, 8.61MB/s]"}}, "e245c09fe14a49929a8274fdc356fd25": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3e5a6d4cd4bb45879ed67ab7d9cbcc16", "placeholder": "​", "style": "IPY_MODEL_22a3640c1e344b5c922e488a9a2feaca", "value": " 3000/0 [00:46&lt;00:00, 120.63 examples/s]"}}, "e2901aa31b3e4ba39ba138468af5c95b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6cdb22652f1f47648c8dfe632e7e87b5", "max": 762453381, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_02b75597dc7c4d1cbf3c51b1fddd3ec6", "value": 762453381}}, "e517fa5b72d2455eb39101c368165b38": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d532a4ba00d7488ead1366f690a28c41", "IPY_MODEL_ff667ca8001e44b0a6146daf3de94e55", "IPY_MODEL_49ac5705579c4035b864db8d06a3efd6"], "layout": "IPY_MODEL_800461f0e86545e6a99fbb868e028a27"}}, "e73ae12084e848a8aa82e73c5efa9d85": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ece8d196afd24a0cae98b6233f7e13cf": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ed980dda0e504e29ae8ca06e1535073b": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ee2af32e73d542f9b089d761eed9d801": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f097c1e989394c7f8a226f08ff7a6e9a": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f5ee29b85e664ef180df1d1a60db8223": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f5fb96b55ae943d5b0a12cef560be493": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f68e5c7237cd4260be1644f4a33ba4b9": {"model_module": "@jupyter-widgets/base", "model_module_version": "1.2.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fe63f948843240c184084e159a8feef3": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "ff667ca8001e44b0a6146daf3de94e55": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ed980dda0e504e29ae8ca06e1535073b", "max": 129, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_680c3c2c0ea24be0b7a29ad770f28abd", "value": 129}}, "ffa5224eb65142d3a3cd053fd43c14b7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "1.5.0", "model_name": "DescriptionStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "state": {}}}}, "nbformat": 4, "nbformat_minor": 0}