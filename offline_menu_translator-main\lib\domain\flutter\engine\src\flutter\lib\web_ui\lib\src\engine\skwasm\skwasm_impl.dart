// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

@DefaultAsset('skwasm')
// The web_sdk/sdk_rewriter.dart uses this directive.
library skwasm_impl;

import 'dart:ffi';

export 'skwasm_impl/canvas.dart';
export 'skwasm_impl/codecs.dart';
export 'skwasm_impl/filters.dart';
export 'skwasm_impl/font_collection.dart';
export 'skwasm_impl/image.dart';
export 'skwasm_impl/memory.dart';
export 'skwasm_impl/paint.dart';
export 'skwasm_impl/paragraph.dart';
export 'skwasm_impl/path.dart';
export 'skwasm_impl/path_metrics.dart';
export 'skwasm_impl/picture.dart';
export 'skwasm_impl/raw/raw_canvas.dart';
export 'skwasm_impl/raw/raw_filters.dart';
export 'skwasm_impl/raw/raw_fonts.dart';
export 'skwasm_impl/raw/raw_geometry.dart';
export 'skwasm_impl/raw/raw_image.dart';
export 'skwasm_impl/raw/raw_memory.dart';
export 'skwasm_impl/raw/raw_paint.dart';
export 'skwasm_impl/raw/raw_path.dart';
export 'skwasm_impl/raw/raw_path_metrics.dart';
export 'skwasm_impl/raw/raw_picture.dart';
export 'skwasm_impl/raw/raw_shaders.dart';
export 'skwasm_impl/raw/raw_skdata.dart';
export 'skwasm_impl/raw/raw_skstring.dart';
export 'skwasm_impl/raw/raw_surface.dart';
export 'skwasm_impl/raw/raw_vertices.dart';
export 'skwasm_impl/raw/skwasm_module.dart';
export 'skwasm_impl/raw/text/raw_line_metrics.dart';
export 'skwasm_impl/raw/text/raw_paragraph.dart';
export 'skwasm_impl/raw/text/raw_paragraph_builder.dart';
export 'skwasm_impl/raw/text/raw_paragraph_style.dart';
export 'skwasm_impl/raw/text/raw_strut_style.dart';
export 'skwasm_impl/raw/text/raw_text_style.dart';
export 'skwasm_impl/renderer.dart';
export 'skwasm_impl/shaders.dart';
export 'skwasm_impl/surface.dart';
export 'skwasm_impl/vertices.dart';
