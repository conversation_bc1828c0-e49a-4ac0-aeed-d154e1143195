// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'dart:io';

import 'package:path/path.dart' as path;

String _flutterBuildDirectoryPath() {
  assert(Platform.environment.containsKey('FLUTTER_BUILD_DIRECTORY'));

  return Platform.environment['FLUTTER_BUILD_DIRECTORY']!;
}

const String _testPath = 'gen/flutter/lib/ui/fixtures/shaders';

/// Gets the [Directory] shader files that are generated by `lib/ui/fixtures/shaders`.
///
/// `folderName` is a leaf folder within the generated directory.
Directory shaderDirectory(String leafFolderName) {
  return Directory(
    path.joinAll(<String>[
      ...path.split(_flutterBuildDirectoryPath()),
      ...path.split(_testPath),
      leafFolderName,
    ]),
  );
}
