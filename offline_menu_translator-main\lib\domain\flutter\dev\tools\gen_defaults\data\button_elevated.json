{"version": "6_1_0", "md.comp.elevated-button.container.color": "surfaceContainerLow", "md.comp.elevated-button.container.elevation": "md.sys.elevation.level1", "md.comp.elevated-button.container.height": 40.0, "md.comp.elevated-button.container.shadow-color": "shadow", "md.comp.elevated-button.container.shape": "md.sys.shape.corner.full", "md.comp.elevated-button.disabled.container.color": "onSurface", "md.comp.elevated-button.disabled.container.elevation": "md.sys.elevation.level0", "md.comp.elevated-button.disabled.container.opacity": 0.12, "md.comp.elevated-button.disabled.label-text.color": "onSurface", "md.comp.elevated-button.disabled.label-text.opacity": 0.38, "md.comp.elevated-button.focus.container.elevation": "md.sys.elevation.level1", "md.comp.elevated-button.focus.indicator.color": "secondary", "md.comp.elevated-button.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.elevated-button.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.elevated-button.focus.label-text.color": "primary", "md.comp.elevated-button.focus.state-layer.color": "primary", "md.comp.elevated-button.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.elevated-button.hover.container.elevation": "md.sys.elevation.level2", "md.comp.elevated-button.hover.label-text.color": "primary", "md.comp.elevated-button.hover.state-layer.color": "primary", "md.comp.elevated-button.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.elevated-button.label-text.color": "primary", "md.comp.elevated-button.label-text.text-style": "labelLarge", "md.comp.elevated-button.pressed.container.elevation": "md.sys.elevation.level1", "md.comp.elevated-button.pressed.label-text.color": "primary", "md.comp.elevated-button.pressed.state-layer.color": "primary", "md.comp.elevated-button.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.elevated-button.with-icon.disabled.icon.color": "onSurface", "md.comp.elevated-button.with-icon.disabled.icon.opacity": 0.38, "md.comp.elevated-button.with-icon.focus.icon.color": "primary", "md.comp.elevated-button.with-icon.hover.icon.color": "primary", "md.comp.elevated-button.with-icon.icon.color": "primary", "md.comp.elevated-button.with-icon.icon.size": 18.0, "md.comp.elevated-button.with-icon.pressed.icon.color": "primary"}