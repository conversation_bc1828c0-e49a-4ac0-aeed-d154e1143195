{"version": "6_1_0", "md.comp.filled-tonal-button.container.color": "secondaryContainer", "md.comp.filled-tonal-button.container.elevation": "md.sys.elevation.level0", "md.comp.filled-tonal-button.container.height": 40.0, "md.comp.filled-tonal-button.container.shadow-color": "shadow", "md.comp.filled-tonal-button.container.shape": "md.sys.shape.corner.full", "md.comp.filled-tonal-button.disabled.container.color": "onSurface", "md.comp.filled-tonal-button.disabled.container.elevation": "md.sys.elevation.level0", "md.comp.filled-tonal-button.disabled.container.opacity": 0.12, "md.comp.filled-tonal-button.disabled.label-text.color": "onSurface", "md.comp.filled-tonal-button.disabled.label-text.opacity": 0.38, "md.comp.filled-tonal-button.focus.container.elevation": "md.sys.elevation.level0", "md.comp.filled-tonal-button.focus.indicator.color": "secondary", "md.comp.filled-tonal-button.focus.indicator.outline.offset": "md.sys.state.focus-indicator.outer-offset", "md.comp.filled-tonal-button.focus.indicator.thickness": "md.sys.state.focus-indicator.thickness", "md.comp.filled-tonal-button.focus.label-text.color": "onSecondaryContainer", "md.comp.filled-tonal-button.focus.state-layer.color": "onSecondaryContainer", "md.comp.filled-tonal-button.focus.state-layer.opacity": "md.sys.state.focus.state-layer-opacity", "md.comp.filled-tonal-button.hover.container.elevation": "md.sys.elevation.level1", "md.comp.filled-tonal-button.hover.label-text.color": "onSecondaryContainer", "md.comp.filled-tonal-button.hover.state-layer.color": "onSecondaryContainer", "md.comp.filled-tonal-button.hover.state-layer.opacity": "md.sys.state.hover.state-layer-opacity", "md.comp.filled-tonal-button.label-text.color": "onSecondaryContainer", "md.comp.filled-tonal-button.label-text.text-style": "labelLarge", "md.comp.filled-tonal-button.pressed.container.elevation": "md.sys.elevation.level0", "md.comp.filled-tonal-button.pressed.label-text.color": "onSecondaryContainer", "md.comp.filled-tonal-button.pressed.state-layer.color": "onSecondaryContainer", "md.comp.filled-tonal-button.pressed.state-layer.opacity": "md.sys.state.pressed.state-layer-opacity", "md.comp.filled-tonal-button.with-icon.disabled.icon.color": "onSurface", "md.comp.filled-tonal-button.with-icon.disabled.icon.opacity": 0.38, "md.comp.filled-tonal-button.with-icon.focus.icon.color": "onSecondaryContainer", "md.comp.filled-tonal-button.with-icon.hover.icon.color": "onSecondaryContainer", "md.comp.filled-tonal-button.with-icon.icon.color": "onSecondaryContainer", "md.comp.filled-tonal-button.with-icon.icon.size": 18.0, "md.comp.filled-tonal-button.with-icon.pressed.icon.color": "onSecondaryContainer"}