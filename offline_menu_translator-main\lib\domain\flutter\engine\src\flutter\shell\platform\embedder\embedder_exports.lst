# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# Linker script that exports the minimal symbols required for the embedder library

{
  global:
    Flutter*;
    __Flutter*;
    kFlutter*;
    kDartIsolateSnapshotData;
    kDartIsolateSnapshotInstructions;
    kDartVmSnapshotData;
    kDartVmSnapshotInstructions;
    InternalFlutterGpu*;
    kInternalFlutterGpu*;
  local:
    *;
};
