# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/testing/android/native_activity/native_activity.gni")
import("//flutter/vulkan/config.gni")
import("../../../tools/impeller.gni")

impeller_component("vulkan_unittests") {
  testonly = true
  sources = [
    "allocator_vk_unittests.cc",
    "command_encoder_vk_unittests.cc",
    "command_pool_vk_unittests.cc",
    "context_vk_unittests.cc",
    "descriptor_pool_vk_unittests.cc",
    "driver_info_vk_unittests.cc",
    "fence_waiter_vk_unittests.cc",
    "formats_vk_unittests.cc",
    "pipeline_cache_data_vk_unittests.cc",
    "render_pass_builder_vk_unittests.cc",
    "render_pass_cache_unittests.cc",
    "render_pass_vk_unittests.cc",
    "resource_manager_vk_unittests.cc",
    "surface_context_vk_unittests.cc",
    "test/gpu_tracer_unittests.cc",
    "test/mock_vulkan.cc",
    "test/mock_vulkan.h",
    "test/mock_vulkan_unittests.cc",
    "test/sampler_library_vk_unittests.cc",
    "test/swapchain_unittests.cc",
  ]
  deps = [
    ":vulkan",
    "../../../playground:playground_test",
    "//flutter/testing:testing_lib",
  ]
}

impeller_component("vulkan") {
  sources = [
    "allocator_vk.cc",
    "allocator_vk.h",
    "barrier_vk.cc",
    "barrier_vk.h",
    "blit_pass_vk.cc",
    "blit_pass_vk.h",
    "capabilities_vk.cc",
    "capabilities_vk.h",
    "command_buffer_vk.cc",
    "command_buffer_vk.h",
    "command_pool_vk.cc",
    "command_pool_vk.h",
    "command_queue_vk.cc",
    "command_queue_vk.h",
    "compute_pass_vk.cc",
    "compute_pass_vk.h",
    "compute_pipeline_vk.cc",
    "compute_pipeline_vk.h",
    "context_vk.cc",
    "context_vk.h",
    "debug_report_vk.cc",
    "debug_report_vk.h",
    "descriptor_pool_vk.cc",
    "descriptor_pool_vk.h",
    "device_buffer_vk.cc",
    "device_buffer_vk.h",
    "device_holder_vk.h",
    "driver_info_vk.cc",
    "driver_info_vk.h",
    "fence_waiter_vk.cc",
    "fence_waiter_vk.h",
    "formats_vk.cc",
    "formats_vk.h",
    "gpu_tracer_vk.cc",
    "gpu_tracer_vk.h",
    "limits_vk.h",
    "pipeline_cache_data_vk.cc",
    "pipeline_cache_data_vk.h",
    "pipeline_cache_vk.cc",
    "pipeline_cache_vk.h",
    "pipeline_library_vk.cc",
    "pipeline_library_vk.h",
    "pipeline_vk.cc",
    "pipeline_vk.h",
    "queue_vk.cc",
    "queue_vk.h",
    "render_pass_builder_vk.cc",
    "render_pass_builder_vk.h",
    "render_pass_vk.cc",
    "render_pass_vk.h",
    "resource_manager_vk.cc",
    "resource_manager_vk.h",
    "sampler_library_vk.cc",
    "sampler_library_vk.h",
    "sampler_vk.cc",
    "sampler_vk.h",
    "shader_function_vk.cc",
    "shader_function_vk.h",
    "shader_library_vk.cc",
    "shader_library_vk.h",
    "shared_object_vk.cc",
    "shared_object_vk.h",
    "surface_context_vk.cc",
    "surface_context_vk.h",
    "swapchain/khr/khr_swapchain_image_vk.cc",
    "swapchain/khr/khr_swapchain_image_vk.h",
    "swapchain/khr/khr_swapchain_impl_vk.cc",
    "swapchain/khr/khr_swapchain_impl_vk.h",
    "swapchain/khr/khr_swapchain_vk.cc",
    "swapchain/khr/khr_swapchain_vk.h",
    "swapchain/surface_vk.cc",
    "swapchain/surface_vk.h",
    "swapchain/swapchain_transients_vk.cc",
    "swapchain/swapchain_transients_vk.h",
    "swapchain/swapchain_vk.cc",
    "swapchain/swapchain_vk.h",
    "texture_source_vk.cc",
    "texture_source_vk.h",
    "texture_vk.cc",
    "texture_vk.h",
    "tracked_objects_vk.cc",
    "tracked_objects_vk.h",
    "vertex_descriptor_vk.cc",
    "vertex_descriptor_vk.h",
    "vk.h",
    "vma.cc",
    "vma.h",
    "workarounds_vk.cc",
    "workarounds_vk.h",
    "yuv_conversion_library_vk.cc",
    "yuv_conversion_library_vk.h",
    "yuv_conversion_vk.cc",
    "yuv_conversion_vk.h",
  ]

  if (is_android) {
    sources += [
      "android/ahb_texture_source_vk.cc",
      "android/ahb_texture_source_vk.h",
      "swapchain/ahb/ahb_formats.h",
      "swapchain/ahb/ahb_swapchain_impl_vk.cc",
      "swapchain/ahb/ahb_swapchain_impl_vk.h",
      "swapchain/ahb/ahb_swapchain_vk.cc",
      "swapchain/ahb/ahb_swapchain_vk.h",
      "swapchain/ahb/ahb_texture_pool_vk.cc",
      "swapchain/ahb/ahb_texture_pool_vk.h",
      "swapchain/ahb/external_semaphore_vk.cc",
      "swapchain/ahb/external_semaphore_vk.h",
    ]
  }

  public_deps = [
    "../../:renderer",
    "../../../shader_archive",
    "//flutter/flutter_vma",
    "//flutter/fml",
    "//flutter/third_party/vulkan-deps/vulkan-headers/src:vulkan_headers",
    "//flutter/third_party/vulkan_memory_allocator",
  ]

  if (is_android) {
    public_deps += [ "../../../toolkit/android" ]
  }
}

if (is_android) {
  config("public_android_config") {
    defines = [ "__ANDROID_UNAVAILABLE_SYMBOLS_ARE_WEAK__" ]
  }

  test_fixtures("unittests_fixtures") {
    fixtures = []
  }

  source_set("unittests_lib") {
    visibility = [ ":*" ]

    testonly = true

    sources = [ "android/ahb_texture_source_vk_unittests.cc" ]

    deps = [
      ":unittests_fixtures",
      ":vulkan",
      "//flutter/testing",
    ]

    defines = [ "TESTING" ]
  }

  executable("vulkan_android_unittests") {
    assert(is_android)

    testonly = true

    output_name = "impeller_vulkan_android_unittests"

    deps = [ ":unittests_lib" ]
  }

  native_activity_apk("vulkan_android_apk_unittests") {
    apk_name = "impeller_vulkan_android_unittests"

    testonly = true

    deps = [
      ":unittests_lib",
      "//flutter/testing/android/native_activity:gtest_activity",
    ]
  }
}
