{"cells": [{"cell_type": "markdown", "metadata": {"id": "cSrJYrFrY2aj"}, "source": ["##### Copyright 2024 Google LLC."]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "i1PHqD-ZY4-c"}, "outputs": [], "source": ["# @title Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "# https://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "metadata": {"id": "YNDq8NbCY7oh"}, "source": ["# Workshop: How to Fine-tuning Gemma\n", "\n", "To illustrate fine-tuning the model for a specific task, You'll learn how to condition a Gemma model to answer in a specific language. Let's consider the example of generating a random Portuguese title based on a user's instruction such as \"Write a title\". To make this possible, you will curate a manageable dataset that can be manually processed. This approach is feasible because Gemma 2 has prior knowledge of general Portuguese language patterns, enabling it to adapt to this specific task effectively.\n", "\n", "<table class=\"tfo-notebook-buttons\" align=\"left\">\n", "  <td>\n", "    <a target=\"_blank\" href=\"https://colab.research.google.com/github/google-gemini/gemma-cookbook/blob/main/Workshops/Workshop_How_to_Fine_tuning_Gemma.ipynb\"><img src=\"https://www.tensorflow.org/images/colab_logo_32px.png\" />Run in Google Colab</a>\n", "  </td>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {"id": "u4EM3g9u2_KA"}, "source": ["## What is Fine-tuning\n", "\n", "In the first place, you have to understand what is fine-tuning. It's a specialized form of [transfer learning](https://en.wikipedia.org/wiki/Transfer_learning). It involves taking a pre-trained language model - one that has already been exposed to a vast corpus of text data and learned the general patterns and structures of language - and further training it on a smaller, more specific dataset. This additional training allows the model to adapt and refine its knowledge, making it better suited for a particular task or domain.\n", "\n", "Imagine you are a skilled gamer who excels at various genres, from action-adventures to strategy games. Fine-tuning is akin to taking you and having you focus intensely on mastering a specific game, like a complex real-time strategy (RTS) title. You already possess a strong foundation of gaming skills and knowledge, but the dedicated practice and study within the RTS genre sharpens your tactics, understanding of game mechanics, and overall proficiency within that particular realm.\n", "\n", "Similarly, pre-trained language models have a broad understanding of language, but fine-tuning helps them specialize. By exposing them to a curated dataset relevant to your desired application, you guide the model to learn the nuances and intricacies specific to that domain. It's like giving the model a crash course in the language of your chosen field, enabling it to perform tasks with greater accuracy and fluency.\n"]}, {"cell_type": "markdown", "metadata": {"id": "3rzH5Ugf5RlJ"}, "source": ["## Setup\n", "\n", "### Select the Colab runtime\n", "To complete this tutorial, you'll need to have a Colab runtime with sufficient resources to run the Gemma model:\n", "\n", "1. In the upper-right of the Colab window, select **▾ (Additional connection options)**.\n", "2. Select **Change runtime type**.\n", "3. Under **Hardware accelerator**, select **T4 GPU**.\n", "\n", "\n", "### Gemma setup on <PERSON><PERSON>\n", "To complete this tutorial, you'll first need to complete the setup instructions at [Gemma setup](https://ai.google.dev/gemma/docs/setup). The Gemma setup instructions show you how to do the following:\n", "\n", "* Get access to <PERSON> on kaggle.com.\n", "* Select a Colab runtime with sufficient resources to run the Gemma 2B model.\n", "* Generate and configure a Kaggle username and API key.\n", "\n", "After you've completed the Gemma setup, move on to the next section, where you'll set environment variables for your Colab environment."]}, {"cell_type": "markdown", "metadata": {"id": "URMuBzkMVxpU"}, "source": ["### Set environemnt variables\n", "\n", "Set environment variables for ```<PERSON>AGGLE_USERNAME``` and ```KAGGLE_KEY```."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "IUOX2hqjV7Ku"}, "outputs": [], "source": ["import os\n", "from google.colab import userdata, drive\n", "\n", "# Note: `userdata.get` is a Colab API. If you're not using Colab, set the env\n", "# vars as appropriate for your system.\n", "os.environ[\"KAGGLE_USERNAME\"] = userdata.get(\"KAGGLE_USERNAME\")\n", "os.environ[\"KAGGLE_KEY\"] = userdata.get(\"KAGGLE_KEY\")\n", "\n", "# Mounting gDrive for to store artifacts\n", "drive.mount(\"/content/drive\")"]}, {"cell_type": "markdown", "metadata": {"id": "LXfDwRTQVns2"}, "source": ["### Install dependencies\n", "\n", "Install Keras and KerasNLP"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zHs7wpZusEML"}, "outputs": [], "source": ["!pip install -q -U keras-nlp datasets\n", "!pip install -q -U keras\n", "\n", "# Set the backbend before importing Ke<PERSON>\n", "os.environ[\"KERAS_BACKEND\"] = \"jax\"\n", "# Avoid memory fragmentation on JAX backend.\n", "os.environ[\"XLA_PYTHON_CLIENT_MEM_FRACTION\"] = \"1.00\"\n", "\n", "import keras_nlp\n", "import keras\n", "\n", "# Run at half precision.\n", "#keras.config.set_floatx(\"bfloat16\")\n", "\n", "# Training Configurations\n", "token_limit = 128\n", "num_data_limit = 100\n", "lora_name = \"my_lora\"\n", "lora_rank = 4\n", "lr_value = 1e-3\n", "train_epoch = 5\n", "model_id = \"gemma2_instruct_2b_en\""]}, {"cell_type": "markdown", "metadata": {"id": "kUl0t469YfQY"}, "source": ["## Load Model\n", "\n", "**Why Fine-tuning?**\n", "\n", "Before embarking on fine-tuning, it's crucial to evaluate if its benefits align with the specific requirements of your application. Fine-tuning involves meticulous data preparation and extensive training, making it an arduous process. Therefore, it's essential to assess whether the potential gains justify the significant effort required.\n", "\n", "**Try \"Prompt Engineering\" first.** before fine-tuning\n", "\n", "Would you like to enable <PERSON>'s multilingual capabilities?\n", "Please note that Gemma 2 already has some multilingual capabilities. Here's the example output from Gemma 2 2B instruction-tuned model.\n", "\n", "Do you wish to adjust the tone or writing style?\n", "Gemma 2 might be familiar with the writing style you have in mind. Here's another output from the same model."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Gm4jIEqmYfQY"}, "outputs": [], "source": ["import keras\n", "import keras_nlp\n", "\n", "import time\n", "\n", "gemma_lm = keras_nlp.models.GemmaCausalLM.from_preset(model_id)\n", "gemma_lm.summary()\n", "\n", "tick_start = 0\n", "\n", "def tick():\n", "    global tick_start\n", "    tick_start = time.time()\n", "\n", "def tock():\n", "    print(f\"TOTAL TIME ELAPSED: {time.time() - tick_start:.2f}s\")\n", "\n", "def text_gen(prompt):\n", "    tick()\n", "    input = f\"<start_of_turn>user\\n{prompt}<end_of_turn>\\n<start_of_turn>model\\n\"\n", "    output = gemma_lm.generate(input, max_length=token_limit)\n", "    print(\"\\nGemma output:\")\n", "    print(output)\n", "    tock()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "o3a-0aezxZ1D"}, "outputs": [], "source": ["# inference before fine-tuning\n", "text_gen(\"Translate the text below to Portuguese.\\n\\\"Hi, how can I get to the MASP museum?\\\"\")\n", "text_gen(\"Speak like a pirate. Teach me why the earth is flat.\")\n", "text_gen(\"Write a title\")\n", "text_gen(\"Write a poem\")"]}, {"cell_type": "markdown", "metadata": {"id": "kpubX_7zUSDc"}, "source": ["## What is a Tokenizer\n", "\n", "A tokenizer is a crucial component that bridges the gap between human language and the numerical representations that AI models can understand. It's essentially a tool that breaks down text into smaller units, called tokens, which can then be processed by the model.\n", "\n", "Think of it like this:\n", "\n", "- Humans: Communicate using words, sentences, and paragraphs.\n", "- LLMs: They \"think\" in terms of numbers and vectors.\n", "\n", "The tokenizer acts as a translator between these two worlds. It takes human-readable text and converts it into a format that the LLM can understand and work with.\n", "\n", "\n", "\n", "Gemma tokenizer is based on [Sentence<PERSON><PERSON><PERSON>](https://github.com/google/sentencepiece). The size of the vocabulary is predetermined before training. SentencePiece then learns the optimal subword segmentation based on the chosen vocabulary size and the training data.\n", "\n", "<PERSON>'s large 256k vocabulary allows it to handle diverse text inputs and potentially improve performance on various tasks, e.g. handling multilingual text inputs.\n", "\n", "<PERSON>'s tokenizer uses byte-level encoding, which means it can handle any character from any language, even those with different writing systems (like Chinese, Japanese, or Korean).\n", "\n", "This avoids the limitations of character-based tokenizers, which might struggle with languages that have a vast number of characters."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "xR1riO7NURFF"}, "outputs": [], "source": ["tokenizer = keras_nlp.models.GemmaTokenizer.from_preset(model_id)\n", "import jax\n", "\n", "def detoken(tokens):\n", "  print(tokens)\n", "  for x in tokens:\n", "    word = tokenizer.detokenize(jax.numpy.array([x]))\n", "    print(f\"{x:6} -> {word}\")\n", "\n", "# (example text: <PERSON><PERSON>, <PERSON> to meet you. The weather is really nice today.”)\n", "detoken(tokenizer(\"ol<PERSON><PERSON> Prazer em conhecê-lo. O tempo está muito bom hoje.\"))\n", "print()\n", "detoken(tokenizer(\"नमस्ते। आपसे मिलकर अच्छा लगा। आज मौसम सचमुच अच्छा है.\"))"]}, {"cell_type": "markdown", "metadata": {"id": "9T7xe_jzslv4"}, "source": ["## Load Dataset\n", "\n", "How many datasets do you need? You can start with a relatively small dataset, approximately 10 to 20, those can have a significant impact on a model's behavior.\n", "\n", "To improve the output quality, a target of around 200 total examples is recommended. Nevertheless, the amount of data required for tuning really depends on how much you want to influence the model's behavior. Our recommendation is to commence with a limited amount of data and gradually incorporate additional data into the training process until the desired behavior is achieved."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ZiS-KU9osh_N"}, "outputs": [], "source": ["tokenizer = keras_nlp.models.GemmaTokenizer.from_preset(model_id)\n", "\n", "# example titles\n", "data = [\n", "    \"O Alquimista\",  # by <PERSON>\n", "    \"<PERSON> Casmurro\",  # by <PERSON><PERSON><PERSON>\n", "    \"Memorial do Convento\",  # by <PERSON>\n", "    \"A Hora da Estrela\",  # by <PERSON><PERSON><PERSON>\n", "    \"Vidas Secas\",  # by <PERSON><PERSON><PERSON><PERSON>\n", "    \"O Cortiço\",  # by <PERSON><PERSON><PERSON><PERSON>\n", "    \"Grande Sertão: Veredas\",  # by <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    \"Capitães da Areia\",  # by <PERSON>\n", "    \"A Sibila\",  # by <PERSON><PERSON><PERSON>\n", "    \"Os Maias\",  # by <PERSON><PERSON>\n", "    \"O Crime do Padre Amaro\",  # by <PERSON><PERSON>\n", "    \"A Relíquia\",  # by <PERSON><PERSON>\n", "    \"O Primo Basílio\",  # by <PERSON><PERSON>\n", "    \"A Ilustre Casa de Ramires\",  # by <PERSON><PERSON>\n", "    \"A Cidade e as Serras\"  # by <PERSON><PERSON> de <PERSON>ós\n", "]\n", "\n", "train = []\n", "\n", "for x in data:\n", "  item = f\"<start_of_turn>user\\nWrite a title<end_of_turn>\\n<start_of_turn>model\\n{x}<end_of_turn>\"\n", "  length = len(tokenizer(item))\n", "  # skip data if the token length is longer than our limit\n", "  if length < token_limit:\n", "    train.append(item)\n", "    if(len(train)>=num_data_limit):\n", "      break\n", "\n", "print(len(train))\n", "print(train[0])\n", "print()\n", "print(train[1])\n", "print()\n", "print(train[2])"]}, {"cell_type": "markdown", "metadata": {"id": "9s1o96HRtwV_"}, "source": ["See below example code, using HF datasets, if your datasets are much bigger."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "BxSqXLeCt9Uu"}, "outputs": [], "source": ["# Example of using HF datasets\n", "# Replying to request emails that a bakery business might get, in Korean\n", "# The given prompt means \"Please write an email reply with below.\"\n", "'''\n", "tokenizer = keras_nlp.models.GemmaTokenizer.from_preset(model_id)\n", "\n", "# prompt structure\n", "# <start_of_turn>user\n", "# 다음에 대한 이메일 답장을 작성해줘.\n", "# \"{EMAIL CONTENT FROM THE CUSTOMER}\"\n", "# <end_of_turn>\n", "# <start_of_turn>model\n", "# {MODEL ANSWER}<end_of_turn>\n", "\n", "# input, output\n", "from datasets import load_dataset\n", "ds = load_dataset(\n", "    \"bebechien/korean_cake_boss\",\n", "    split=\"train\",\n", ")\n", "print(ds)\n", "data = ds.with_format(\"np\", columns=[\"input\", \"output\"], output_all_columns=False)\n", "train = []\n", "\n", "for x in data:\n", "  item = f\"<start_of_turn>user\\n다음에 대한 이메일 답장을 작성해줘.\\n\\\"{x['input']}\\\"<end_of_turn>\\n<start_of_turn>model\\n{x['output']}<end_of_turn>\"\n", "  length = len(tokenizer(item))\n", "  # skip data if the token length is longer than our limit\n", "  if length < token_limit:\n", "    train.append(item)\n", "    if(len(train)>=num_data_limit):\n", "      break\n", "\n", "print(len(train))\n", "print(train[0])\n", "print(train[1])\n", "print(train[2])\n", "'''"]}, {"cell_type": "markdown", "metadata": {"id": "5NTIrFbJ3dBv"}, "source": ["In the context of a small dataset, the primary concern is that the model may prioritize memorizing specific examples rather than generalizing well to new and unobserved data. This limitation highlights the importance of utilizing a larger dataset during fine-tuning, as it enhances the model's ability to capture broader patterns and relationships."]}, {"cell_type": "markdown", "metadata": {"id": "th0WS33gayn9"}, "source": ["## <PERSON><PERSON> Fine-tuning"]}, {"cell_type": "markdown", "metadata": {"id": "ugc2ub4nau1j"}, "source": ["![lora.png](data:image/png;base64,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)"]}, {"cell_type": "markdown", "metadata": {"id": "Pt7Nr6a7tItO"}, "source": ["Fine-tuning a model involves updating its weights (also called parameters). LLMs have a lot of weights. The Gemma 2 2B that is being used in this notebook has 2,617,270,528 parameters!\n", "\n", "Changing all of them can take quite some time and requires a lot of resources.\n", "\n", "To mitigate this issue, you are going to use a technique called: [LoRA: Low-Rank Adaptation](https://arxiv.org/abs/2106.09685)\n", "\n", "This technique, in summary, helps lower the number of trained weights needed by a lot, making fine-tuning more accessible.\n", "\n", "The key parameter used is the `rank`. In this notebook it set to 4 but you can use higher numbers to get better results but, of course, needed more resources.\n", "\n", "**TIP**: Train your model with lower ranks and evaluate the performance improvemnet on your task. Gradually increase the rank in subsequent trials and see if that further boosts performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "RCucu6oHz53G"}, "outputs": [], "source": ["# Enable LoRA for the model and set the LoRA rank to 4.\n", "gemma_lm.backbone.enable_lora(rank=lora_rank)\n", "gemma_lm.summary()\n", "\n", "# Limit the input sequence length (to control memory usage).\n", "gemma_lm.preprocessor.sequence_length = token_limit\n", "# Use AdamW (a common optimizer for transformer models).\n", "optimizer = keras.optimizers.AdamW(\n", "    learning_rate=lr_value,\n", "    weight_decay=0.01,\n", ")\n", "# Exclude layernorm and bias terms from decay.\n", "optimizer.exclude_from_weight_decay(var_names=[\"bias\", \"scale\"])\n", "\n", "gemma_lm.compile(\n", "    loss=keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "    optimizer=optimizer,\n", "    weighted_metrics=[keras.metrics.SparseCategoricalAccuracy()],\n", ")\n"]}, {"cell_type": "markdown", "metadata": {"id": "hQQ47kcdpbZ9"}, "source": ["Note that enabling LoRA reduces the number of trainable parameters significantly.\n", "\n", "From 2,617,270,528 to **2,928,640**\n", "\n", "To monitor the learning progress, you will evaluate the model at the end of each epoch and save the lora weights."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "9G9sULJE5vpw"}, "outputs": [], "source": ["!ls"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pc-Nea7L1fBc"}, "outputs": [], "source": ["# !cd drive\n", "!mkdir -p ./drive/MyDrive/gemma_workshop\n", "# !cd .."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "26d9npFhAOSp"}, "outputs": [], "source": ["class CustomCallback(keras.callbacks.Callback):\n", "  def on_epoch_end(self, epoch, logs=None):\n", "    model_name = f\"./drive/MyDrive/gemma_workshop/{lora_name}_{lora_rank}_epoch{epoch+1}.lora.h5\"\n", "    gemma_lm.backbone.save_lora_weights(model_name)\n", "\n", "    # Evaluate\n", "    text_gen(\"Write a title\")\n", "    text_gen(\"Write a poem\")\n", "\n", "history = gemma_lm.fit(train, epochs=train_epoch, batch_size=2, callbacks=[CustomCallback()])\n", "\n", "import matplotlib.pyplot as plt\n", "plt.plot(history.history['loss'])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "tn-jgVULyBXq"}, "source": ["Note that the model began to grasp our intent more effectively from Epoch #3 onwards.\n", "\n", "To compare and contrast, it was utlized the \"Write a poem\" prompt. Interestingly, in Epoch #5, the model began to generate Portuguese in response to that prompt. This shift indicates a strong influence of our training dataset on the model's behavior. However, depending on your application, such a significat change might not be desirable. In such cases, Epoch #4 would be a more suitable choice."]}, {"cell_type": "markdown", "metadata": {"id": "P-tVAKmda2Zt"}, "source": ["## <PERSON><PERSON> LoRA\n", "\n", "Use the code below if you shared LoRA weights. It's much more lightweight than the model files themselves - for instance, a LoRA rank 4 weights file for a 10gb model might only be on the order of a few megabytes, easily shared over email."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hSW8-HRMa4ZB"}, "outputs": [], "source": ["# Example Code for Load LoRA\n", "'''\n", "gemma_lm = keras_nlp.models.GemmaCausalLM.from_preset(model_id)\n", "# Use the same LoRA rank that you trained\n", "gemma_lm.backbone.enable_lora(rank=4)\n", "train_epoch = 3\n", "# Load pre-trained LoRA weights\n", "gemma_lm.backbone.load_lora_weights(f\"/content/drive/MyDrive/{lora_name}_{lora_rank}_epoch{train_epoch}.lora.h5\")\n", "'''"]}, {"cell_type": "markdown", "metadata": {"id": "ipg1u_wEKTxG"}, "source": ["## Try a different sampler\n", "\n", "The top-K algorithm randomly picks the next token from the tokens of top K probability."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "nV5mD_HqKZRF"}, "outputs": [], "source": ["gemma_lm.compile(sampler=\"top_k\")\n", "text_gen(\"Write a title\")\n", "text_gen(\"Write a title\")\n", "text_gen(\"Write a title\")\n", "text_gen(\"Write a title\")\n", "text_gen(\"Write a title\")"]}, {"cell_type": "markdown", "metadata": {"id": "3m1XaCrlMu3Y"}, "source": ["Try a slight different prompts"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "qC-MLxYWM1HU"}, "outputs": [], "source": ["text_gen(\"Write a music title\")\n", "text_gen(\"Write a poem title\")\n", "text_gen(\"Write a blog title\")\n", "text_gen(\"Write a movie title\")\n", "text_gen(\"Write a novel title\")"]}, {"cell_type": "markdown", "metadata": {"id": "aEptDCED9tVp"}, "source": ["## Publish your model"]}, {"cell_type": "markdown", "metadata": {"id": "T3Qhrlyy5ReL"}, "source": ["Lets save our model. It takes some time (~11 minutes) as it is a very large file"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4TcvzBH995FE"}, "outputs": [], "source": ["# Save the finetuned model\n", "my_model_name = \"my_gemma2_pt\"\n", "gemma_lm.save_to_preset(f\"./{my_model_name}\")"]}, {"cell_type": "markdown", "metadata": {"id": "xQ4de1a79zy0"}, "source": ["## Publishing on Hugging Face\n", "\n", "To publish your model on Hugging Face, you'll need to add your hugging face user (`HF_USER`) and an access token with write permission (`HF_TOKEN`) to the your secret keys."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "7rNj6rpB9LW4"}, "outputs": [], "source": ["my_hf_username = userdata.get(\"HF_USER\")\n", "os.environ[\"HF_USER\"] = my_hf_username\n", "os.environ[\"HF_TOKEN\"] = userdata.get(\"HF_TOKEN\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "H4v02q0H-QFL"}, "outputs": [], "source": ["# Upload the model to Hugging Face Hub\n", "my_model_name = \"my_gemma2_pt\"\n", "hf_uri = f\"hf://{my_hf_username}/{my_model_name}\"\n", "keras_nlp.upload_preset(hf_uri, f\"./{my_model_name}\")"]}, {"cell_type": "markdown", "metadata": {"id": "JzvyPgpX-XZk"}, "source": ["## Publishing on Kaggle\n", "\n", "To publish on Kaggle you will need you username (already using if you followed the suggested setup process) and a name for your model and run the following cell\n", "\n", "**Note**: After the following cell, the model will be private to you on Kaggle. To share more broadly, change it to public."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "H-T2XQtS-k3y"}, "outputs": [], "source": ["# Upload the model to Kaggle\n", "my_user_name = os.environ[\"KAGGLE_USERNAME\"]\n", "my_model_name = \"my_gemma2_pt\"\n", "kaggle_uri = f\"kaggle://{my_user_name}/gemma/keras/{my_model_name}\"\n", "keras_nlp.upload_preset(kaggle_uri, f\"./{my_model_name}\")"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "Workshop_How_to_Fine_tuning_Gemma.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}