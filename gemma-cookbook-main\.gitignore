# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
# Usually these files are written by a Python script from a template
# before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
*.py,cover
.cache
nosetests.xml
coverage/
*.cover
.hypothesis/

# Pytest cache
.pytest_cache/
.cache/

# MyPy cache
.mypy_cache/

# Profiling data
*.lprof
.prof

# Virtual environment directories
venv/
ENV/
env/
.venv/
env.bak/
venv.bak/

# Jupyter Notebook checkpoints
.ipynb_checkpoints

# pyenv
.python-version

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# macOS system files
.DS_Store

# Temporary files
*.tmp
*.log
*.bak
*.orig

# Local development overrides
.local/
.env

# Docker-related files
docker-compose.override.yml

*.pyc
k-gemma-it/.env
k-gemma-it/weights/*.h5
k-mail-replier/k_mail_replier/.env
k-mail-replier/k_mail_replier/weights/*.backup
venv/
Gemma/personal-code-assistant/gemma-web-service/.env
