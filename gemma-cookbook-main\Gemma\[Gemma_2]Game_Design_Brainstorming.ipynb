{"cells": [{"cell_type": "markdown", "metadata": {"id": "N0WWINoe_H78"}, "source": ["##### Copyright 2024 Google LLC."]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "KCbtuTFR_Qot"}, "outputs": [], "source": ["# @title Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "# https://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "metadata": {"id": "H3LrD1zZ_T1v"}, "source": ["# Gemma - Game Design Brainstorming\n", "\n", "This cookbook illustrates how you can employ the instruction-tuned model version of <PERSON> as a chatbot to aid in your game design process by fostering brainstorming sessions effectively.\n", "\n", "<table align=\"left\">\n", "  <td>\n", "    <a target=\"_blank\" href=\"https://colab.research.google.com/github/google-gemini/gemma-cookbook/blob/main/Gemma/[Gemma_2]Game_Design_Brainstorming.ipynb\"><img src=\"https://www.tensorflow.org/images/colab_logo_32px.png\" />Run in Google Colab</a>\n", "  </td>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {"id": "00NyqqWcAXw1"}, "source": ["## Setup\n", "\n", "### Select the Colab runtime\n", "To complete this tutorial, you'll need to have a Colab runtime with sufficient resources to run the Gemma model. In this case, you can use a T4 GPU:\n", "\n", "1. In the upper-right of the Colab window, select **▾ (Additional connection options)**.\n", "2. Select **Change runtime type**.\n", "3. Under **Hardware accelerator**, select **T4 GPU**.\n", "\n", "\n", "### Gemma setup on <PERSON><PERSON>\n", "To complete this tutorial, you'll first need to complete the setup instructions at [Gemma setup](https://ai.google.dev/gemma/docs/setup). The Gemma setup instructions show you how to do the following:\n", "\n", "* Get access to <PERSON> on kaggle.com.\n", "* Select a Colab runtime with sufficient resources to run the Gemma 2B model.\n", "* Generate and configure a Kaggle username and API key.\n", "\n", "After you've completed the Gemma setup, move on to the next section, where you'll set environment variables for your Colab environment."]}, {"cell_type": "markdown", "metadata": {"id": "ufH54qq0A7aJ"}, "source": ["### Set environment variables\n", "\n", "Set environment variables for `<PERSON><PERSON><PERSON><PERSON>_USERNAME` and `<PERSON>AGGLE_KEY`."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ybKQhkYrBNkv"}, "outputs": [], "source": ["import os\n", "from google.colab import userdata\n", "\n", "os.environ[\"KERAS_BACKEND\"] = \"jax\"  # Or \"tensorflow\" or \"torch\".\n", "\n", "# Note: `userdata.get` is a Colab API. If you're not using Colab, set the env\n", "# vars as appropriate for your system.\n", "os.environ[\"KAGGLE_USERNAME\"] = userdata.get(\"KAGGLE_USERNAME\")\n", "os.environ[\"KAGGLE_KEY\"] = userdata.get(\"KAGGLE_KEY\")"]}, {"cell_type": "markdown", "metadata": {"id": "BLwEJIILBQ17"}, "source": ["### Install dependencies\n", "\n", "Install Keras and KerasNLP."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "VPurjYcxBTxl"}, "outputs": [], "source": ["# Install Keras 3 last. See https://keras.io/getting_started/ for more details.\n", "!pip install -q -U keras-nlp\n", "!pip install -q -U keras"]}, {"cell_type": "markdown", "metadata": {"id": "QOj_rAJtkOtV"}, "source": ["## Core Concept"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "7Fzu_ah4kR9E"}, "outputs": [], "source": ["# A super concise description of your game\n", "pitch = \"In the heart of a reimagined Joseon Dynasty, where steam-powered machines and clockwork contraptions reign supreme, embark on an epic adventure to unravel the mysteries of a forgotten past and shape the destiny of a nation on the brink of revolution.\""]}, {"cell_type": "markdown", "metadata": {"id": "cwjJp6YZj2jp"}, "source": ["## Load Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "9NQ431sej9Q6"}, "outputs": [], "source": ["import keras\n", "import keras_nlp\n", "\n", "# Run at half precision to fit in memory\n", "keras.config.set_floatx(\"bfloat16\")\n", "\n", "import textwrap\n", "from IPython.display import Markdown\n", "\n", "\n", "def to_markdown(text):\n", "    text = text.replace(\"•\", \"  *\")\n", "    return Markdown(textwrap.indent(text, \"> \", predicate=lambda _: True))\n", "\n", "gemma_lm = keras_nlp.models.GemmaCausalLM.from_preset(\"gemma2_instruct_2b_en\")\n", "gemma_lm.compile(sampler=\"top_k\")\n", "\n", "\n", "class ChatState():\n", "  \"\"\"\n", "  Manages the conversation history for a turn-based chatbot\n", "  Follows the turn-based conversation guidelines for the <PERSON> family of models\n", "  documented at https://ai.google.dev/gemma/docs/formatting\n", "  \"\"\"\n", "\n", "  __START_TURN_USER__ = \"<start_of_turn>user\\n\"\n", "  __START_TURN_MODEL__ = \"<start_of_turn>model\\n\"\n", "  __END_TURN__ = \"<end_of_turn>\\n\"\n", "\n", "  def __init__(self, model, system=\"\"):\n", "    \"\"\"\n", "    Initializes the chat state.\n", "\n", "    Args:\n", "        model: The language model to use for generating responses.\n", "        system: (Optional) System instructions or bot description.\n", "    \"\"\"\n", "    self.model = model\n", "    self.system = system\n", "    self.history = []\n", "\n", "  def add_to_history_as_user(self, message):\n", "      \"\"\"\n", "      Adds a user message to the history with start/end turn markers.\n", "      \"\"\"\n", "      self.history.append(self.__START_TURN_USER__ + message + self.__END_TURN__)\n", "\n", "  def add_to_history_as_model(self, message):\n", "      \"\"\"\n", "      Adds a model response to the history with start/end turn markers.\n", "      \"\"\"\n", "      self.history.append(self.__START_TURN_MODEL__ + message)\n", "\n", "  def get_history(self):\n", "      \"\"\"\n", "      Returns the entire chat history as a single string.\n", "      \"\"\"\n", "      return \"\".join([*self.history])\n", "\n", "  def get_full_prompt(self):\n", "    \"\"\"\n", "    Builds the prompt for the language model, including history and system description.\n", "    \"\"\"\n", "    prompt = self.get_history() + self.__START_TURN_MODEL__\n", "    if len(self.system)>0:\n", "      prompt = self.system + \"\\n\" + prompt\n", "    return prompt\n", "\n", "  def send_message(self, message):\n", "    \"\"\"\n", "    Handles sending a user message and getting a model response.\n", "\n", "    Args:\n", "        message: The user's message.\n", "\n", "    Returns:\n", "        The model's response.\n", "    \"\"\"\n", "    self.add_to_history_as_user(message)\n", "    prompt = self.get_full_prompt()\n", "    response = self.model.generate(prompt, max_length=4096)\n", "    result = response.replace(prompt, \"\")  # Extract only the new response\n", "    self.add_to_history_as_model(result)\n", "    return result\n", "\n", "  def show_history(self):\n", "    for h in self.history:\n", "      print(h)\n", "\n", "\n", "chat = ChatState(gemma_lm)"]}, {"cell_type": "markdown", "metadata": {"id": "slvLngXKlIIh"}, "source": ["## Elaborate with the given core game concept"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "B49foWx2lQe0"}, "outputs": [{"data": {"text/markdown": ["> ##  \"The Clockwork Shogun\" - A Game of Steam and Secrets\n", "> \n", "> **Core Concept:**  Imagine the captivating beauty of Joseon, with its traditional aesthetics, interwoven with the technological marvels of the Industrial Revolution. This is the setting for \"The Clockwork Shogun,\" a captivating blend of strategy, exploration, and historical fiction.\n", "> \n", "> **Gameplay:**  \"The Clockwork Shogun\" is a blend of multiple genres:\n", "> \n", "> * **Turn-Based Strategic Gameplay:**  Players take on the role of a young, ambitious official, tasked with navigating a tumultuous Joseon, where the old world clashes with the new. Players must make decisions about resource management, trade, diplomacy, and technological advancement, all within a complex political landscape and the looming threat of revolution.\n", "> * **Exploration and Discovery:**  The game features a vast, detailed map, showcasing the diverse regions of a Joseon transformed by steam power. Players explore these territories, uncovering hidden secrets, encountering unique cultures and factions, and discovering ancient technology long forgotten.\n", "> * **Historical Fiction with a Twist:**  The game weaves in historical events and figures, but with a fantastical twist.  Players will meet characters inspired by historical figures, but with unique abilities and personalities. The narrative will focus on uncovering a long-lost secret, a \"Clockwork Shogun\" rumored to possess the power to control the very flow of the kingdom's destiny.\n", "> * **Clockwork Mechanics:**  The core of the game's intrigue comes from its unique \"Clockwork\" mechanics. Players will be able to research, develop and deploy various clockwork devices and inventions. These devices could range from simple automatons and steam-powered machines to complex clockwork weaponry and transportation systems, each offering unique tactical and strategic options.\n", "> \n", "> **Key Features:**\n", "> \n", "> * **Deep Political Intrigue:**  Players will navigate the complex web of court politics, forging alliances, manipulating rivals, and making strategic decisions to influence the course of events. \n", "> * **Unique Character Development:**  Players will develop their characters, choosing their path through the game, impacting their skills, reputation, and alliances.  \n", "> * **Dynamic World:**  The world of the game is in constant flux. Players will need to react to events, making choices that shape the kingdom's future. \n", "> * **Engaging Narrative:**   Players will uncover the secrets hidden in the game's rich narrative, piecing together clues to unravel the mystery of the Clockwork Shogun. \n", "> \n", "> \n", "> **Target Audience:**\n", "> \n", "> * Players who enjoy strategy and simulation games.\n", "> * Fans of historical fiction and fantasy settings with a unique twist.\n", "> * Players seeking a captivating narrative filled with political intrigue and exciting discoveries.\n", "> \n", "> **Thematic Considerations:**\n", "> \n", "> * **The Conflict Between Tradition and Innovation:**  The game explores the challenges and opportunities presented by the clash between the old ways and the rise of modern technology.\n", "> * **The Power of Knowledge:**  Players will learn about the importance of knowledge and its impact on society, particularly in a world where secrets and technology are key to shaping power.\n", "> * **The Nature of Power:**  The game explores the different forms of power and authority, and the ethical dilemmas faced by those who wield power in different ways.\n", "> \n", "> \n", "> **Visuals and Sound:**\n", "> \n", "> * A visually appealing blend of traditional Joseon aesthetics and steampunk elements. \n", "> * A dynamic soundtrack that blends traditional Korean instruments with modern, industrial sounds. \n", "> \n", "> \n", "> **Potential Expansion:**\n", "> \n", "> *  Multiplayer modes, allowing players to compete against each other or cooperate in shaping the future of the kingdom.  \n", "> * A robust crafting and building system, enabling players to create unique clockwork devices and build complex infrastructures.\n", "> \n", "> \n", "> \"The Clockwork Shogun\" has the potential to be a truly engaging and immersive experience.  It blends a fascinating historical setting with a unique and intriguing gameplay experience, offering players a chance to experience the rise and fall of a nation on the brink of revolution. It's not just a game; it's a journey through a world where tradition meets progress, and secrets are buried deep in the heart of an empire reborn. \n", "> <end_of_turn>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result = chat.send_message(\n", "    f\"Elaborate about this game with the given core concept below.\\n{pitch}\"\n", ")\n", "to_markdown(result)"]}, {"cell_type": "markdown", "metadata": {"id": "rePDwH-kesBv"}, "source": ["## Main Characters"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "5SWcXllmm-GW"}, "outputs": [{"data": {"text/markdown": ["> Here are a few character concepts to get you started on your \"The Clockwork Shogun\" cast:\n", "> \n", "> **Main Protagonist:**\n", "> \n", "> * **Name:**  <PERSON><PERSON><PERSON><PERSON><PERSON> (or <PERSON><PERSON><PERSON><PERSON> if you prefer a less common, slightly more \"Korean\" name)\n", "> * **Background:**  A young, ambitious official from a noble family, but with a strong sense of justice and a desire to make a real impact on society. <PERSON><PERSON><PERSON><PERSON><PERSON> is skilled in both political maneuvering and technological advancements, but lacks experience in the practical application of his knowledge.\n", "> * **Motivation:**   <PERSON><PERSON><PERSON><PERSON><PERSON>'s family has long been on the sidelines of the political power struggles. He sees the opportunity to shape the future of the country through his own actions, driven by a deep belief in his vision for a more equitable and prosperous Joseon. \n", "> * **Special Ability/Trait:** <PERSON><PERSON><PERSON><PERSON><PERSON> has a natural talent for problem solving, particularly with clockwork devices, allowing him to quickly understand and adapt to new technologies.\n", "> \n", "> **Supporting Characters:**\n", "> \n", "> * **Name:** <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "> * **Background:**   A talented inventor and clockwork expert, but ostracized for her unorthodox methods and rebellious spirit. She has a deep connection to the \"Clockwork Shogun\", but refuses to share her knowledge. \n", "> * **Motivation:** <PERSON><PERSON><PERSON><PERSON><PERSON> wants to see her inventions used for good and is hesitant to believe in the potential of the \"Clockwork Shogun\" for the betterment of the kingdom. \n", "> *  **Special Ability/Trait:**  She is a gifted engineer and tinkerer, able to create complex clockwork contraptions, including powerful weapons.  She has an uncanny ability to understand and repair even the most complex mechanical devices.  \n", "> \n", "> * **Name:**  <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "> * **Background:**  A cunning and ambitious court advisor, who has a deep understanding of the political landscape of Joseon. He is a master of manipulation and intrigue.\n", "> * **Motivation:**  <PERSON><PERSON><PERSON><PERSON><PERSON> is a loyal servant to the current ruling family, but he has his own ambitions. He sees the potential of a revolution and wants to seize power for his own gain.\n", "> * **Special Ability/Trait:** <PERSON><PERSON><PERSON><PERSON><PERSON> is an expert in political maneuvering, able to manipulate events to his advantage and sow seeds of doubt and discontent.  He is a master of disguise and deception, able to blend in with any crowd. \n", "> \n", "> * **Name:**  Gyeol\n", "> * **Background:**  A wise elder, who has spent his life studying and preserving Joseon's history. He possesses knowledge of ancient technologies, including the legendary \"Clockwork Shogun\", and is deeply concerned about their rediscovery.\n", "> * **Motivation:**  <PERSON><PERSON><PERSON> wants to preserve the balance between tradition and progress, but he is also worried about the power the \"Clockwork Shogun\" holds.\n", "> * **Special Ability/Trait:** <PERSON><PERSON><PERSON> has an extensive knowledge of ancient texts, including prophecies and forgotten legends. He can offer valuable insights and wisdom to <PERSON><PERSON><PERSON><PERSON><PERSON> and other characters. \n", "> \n", "> \n", "> \n", "> **Additional Notes:**\n", "> \n", "> * **Diversity:**  Include characters of different genders, ages, races and backgrounds, reflecting the diverse population of a modern, reimagined Joseon.\n", "> * **Relationships:**  Create relationships and rivalries between these characters, leading to conflicts and opportunities for alliances and betrayal. \n", "> * **Growth:**  Allow characters to evolve and change over time based on their experiences and choices in the game. \n", "> \n", "> \n", "> \n", "> Let me know if you'd like to expand on any of these characters or create new ones! I'd be happy to help flesh out your game world further.<end_of_turn>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["result = chat.send_message(\"Design main characters.\")\n", "to_markdown(result)"]}, {"cell_type": "markdown", "metadata": {"id": "T6nTOt5reta2"}, "source": ["## Villain Characters"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "eSeNGJZKWwM0"}, "outputs": [{"data": {"text/markdown": ["> Here are some villainous characters for \"The Clockwork Shogun,\" each bringing a different threat to the table:\n", "> \n", ">  **1. The Iron Shogun:**\n", "> \n", "> * **Name:**  <PERSON><PERSON><PERSON><PERSON> (meaning “Iron” in Korean)\n", "> * **Background:**  A former noble, disillusioned with the old ways of Joseon, <PERSON><PERSON><PERSON><PERSON> believes technology is the only way to achieve true power. He was a prodigy in clockwork engineering, but his ambition turned to ruthlessness. He sees the \"Clockwork Shogun\" not as a legend, but as a tool to reshape the world to his own vision of \"progress.\"  \n", "> * **Motivation:**  To establish an iron-fisted regime, using the \"Clockwork Shogun\" to enforce his will and eliminate any opposition who stand in his way. \n", "> * **Special Ability/Trait:**  Master strategist and tactician who uses clockwork technology to create a vast network of spies, assassins, and automated defenses to control key regions.\n", "> \n", ">  **2. The Crimson Empress:**\n", "> \n", "> * **Name:**  <PERSON><PERSON><PERSON><PERSON>\n", "> * **Background:**  A cunning and ruthless empress, driven by a thirst for power and control. She sees the potential of the \"Clockwork Shogun\" not as a tool but as a weapon, a means to dominate and control the entire kingdom for her own ambition.\n", "> *  **Motivation:**  To usurp the throne and establish a tyrannical reign, using the \"Clockwork Shogun\" to suppress dissent and maintain absolute control. She is a master manipulator, using propaganda and intimidation to keep the people in line.\n", "> * **Special Ability/Trait:** She has a network of informants throughout the kingdom, able to gather information and influence events from within the court, manipulating the populace with subtle propaganda and fear.\n", "> \n", ">  **3. The Clockwork Cult:** \n", "> \n", "> * **Name:**  The Order of the Iron Hand\n", "> * **Background:** A secretive and fanatic group that believes the \"Clockwork Shogun\" is a divine artifact, destined to bring about a new age of enlightenment. Their members are driven by a fervent belief in their twisted vision of the \"Clockwork Shogun's\" power. \n", "> * **Motivation:**  To use the \"Clockwork Shogun\" to create a utopian society, where technology reigns supreme and traditional beliefs are eradicated. They are willing to sacrifice anyone who stands in their way.\n", "> * **Special Ability/Trait:**  Master manipulators of clockwork technology, using intricate devices to create distractions, manipulate the public and even control individuals. \n", "> \n", "> \n", ">  **Key Considerations for Villain Design:**\n", "> \n", "> * **Complexity:**  Avoid making villains one-dimensional.  Give them believable motivations, flaws, and internal struggles that contribute to their villainy. \n", "> * **Consequences:**  Ensure their actions directly impact the protagonist and the world around them.  \n", "> * **Redemption Arc (Potential):** Consider whether any of them have a glimmer of remorse, or even an opportunity for redemption within the narrative.\n", "> \n", "> \n", "> By crafting well-rounded villains, \"The Clockwork Shogun\" can deliver a compelling and engaging experience, pushing players to confront not only external threats, but also the darker side of ambition and technology.  \n", "> \n", "> \n", "> \n", "> I hope these villain characters inspire further creativity and development. \n", "> <end_of_turn>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["result = chat.send_message(\"Design villain characters.\")\n", "to_markdown(result)"]}, {"cell_type": "markdown", "metadata": {"id": "Bxx6ftEve5C1"}, "source": ["## Main Story"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "tG6HM-yJnJoF"}, "outputs": [{"data": {"text/markdown": ["> Here's a potential storyline for \"The Clockwork Shogun\" with an introductory arc, core development phases, turning points, and a conclusion. \n", "> \n", "> **Introduction:**\n", "> \n", "> * The game starts in a Joseon, but with a distinct steampunk twist. Steam-powered machines are commonplace, clockwork contraptions are used in everyday life, and whispers of a \"Clockwork Shogun\" - a mythical figure said to wield immense power - echo through the court.  \n", "> * The player character, <PERSON><PERSON><PERSON><PERSON><PERSON>,  is a bright young official who's eager to prove his worth. He's been tasked with uncovering the mystery of the \"Clockwork Shogun\" and its potential influence on the kingdom's future. \n", "> \n", "> **Development (Core Stages):**\n", "> \n", "> * **Phase 1: The Rise of the Clockwork:**  <PERSON><PERSON><PERSON><PERSON><PERSON>, through investigation and research, uncovers fragments of a forgotten legend, hinting at a powerful Clockwork Shogun, capable of controlling the very fabric of time and technology. This discovery sparks intrigue and controversy, with some fearing the legend as a harbinger of tyranny. \n", ">     * <PERSON><PERSON><PERSON><PERSON><PERSON> must balance his desire to learn more about the Clockwork Shogun with concerns over the power it may hold.\n", ">     * The game introduces key allies: <PERSON><PERSON><PERSON><PERSON><PERSON>, a brilliant inventor, wary of the legend but drawn to <PERSON><PERSON><PERSON><PERSON><PERSON>'s ambition; and <PERSON><PERSON><PERSON>, an elder steeped in ancient wisdom who warns of the dangers of such power. \n", "> * **Phase 2: The Court's Divide:** <PERSON><PERSON><PERSON><PERSON><PERSON>'s pursuit of the Clockwork Shogun leads him to uncover a plot by <PERSON><PERSON><PERSON><PERSON>, a former noble seeking to exploit the legend for his own power.  He manipulates events to create a power vacuum, aiming to seize control of the nation using the clockwork technology he's developed.\n", ">      *  <PERSON><PERSON><PERSON><PERSON><PERSON> must navigate treacherous court politics, forming alliances while confronting the growing divide between those who see the Clockwork Shogun as a symbol of change and those who fear its implications.\n", ">     *  The Crimson Empress, a powerful empress driven by an ambition to control the kingdom, also becomes a threat, seeking to utilize the Clockwork Shogun for her own ends.\n", ">     *  Players can choose to side with the court, or forge independent alliances. Each path will lead to different outcomes.\n", "> * **Phase 3: The Clockwork War:**  <PERSON><PERSON><PERSON><PERSON><PERSON> must rally allies and utilize both his knowledge of clockwork devices and his diplomatic skills to stop <PERSON><PERSON><PERSON><PERSON> and the Crimson Empress. He discovers the \"Clockwork Shogun\" isn't just a legend, but a complex machine with immense potential.\n", ">     * Players engage in strategic battles and resource management as they fight to secure control of key technologies, while also managing internal conflicts within <PERSON><PERSON><PERSON><PERSON><PERSON>'s own ranks.\n", ">     * A climactic confrontation takes place between <PERSON><PERSON><PERSON><PERSON><PERSON>'s faction and <PERSON><PERSON><PERSON><PERSON>, testing their technological mastery and political prowess.\n", "> \n", "> **Turning Points:**\n", "> \n", "> * **Discovery of the Clockwork Shogun's True Power:**  A key moment where players realize the Clockwork Shogun isn't merely a weapon, but a complex machine capable of altering the very fabric of reality, forcing a difficult choice about its use. \n", "> * **The Rise of the Clockwork Cult**: A splinter faction emerges, seeking to use the \"Clockwork Shogun\" to create a new world order. This introduces a new level of complexity as <PERSON><PERSON><PERSON><PERSON><PERSON> must contend with both external and internal threats. \n", "> \n", "> **Conclusion:**\n", "> \n", "> * <PERSON><PERSON><PERSON><PERSON><PERSON>, having learned from his experiences and allies, must make a final stand.  He must use the \"Clockwork Shogun\" wisely and make choices about its future. The ending could lead to a restoration of the old ways, a technological utopia, or perhaps a new order entirely.\n", "> \n", "> **Themes:**\n", "> \n", "> * **The Balance of Progress and Tradition:** \"The Clockwork Shogun\" is about finding a harmonious balance between the old and the new, between tradition and technology. \n", "> * **The Nature of Power:** The game explores the different ways power can be acquired and wielded, and the dangers of absolute control.\n", "> * **The Responsibility of Knowledge:**  Knowledge can be used for good or evil, and the game encourages players to consider its consequences. \n", "> * **Hope in the Face of Adversity:** Despite the dangers and challenges, there's always hope that even the most powerful machines can be used to create a better future.\n", "> \n", "> \n", "> **Remember:** This is just a basic framework.  You can flesh out the story with more details, specific locations, and engaging characters.  The key is to weave a narrative that is both engaging and thought-provoking! \n", "> \n", "> \n", "> Let me know if you want to delve deeper into specific aspects, such as character development, locations, or the mechanics of the game itself.<end_of_turn>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["result = chat.send_message(\n", "    \"Write the main story of this game with an introduction, development, turn, and conclusion.\"\n", ")\n", "to_markdown(result)"]}, {"cell_type": "markdown", "metadata": {"id": "CNAwQXG2Tbee"}, "source": ["## Show the chat history"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4qW12hdKTfc2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<start_of_turn>user\n", "Elaborate about this game with the given core concept below.\n", "In the heart of a reimagined Joseon Dynasty, where steam-powered machines and clockwork contraptions reign supreme, embark on an epic adventure to unravel the mysteries of a forgotten past and shape the destiny of a nation on the brink of revolution.<end_of_turn>\n", "\n", "<start_of_turn>model\n", "##  \"The Clockwork Shogun\" - A Game of Steam and Secrets\n", "\n", "**Core Concept:**  Imagine the captivating beauty of Joseon, with its traditional aesthetics, interwoven with the technological marvels of the Industrial Revolution. This is the setting for \"The Clockwork Shogun,\" a captivating blend of strategy, exploration, and historical fiction.\n", "\n", "**Gameplay:**  \"The Clockwork Shogun\" is a blend of multiple genres:\n", "\n", "* **Turn-Based Strategic Gameplay:**  Players take on the role of a young, ambitious official, tasked with navigating a tumultuous Joseon, where the old world clashes with the new. Players must make decisions about resource management, trade, diplomacy, and technological advancement, all within a complex political landscape and the looming threat of revolution.\n", "* **Exploration and Discovery:**  The game features a vast, detailed map, showcasing the diverse regions of a Joseon transformed by steam power. Players explore these territories, uncovering hidden secrets, encountering unique cultures and factions, and discovering ancient technology long forgotten.\n", "* **Historical Fiction with a Twist:**  The game weaves in historical events and figures, but with a fantastical twist.  Players will meet characters inspired by historical figures, but with unique abilities and personalities. The narrative will focus on uncovering a long-lost secret, a \"Clockwork Shogun\" rumored to possess the power to control the very flow of the kingdom's destiny.\n", "* **Clockwork Mechanics:**  The core of the game's intrigue comes from its unique \"Clockwork\" mechanics. Players will be able to research, develop and deploy various clockwork devices and inventions. These devices could range from simple automatons and steam-powered machines to complex clockwork weaponry and transportation systems, each offering unique tactical and strategic options.\n", "\n", "**Key Features:**\n", "\n", "* **Deep Political Intrigue:**  Players will navigate the complex web of court politics, forging alliances, manipulating rivals, and making strategic decisions to influence the course of events. \n", "* **Unique Character Development:**  Players will develop their characters, choosing their path through the game, impacting their skills, reputation, and alliances.  \n", "* **Dynamic World:**  The world of the game is in constant flux. Players will need to react to events, making choices that shape the kingdom's future. \n", "* **Engaging Narrative:**   Players will uncover the secrets hidden in the game's rich narrative, piecing together clues to unravel the mystery of the Clockwork Shogun. \n", "\n", "\n", "**Target Audience:**\n", "\n", "* Players who enjoy strategy and simulation games.\n", "* Fans of historical fiction and fantasy settings with a unique twist.\n", "* Players seeking a captivating narrative filled with political intrigue and exciting discoveries.\n", "\n", "**Thematic Considerations:**\n", "\n", "* **The Conflict Between Tradition and Innovation:**  The game explores the challenges and opportunities presented by the clash between the old ways and the rise of modern technology.\n", "* **The Power of Knowledge:**  Players will learn about the importance of knowledge and its impact on society, particularly in a world where secrets and technology are key to shaping power.\n", "* **The Nature of Power:**  The game explores the different forms of power and authority, and the ethical dilemmas faced by those who wield power in different ways.\n", "\n", "\n", "**Visuals and Sound:**\n", "\n", "* A visually appealing blend of traditional Joseon aesthetics and steampunk elements. \n", "* A dynamic soundtrack that blends traditional Korean instruments with modern, industrial sounds. \n", "\n", "\n", "**Potential Expansion:**\n", "\n", "*  Multiplayer modes, allowing players to compete against each other or cooperate in shaping the future of the kingdom.  \n", "* A robust crafting and building system, enabling players to create unique clockwork devices and build complex infrastructures.\n", "\n", "\n", "\"The Clockwork Shogun\" has the potential to be a truly engaging and immersive experience.  It blends a fascinating historical setting with a unique and intriguing gameplay experience, offering players a chance to experience the rise and fall of a nation on the brink of revolution. It's not just a game; it's a journey through a world where tradition meets progress, and secrets are buried deep in the heart of an empire reborn. \n", "<end_of_turn>\n", "<start_of_turn>user\n", "Design main characters.<end_of_turn>\n", "\n", "<start_of_turn>model\n", "Here are a few character concepts to get you started on your \"The Clockwork Shogun\" cast:\n", "\n", "**Main Protagonist:**\n", "\n", "* **Name:**  <PERSON><PERSON><PERSON><PERSON><PERSON> (or <PERSON><PERSON><PERSON><PERSON> if you prefer a less common, slightly more \"Korean\" name)\n", "* **Background:**  A young, ambitious official from a noble family, but with a strong sense of justice and a desire to make a real impact on society. <PERSON><PERSON><PERSON><PERSON><PERSON> is skilled in both political maneuvering and technological advancements, but lacks experience in the practical application of his knowledge.\n", "* **Motivation:**   <PERSON><PERSON><PERSON><PERSON><PERSON>'s family has long been on the sidelines of the political power struggles. He sees the opportunity to shape the future of the country through his own actions, driven by a deep belief in his vision for a more equitable and prosperous Joseon. \n", "* **Special Ability/Trait:** <PERSON><PERSON><PERSON><PERSON><PERSON> has a natural talent for problem solving, particularly with clockwork devices, allowing him to quickly understand and adapt to new technologies.\n", "\n", "**Supporting Characters:**\n", "\n", "* **Name:** <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "* **Background:**   A talented inventor and clockwork expert, but ostracized for her unorthodox methods and rebellious spirit. She has a deep connection to the \"Clockwork Shogun\", but refuses to share her knowledge. \n", "* **Motivation:** <PERSON><PERSON><PERSON><PERSON><PERSON> wants to see her inventions used for good and is hesitant to believe in the potential of the \"Clockwork Shogun\" for the betterment of the kingdom. \n", "*  **Special Ability/Trait:**  She is a gifted engineer and tinkerer, able to create complex clockwork contraptions, including powerful weapons.  She has an uncanny ability to understand and repair even the most complex mechanical devices.  \n", "\n", "* **Name:**  <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "* **Background:**  A cunning and ambitious court advisor, who has a deep understanding of the political landscape of Joseon. He is a master of manipulation and intrigue.\n", "* **Motivation:**  <PERSON><PERSON><PERSON><PERSON><PERSON> is a loyal servant to the current ruling family, but he has his own ambitions. He sees the potential of a revolution and wants to seize power for his own gain.\n", "* **Special Ability/Trait:** <PERSON><PERSON><PERSON><PERSON><PERSON> is an expert in political maneuvering, able to manipulate events to his advantage and sow seeds of doubt and discontent.  He is a master of disguise and deception, able to blend in with any crowd. \n", "\n", "* **Name:**  Gyeol\n", "* **Background:**  A wise elder, who has spent his life studying and preserving Joseon's history. He possesses knowledge of ancient technologies, including the legendary \"Clockwork Shogun\", and is deeply concerned about their rediscovery.\n", "* **Motivation:**  <PERSON><PERSON><PERSON> wants to preserve the balance between tradition and progress, but he is also worried about the power the \"Clockwork Shogun\" holds.\n", "* **Special Ability/Trait:** <PERSON><PERSON><PERSON> has an extensive knowledge of ancient texts, including prophecies and forgotten legends. He can offer valuable insights and wisdom to <PERSON><PERSON><PERSON><PERSON><PERSON> and other characters. \n", "\n", "\n", "\n", "**Additional Notes:**\n", "\n", "* **Diversity:**  Include characters of different genders, ages, races and backgrounds, reflecting the diverse population of a modern, reimagined Joseon.\n", "* **Relationships:**  Create relationships and rivalries between these characters, leading to conflicts and opportunities for alliances and betrayal. \n", "* **Growth:**  Allow characters to evolve and change over time based on their experiences and choices in the game. \n", "\n", "\n", "\n", "Let me know if you'd like to expand on any of these characters or create new ones! I'd be happy to help flesh out your game world further.<end_of_turn>\n", "<start_of_turn>user\n", "Design villain characters.<end_of_turn>\n", "\n", "<start_of_turn>model\n", "Here are some villainous characters for \"The Clockwork Shogun,\" each bringing a different threat to the table:\n", "\n", " **1. The Iron Shogun:**\n", "\n", "* **Name:**  <PERSON><PERSON><PERSON><PERSON> (meaning “Iron” in Korean)\n", "* **Background:**  A former noble, disillusioned with the old ways of Joseon, <PERSON><PERSON><PERSON><PERSON> believes technology is the only way to achieve true power. He was a prodigy in clockwork engineering, but his ambition turned to ruthlessness. He sees the \"Clockwork Shogun\" not as a legend, but as a tool to reshape the world to his own vision of \"progress.\"  \n", "* **Motivation:**  To establish an iron-fisted regime, using the \"Clockwork Shogun\" to enforce his will and eliminate any opposition who stand in his way. \n", "* **Special Ability/Trait:**  Master strategist and tactician who uses clockwork technology to create a vast network of spies, assassins, and automated defenses to control key regions.\n", "\n", " **2. The Crimson Empress:**\n", "\n", "* **Name:**  <PERSON><PERSON><PERSON><PERSON>\n", "* **Background:**  A cunning and ruthless empress, driven by a thirst for power and control. She sees the potential of the \"Clockwork Shogun\" not as a tool but as a weapon, a means to dominate and control the entire kingdom for her own ambition.\n", "*  **Motivation:**  To usurp the throne and establish a tyrannical reign, using the \"Clockwork Shogun\" to suppress dissent and maintain absolute control. She is a master manipulator, using propaganda and intimidation to keep the people in line.\n", "* **Special Ability/Trait:** She has a network of informants throughout the kingdom, able to gather information and influence events from within the court, manipulating the populace with subtle propaganda and fear.\n", "\n", " **3. The Clockwork Cult:** \n", "\n", "* **Name:**  The Order of the Iron Hand\n", "* **Background:** A secretive and fanatic group that believes the \"Clockwork Shogun\" is a divine artifact, destined to bring about a new age of enlightenment. Their members are driven by a fervent belief in their twisted vision of the \"Clockwork Shogun's\" power. \n", "* **Motivation:**  To use the \"Clockwork Shogun\" to create a utopian society, where technology reigns supreme and traditional beliefs are eradicated. They are willing to sacrifice anyone who stands in their way.\n", "* **Special Ability/Trait:**  Master manipulators of clockwork technology, using intricate devices to create distractions, manipulate the public and even control individuals. \n", "\n", "\n", " **Key Considerations for Villain Design:**\n", "\n", "* **Complexity:**  Avoid making villains one-dimensional.  Give them believable motivations, flaws, and internal struggles that contribute to their villainy. \n", "* **Consequences:**  Ensure their actions directly impact the protagonist and the world around them.  \n", "* **Redemption Arc (Potential):** Consider whether any of them have a glimmer of remorse, or even an opportunity for redemption within the narrative.\n", "\n", "\n", "By crafting well-rounded villains, \"The Clockwork Shogun\" can deliver a compelling and engaging experience, pushing players to confront not only external threats, but also the darker side of ambition and technology.  \n", "\n", "\n", "\n", "I hope these villain characters inspire further creativity and development. \n", "<end_of_turn>\n", "<start_of_turn>user\n", "Write the main story of this game with an introduction, development, turn, and conclusion.<end_of_turn>\n", "\n", "<start_of_turn>model\n", "Here's a potential storyline for \"The Clockwork Shogun\" with an introductory arc, core development phases, turning points, and a conclusion. \n", "\n", "**Introduction:**\n", "\n", "* The game starts in a Joseon, but with a distinct steampunk twist. Steam-powered machines are commonplace, clockwork contraptions are used in everyday life, and whispers of a \"Clockwork Shogun\" - a mythical figure said to wield immense power - echo through the court.  \n", "* The player character, <PERSON><PERSON><PERSON><PERSON><PERSON>,  is a bright young official who's eager to prove his worth. He's been tasked with uncovering the mystery of the \"Clockwork Shogun\" and its potential influence on the kingdom's future. \n", "\n", "**Development (Core Stages):**\n", "\n", "* **Phase 1: The Rise of the Clockwork:**  <PERSON><PERSON><PERSON><PERSON><PERSON>, through investigation and research, uncovers fragments of a forgotten legend, hinting at a powerful Clockwork Shogun, capable of controlling the very fabric of time and technology. This discovery sparks intrigue and controversy, with some fearing the legend as a harbinger of tyranny. \n", "    * <PERSON><PERSON><PERSON><PERSON><PERSON> must balance his desire to learn more about the Clockwork Shogun with concerns over the power it may hold.\n", "    * The game introduces key allies: <PERSON><PERSON><PERSON><PERSON><PERSON>, a brilliant inventor, wary of the legend but drawn to <PERSON><PERSON><PERSON><PERSON><PERSON>'s ambition; and <PERSON><PERSON><PERSON>, an elder steeped in ancient wisdom who warns of the dangers of such power. \n", "* **Phase 2: The Court's Divide:** <PERSON><PERSON><PERSON><PERSON><PERSON>'s pursuit of the Clockwork Shogun leads him to uncover a plot by <PERSON><PERSON><PERSON><PERSON>, a former noble seeking to exploit the legend for his own power.  He manipulates events to create a power vacuum, aiming to seize control of the nation using the clockwork technology he's developed.\n", "     *  <PERSON><PERSON><PERSON><PERSON><PERSON> must navigate treacherous court politics, forming alliances while confronting the growing divide between those who see the Clockwork Shogun as a symbol of change and those who fear its implications.\n", "    *  The Crimson Empress, a powerful empress driven by an ambition to control the kingdom, also becomes a threat, seeking to utilize the Clockwork Shogun for her own ends.\n", "    *  Players can choose to side with the court, or forge independent alliances. Each path will lead to different outcomes.\n", "* **Phase 3: The Clockwork War:**  <PERSON><PERSON><PERSON><PERSON><PERSON> must rally allies and utilize both his knowledge of clockwork devices and his diplomatic skills to stop <PERSON><PERSON><PERSON><PERSON> and the Crimson Empress. He discovers the \"Clockwork Shogun\" isn't just a legend, but a complex machine with immense potential.\n", "    * Players engage in strategic battles and resource management as they fight to secure control of key technologies, while also managing internal conflicts within <PERSON><PERSON><PERSON><PERSON><PERSON>'s own ranks.\n", "    * A climactic confrontation takes place between <PERSON><PERSON><PERSON><PERSON><PERSON>'s faction and <PERSON><PERSON><PERSON><PERSON>, testing their technological mastery and political prowess.\n", "\n", "**Turning Points:**\n", "\n", "* **Discovery of the Clockwork Shogun's True Power:**  A key moment where players realize the Clockwork Shogun isn't merely a weapon, but a complex machine capable of altering the very fabric of reality, forcing a difficult choice about its use. \n", "* **The Rise of the Clockwork Cult**: A splinter faction emerges, seeking to use the \"Clockwork Shogun\" to create a new world order. This introduces a new level of complexity as <PERSON><PERSON><PERSON><PERSON><PERSON> must contend with both external and internal threats. \n", "\n", "**Conclusion:**\n", "\n", "* <PERSON><PERSON><PERSON><PERSON><PERSON>, having learned from his experiences and allies, must make a final stand.  He must use the \"Clockwork Shogun\" wisely and make choices about its future. The ending could lead to a restoration of the old ways, a technological utopia, or perhaps a new order entirely.\n", "\n", "**Themes:**\n", "\n", "* **The Balance of Progress and Tradition:** \"The Clockwork Shogun\" is about finding a harmonious balance between the old and the new, between tradition and technology. \n", "* **The Nature of Power:** The game explores the different ways power can be acquired and wielded, and the dangers of absolute control.\n", "* **The Responsibility of Knowledge:**  Knowledge can be used for good or evil, and the game encourages players to consider its consequences. \n", "* **Hope in the Face of Adversity:** Despite the dangers and challenges, there's always hope that even the most powerful machines can be used to create a better future.\n", "\n", "\n", "**Remember:** This is just a basic framework.  You can flesh out the story with more details, specific locations, and engaging characters.  The key is to weave a narrative that is both engaging and thought-provoking! \n", "\n", "\n", "Let me know if you want to delve deeper into specific aspects, such as character development, locations, or the mechanics of the game itself.<end_of_turn>\n"]}], "source": ["chat.show_history()"]}, {"cell_type": "markdown", "metadata": {"id": "YYvLo-OLkj0g"}, "source": ["## Clear History"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ngWPjllXkljM"}, "outputs": [], "source": ["chat.history.clear()"]}, {"cell_type": "markdown", "metadata": {"id": "O0fZNg2Eykbq"}, "source": ["## Expansion Idea"]}, {"cell_type": "markdown", "metadata": {"id": "QP1zppatyosv"}, "source": ["### Marketing Phrase"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "H2PcNCO_ytEP"}, "outputs": [{"data": {"text/markdown": ["> Here are some marketing phrases for a new steam-powered toothbrush:\n", "> \n", "> **Highlighting Benefits:**\n", "> \n", "> * **\"Experience the Future of Oral Care: Steam-Powered Clean.\"**\n", "> * **\"Deep Clean. Radiant Smile. Powered by Steam.\"**\n", "> * **\"Unleash the Power of Steam:  A Revolutionary Toothbrush.\"**\n", "> * **\"Say Goodbye to <PERSON><PERSON><PERSON>, Hello to Steam Clean.\"**\n", "> \n", "> **Emphasize Technology:**\n", "> \n", "> * **\"The Steam Revolution in Oral Hygiene.\"**\n", "> * **\"Steam Technology:  For a Brighter, Healthier Smile.\"**\n", "> * **\"Introducing the Smart Steam Toothbrush.\"**\n", "> \n", "> **Focus on User Experience:**\n", "> \n", "> * **\"The Steam Clean You've Been Dreaming Of.\"**\n", "> * **\"Gentle, Powerful Steam Cleaning for a Spa-like Oral Experience.\"**\n", "> \n", "> **Catchy & Creative:**\n", "> \n", "> * **\"Steam Up Your Smile.\"** \n", "> * **\"Get a Steam Ahead of Plaque.\"** \n", "> * **\"The Power of Steam:  Cleaning Beyond Compare.\" **\n", "> \n", "> \n", "> **Tips for Selecting:**\n", "> \n", "> * **Target audience:**  Consider who you are marketing to and what resonates with them.\n", "> * **Brand voice:**  Match the phrase to the overall style and message of your brand.\n", "> * **Keywords:**  Use words that people are likely to search for when looking for a new toothbrush. \n", ">  \n", "> Remember to test different phrases and analyze their effectiveness to find the best fit for your product launch. \n", "> <end_of_turn>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["chat.history.clear()\n", "pitch = \"A new steam-powered toothbrush\"\n", "result = chat.send_message(\n", "    f\"Generate a marketing phrase for the new product below.\\n{pitch}\"\n", ")\n", "to_markdown(result)"]}, {"cell_type": "markdown", "metadata": {"id": "oJbiCXoszJVN"}, "source": ["### Florist Ideas"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "BKXXigmxzZp_"}, "outputs": [{"data": {"text/markdown": ["> ## Floral Concept: Co<PERSON> Blooms\n", "> \n", "> **Idea:** A florist that celebrates the wonder and beauty of the cosmos. Imagine an ethereal, celestial experience where customers can find unique arrangements inspired by the stars. \n", "> \n", "> **Atmosphere and Design:**\n", "> * **Color Palette:** Deep blues, silvers, purples, golds, and twinkling lights.  \n", "> * **Materials:** Use crystals, star-shaped ornaments, and celestial-themed fabric accents. Consider hanging paper lanterns or creating an \"infinity loop\" display using hanging floral wire with star-shaped blossoms cascading down.\n", "> * **Scent:** Incorporate subtle scents like vanilla, lavender, or cedarwood that evoke nighttime skies or starry nights.\n", "> * **Music:** Ambient music with celestial soundscapes. \n", "> \n", "> **Flower Suggestions:**\n", "> * **Stargazer Lilies:** These dramatic lilies with their starry white and purple blooms are the perfect centerpiece.\n", "> * **Lavender:** A classic symbol of the night, lavender adds a calming, dreamy touch.\n", "> * **White Roses:** Elegant and timeless, white roses are perfect for representing purity and hope.\n", "> * **Purple Irises:** Their deep hues and unique shape evoke the beauty of distant stars. \n", "> * **Silver Dollar Eucalyptus:** These silvery leaves add a modern touch and create interesting texture. \n", "> * **White Hydrangeas:**  These elegant hydrangeas symbolize serenity and are perfect for representing the vastness of space. \n", "> * **Bluebells:**  Small but impactful, these bluebell flowers evoke the twinkling of night skies. \n", "> \n", "> **Special Offerings:**\n", "> * **Starlight Bouquet:** A custom-designed bouquet with a unique star motif and a celestial-themed vase.\n", "> * **Cosmic Candle:** Hand-poured scented candles with a celestial scent and packaging inspired by the cosmos.\n", "> * **Celestial Terrariums:** Mini terrariums featuring a variety of starry-themed flowers and miniature planets.\n", "> * **Astrology Readings:**  Collaborate with a local astrologer for personalized readings based on the customer's birth chart and the meaning of their chosen flowers.\n", "> \n", "> **Overall Experience:**  \n", "> The Cosmic Blooms florist should be an experience, not just a purchase. Encourage customers to share photos of their arrangements on social media using a special hashtag for a chance to be featured. Create a sense of community around the space, perhaps by offering a \"Starry Night Workshop\" where customers can learn to create their own floral arrangements. \n", "> \n", "> \n", "> \n", "> \n", "> <end_of_turn>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["chat.history.clear()\n", "pitch = \"Universe and shooting stars\"\n", "result = chat.send_message(\n", "    f\"Generate a florist idea inspired by the concept below, along with suggestions for suitable flowers.\\n{pitch}\"\n", ")\n", "to_markdown(result)"]}, {"cell_type": "markdown", "metadata": {"id": "jsNqqjZwziHi"}, "source": ["### Food Recipe"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "FbXyDjkfznJ_"}, "outputs": [{"data": {"text/markdown": ["> ## Cyberpunk Kraken Bites - Spicy Squid Ink Dip with <PERSON><PERSON><PERSON><PERSON><PERSON> Tentacles\n", "> \n", "> This recipe takes inspiration from the mythical cephalopod and cyberpunk aesthetics with a spicy twist. \n", "> \n", "> **Yield:** 6 servings\n", "> **Prep Time:** 30 minutes\n", "> **Cook Time:** 15 minutes\n", "> \n", "> \n", "> **Ingredients:**\n", "> \n", "> **For the Spicy Squid Ink Dip:**\n", "> \n", "> * 8 oz squid ink\n", "> * ½ cup water\n", "> * ¼ cup mayonnaise\n", "> * 2 tablespoons <PERSON><PERSON><PERSON>\n", "> * 1 tablespoon lemon juice\n", "> * 1 teaspoon black pepper\n", "> * 1 teaspoon salt\n", "> * 1 tablespoon chopped cilantro (optional)\n", "> \n", "> **For the Crispy Kraken Tentacles:**\n", "> \n", "> * 1 lb calamari, cleaned and cut into small tentacles\n", "> * 1/2 cup cornstarch\n", "> * 1/4 cup water\n", "> * 1/2 teaspoon salt\n", "> * 1/4 teaspoon black pepper\n", "> * 1 cup all-purpose flour\n", "> * 1/2 teaspoon paprika\n", "> * 1/4 teaspoon garlic powder\n", "> * Vegetable oil for frying\n", "> \n", "> **Optional Garnish:**\n", "> \n", "> * Chopped green onions\n", "> * Microgreens\n", "> * Smoked Paprika Dust\n", "> \n", "> **Instructions:**\n", "> \n", "> **1. Prepare the Squid Ink Dip:**\n", ">    * In a small saucepan, whisk together squid ink, water, mayonnaise, Sriracha, lemon juice, pepper, and salt until smooth. \n", ">    * Bring the mixture to a simmer over medium heat, stirring occasionally.  \n", ">    * Continue to simmer for 5 minutes, stirring occasionally, until the mixture slightly thickens.\n", ">    * Remove from heat and stir in cilantro (if using). \n", ">    * Set aside until ready to serve.\n", "> \n", "> **2. Prepare the Kraken Tentacles:**\n", ">    * In a shallow dish, combine corn starch, water, salt, and pepper. \n", ">    * In another shallow dish, combine all-purpose flour, paprika, and garlic powder. \n", ">    * Dip the calamari tentacles into the cornstarch mixture, allowing the excess to drip off.\n", ">    * Dredge the tentacles in the flour mixture, ensuring a good coating on all sides.\n", ">    * Heat about 2 inches of oil in a deep skillet over medium-high heat to 350°F (175°C). \n", ">    * Gently place the tentacles into the hot oil, working in batches to avoid overcrowding. \n", ">    * Fry until golden brown and crispy, about 2-3 minutes per side.\n", ">    * Carefully remove the tentacles from the oil and drain on paper towels.\n", "> \n", "> **3. <PERSON><PERSON><PERSON> and <PERSON><PERSON>:**\n", ">    * Arrange the crispy kraken tentacles on a platter. \n", ">    * Serve the spicy squid ink dip alongside the tentacles, garnished with chopped green onions, microgreens, or a sprinkle of smoked paprika dust. \n", "> \n", "> \n", "> **Cyberpunk Flair:**\n", "> \n", "> * Serve the dish in black metal-themed bowls for a futuristic feel. \n", "> * Use neon lighting to highlight the squid ink dip. \n", "> * Incorporate cyberpunk-inspired edible decorations such as LED lights in the tentacles or glow-in-the-dark sprinkles for the dip. \n", "> \n", "> \n", "> \n", "> This recipe is a playful interpretation of the cyberpunk theme, offering a unique and exciting culinary experience. Remember to enjoy the vibrant colours, bold flavors, and futuristic presentation!<end_of_turn>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["chat.history.clear()\n", "pitch = \"Cyberpunk Kraken\"\n", "result = chat.send_message(\n", "    f\"Generate a cooking recipe with the concept below.\\n{pitch}\"\n", ")\n", "to_markdown(result)"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "[Gemma_2]Game_Design_Brainstorming.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}