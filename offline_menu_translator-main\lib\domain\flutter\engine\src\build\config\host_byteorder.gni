# Copyright (c) 2017 The Chromium Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# This header file defines the "host_byteorder" variable.
# Not that this is currently used only for building v8.
# The chromium code generally assumes little-endianness.
declare_args() {
  host_byteorder = "undefined"
}

# Detect host byteorder
# ppc64 can be either BE or LE
if (host_cpu == "ppc64") {
  if (current_os == "aix") {
    host_byteorder = "big"
  } else {
    # Only use the script when absolutely necessary
    host_byteorder =
        exec_script("//build/config/get_host_byteorder.py", [], "trim string")
  }
} else if (host_cpu == "ppc" || host_cpu == "s390" || host_cpu == "s390x") {
  host_byteorder = "big"
} else {
  host_byteorder = "little"
}
