import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_gemma/flutter_gemma.dart';
import 'package:path_provider/path_provider.dart';

/// Data source for downloading and managing Gemma 3N model
/// Following the flutter_gemma example pattern
class GemmaDownloaderDatasource {
  static final GemmaDownloaderDatasource _instance =
      GemmaDownloaderDatasource._internal();
  factory GemmaDownloaderDatasource() => _instance;
  static GemmaDownloaderDatasource get instance => _instance;
  GemmaDownloaderDatasource._internal();

  // Gemma 3N model we download following the flutter_gemma example
  static const String _modelUrl =
      'https://huggingface.co/google/gemma-3n-E4B-it-litert-preview/resolve/main/gemma-3n-E4B-it-int4.task';
  static const String _modelFilename = 'gemma-3n-E4B-it-int4.task';

  bool _isDownloading = false;
  double _downloadProgress = 0.0;

  /// Check if model exists locally
  Future<bool> checkModelExistence() async {
    try {
      if (kDebugMode) {
        print('🔍 Checking if Gemma-3N model exists locally...');
      }

      // For web, flutter_gemma doesn't work
      if (kIsWeb) {
        if (kDebugMode) {
          print('🌐 Web platform: flutter_gemma not supported');
        }
        return false;
      }

      // Get the application documents directory
      final directory = await getApplicationDocumentsDirectory();
      final modelPath = '${directory.path}/$_modelFilename';
      final modelFile = File(modelPath);

      final exists = await modelFile.exists();

      if (kDebugMode) {
        print('📁 Model path: $modelPath');
        print('📦 Model exists: $exists');
      }

      return exists;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking model existence: $e');
      }
      return false;
    }
  }

  /// Download the Gemma 3N model with progress tracking
  /// Following the flutter_gemma example pattern
  Future<bool> downloadModel({
    required Function(double progress) onProgress,
  }) async {
    if (_isDownloading) {
      if (kDebugMode) {
        print('⚠️ Download already in progress');
      }
      return false;
    }

    try {
      _isDownloading = true;
      _downloadProgress = 0.0;

      if (kDebugMode) {
        print('📥 Starting Gemma-3N model download...');
        print('🔗 URL: $_modelUrl');
        print('📁 Filename: $_modelFilename');
        print('💾 Size: ~1.8GB (estimated)');
      }

      // For web, flutter_gemma doesn't work
      if (kIsWeb) {
        throw Exception(
            'Web platform not supported by flutter_gemma. Please use mobile or desktop app.');
      }

      // Use ModelFileManager from FlutterGemmaPlugin to download the model
      final gemma = FlutterGemmaPlugin.instance;
      final modelManager = gemma.modelManager;

      // Download the model with progress tracking
      await for (final progress
          in modelManager.downloadModelFromNetworkWithProgress(_modelUrl)) {
        _downloadProgress = progress / 100.0;

        if (kDebugMode) {
          print('📊 Download progress: ${progress.toStringAsFixed(1)}%');
        }

        // Call the progress callback
        onProgress(_downloadProgress);
      }

      // Verify the download was successful
      final isDownloaded = await checkModelExistence();

      if (isDownloaded) {
        if (kDebugMode) {
          print('✅ Gemma-3N model downloaded successfully!');
          print('📁 Model ready for offline translation');
        }
        return true;
      } else {
        throw Exception('Model download completed but file not found');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Model download failed: $e');
      }
      return false;
    } finally {
      _isDownloading = false;
    }
  }

  /// Get model information
  Map<String, dynamic> getModelInfo() {
    return {
      'modelName': 'Gemma-3N-E4B-it',
      'modelUrl': _modelUrl,
      'filename': _modelFilename,
      'size': '~1.8GB',
      'type': 'LiteRT optimized',
      'quantization': 'int4',
      'capabilities': [
        'Text translation',
        'Multimodal support',
        '100% offline processing',
        'High accuracy',
      ],
      'isDownloading': _isDownloading,
      'downloadProgress': _downloadProgress,
    };
  }

  /// Get download progress (0.0 to 1.0)
  double get downloadProgress => _downloadProgress;

  /// Check if currently downloading
  bool get isDownloading => _isDownloading;

  /// Get model filename
  String get modelFilename => _modelFilename;

  /// Get model URL
  String get modelUrl => _modelUrl;
}
