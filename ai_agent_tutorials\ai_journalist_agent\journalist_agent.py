import os
from dotenv import load_dotenv
from textwrap import dedent
from phi.assistant import Assistant
from phi.llm.base import LLM
from phi.tools.serpapi_tools import SerpApiTools
from phi.tools.newspaper4k import Newspaper4k as NewspaperToolkit
import streamlit as st
from groq import Groq
from pydantic import Field, PrivateAttr
from typing import List, Dict

load_dotenv()

class GroqChat(LLM):
    model: str = Field(default="llama-3.1-8b-instant")
    api_key: str = Field(...)

    _client: Groq = PrivateAttr()

    def __init__(self, api_key: str, model: str = "llama-3.1-8b-instant"):
        super().__init__(model=model, api_key=api_key)
        self._client = Groq(api_key=api_key)

    def chat(self, messages: List[Dict[str, str]]) -> str:
        completion = self._client.chat.completions.create(
            messages=messages,
            model=self.model
        )
        return completion.choices[0].message.content

    def complete(self, prompt: str) -> str:
        return self.chat([{"role": "user", "content": prompt}])

    def response(self, messages: List[Dict[str, str]]) -> str:
        return self.chat(messages)

st.title("AI Journalist Agent 🗞️")
st.caption("Generate High-quality articles with AI Journalist using Groq LLM")

# Layout improvements
st.sidebar.header("Settings")
groq_api_key = st.sidebar.text_input("Enter Groq API Key", value=os.getenv("GROQ_API_KEY", ""), type="password")
serp_api_key = st.sidebar.text_input("Enter Serp API Key", value=os.getenv("SERP_API_KEY", ""), type="password")

# Model selection dropdown
model_selection = st.sidebar.selectbox("Choose AI Model", 
                                       options=["llama-3.1-8b-instant", "llama3-8b-8192", "llama-3.3-70b-versatile", "llama-3.2-3b-preview","llama3-groq-70b-8192-tool-use-preview",
                                       "llama-3.2-1b-preview",
                                       "llama-3.2-11b-vision-preview"])

# Initialize API keys and setup assistants
if groq_api_key and serp_api_key:
    llm = GroqChat(api_key=groq_api_key, model=model_selection)

    searcher = Assistant(
        name="Searcher",
        role="Searches for top URLs based on a topic",
        llm=llm,
        description=dedent(
            """You are a world-class journalist for the New York Times. Given a topic, generate a list of 3 search terms
            for writing an article on that topic. Then search the web for each term, analyse the results
            and return the 10 most relevant URLs."""
        ),
        instructions=[ 
            "Given a topic, first generate a list of 3 search terms related to that topic.",
            "For each search term, `search_google` and analyze the results.",
            "From the results of all searcher, return the 10 most relevant URLs to the topic.",
            "Remember: you are writing for the New York Times, so the quality of the sources is important.",
        ],
        tools=[SerpApiTools(api_key=serp_api_key)],
        add_datetime_to_instructions=True,
    )

    writer = Assistant(
        name="Writer",
        role="Retrieves text from URLs and writes a high-quality article",
        llm=llm,
        description=dedent(
            """You are a senior writer for the New York Times. Given a topic and a list of URLs,
            your goal is to write a high-quality NYT-worthy article on the topic."""
        ),
        instructions=[ 
            "Given a topic and a list of URLs, first read the article using `get_article_text`.",
            "Then write a high-quality NYT-worthy article on the topic.",
            "The article should be well-structured, informative, and engaging.",
            "Ensure the length is at least as long as a NYT cover story -- at a minimum, 15 paragraphs.",
            "Ensure you provide a nuanced and balanced opinion, quoting facts where possible.",
            "Remember: you are writing for the New York Times, so the quality of the article is important.",
            "Focus on clarity, coherence, and overall quality.",
            "Never make up facts or plagiarize. Always provide proper attribution.",
        ],
        tools=[NewspaperToolkit()],
        add_datetime_to_instructions=True,
        add_chat_history_to_prompt=True,
        num_history_messages=3,
    )

    editor = Assistant(
        name="Editor",
        llm=llm,
        team=[searcher, writer],
        description="You are a senior NYT editor. Given a topic, your goal is to write a NYT worthy article.",
        instructions=[ 
            "Given a topic, ask the search journalist to search for the most relevant URLs for that topic.",
            "Then pass a description of the topic and URLs to the writer to get a draft of the article.",
            "Edit, proofread, and refine the article to ensure it meets the high standards of the New York Times.",
            "The article should be extremely articulate and well written. "
            "Focus on clarity, coherence, and overall quality.",
            "Ensure the article is engaging and informative.",
            "Remember: you are the final gatekeeper before the article is published.",
        ],
        add_datetime_to_instructions=True,
        markdown=True,
    )

    # Unique key for the text input to avoid duplicate IDs
    query = st.text_input("What do you want the AI journalist to write an Article on?", key="query_input")

    # Create Article button next to the query input
    col1, col2 = st.columns([4, 1])  # Adjust the column layout
    with col1:
        # Use the same key for the input field but unique key for the button
        create_article_button = st.button("Create Article", key="create_article_button")
    with col2:
        # Display the button in the second column to align with the input field
        pass

    if create_article_button and query:
        with st.spinner("Generating Article..."):
            response = editor.run(query, stream=False)
            st.write(response)
else:
    st.sidebar.warning("Please enter valid API keys.")
