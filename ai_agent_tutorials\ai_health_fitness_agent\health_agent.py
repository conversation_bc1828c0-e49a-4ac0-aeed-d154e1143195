import streamlit as st
from phi.agent import Agent
from phi.model.google import Gemini

st.set_page_config(
    page_title="Planejador de Saúde e Fitness AI",
    page_icon="🏋️‍♂️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Estilo para personalizar a interface do Streamlit
st.markdown("""
    <style>
    .main { padding: 2rem; }
    .stButton>button { width: 100%; border-radius: 5px; height: 3em; }
    .success-box { padding: 1rem; border-radius: 0.5rem; background-color: #f0fff4; border: 1px solid #9ae6b4; }
    .warning-box { padding: 1rem; border-radius: 0.5rem; background-color: #fffaf0; border: 1px solid #fbd38d; }
    div[data-testid="stExpander"] div[role="button"] p { font-size: 1.1rem; font-weight: 600; }
    </style>
""", unsafe_allow_html=True)

def display_plan(plan_type, plan_content):
    with st.expander(f"📋 Seu Plano Personalizado de {plan_type}", expanded=True):
        col1, col2 = st.columns([2, 1])
        
        with col1:
            if plan_type == 'Dieta':
                st.markdown("### 🎯 Por que este plano funciona")
                st.info(plan_content.get("why_this_plan_works", "Informação não disponível"))
                st.markdown("### 🍽️ Plano de Refeições")
                st.write(plan_content.get("meal_plan", "Plano não disponível"))
            else:
                st.markdown("### 🎯 Objetivos")
                st.success(plan_content.get("goals", "Objetivos não especificados"))
                st.markdown("### 🏋️‍♂️ Rotina de Exercícios")
                st.write(plan_content.get("routine", "Rotina não disponível"))
        
        with col2:
            if plan_type == 'Dieta':
                st.markdown("### ⚠️ Considerações Importantes")
                considerations = plan_content.get("important_considerations", "").split('\n')
                for consideration in considerations:
                    if consideration.strip():
                        st.warning(consideration)
            else:
                st.markdown("### 💡 Dicas Profissionais")
                tips = plan_content.get("tips", "").split('\n')
                for tip in tips:
                    if tip.strip():
                        st.info(tip)

def handle_error(message):
    st.error(f"❌ Erro: {message}")
    
def main():
    if 'dietary_plan' not in st.session_state:
        st.session_state.dietary_plan = {}
        st.session_state.fitness_plan = {}
        st.session_state.qa_pairs = []
        st.session_state.plans_generated = False

    st.title("🏋️‍♂️ Planejador de Saúde e Fitness AI")
    st.markdown("""
        <div style='background-color: #00008B; padding: 1rem; border-radius: 0.5rem; margin-bottom: 2rem;'>
        Obtenha planos personalizados de dieta e fitness adaptados aos seus objetivos e preferências.
        Nosso sistema AI cria o plano perfeito para você, considerando seu perfil único.
        </div>
    """, unsafe_allow_html=True)

    with st.sidebar:
        st.header("🔑 Configuração da API")
        gemini_api_key = st.text_input(
            "Chave da API Gemini",
            type="password",
            help="Insira sua chave da API Gemini para acessar o serviço"
        )
        
        if not gemini_api_key:
            st.warning("⚠️ Por favor, insira sua chave da API Gemini para continuar")
            st.markdown("[Obtenha sua chave da API aqui](https://aistudio.google.com/apikey)")
            return
        
        st.success("Chave da API aceita!")

    if gemini_api_key:
        try:
            gemini_model = Gemini(id="gemini-1.5-flash", api_key=gemini_api_key)
        except Exception as e:
            handle_error(f"Erro ao inicializar o modelo Gemini: {e}")
            return

        st.header("👤 Seu Perfil")
        
        col1, col2 = st.columns(2)
        
        with col1:
            age = st.number_input("Idade", min_value=10, max_value=100, step=1, help="Digite sua idade")
            height = st.number_input("Altura (cm)", min_value=100.0, max_value=250.0, step=0.1)
            activity_level = st.selectbox(
                "Nível de Atividade",
                options=["Sedentário", "Levemente Ativo", "Moderadamente Ativo", "Muito Ativo", "Extremamente Ativo"],
                help="Escolha seu nível de atividade"
            )
            dietary_preferences = st.selectbox(
                "Preferências Alimentares",
                options=["Vegetariano", "Keto", "Sem Glúten", "Low Carb", "Sem Lactose"],
                help="Escolha sua preferência alimentar"
            )

        with col2:
            weight = st.number_input("Peso (kg)", min_value=20.0, max_value=300.0, step=0.1)
            sex = st.selectbox("Sexo", options=["Masculino", "Feminino", "Outro"])
            fitness_goals = st.selectbox(
                "Objetivos de Fitness",
                options=["Perder Peso", "Ganhar Massa Muscular", "Endurance", "Manter-se em Forma", "Treinamento de Força"],
                help="O que você deseja alcançar?"
            )

        if st.button("🎯 Gerar Meu Plano Personalizado", use_container_width=True):
            with st.spinner("Criando seu plano de saúde e fitness perfeito..."):
                try:
                    dietary_agent = Agent(
                        name="Especialista em Dieta",
                        role="Fornece recomendações alimentares personalizadas",
                        model=gemini_model,
                        instructions=[
                            "Considere as entradas do usuário, incluindo restrições alimentares e preferências.",
                            "Sugira um plano de refeições detalhado para o dia, incluindo café da manhã, almoço, jantar e lanches.",
                            "Forneça uma breve explicação de por que o plano é adequado aos objetivos do usuário.",
                            "Foque em clareza, coerência e qualidade das recomendações.",
                        ]
                    )

                    fitness_agent = Agent(
                        name="Especialista em Fitness",
                        role="Fornece recomendações personalizadas de fitness",
                        model=gemini_model,
                        instructions=[
                            "Forneça exercícios adaptados aos objetivos do usuário.",
                            "Inclua aquecimento, treino principal e exercícios de recuperação.",
                            "Explique os benefícios de cada exercício recomendado.",
                            "Garanta que o plano seja acionável e detalhado.",
                        ]
                    )

                    user_profile = f"""
                    Idade: {age}
                    Peso: {weight}kg
                    Altura: {height}cm
                    Sexo: {sex}
                    Nível de Atividade: {activity_level}
                    Preferências Alimentares: {dietary_preferences}
                    Objetivos de Fitness: {fitness_goals}
                    """

                    dietary_plan_response = dietary_agent.run(user_profile)
                    dietary_plan = {
                        "why_this_plan_works": "Alto em Proteínas, Gorduras Saudáveis, Carboidratos Moderados e Equilíbrio Calórico",
                        "meal_plan": dietary_plan_response.content,
                        "important_considerations": """
                        - Hidratação: Beba bastante água ao longo do dia
                        - Eletrólitos: Monitore níveis de sódio, potássio e magnésio
                        - Fibras: Assegure uma ingestão adequada através de vegetais e frutas
                        - Ouça seu corpo: Ajuste as porções conforme necessário
                        """
                    }

                    fitness_plan_response = fitness_agent.run(user_profile)
                    fitness_plan = {
                        "goals": "Construir força, melhorar endurance e manter a forma geral",
                        "routine": fitness_plan_response.content,
                        "tips": """
                        - Acompanhe seu progresso regularmente
                        - Permita o descanso adequado entre os treinos
                        - Foque na forma correta dos exercícios
                        - Seja consistente com sua rotina
                        """
                    }

                    st.session_state.dietary_plan = dietary_plan
                    st.session_state.fitness_plan = fitness_plan
                    st.session_state.plans_generated = True
                    st.session_state.qa_pairs = []

                    display_plan('Dieta', dietary_plan)
                    display_plan('Fitness', fitness_plan)

                except Exception as e:
                    handle_error(f"Erro ao gerar planos: {e}")

        if st.session_state.plans_generated:
            st.header("❓ Perguntas sobre seu plano?")
            question_input = st.text_input("O que você gostaria de saber?")

            if st.button("Obter Resposta"):
                if question_input:
                    with st.spinner("Buscando a melhor resposta para você..."):
                        dietary_plan = st.session_state.dietary_plan
                        fitness_plan = st.session_state.fitness_plan

                        context = f"Dietary Plan: {dietary_plan.get('meal_plan', '')}\n\nFitness Plan: {fitness_plan.get('routine', '')}"
                        full_context = f"{context}\nUser Question: {question_input}"

                        try:
                            agent = Agent(model=gemini_model, show_tool_calls=True, markdown=True)
                            run_response = agent.run(full_context)

                            if hasattr(run_response, 'content'):
                                answer = run_response.content
                            else:
                                answer = "Desculpe, não consegui gerar uma resposta neste momento."

                            st.session_state.qa_pairs.append((question_input, answer))
                        except Exception as e:
                            handle_error(f"Erro ao buscar resposta: {e}")

            if st.session_state.qa_pairs:
                st.header("💬 Histórico de Perguntas & Respostas")
                for question, answer in st.session_state.qa_pairs:
                    st.markdown(f"**Q:** {question}")
                    st.markdown(f"**A:** {answer}")

if __name__ == "__main__":
    main()
