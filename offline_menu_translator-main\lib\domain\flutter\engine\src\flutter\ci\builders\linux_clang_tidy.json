{"builds": [{"archives": [], "drone_dimensions": ["device_type=none", "os=Linux"], "gclient_variables": {"use_rbe": true}, "gn": ["--android", "--android-cpu", "arm64", "--no-lto", "--target-dir", "ci/android_debug_arm64_clang_tidy", "--rbe", "--no-goma"], "name": "ci/android_debug_arm64_clang_tidy", "description": "A debug mode arm64 Android build used only as the basis for a clang-tidy run.", "ninja": {"config": "ci/android_debug_arm64_clang_tidy"}}, {"archives": [], "drone_dimensions": ["device_type=none", "os=Linux"], "gclient_variables": {"use_rbe": true}, "gn": ["--runtime-mode", "debug", "--prebuilt-dart-sdk", "--no-lto", "--target-dir", "ci/host_debug_clang_tidy", "--rbe", "--no-goma"], "name": "ci/host_debug_clang_tidy", "description": "A debug mode Linux host build used only as the basis for a clang-tidy run.", "ninja": {"config": "ci/host_debug_clang_tidy"}}], "tests": [{"name": "test: lint host_debug 0", "recipe": "engine_v2/tester_engine", "drone_dimensions": ["device_type=none", "os=Linux", "cores=32"], "gclient_variables": {"download_android_deps": false, "download_jdk": false}, "dependencies": ["ci/host_debug_clang_tidy"], "tasks": [{"name": "test: lint host_debug", "parameters": ["--variant", "ci/host_debug_clang_tidy", "--lint-all", "--shard-id=0", "--shard-variants=ci/host_debug_clang_tidy,ci/host_debug_clang_tidy,ci/host_debug_clang_tidy"], "max_attempts": 1, "script": "flutter/ci/clang_tidy.sh"}]}, {"name": "test: lint host_debug 1", "recipe": "engine_v2/tester_engine", "drone_dimensions": ["device_type=none", "os=Linux", "cores=32"], "gclient_variables": {"download_android_deps": false, "download_jdk": false}, "dependencies": ["ci/host_debug_clang_tidy"], "tasks": [{"name": "test: lint host_debug", "parameters": ["--variant", "ci/host_debug_clang_tidy", "--lint-all", "--shard-id=1", "--shard-variants=ci/host_debug_clang_tidy,ci/host_debug_clang_tidy,ci/host_debug_clang_tidy"], "max_attempts": 1, "script": "flutter/ci/clang_tidy.sh"}]}, {"name": "test: lint host_debug 2", "recipe": "engine_v2/tester_engine", "drone_dimensions": ["device_type=none", "os=Linux", "cores=32"], "gclient_variables": {"download_android_deps": false, "download_jdk": false}, "dependencies": ["ci/host_debug_clang_tidy"], "tasks": [{"name": "test: lint host_debug", "parameters": ["--variant", "ci/host_debug_clang_tidy", "--lint-all", "--shard-id=2", "--shard-variants=ci/host_debug_clang_tidy,ci/host_debug_clang_tidy,ci/host_debug_clang_tidy"], "max_attempts": 1, "script": "flutter/ci/clang_tidy.sh"}]}, {"name": "test: lint host_debug 3", "recipe": "engine_v2/tester_engine", "drone_dimensions": ["device_type=none", "os=Linux", "cores=32"], "gclient_variables": {"download_android_deps": false, "download_jdk": false}, "dependencies": ["ci/host_debug_clang_tidy"], "tasks": [{"name": "test: lint host_debug", "parameters": ["--variant", "ci/host_debug_clang_tidy", "--lint-all", "--shard-id=3", "--shard-variants=ci/host_debug_clang_tidy,ci/host_debug_clang_tidy,ci/host_debug_clang_tidy"], "max_attempts": 1, "script": "flutter/ci/clang_tidy.sh"}]}, {"name": "test: lint android_debug_arm64", "recipe": "engine_v2/tester_engine", "drone_dimensions": ["device_type=none", "os=Linux", "cores=32"], "dependencies": ["ci/host_debug_clang_tidy", "ci/android_debug_arm64_clang_tidy"], "tasks": [{"name": "test: lint android_debug_arm64", "parameters": ["--variant", "ci/android_debug_arm64_clang_tidy", "--lint-all", "--shard-id=0", "--shard-variants=ci/host_debug_clang_tidy,ci/host_debug_clang_tidy,ci/host_debug_clang_tidy,ci/host_debug_clang_tidy"], "max_attempts": 1, "script": "flutter/ci/clang_tidy.sh"}]}]}