# Get your OpenAI API Key here: https://platform.openai.com/account/api-keys
OPENAI_API_KEY=****

# Get your Financial Datasets API Key here: https://financialdatasets.ai/
FINANCIAL_DATASETS_API_KEY=****

# Get your LangSmith API Key here: https://smith.langchain.com/
LANGCHAIN_API_KEY=****
LANGCHAIN_TRACING_V2=true
LANGCHAIN_PROJECT=ai-financial-agent

# Generate a random secret: https://generate-secret.vercel.app/32 or `openssl rand -base64 32`
AUTH_SECRET=****

# The following keys below are automatically created and
# added to your environment when you deploy on vercel

# Instructions to create a Vercel Blob Store here: https://vercel.com/docs/storage/vercel-blob
BLOB_READ_WRITE_TOKEN=****

# Instructions to create a database here: https://vercel.com/docs/storage/vercel-postgres/quickstart
POSTGRES_URL=****
