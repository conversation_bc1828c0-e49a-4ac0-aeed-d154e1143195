import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:google_generative_ai/google_generative_ai.dart';

import '../models/translation_result.dart';
import '../config/api_keys.dart';

/// Real translation service using Google Gemini API
///
/// This service provides AI translation with multimodal support
/// using Google Generative AI for Web and Mobile compatibility.
class RealTranslationService {
  static final RealTranslationService _instance =
      RealTranslationService._internal();
  static RealTranslationService get instance => _instance;
  RealTranslationService._internal();

  GenerativeModel? _model;
  bool _isInitialized = false;
  String _currentModel = '';
  bool _isModelLoaded = false;

  bool get isInitialized => _isInitialized;
  String get currentModel => _currentModel;
  bool get isOfflineMode => false; // Online with Google Gemini API
  bool get isModelLoaded => _isModelLoaded;

  /// Initialize Google Gemini translation service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🚀 Initializing Google Gemini Translation Service...');
      }

      // Check if API key is configured
      if (!ApiKeys.isGoogleAIConfigured) {
        if (kDebugMode) {
          print('⚠️ Google AI API key not configured');
          print('💡 Please set GEMINI_API_KEY in .env file');
        }
        throw Exception('Google AI API key not configured');
      }

      // Initialize Google Gemini model
      await _loadGeminiModel();

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Google Gemini Translation Service initialized successfully!');
        print('🧠 Using Google Gemini ${ApiKeys.geminiModel}');
        print('📱 Platform: ${defaultTargetPlatform.name}');
        print('🌐 Mode: Online with Google AI');
        print('📦 Model loaded: $_isModelLoaded');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Google Gemini Translation Service: $e');
        print('⚠️ Falling back to placeholder mode');
      }

      // Fallback to placeholder mode
      _isInitialized = true;
      _currentModel = 'gemini_fallback';
    }
  }

  /// Load Google Gemini model for translation
  Future<void> _loadGeminiModel() async {
    try {
      if (kDebugMode) {
        print('🔄 Loading Google Gemini model...');
      }

      // Create Google Gemini model
      _model = GenerativeModel(
        model: ApiKeys.geminiModel,
        apiKey: ApiKeys.googleAI,
        generationConfig: GenerationConfig(
          temperature: ApiKeys.temperature,
          topK: ApiKeys.topK,
          topP: ApiKeys.topP,
          maxOutputTokens: ApiKeys.maxTokens,
        ),
        safetySettings: [
          SafetySetting(HarmCategory.harassment, HarmBlockThreshold.medium),
          SafetySetting(HarmCategory.hateSpeech, HarmBlockThreshold.medium),
          SafetySetting(
              HarmCategory.sexuallyExplicit, HarmBlockThreshold.medium),
          SafetySetting(
              HarmCategory.dangerousContent, HarmBlockThreshold.medium),
        ],
      );

      _isModelLoaded = true;
      _currentModel = ApiKeys.geminiModel;

      if (kDebugMode) {
        print('✅ Google Gemini model loaded successfully!');
        print('🧠 Model: ${ApiKeys.geminiModel}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Failed to load Google Gemini model: $e');
        print('💡 Check your API key and internet connection');
      }
      _isModelLoaded = false;
      _currentModel = 'gemini-placeholder';
    }
  }

  /// Download and install recommended model (placeholder)
  Future<bool> downloadRecommendedModel({
    Function(double)? onProgress,
    VoidCallback? onComplete,
    Function(String)? onError,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      if (kDebugMode) {
        print('🔄 Downloading recommended Gemma-3N model (placeholder)...');
      }

      // Simulate download progress
      for (int i = 0; i <= 100; i += 10) {
        await Future.delayed(const Duration(milliseconds: 100));
        onProgress?.call(i / 100.0);
      }

      onComplete?.call();

      if (kDebugMode) {
        print('✅ Model download simulation completed!');
      }

      return true;
    } catch (e) {
      final error = 'Failed to download model: $e';
      if (kDebugMode) print('❌ $error');
      onError?.call(error);
      return false;
    }
  }

  /// Translate text using native Flutter Gemma
  Future<TranslationResult> translateText({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String? context,
    String? domain,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final startTime = DateTime.now();

    try {
      if (kDebugMode) {
        print('🔄 Translating text with Flutter Gemma...');
        print(
            '📝 Text: ${text.length > 50 ? '${text.substring(0, 50)}...' : text}');
        print('🌍 From: $sourceLanguage → To: $targetLanguage');
      }

      String translatedText;

      if (_isModelLoaded && _model != null) {
        // Use real Google Gemini model
        translatedText = await _translateWithGemini(
            text, sourceLanguage, targetLanguage, context, domain);
      } else {
        // Force real translation even without model loaded
        if (ApiKeys.isGoogleAIConfigured) {
          // Try to initialize and translate
          await _loadGeminiModel();
          if (_model != null) {
            translatedText = await _translateWithGemini(
                text, sourceLanguage, targetLanguage, context, domain);
          } else {
            throw Exception('Failed to load Google Gemini model');
          }
        } else {
          throw Exception(
              'Google AI API key not configured. Please set GEMINI_API_KEY in .env file');
        }
      }

      final processingTime = DateTime.now().difference(startTime);

      if (kDebugMode) {
        print('✅ Translation completed!');
        print('⏱️ Processing time: ${processingTime.inMilliseconds}ms');
        print(
            '🎯 Result: ${translatedText.length > 50 ? '${translatedText.substring(0, 50)}...' : translatedText}');
      }

      return TranslationResult(
        originalText: text,
        translatedText: translatedText,
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        confidence: _isModelLoaded ? 0.95 : 0.7,
        timestamp: DateTime.now(),
        processingTime: processingTime,
        metadata: {
          'model': _currentModel,
          'service': 'flutter_gemma_native',
          'platform': defaultTargetPlatform.name,
          'native': true,
          'model_loaded': _isModelLoaded,
          'processing_time_ms': processingTime.inMilliseconds,
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Translation failed: $e');
      }

      // Return fallback translation
      return TranslationResult(
        originalText: text,
        translatedText: 'Translation error: $e',
        sourceLanguage: sourceLanguage,
        targetLanguage: targetLanguage,
        confidence: 0.0,
        timestamp: DateTime.now(),
        metadata: {'error': e.toString(), 'service': 'flutter_gemma_native'},
      );
    }
  }

  /// Translate using Google Gemini model - REAL TRANSLATION
  Future<String> _translateWithGemini(String text, String sourceLanguage,
      String targetLanguage, String? context, String? domain) async {
    try {
      if (kDebugMode) {
        print(
            '🌐 REAL TRANSLATION: Translating "$text" from $sourceLanguage to $targetLanguage');
      }

      // Build optimized prompt for REAL translation
      final prompt = _buildRealTranslationPrompt(
          text, sourceLanguage, targetLanguage, context, domain);

      if (kDebugMode) {
        print('📝 Prompt: $prompt');
      }

      // Create content for Gemini with proper format
      final content = Content.text(prompt);

      // Generate REAL response using Gemini API
      final response = await _model!.generateContent([content]);

      if (response.text != null && response.text!.isNotEmpty) {
        final translation = _extractRealTranslation(response.text!);

        if (kDebugMode) {
          print('✅ REAL TRANSLATION SUCCESS: "$text" → "$translation"');
        }

        return translation;
      } else {
        throw Exception('Empty response from Gemini model');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ REAL TRANSLATION FAILED: $e');
      }
      // Re-throw the error instead of using fallback to force real translation
      throw Exception('Real translation failed: $e');
    }
  }

  /// Build REAL translation prompt for Google Gemini
  String _buildRealTranslationPrompt(String text, String sourceLanguage,
      String targetLanguage, String? context, String? domain) {
    final buffer = StringBuffer();

    buffer.writeln(
        'You are a professional translator with expertise in multiple languages.');
    buffer.writeln('Your task is to provide accurate, natural translations.');
    buffer.writeln('');

    // Language specification
    if (sourceLanguage.toLowerCase() == 'auto') {
      buffer.writeln(
          'TASK: Detect the language of the input text and translate it to $targetLanguage.');
    } else {
      buffer
          .writeln('TASK: Translate from $sourceLanguage to $targetLanguage.');
    }

    // Context and domain
    if (context != null && context.isNotEmpty) {
      buffer.writeln('CONTEXT: $context');
    }
    if (domain != null && domain.isNotEmpty) {
      buffer.writeln('DOMAIN: $domain');
    }

    buffer.writeln('');
    buffer.writeln('RULES:');
    buffer.writeln('1. Provide ONLY the translation, no explanations');
    buffer.writeln('2. Maintain the original tone and style');
    buffer.writeln('3. Preserve formatting (line breaks, punctuation)');
    buffer.writeln('4. Use natural, fluent language in the target language');
    buffer.writeln(
        '5. If the text is already in the target language, return it unchanged');
    buffer.writeln('');
    buffer.writeln('TEXT TO TRANSLATE:');
    buffer.writeln('"$text"');
    buffer.writeln('');
    buffer.writeln('TRANSLATION:');

    return buffer.toString();
  }

  /// Build optimized prompt for translation (legacy method)
  String _buildTranslationPrompt(String text, String sourceLanguage,
      String targetLanguage, String? context, String? domain) {
    return _buildRealTranslationPrompt(
        text, sourceLanguage, targetLanguage, context, domain);
  }

  /// Extract REAL translation from Google Gemini response
  String _extractRealTranslation(String response) {
    String cleaned = response.trim();

    if (kDebugMode) {
      print('🔍 Raw Gemini response: "$response"');
    }

    // Remove common prefixes and labels
    cleaned = cleaned.replaceAll(
        RegExp(
            r'^(Translation:|Tradução:|Translated text:|Texto traduzido:|TRANSLATION:)\s*',
            caseSensitive: false),
        '');
    cleaned = cleaned.replaceAll(
        RegExp(r'^(Here is the translation:|Aqui está a tradução:)\s*',
            caseSensitive: false),
        '');

    // Remove markdown formatting
    cleaned = cleaned.replaceAll(RegExp(r'```[a-zA-Z]*\n?'), '');
    cleaned = cleaned.replaceAll(RegExp(r'\n```'), '');

    // Remove quotes if the entire response is quoted
    if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }

    // Remove single quotes if the entire response is quoted
    if (cleaned.startsWith("'") && cleaned.endsWith("'")) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }

    // Clean up extra whitespace and newlines
    cleaned = cleaned.replaceAll(RegExp(r'\n+'), ' ');
    cleaned = cleaned.replaceAll(RegExp(r'\s+'), ' ');

    final result = cleaned.trim();

    if (kDebugMode) {
      print('✨ Extracted translation: "$result"');
    }

    return result;
  }

  /// Extract clean translation from model response (legacy method)
  String _extractTranslation(String response) {
    return _extractRealTranslation(response);
  }

  /// REAL Image Translation with Google Gemini Vision API
  Future<Map<String, String>> _translateImageWithGeminiVision(
    Uint8List imageBytes,
    String sourceLanguage,
    String targetLanguage,
    String? additionalContext,
  ) async {
    try {
      if (kDebugMode) {
        print(
            '🔍 REAL GEMINI VISION: Analyzing image for text extraction and translation...');
      }

      // Build prompt for image analysis and translation
      final prompt = _buildImageTranslationPrompt(
        sourceLanguage,
        targetLanguage,
        additionalContext,
      );

      if (kDebugMode) {
        print('📝 Vision prompt: $prompt');
      }

      // Create content with image and text prompt for Gemini Vision
      final content = Content.multi([
        TextPart(prompt),
        DataPart('image/jpeg',
            imageBytes), // Gemini Vision supports multiple image formats
      ]);

      // Generate REAL response using Gemini Vision API
      final response = await _model!.generateContent([content]);

      if (response.text != null && response.text!.isNotEmpty) {
        final result = _parseImageTranslationResponse(response.text!);

        if (kDebugMode) {
          print(
              '✅ REAL VISION SUCCESS: "${result['extractedText']}" → "${result['translatedText']}"');
        }

        return result;
      } else {
        throw Exception('Empty response from Gemini Vision API');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ REAL VISION TRANSLATION FAILED: $e');
      }
      // Return error result instead of throwing to handle gracefully
      return {
        'extractedText': 'Error extracting text from image',
        'translatedText': _getNoTextInImageMessage(targetLanguage),
      };
    }
  }

  /// Build prompt for image translation with Gemini Vision
  String _buildImageTranslationPrompt(
    String sourceLanguage,
    String targetLanguage,
    String? additionalContext,
  ) {
    final buffer = StringBuffer();

    buffer.writeln('You are a professional OCR and translation expert.');
    buffer.writeln('Analyze this image and perform the following tasks:');
    buffer.writeln('');
    buffer
        .writeln('1. EXTRACT: Find and extract ALL text visible in the image');
    buffer.writeln('2. TRANSLATE: Translate the extracted text');
    buffer.writeln('');

    if (sourceLanguage.toLowerCase() == 'auto') {
      buffer.writeln(
          'TASK: Detect the language of any text in the image and translate it to $targetLanguage.');
    } else {
      buffer.writeln(
          'TASK: Extract text in $sourceLanguage and translate it to $targetLanguage.');
    }

    if (additionalContext != null && additionalContext.isNotEmpty) {
      buffer.writeln('CONTEXT: $additionalContext');
    }

    buffer.writeln('');
    buffer.writeln('RESPONSE FORMAT:');
    buffer.writeln('EXTRACTED_TEXT: [the text you found in the image]');
    buffer.writeln('TRANSLATED_TEXT: [the translation of that text]');
    buffer.writeln('');
    buffer.writeln('RULES:');
    buffer.writeln(
        '1. If no text is found, respond with "EXTRACTED_TEXT: No text detected"');
    buffer.writeln('2. Provide accurate OCR extraction');
    buffer.writeln('3. Provide natural, fluent translation');
    buffer.writeln('4. Preserve formatting and structure');
    buffer.writeln('5. Use the exact format specified above');

    return buffer.toString();
  }

  /// Parse the response from Gemini Vision API
  Map<String, String> _parseImageTranslationResponse(String response) {
    if (kDebugMode) {
      print('🔍 Parsing Vision response: "$response"');
    }

    String extractedText = 'No text detected';
    String translatedText = 'No text detected';

    // Try to parse the structured response
    final lines = response.split('\n');
    for (final line in lines) {
      if (line.startsWith('EXTRACTED_TEXT:')) {
        extractedText = line.substring('EXTRACTED_TEXT:'.length).trim();
      } else if (line.startsWith('TRANSLATED_TEXT:')) {
        translatedText = line.substring('TRANSLATED_TEXT:'.length).trim();
      }
    }

    // Fallback parsing if structured format not found
    if (extractedText == 'No text detected' &&
        translatedText == 'No text detected') {
      final cleanResponse = response.trim();
      if (cleanResponse.isNotEmpty &&
          !cleanResponse.toLowerCase().contains('no text')) {
        // Assume the entire response is the translation
        extractedText = 'Text found in image';
        translatedText = cleanResponse;
      }
    }

    return {
      'extractedText': extractedText,
      'translatedText': translatedText,
    };
  }

  /// Get "no text in image" message in target language
  String _getNoTextInImageMessage(String targetLanguage) {
    switch (targetLanguage) {
      case 'pt':
        return 'Nenhum texto detectado na imagem';
      case 'es':
        return 'No se detectó texto en la imagen';
      case 'fr':
        return 'Aucun texte détecté dans l\'image';
      case 'de':
        return 'Kein Text im Bild erkannt';
      case 'it':
        return 'Nessun testo rilevato nell\'immagine';
      case 'ja':
        return '画像にテキストが検出されませんでした';
      case 'ko':
        return '이미지에서 텍스트가 감지되지 않았습니다';
      case 'zh':
        return '图像中未检测到文本';
      default:
        return 'No text detected in image';
    }
  }

  /// REMOVED: Fallback translation method - NO MORE SIMULATION!
  /// This method has been removed to force REAL translation only.
  /// All translations now use Google Gemini API for authentic results.
  Future<String> _translateFallback(
      String text, String sourceLanguage, String targetLanguage) async {
    // NO MORE FAKE TRANSLATIONS!
    throw Exception('Fallback translation disabled! '
        'This app now uses REAL Google Gemini translation only. '
        'Please ensure your GEMINI_API_KEY is configured in .env file.');
  }

  /// REMOVED: Translation database - NO MORE SIMULATION!
  /// This database has been removed to force REAL translation only.

  /// REMOVED: Pattern-based translation methods - NO MORE SIMULATION!
  /// These methods have been removed to force REAL translation only.

  /// REMOVED: No translation message method - NO MORE SIMULATION!
  /// This method has been removed to force REAL translation only.

  /// Translate image using REAL Google Gemini Vision API
  Future<TranslationResult> translateImage({
    required Uint8List imageBytes,
    required String targetLanguage,
    String? sourceLanguage,
    String? additionalContext,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final startTime = DateTime.now();

    try {
      if (kDebugMode) {
        print('🔄 REAL IMAGE TRANSLATION with Google Gemini Vision...');
        print('🖼️ Image size: ${imageBytes.length} bytes');
        print('🌍 Target language: $targetLanguage');
      }

      // Force real translation - no more simulation!
      if (!_isModelLoaded || _model == null) {
        await _loadGeminiModel();
        if (_model == null) {
          throw Exception(
              'Failed to load Google Gemini model for image translation');
        }
      }

      // Step 1: Use REAL Google Gemini Vision for OCR + Translation
      final result = await _translateImageWithGeminiVision(
        imageBytes,
        sourceLanguage ?? 'auto',
        targetLanguage,
        additionalContext,
      );

      final extractedText = result['extractedText'] as String;
      final translatedText = result['translatedText'] as String;

      if (kDebugMode) {
        print(
            '✅ REAL IMAGE TRANSLATION SUCCESS: "$extractedText" → "$translatedText"');
      }

      final processingTime = DateTime.now().difference(startTime);

      if (kDebugMode) {
        print('✅ Image translation completed!');
        print('⏱️ Processing time: ${processingTime.inMilliseconds}ms');
        print(
            '🎯 Result: ${translatedText.length > 50 ? '${translatedText.substring(0, 50)}...' : translatedText}');
      }

      return TranslationResult(
        originalText: extractedText,
        translatedText: translatedText,
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence:
            _calculateImageTranslationConfidence(extractedText, translatedText),
        timestamp: DateTime.now(),
        processingTime: processingTime,
        metadata: {
          'model': _currentModel,
          'service': 'flutter_gemma_native',
          'platform': defaultTargetPlatform.name,
          'native': true,
          'multimodal': true,
          'ocr_enabled': true,
          'image_size': imageBytes.length,
          'extracted_text': extractedText,
          'processing_time_ms': processingTime.inMilliseconds,
          'model_loaded': _isModelLoaded,
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Image translation failed: $e');
      }

      // Return fallback translation
      return TranslationResult(
        originalText: 'Image content',
        translatedText: 'Image translation error: $e',
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: 0.0,
        timestamp: DateTime.now(),
        metadata: {
          'error': e.toString(),
          'type': 'image',
          'service': 'flutter_gemma_native'
        },
      );
    }
  }

  /// REMOVED: OCR simulation method - NO MORE SIMULATION!
  /// This method has been removed to force REAL translation only.

  /// REMOVED: Simulated image texts method - NO MORE SIMULATION!
  /// This method has been removed to force REAL translation only.

  /// Calculate confidence for image translation
  double _calculateImageTranslationConfidence(
      String extractedText, String translatedText) {
    if (extractedText == 'No text detected') {
      return 0.3; // Low confidence for no text
    }

    if (translatedText.contains('Translation not available') ||
        translatedText.contains('error')) {
      return 0.1; // Very low confidence for errors
    }

    // Base confidence
    double confidence = 0.8;

    // Increase confidence for longer, more detailed text
    if (extractedText.length > 20) confidence += 0.1;
    if (extractedText.length > 50) confidence += 0.05;

    // Increase confidence if translation looks good
    if (translatedText.length > extractedText.length * 0.5) confidence += 0.05;

    return confidence.clamp(0.0, 1.0);
  }

  /// REMOVED: Duplicate method - already defined above

  /// Check if service is healthy
  Future<bool> isHealthy() async {
    return _isInitialized;
  }

  /// Check if offline mode is available (always true for native)
  Future<bool> isOfflineAvailable() async {
    return _isInitialized;
  }

  /// Switch to offline mode (no-op since always offline)
  Future<void> switchToOfflineMode() async {
    if (kDebugMode) {
      print('🔒 Already in native offline mode');
    }
  }

  /// Switch to online mode (no-op since always offline)
  Future<void> switchToOnlineMode() async {
    if (kDebugMode) {
      print('🔒 Cannot switch to online mode - using native AI exclusively');
    }
  }

  /// Get current service status
  Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'offline_mode': true, // Always offline with native AI
      'current_model': _currentModel,
      'service_type': 'flutter_gemma_placeholder',
      'platform': defaultTargetPlatform.name,
      'model_loaded': _isInitialized,
      'multimodal_capable': true,
      'native': true,
      'placeholder': true,
    };
  }

  /// Dispose resources
  Future<void> dispose() async {
    try {
      _isInitialized = false;
      _currentModel = '';

      if (kDebugMode) {
        print('🗑️ Native Translation Service disposed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error disposing Native Translation Service: $e');
      }
    }
  }
}
