# Copyright 2013 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

import("//flutter/build/dart/dart.gni")

# Generates an assembly file defining a given symbol with the bytes from a
# binary file. Places the symbol in a text section if 'executable' is true,
# otherwise places the symbol in a read-only data section.
template("bin_to_assembly") {
  assert(defined(invoker.deps), "Must define deps")
  assert(defined(invoker.input), "Must define input binary file")
  assert(defined(invoker.symbol), "Must define symbol name")
  assert(defined(invoker.executable), "Must define boolean executable")

  action(target_name) {
    deps = invoker.deps
    script = "$dart_src/runtime/tools/bin_to_assembly.py"
    output = "$target_gen_dir/${invoker.input}.S"
    args = [
      "--input",
      rebase_path(invoker.input),
      "--output",
      rebase_path(output),
      "--symbol_name",
      invoker.symbol,
      "--target_os",
      current_os,
    ]
    if (defined(invoker.size_symbol)) {
      args += [
        "--size_symbol_name",
        invoker.size_symbol,
        "--target_arch",
        current_cpu,
      ]
    }
    if (invoker.executable) {
      args += [ "--executable" ]
    }
    if (current_os != "win") {
      args += [ "--incbin" ]
    }
    inputs = [
      script,
      invoker.input,
    ]
    outputs = [ output ]
  }
}

# Generates an object file defining a given symbol with the bytes from a
# binary file. Places the symbol in the read-only data section.
template("bin_to_coff") {
  assert(defined(invoker.deps), "Must define deps")
  assert(defined(invoker.input), "Must define input binary file")
  assert(defined(invoker.symbol), "Must define symbol name")
  assert(defined(invoker.executable), "Must define executable")

  action(target_name) {
    deps = invoker.deps
    script = "$dart_src/runtime/tools/bin_to_coff.py"
    output = "$target_gen_dir/${invoker.input}.o"
    args = [
      "--input",
      rebase_path(invoker.input),
      "--output",
      rebase_path(output),
      "--symbol_name",
      invoker.symbol,
    ]

    if (defined(invoker.size_symbol)) {
      args += [
        "--size_symbol_name",
        invoker.size_symbol,
      ]
    }

    if (invoker.executable) {
      args += [ "--executable" ]
    }

    args += [ "--arch=$current_cpu" ]
    inputs = [ invoker.input ]
    outputs = [ output ]
  }
}

# Generates a linkable output file defining the specified symbol with the bytes
# from the binary file. Emits a COFF object file when targeting Windows,
# otherwise assembly.
template("bin_to_linkable") {
  assert(defined(invoker.deps), "Must define deps")
  assert(defined(invoker.input), "Must define input binary file")
  assert(defined(invoker.symbol), "Must define symbol name")
  target_type = "bin_to_assembly"
  if (is_win) {
    target_type = "bin_to_coff"
  }

  target(target_type, target_name) {
    forward_variables_from(invoker, "*")
  }
}
