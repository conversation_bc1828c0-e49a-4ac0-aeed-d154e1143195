{"http://arxiv.org/abs/2407.09394v1": {"url": "http://arxiv.org/abs/2407.09394v1", "title": "PersonaRAG: Enhancing Retrieval-Augmented Generation Systems with User-Centric Agents", "description": "Large Language Models (LLMs) struggle with generating reliable outputs due to outdated knowledge and hallucinations. Retrieval-Augmented Generation (RAG) models address this by enhancing LLMs with external knowledge, but often fail to personalize the retrieval process. This paper introduces PersonaRAG, a novel framework incorporating user-centric agents to adapt retrieval and generation based on real-time user data and interactions. Evaluated across various question answering datasets, PersonaRAG demonstrates superiority over baseline models, providing tailored answers to user needs. The results suggest promising directions for user-adapted information retrieval systems.", "authors": ["<PERSON><PERSON>", "<PERSON>"], "published": "2024-07-12", "pdf_url": "http://arxiv.org/pdf/2407.09394v1", "categories": ["cs.IR"]}, "http://arxiv.org/abs/2410.09942v1": {"url": "http://arxiv.org/abs/2410.09942v1", "title": "Learning to Rank for Multiple Retrieval-Augmented Models through Iterative Utility Maximization", "description": "This paper investigates the design of a unified search engine to serve multiple retrieval-augmented generation (RAG) agents, each with a distinct task, backbone large language model (LLM), and retrieval-augmentation strategy. We introduce an iterative approach where the search engine generates retrieval results for these RAG agents and gathers feedback on the quality of the retrieved documents during an offline phase. This feedback is then used to iteratively optimize the search engine using a novel expectation-maximization algorithm, with the goal of maximizing each agent's utility function. Additionally, we adapt this approach to an online setting, allowing the search engine to refine its behavior based on real-time individual agents feedback to better serve the results for each of them. Experiments on diverse datasets from the Knowledge-Intensive Language Tasks (KILT) benchmark demonstrates that our approach significantly on average outperforms competitive baselines across 18 RAG...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2024-10-13", "pdf_url": "http://arxiv.org/pdf/2410.09942v1", "categories": ["cs.CL", "cs.IR"]}, "http://arxiv.org/abs/2501.09136v3": {"url": "http://arxiv.org/abs/2501.09136v3", "title": "Agentic Retrieval-Augmented Generation: A Survey on Agentic RAG", "description": "Large Language Models (LLMs) have revolutionized artificial intelligence (AI) by enabling human like text generation and natural language understanding. However, their reliance on static training data limits their ability to respond to dynamic, real time queries, resulting in outdated or inaccurate outputs. Retrieval Augmented Generation (RAG) has emerged as a solution, enhancing LLMs by integrating real time data retrieval to provide contextually relevant and up-to-date responses. Despite its promise, traditional RAG systems are constrained by static workflows and lack the adaptability required for multistep reasoning and complex task management.   Agentic Retrieval-Augmented Generation (Agentic RAG) transcends these limitations by embedding autonomous AI agents into the RAG pipeline. These agents leverage agentic design patterns reflection, planning, tool use, and multiagent collaboration to dynamically manage retrieval strategies, iteratively refine contextual understanding, and ...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2025-01-15", "pdf_url": "http://arxiv.org/pdf/2501.09136v3", "categories": ["cs.AI", "cs.CL", "cs.IR"]}, "http://arxiv.org/abs/2412.04759v2": {"url": "http://arxiv.org/abs/2412.04759v2", "title": "REGENT: A Retrieval-Augmented Generalist Agent That Can Act In-Context in New Environments", "description": "Building generalist agents that can rapidly adapt to new environments is a key challenge for deploying AI in the digital and real worlds. Is scaling current agent architectures the most effective way to build generalist agents? We propose a novel approach to pre-train relatively small policies on relatively small datasets and adapt them to unseen environments via in-context learning, without any finetuning. Our key idea is that retrieval offers a powerful bias for fast adaptation. Indeed, we demonstrate that even a simple retrieval-based 1-nearest neighbor agent offers a surprisingly strong baseline for today's state-of-the-art generalist agents. From this starting point, we construct a semi-parametric agent, REGENT, that trains a transformer-based policy on sequences of queries and retrieved neighbors. REGENT can generalize to unseen robotics and game-playing environments via retrieval augmentation and in-context learning, achieving this with up to 3x fewer parameters and up to an ...", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "published": "2024-12-06", "pdf_url": "http://arxiv.org/pdf/2412.04759v2", "categories": ["cs.AI"]}, "http://arxiv.org/abs/2405.18111v3": {"url": "http://arxiv.org/abs/2405.18111v3", "title": "ATM: Adversarial Tuning Multi-agent System Makes a Robust Retrieval-Augmented Generator", "description": "Large language models (LLMs) are proven to benefit a lot from retrieval-augmented generation (RAG) in alleviating hallucinations confronted with knowledge-intensive questions. RAG adopts information retrieval techniques to inject external knowledge from semantic-relevant documents as input contexts. However, since today's Internet is flooded with numerous noisy and fabricating content, it is inevitable that RAG systems are vulnerable to these noises and prone to respond incorrectly. To this end, we propose to optimize the retrieval-augmented Generator with an Adversarial Tuning Multi-agent system (ATM). The ATM steers the Generator to have a robust perspective of useful documents for question answering with the help of an auxiliary Attacker agent through adversarially tuning the agents for several iterations. After rounds of multi-agent iterative tuning, the Generator can eventually better discriminate useful documents amongst fabrications. The experimental results verify the effect...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> Yan", "Haibo Shi", "<PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2024-05-28", "pdf_url": "http://arxiv.org/pdf/2405.18111v3", "categories": ["cs.CL"]}, "http://arxiv.org/abs/2404.11964v1": {"url": "http://arxiv.org/abs/2404.11964v1", "title": "From Language Models to Practical Self-Improving Computer Agents", "description": "We develop a simple and straightforward methodology to create AI computer agents that can carry out diverse computer tasks and self-improve by developing tools and augmentations to enable themselves to solve increasingly complex tasks. As large language models (LLMs) have been shown to benefit from non-parametric augmentations, a significant body of recent work has focused on developing software that augments LLMs with various capabilities. Rather than manually developing static software to augment LLMs through human engineering effort, we propose that an LLM agent can systematically generate software to augment itself. We show, through a few case studies, that a minimal querying loop with appropriate prompt engineering allows an LLM to generate and use various augmentations, freely extending its own capabilities to carry out real-world computer tasks. Starting with only terminal access, we prompt an LLM agent to augment itself with retrieval, internet search, web navigation, and te...", "authors": ["<PERSON>"], "published": "2024-04-18", "pdf_url": "http://arxiv.org/pdf/2404.11964v1", "categories": ["cs.AI", "68T01", "I.2.0"]}, "http://arxiv.org/abs/2412.05838v1": {"url": "http://arxiv.org/abs/2412.05838v1", "title": "A Collaborative Multi-Agent Approach to Retrieval-Augmented Generation Across Diverse Data", "description": "Retrieval-Augmented Generation (RAG) enhances Large Language Models (LLMs) by incorporating external, domain-specific data into the generative process. While LLMs are highly capable, they often rely on static, pre-trained datasets, limiting their ability to integrate dynamic or private data. Traditional RAG systems typically use a single-agent architecture to handle query generation, data retrieval, and response synthesis. However, this approach becomes inefficient when dealing with diverse data sources, such as relational databases, document stores, and graph databases, often leading to performance bottlenecks and reduced accuracy. This paper proposes a multi-agent RAG system to address these limitations. Specialized agents, each optimized for a specific data source, handle query generation for relational, NoSQL, and document-based systems. These agents collaborate within a modular framework, with query execution delegated to an environment designed for compatibility across various...", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "Saba Attar", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "published": "2024-12-08", "pdf_url": "http://arxiv.org/pdf/2412.05838v1", "categories": ["cs.AI"]}, "http://arxiv.org/abs/2410.14594v2": {"url": "http://arxiv.org/abs/2410.14594v2", "title": "Toolshed: Scale Tool-Equipped Agents with Advanced RAG-Tool Fusion and Tool Knowledge Bases", "description": "Recent advancements in tool-equipped Agents (LLMs) have enabled complex tasks like secure database interactions and multi-agent code development. However, scaling tool capacity beyond agent reasoning or model limits remains a challenge. In this paper, we address these challenges by introducing Toolshed Knowledge Bases, a tool knowledge base (vector database) designed to store enhanced tool representations and optimize tool selection for large-scale tool-equipped Agents. Additionally, we propose Advanced RAG-Tool Fusion, a novel ensemble of tool-applied advanced retrieval-augmented generation (RAG) techniques across the pre-retrieval, intra-retrieval, and post-retrieval phases, without requiring model fine-tuning. During pre-retrieval, tool documents are enhanced with key information and stored in the Toolshed Knowledge Base. Intra-retrieval focuses on query planning and transformation to increase retrieval accuracy. Post-retrieval refines the retrieved tool documents and enables sel...", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "published": "2024-10-18", "pdf_url": "http://arxiv.org/pdf/2410.14594v2", "categories": ["cs.CL"]}, "http://arxiv.org/abs/2412.18431v1": {"url": "http://arxiv.org/abs/2412.18431v1", "title": "GeAR: Graph-enhanced Agent for Retrieval-augmented Generation", "description": "Retrieval-augmented generation systems rely on effective document retrieval capabilities. By design, conventional sparse or dense retrievers face challenges in multi-hop retrieval scenarios. In this paper, we present GeAR, which advances RAG performance through two key innovations: (i) graph expansion, which enhances any conventional base retriever, such as BM25, and (ii) an agent framework that incorporates graph expansion. Our evaluation demonstrates GeAR's superior retrieval performance on three multi-hop question answering datasets. Additionally, our system achieves state-of-the-art results with improvements exceeding 10% on the challenging MuSiQue dataset, while requiring fewer tokens and iterations compared to other multi-step retrieval systems.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "published": "2024-12-24", "pdf_url": "http://arxiv.org/pdf/2412.18431v1", "categories": ["cs.CL", "cs.AI", "cs.IR"]}, "http://arxiv.org/abs/2408.00798v1": {"url": "http://arxiv.org/abs/2408.00798v1", "title": "Golden-Retriever: High-Fidelity Agentic Retrieval Augmented Generation for Industrial Knowledge Base", "description": "This paper introduces Golden-Retriever, designed to efficiently navigate vast industrial knowledge bases, overcoming challenges in traditional LLM fine-tuning and RAG frameworks with domain-specific jargon and context interpretation. Golden-Retriever incorporates a reflection-based question augmentation step before document retrieval, which involves identifying jargon, clarifying its meaning based on context, and augmenting the question accordingly. Specifically, our method extracts and lists all jargon and abbreviations in the input question, determines the context against a pre-defined list, and queries a jargon dictionary for extended definitions and descriptions. This comprehensive augmentation ensures the RAG framework retrieves the most relevant documents by providing clear context and resolving ambiguities, significantly improving retrieval accuracy. Evaluations using three open-source LLMs on a domain-specific question-answer dataset demonstrate Golden-Retriever's superior p...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "published": "2024-07-20", "pdf_url": "http://arxiv.org/pdf/2408.00798v1", "categories": ["cs.IR", "cs.AI", "cs.CL", "cs.DL"]}}