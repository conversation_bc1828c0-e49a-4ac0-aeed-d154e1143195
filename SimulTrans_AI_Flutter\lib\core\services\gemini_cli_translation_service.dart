import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/translation_result.dart';

/// Translation service using Google Gemini CLI for real AI translation
class GeminiCliTranslationService {
  static final GeminiCliTranslationService _instance = GeminiCliTranslationService._internal();
  factory GeminiCliTranslationService() => _instance;
  static GeminiCliTranslationService get instance => _instance;
  GeminiCliTranslationService._internal();

  bool _isInitialized = false;
  bool _isAvailable = false;

  /// Initialize the Gemini CLI service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🚀 Initializing Gemini CLI Translation Service...');
      }

      // Check if Gemini CLI is available
      final result = await Process.run('gemini', ['--version']);
      
      if (result.exitCode == 0) {
        _isAvailable = true;
        if (kDebugMode) {
          print('✅ Gemini CLI found: ${result.stdout.toString().trim()}');
        }
      } else {
        _isAvailable = false;
        if (kDebugMode) {
          print('❌ Gemini CLI not available');
        }
      }

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Gemini CLI Translation Service initialized!');
        print('🌐 Available: $_isAvailable');
        print('🔒 Privacy: Cloud-based with Google Gemini');
        print('💰 Cost: Uses Google Gemini API');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to initialize Gemini CLI service: $e');
      }
      _isAvailable = false;
      _isInitialized = true;
    }
  }

  /// Check if service is ready for translation
  bool get isReady => _isInitialized && _isAvailable;

  /// Check if Gemini CLI is available
  bool get isAvailable => _isAvailable;

  /// Translate text using Gemini CLI
  Future<TranslationResult> translateText({
    required String text,
    required String targetLanguage,
    String? sourceLanguage,
    String? context,
    String? domain,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    final startTime = DateTime.now();

    try {
      if (kDebugMode) {
        print('🔄 Translating text with Gemini CLI...');
        print('📝 Text: ${text.length > 50 ? '${text.substring(0, 50)}...' : text}');
        print('🌍 From: ${sourceLanguage ?? 'auto'} → To: $targetLanguage');
      }

      if (!_isAvailable) {
        throw Exception('Gemini CLI not available. Please install it first.');
      }

      // Build translation prompt
      final prompt = _buildTranslationPrompt(text, sourceLanguage ?? 'auto', targetLanguage, context, domain);

      if (kDebugMode) {
        print('📝 Prompt: ${prompt.substring(0, 100)}...');
      }

      // Execute Gemini CLI command
      final result = await Process.run('gemini', ['chat', prompt]);

      if (result.exitCode != 0) {
        throw Exception('Gemini CLI error: ${result.stderr}');
      }

      final response = result.stdout.toString().trim();
      final translatedText = _extractTranslation(response);

      final processingTime = DateTime.now().difference(startTime);

      if (kDebugMode) {
        print('✅ GEMINI CLI TRANSLATION SUCCESS: "$text" → "$translatedText"');
        print('⏱️ Processing time: ${processingTime.inMilliseconds}ms');
      }

      return TranslationResult(
        originalText: text,
        translatedText: translatedText,
        sourceLanguage: sourceLanguage ?? 'auto',
        targetLanguage: targetLanguage,
        confidence: 0.95, // High confidence for Gemini
        timestamp: DateTime.now(),
        processingTime: processingTime,
        metadata: {
          'translationEngine': 'Google-Gemini-CLI',
          'isOffline': false,
          'modelType': 'cloud',
          'version': 'gemini-pro',
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ GEMINI CLI TRANSLATION FAILED: $e');
      }
      rethrow;
    }
  }

  /// Build translation prompt for Gemini
  String _buildTranslationPrompt(String text, String sourceLanguage, String targetLanguage, String? context, String? domain) {
    final buffer = StringBuffer();
    
    buffer.writeln('You are a professional translator. Translate the following text accurately and naturally.');
    buffer.writeln('');
    buffer.writeln('Source language: $sourceLanguage');
    buffer.writeln('Target language: $targetLanguage');
    
    if (context != null && context.isNotEmpty) {
      buffer.writeln('Context: $context');
    }
    
    if (domain != null && domain.isNotEmpty) {
      buffer.writeln('Domain: $domain');
    }
    
    buffer.writeln('');
    buffer.writeln('Text to translate: "$text"');
    buffer.writeln('');
    buffer.writeln('Provide only the translation, no explanations or additional text.');
    
    return buffer.toString();
  }

  /// Extract translation from Gemini response
  String _extractTranslation(String response) {
    // Clean up the response
    String cleaned = response.trim();
    
    // Remove common prefixes
    final prefixes = [
      'Translation:',
      'Translated text:',
      'Result:',
      'Output:',
    ];
    
    for (final prefix in prefixes) {
      if (cleaned.toLowerCase().startsWith(prefix.toLowerCase())) {
        cleaned = cleaned.substring(prefix.length).trim();
        break;
      }
    }
    
    // Remove quotes if present
    if (cleaned.startsWith('"') && cleaned.endsWith('"')) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }
    
    if (cleaned.startsWith("'") && cleaned.endsWith("'")) {
      cleaned = cleaned.substring(1, cleaned.length - 1);
    }
    
    return cleaned.isNotEmpty ? cleaned : response.trim();
  }

  /// Get service information
  Map<String, dynamic> getServiceInfo() {
    return {
      'serviceName': 'Gemini CLI Translation',
      'isInitialized': _isInitialized,
      'isAvailable': _isAvailable,
      'provider': 'Google Gemini',
      'type': 'cloud',
      'features': [
        'Real-time translation',
        'High accuracy',
        'Multiple languages',
        'Context-aware',
      ],
    };
  }
}
