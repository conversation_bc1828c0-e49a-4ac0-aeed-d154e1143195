## 🦙💬 ChatGPT Clone using Llama-3
This project demonstrates how to build a ChatGPT clone using the Llama-3 model running locally on your computer. The application is built using Python and Streamlit, providing a user-friendly interface for interacting with the language model. Best of all, it's 100% free and doesn't require an internet connection!

### Features
- Runs locally on your computer without the need for an internet connection and completely free to use.
- Utilizes the Llama-3 instruct model for generating responses
- Provides a chat-like interface for seamless interaction

### How to get Started?

1. Clone the GitHub repository

```bash
git clone https://github.com/Shubhamsaboo/awesome-llm-apps.git
cd awesome-llm-apps/advanced_tools_frameworks/local_chatgpt_clone
```
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```
3. Download and install the [LM Studio desktop app](https://lmstudio.ai/). Download the Llama-3 instruct model.

4. Expose the Llama-3 model as an OpenAI API by starting the server in LM Studio. Watch this [video walkthrough](https://x.com/Saboo_Shubham_/status/1783715814790549683).

5. Run the Streamlit App
```bash
streamlit run chatgpt_clone_llama3.py
```

