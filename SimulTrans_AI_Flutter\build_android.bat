@echo off
echo.
echo ========================================
echo   SimulTrans AI - Build Android
echo ========================================
echo.

REM Verificar se Flutter está instalado
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter não encontrado! Instale o Flutter primeiro.
    echo 💡 https://flutter.dev/docs/get-started/install
    pause
    exit /b 1
)

echo ✅ Flutter encontrado!
echo.

REM Verificar arquivo .env
if not exist ".env" (
    echo ⚠️  Arquivo .env não encontrado!
    echo 💡 Crie o arquivo .env com sua GEMINI_API_KEY
    echo.
    echo Exemplo:
    echo GEMINI_API_KEY=sua_chave_aqui
    echo GEMINI_MODEL_NAME=gemini-2.5-flash
    echo USE_GEMINI_API=true
    pause
    exit /b 1
)

echo ✅ Arquivo .env encontrado!
echo.

REM Limpar builds anteriores
echo 🧹 Limpando builds anteriores...
flutter clean
if %errorlevel% neq 0 (
    echo ❌ Erro ao limpar projeto
    pause
    exit /b 1
)

echo ✅ Projeto limpo!
echo.

REM Instalar dependências
echo 📦 Instalando dependências...
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Erro ao instalar dependências
    pause
    exit /b 1
)

echo ✅ Dependências instaladas!
echo.

REM Verificar dispositivos Android
echo 📱 Verificando dispositivos Android...
flutter devices | findstr "android"
if %errorlevel% neq 0 (
    echo ⚠️  Nenhum dispositivo Android encontrado!
    echo.
    echo 💡 Opções:
    echo 1. Conecte um dispositivo Android via USB
    echo 2. Inicie um emulador Android
    echo 3. Execute: flutter emulators --launch nome_do_emulador
    echo.
    echo Pressione qualquer tecla para continuar mesmo assim...
    pause >nul
)

echo.
echo 🚀 Iniciando build para Android...
echo.

REM Menu de opções
echo Escolha o tipo de build:
echo 1. Debug (desenvolvimento)
echo 2. Release (otimizado)
echo 3. Profile (análise de performance)
echo.
set /p choice="Digite sua escolha (1-3): "

if "%choice%"=="1" (
    echo 🔧 Executando em modo DEBUG...
    flutter run -d android --debug
) else if "%choice%"=="2" (
    echo ⚡ Executando em modo RELEASE...
    flutter run -d android --release
) else if "%choice%"=="3" (
    echo 📊 Executando em modo PROFILE...
    flutter run -d android --profile
) else (
    echo ❌ Opção inválida! Executando em modo DEBUG...
    flutter run -d android --debug
)

if %errorlevel% neq 0 (
    echo.
    echo ❌ Erro ao executar no Android!
    echo.
    echo 💡 Possíveis soluções:
    echo 1. Verifique se o dispositivo está conectado
    echo 2. Verifique se o USB debugging está habilitado
    echo 3. Execute: flutter doctor para diagnóstico
    echo 4. Verifique se a GEMINI_API_KEY está configurada
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ SimulTrans AI executando no Android!
echo 🎉 Aproveite a tradução com Google Gemini AI!
echo.
pause
