[{"url": "http://arxiv.org/abs/2108.01468v1", "title": "Quantum Neural Networks: Concepts, Applications, and Challenges", "description": "Quantum deep learning is a research field for the use of quantum computing techniques for training deep neural networks. The research topics and directions of deep learning and quantum computing have been separated for long time, however by discovering that quantum circuits can act like artificial neural networks, quantum deep learning research is widely adopted. This paper explains the backgrounds and basic principles of quantum deep learning and also introduces major achievements. After that, this paper discusses the challenges of quantum deep learning research in multiple perspectives. Lastly, this paper presents various future research directions and application fields of quantum deep learning.", "authors": ["Yunseok Kwak", "Won Joon Yun", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "published": "2021-08-02", "pdf_url": "http://arxiv.org/pdf/2108.01468v1", "categories": ["quant-ph", "cs.LG"]}, {"url": "http://arxiv.org/abs/1803.07608v1", "title": "A Survey of Deep Learning Techniques for Mobile Robot Applications", "description": "Advancements in deep learning over the years have attracted research into how deep artificial neural networks can be used in robotic systems. This research survey will present a summarization of the current research with a specific focus on the gains and obstacles for deep learning to be applied to mobile robotics.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "published": "2018-03-20", "pdf_url": "http://arxiv.org/pdf/1803.07608v1", "categories": ["cs.CV", "cs.R<PERSON>"]}, {"url": "http://arxiv.org/abs/1705.03921v1", "title": "Why & When Deep Learning Works: Looking Inside Deep Learnings", "description": "The Intel Collaborative Research Institute for Computational Intelligence (ICRI-CI) has been heavily supporting Machine Learning and Deep Learning research from its foundation in 2012. We have asked six leading ICRI-CI Deep Learning researchers to address the challenge of \"Why & When Deep Learning works\", with the goal of looking inside Deep Learning, providing insights on how deep networks function, and uncovering key observations on their expressiveness, limitations, and potential. The output of this challenge resulted in five papers that address different facets of deep learning. These different facets include a high-level understating of why and when deep networks work (and do not work), the impact of geometry on the expressiveness of deep networks, and making deep networks interpretable.", "authors": ["<PERSON><PERSON>"], "published": "2017-05-10", "pdf_url": "http://arxiv.org/pdf/1705.03921v1", "categories": ["cs.LG"]}, {"url": "http://arxiv.org/abs/1805.04825v1", "title": "Deep Learning in Software Engineering", "description": "Recent years, deep learning is increasingly prevalent in the field of Software Engineering (SE). However, many open issues still remain to be investigated. How do researchers integrate deep learning into SE problems? Which SE phases are facilitated by deep learning? Do practitioners benefit from deep learning? The answers help practitioners and researchers develop practical deep learning models for SE tasks. To answer these questions, we conduct a bibliography analysis on 98 research papers in SE that use deep learning techniques. We find that 41 SE tasks in all SE phases have been facilitated by deep learning integrated solutions. In which, 84.7% papers only use standard deep learning models and their variants to solve SE problems. The practicability becomes a concern in utilizing deep learning techniques. How to improve the effectiveness, efficiency, understandability, and testability of deep learning based solutions may attract more SE researchers in the future.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Ge Li", "<PERSON><PERSON><PERSON>"], "published": "2018-05-13", "pdf_url": "http://arxiv.org/pdf/1805.04825v1", "categories": ["cs.SE"]}, {"url": "http://arxiv.org/abs/1812.06110v1", "title": "Dopamine: A Research Framework for Deep Reinforcement Learning", "description": "Deep reinforcement learning (deep RL) research has grown significantly in recent years. A number of software offerings now exist that provide stable, comprehensive implementations for benchmarking. At the same time, recent deep RL research has become more diverse in its goals. In this paper we introduce Dopamine, a new research framework for deep RL that aims to support some of that diversity. Dopamine is open-source, TensorFlow-based, and provides compact and reliable implementations of some state-of-the-art deep RL agents. We complement this offering with a taxonomy of the different research objectives in deep RL research. While by no means exhaustive, our analysis highlights the heterogeneity of research in the field, and the value of frameworks such as ours.", "authors": ["<PERSON>", "Subho<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "published": "2018-12-14", "pdf_url": "http://arxiv.org/pdf/1812.06110v1", "categories": ["cs.LG", "cs.AI"]}, {"url": "http://arxiv.org/abs/2012.15754v1", "title": "Limitations of Deep Neural Networks: a discussion of <PERSON><PERSON>' critical appraisal of deep learning", "description": "Deep neural networks have triggered a revolution in artificial intelligence, having been applied with great results in medical imaging, semi-autonomous vehicles, ecommerce, genetics research, speech recognition, particle physics, experimental art, economic forecasting, environmental science, industrial manufacturing, and a wide variety of applications in nearly every field. This sudden success, though, may have intoxicated the research community and blinded them to the potential pitfalls of assigning deep learning a higher status than warranted. Also, research directed at alleviating the weaknesses of deep learning may seem less attractive to scientists and engineers, who focus on the low-hanging fruit of finding more and more applications for deep learning models, thus letting short-term benefits hamper long-term scientific progress. <PERSON> wrote a paper entitled Deep Learning: A Critical Appraisal, and here we discuss <PERSON>' core ideas, as well as attempt a general assessmen...", "authors": ["<PERSON><PERSON>"], "published": "2020-12-22", "pdf_url": "http://arxiv.org/pdf/2012.15754v1", "categories": ["cs.AI", "cs.CY", "cs.LG"]}, {"url": "http://arxiv.org/abs/1806.10897v3", "title": "Deep learning in business analytics and operations research: Models, applications and managerial implications", "description": "Business analytics refers to methods and practices that create value through data for individuals, firms, and organizations. This field is currently experiencing a radical shift due to the advent of deep learning: deep neural networks promise improvements in prediction performance as compared to models from traditional machine learning. However, our research into the existing body of literature reveals a scarcity of research works utilizing deep learning in our discipline. Accordingly, the objectives of this overview article are as follows: (1) we review research on deep learning for business analytics from an operational point of view. (2) We motivate why researchers and practitioners from business analytics should utilize deep neural networks and review potential use cases, necessary requirements, and benefits. (3) We investigate the added value to operations research in different case studies with real data from entrepreneurial undertakings. All such cases demonstrate improvement...", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "published": "2018-06-28", "pdf_url": "http://arxiv.org/pdf/1806.10897v3", "categories": ["cs.LG", "stat.ML"]}, {"url": "http://arxiv.org/abs/1812.05448v4", "title": "A First Look at Deep Learning Apps on Smartphones", "description": "We are in the dawn of deep learning explosion for smartphones. To bridge the gap between research and practice, we present the first empirical study on 16,500 the most popular Android apps, demystifying how smartphone apps exploit deep learning in the wild. To this end, we build a new static tool that dissects apps and analyzes their deep learning functions. Our study answers threefold questions: what are the early adopter apps of deep learning, what do they use deep learning for, and how do their deep learning models look like. Our study has strong implications for app developers, smartphone vendors, and deep learning R\\&D. On one hand, our findings paint a promising picture of deep learning for smartphones, showing the prosperity of mobile deep learning frameworks as well as the prosperity of apps building their cores atop deep learning. On the other hand, our findings urge optimizations on deep learning models deployed on smartphones, the protection of these models, and validatio...", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "published": "2018-11-08", "pdf_url": "http://arxiv.org/pdf/1812.05448v4", "categories": ["cs.LG", "cs.CY"]}, {"url": "http://arxiv.org/abs/1707.07217v1", "title": "Deep Learning in Robotics: A Review of Recent Research", "description": "Advances in deep learning over the last decade have led to a flurry of research in the application of deep artificial neural networks to robotic systems, with at least thirty papers published on the subject between 2014 and the present. This review discusses the applications, benefits, and limitations of deep learning vis-\\`a-vis physical robotic systems, using contemporary research as exemplars. It is intended to communicate recent advances to the wider robotics community and inspire additional interest in and application of deep learning in robotics.", "authors": ["<PERSON>", "<PERSON>"], "published": "2017-07-22", "pdf_url": "http://arxiv.org/pdf/1707.07217v1", "categories": ["cs.R<PERSON>"]}, {"url": "http://arxiv.org/abs/1908.02130v1", "title": "Deep learning research landscape & roadmap in a nutshell: past, present and future -- Towards deep cortical learning", "description": "The past, present and future of deep learning is presented in this work. Given this landscape & roadmap, we predict that deep cortical learning will be the convergence of deep learning & cortical learning which builds an artificial cortical column ultimately.", "authors": ["<PERSON><PERSON>"], "published": "2019-07-30", "pdf_url": "http://arxiv.org/pdf/1908.02130v1", "categories": ["cs.NE", "cs.LG"]}, {"url": "http://arxiv.org/abs/2309.15421v1", "title": "Deep Learning in Deterministic Computational Mechanics", "description": "The rapid growth of deep learning research, including within the field of computational mechanics, has resulted in an extensive and diverse body of literature. To help researchers identify key concepts and promising methodologies within this field, we provide an overview of deep learning in deterministic computational mechanics. Five main categories are identified and explored: simulation substitution, simulation enhancement, discretizations as neural networks, generative approaches, and deep reinforcement learning. This review focuses on deep learning methods rather than applications for computational mechanics, thereby enabling researchers to explore this field more effectively. As such, the review is not necessarily aimed at researchers with extensive knowledge of deep learning -- instead, the primary audience is researchers at the verge of entering this field or those who attempt to gain an overview of deep learning in computational mechanics. The discussed concepts are, therefo...", "authors": ["<PERSON>", "<PERSON>"], "published": "2023-09-27", "pdf_url": "http://arxiv.org/pdf/2309.15421v1", "categories": ["cs.LG"]}, {"url": "http://arxiv.org/abs/1603.06430v5", "title": "Deep Learning in Bioinformatics", "description": "In the era of big data, transformation of biomedical big data into valuable knowledge has been one of the most important challenges in bioinformatics. Deep learning has advanced rapidly since the early 2000s and now demonstrates state-of-the-art performance in various fields. Accordingly, application of deep learning in bioinformatics to gain insight from data has been emphasized in both academia and industry. Here, we review deep learning in bioinformatics, presenting examples of current research. To provide a useful and comprehensive perspective, we categorize research both by the bioinformatics domain (i.e., omics, biomedical imaging, biomedical signal processing) and deep learning architecture (i.e., deep neural networks, convolutional neural networks, recurrent neural networks, emergent architectures) and present brief descriptions of each study. Additionally, we discuss theoretical and practical issues of deep learning in bioinformatics and suggest future research directions. ...", "authors": ["<PERSON><PERSON><PERSON><PERSON> Min", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "published": "2016-03-21", "pdf_url": "http://arxiv.org/pdf/1603.06430v5", "categories": ["cs.LG", "q-bio.GN"]}, {"url": "http://arxiv.org/abs/1805.08355v1", "title": "Opening the black box of deep learning", "description": "The great success of deep learning shows that its technology contains profound truth, and understanding its internal mechanism not only has important implications for the development of its technology and effective application in various fields, but also provides meaningful insights into the understanding of human brain mechanism. At present, most of the theoretical research on deep learning is based on mathematics. This dissertation proposes that the neural network of deep learning is a physical system, examines deep learning from three different perspectives: microscopic, macroscopic, and physical world views, answers multiple theoretical puzzles in deep learning by using physics principles. For example, from the perspective of quantum mechanics and statistical physics, this dissertation presents the calculation methods for convolution calculation, pooling, normalization, and Restricted Boltzmann Machine, as well as the selection of cost functions, explains why deep learning must ...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "published": "2018-05-22", "pdf_url": "http://arxiv.org/pdf/1805.08355v1", "categories": ["cs.LG", "stat.ML"]}, {"url": "http://arxiv.org/abs/2010.12717v2", "title": "Deep Learning for Radio-based Human Sensing: Recent Advances and Future Directions", "description": "While decade-long research has clearly demonstrated the vast potential of radio frequency (RF) for many human sensing tasks, scaling this technology to large scenarios remained problematic with conventional approaches. Recently, researchers have successfully applied deep learning to take radio-based sensing to a new level. Many different types of deep learning models have been proposed to achieve high sensing accuracy over a large population and activity set, as well as in unseen environments. Deep learning has also enabled detection of novel human sensing phenomena that were previously not possible. In this survey, we provide a comprehensive review and taxonomy of recent research efforts on deep learning based RF sensing. We also identify and compare several publicly released labeled RF sensing datasets that can facilitate such deep learning research. Finally, we summarize the lessons learned and discuss the current limitations and future directions of deep learning based RF sensing.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Zhu"], "published": "2020-10-23", "pdf_url": "http://arxiv.org/pdf/2010.12717v2", "categories": ["eess.SP", "cs.LG"]}, {"url": "http://arxiv.org/abs/2108.11510v1", "title": "Deep Reinforcement Learning in Computer Vision: A Comprehensive Survey", "description": "Deep reinforcement learning augments the reinforcement learning framework and utilizes the powerful representation of deep neural networks. Recent works have demonstrated the remarkable successes of deep reinforcement learning in various domains including finance, medicine, healthcare, video games, robotics, and computer vision. In this work, we provide a detailed review of recent and state-of-the-art research advances of deep reinforcement learning in computer vision. We start with comprehending the theories of deep learning, reinforcement learning, and deep reinforcement learning. We then propose a categorization of deep reinforcement learning methodologies and discuss their advantages and limitations. In particular, we divide deep reinforcement learning into seven main categories according to their applications in computer vision, i.e. (i)landmark localization (ii) object detection; (iii) object tracking; (iv) registration on both 2D image and 3D image volumetric data (v) image s...", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "published": "2021-08-25", "pdf_url": "http://arxiv.org/pdf/2108.11510v1", "categories": ["cs.CV", "cs.AI"]}]