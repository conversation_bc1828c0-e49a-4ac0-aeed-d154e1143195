// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:flutter/material.dart';

class GalleryIcons {
  GalleryIcons._();

  static const IconData tooltip = IconData(0xe900, fontFamily: 'GalleryIcons');
  static const IconData textFieldsAlt = IconData(0xe901, fontFamily: 'GalleryIcons');
  static const IconData tabs = IconData(0xe902, fontFamily: 'GalleryIcons');
  static const IconData switches = IconData(0xe903, fontFamily: 'GalleryIcons');
  static const IconData sliders = IconData(0xe904, fontFamily: 'GalleryIcons');
  static const IconData shrine = IconData(0xe905, fontFamily: 'GalleryIcons');
  static const IconData sentimentVerySatisfied = IconData(0xe906, fontFamily: 'GalleryIcons');
  static const IconData refresh = IconData(0xe907, fontFamily: 'GalleryIcons');
  static const IconData progressActivity = IconData(0xe908, fontFamily: 'GalleryIcons');
  static const IconData phoneIphone = IconData(0xe909, fontFamily: 'GalleryIcons');
  static const IconData pageControl = IconData(0xe90a, fontFamily: 'GalleryIcons');
  static const IconData moreVert = IconData(0xe90b, fontFamily: 'GalleryIcons');
  static const IconData menu = IconData(0xe90c, fontFamily: 'GalleryIcons');
  static const IconData listAlt = IconData(0xe90d, fontFamily: 'GalleryIcons');
  static const IconData gridOn = IconData(0xe90e, fontFamily: 'GalleryIcons');
  static const IconData expandAll = IconData(0xe90f, fontFamily: 'GalleryIcons');
  static const IconData event = IconData(0xe910, fontFamily: 'GalleryIcons');
  static const IconData driveVideo = IconData(0xe911, fontFamily: 'GalleryIcons');
  static const IconData dialogs = IconData(0xe912, fontFamily: 'GalleryIcons');
  static const IconData dataTable = IconData(0xe913, fontFamily: 'GalleryIcons');
  static const IconData customTypography = IconData(0xe914, fontFamily: 'GalleryIcons');
  static const IconData colors = IconData(0xe915, fontFamily: 'GalleryIcons');
  static const IconData chips = IconData(0xe916, fontFamily: 'GalleryIcons');
  static const IconData checkBox = IconData(0xe917, fontFamily: 'GalleryIcons');
  static const IconData cards = IconData(0xe918, fontFamily: 'GalleryIcons');
  static const IconData buttons = IconData(0xe919, fontFamily: 'GalleryIcons');
  static const IconData bottomSheets = IconData(0xe91a, fontFamily: 'GalleryIcons');
  static const IconData bottomNavigation = IconData(0xe91b, fontFamily: 'GalleryIcons');
  static const IconData animation = IconData(0xe91c, fontFamily: 'GalleryIcons');
  static const IconData accountBox = IconData(0xe91d, fontFamily: 'GalleryIcons');
  static const IconData snackbar = IconData(0xe91e, fontFamily: 'GalleryIcons');
  static const IconData categoryMdc = IconData(0xe91f, fontFamily: 'GalleryIcons');
  static const IconData cupertinoProgress = IconData(0xe920, fontFamily: 'GalleryIcons');
  static const IconData cupertinoPullToRefresh = IconData(0xe921, fontFamily: 'GalleryIcons');
  static const IconData cupertinoSwitch = IconData(0xe922, fontFamily: 'GalleryIcons');
  static const IconData genericButtons = IconData(0xe923, fontFamily: 'GalleryIcons');
  static const IconData backdrop = IconData(0xe924, fontFamily: 'GalleryIcons');
  static const IconData bottomAppBar = IconData(0xe925, fontFamily: 'GalleryIcons');
  static const IconData bottomSheetPersistent = IconData(0xe926, fontFamily: 'GalleryIcons');
  static const IconData listsLeaveBehind = IconData(0xe927, fontFamily: 'GalleryIcons');
  static const IconData navigationRail = Icons.vertical_split;
  static const IconData appbar = Icons.web_asset;
  static const IconData divider = Icons.credit_card;
  static const IconData search = Icons.search;
}
