{"cells": [{"cell_type": "markdown", "metadata": {"id": "av9C5VQh7LPR"}, "source": ["#### Copyright 2024 Google LLC.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "5mvpC35m61BC"}, "outputs": [], "source": ["# @title Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "# https://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "metadata": {"id": "QMWaKHtB7Txj"}, "source": ["# Local Agentic RAG without APIs - using FastEmbed, Ollama-Gemma 3 and Qdrant Vector database\n", "\n", "Author: <PERSON><PERSON>\n", "\n", "- GitHub: [lucifertrj](https://github.com/lucifertrj/)\n", "- Twitter: [TRJ_0751](https://x.com/trj_0751)\n", "\n", "## Overview\n", "\n", "We will explore how to build a 100% local agentic RAG system using open-source stack. This system allows you to create a knowledge base from web data and answer user queries without relying on external APIs, ensuring data privacy and flexibility.\n", "\n", "Running AI inference locally — processing AI models on an organization’s own hardware, such as on-premises servers or devices, rather than relying on cloud-based services has become an increasingly popular choice across various industries. The primary appeal lies in the enhanced control and security it offers over sensitive data.  \n", "\n", "![1_pas4gyU_facwVjqpjENt_Q.webp](data:image/webp;base64,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)"]}, {"cell_type": "markdown", "metadata": {"id": "JB357StN70-S"}, "source": ["## Installation\n", "\n", "Install Ollama through the offical installation script."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4zeg8fEB7xOf"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>> Cleaning up old version at /usr/local/lib/ollama\n", ">>> Installing ollama to /usr/local\n", ">>> Downloading Linux amd64 bundle\n", "######################################################################## 100.0%\n", ">>> Adding ollama user to video group...\n", ">>> Adding current user to ollama group...\n", ">>> Creating ollama systemd service...\n", "\u001b[1m\u001b[31mWARNING:\u001b[m systemd is not running\n", "\u001b[1m\u001b[31mWARNING:\u001b[m Unable to detect NVIDIA/AMD GPU. Install lspci or lshw to automatically detect and install GPU dependencies.\n", ">>> The Ollama API is now available at 127.0.0.1:11434.\n", ">>> Install complete. Run \"ollama\" from the command line.\n"]}], "source": ["!curl -fsSL https://ollama.com/install.sh | sh"]}, {"cell_type": "markdown", "metadata": {"id": "UHQqBXXu73Ty"}, "source": ["Install:\n", "\n", "- Ollama: To get the open source model inference i.e., Gemma-3-4b\n", "- Langchain: To orchestrate the Retriever pipeline by indexing the data into the knowledge base.\n", "- FastEmbed: For the lightweight embeddings\n", "- Qdrant: To save the vector embeddings and index the data.\n", "- Agno: For the Agentic AI capabilites\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iwYSJp_074y5"}, "outputs": [], "source": ["!pip install ollama\n", "!pip install langchain langchain-community\n", "!pip install fastembed langchain-qdrant==0.2.0\n", "!pip install agno"]}, {"cell_type": "markdown", "metadata": {"id": "liiXQRe178O-"}, "source": ["## <PERSON> O<PERSON>ma\n", "\n", "<PERSON> <PERSON><PERSON><PERSON> in background using nohup."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ukInL5AB77e-"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["nohup: redirecting stderr to stdout\n"]}], "source": ["!nohup ollama serve > ollama.log &"]}, {"cell_type": "markdown", "metadata": {"id": "QuEUMKoNAYgL"}, "source": ["## Switch the Runtime\n", "\n", "- Since we are using Colab, lets switch to GPU\n", "- Click on `Runtime` and select `Change Runtime type`\n", "- Choose `T4 GPU` one can access it for free."]}, {"cell_type": "markdown", "metadata": {"id": "HTRC0-th8C0D"}, "source": ["## Get <PERSON> 3\n", "\n", "- Gemma 3 is available in 4 variants: 1B, 4B, 12B, and 27B.\n", "- Pull the gemma3 model to use with the library: ollama pull gemma3:12b\n", "- See [https://ollama.com/search](https://ollama.com/search) for more information on the models available."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4wJk-SPU8Umm"}, "outputs": [], "source": ["import ollama"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pzWEdaiH8aDN"}, "outputs": [{"data": {"text/plain": ["ProgressResponse(status='success', completed=None, total=None, digest=None)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["ollama.pull('gemma3:4b') # get the model from here: https://ollama.com/library/gemma3"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-x8gyi7Z-U_u"}, "outputs": [], "source": ["from agno.agent import Agent\n", "from agno.models.ollama import Ollama"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "NvAT0-Sw-aW8"}, "outputs": [], "source": ["test_ollama = Agent(\n", "    model=Ollama(id=\"gemma3:4b\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MSad7fz--gKn"}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f47af978650d49ea8f555dfadbedd7f9", "version_major": 2, "version_minor": 0}, "text/plain": ["Output()"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"></pre>\n"], "text/plain": []}, "metadata": {}, "output_type": "display_data"}], "source": ["test_ollama.print_response(\"who is <PERSON><PERSON><PERSON>. give brief intro\",stream=True)"]}, {"cell_type": "markdown", "metadata": {"id": "0180E14VArBn"}, "source": ["Alright, the model is running, its time to build the Agentic RAG completely local, lets start by saving our external data into Vector database and prepare the knowledge base."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "7BJnFZ3WA08N"}, "outputs": [], "source": ["from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "from langchain_community.embeddings.fastembed import FastEmbedEmbeddings\n", "\n", "from langchain_qdrant import QdrantVectorStore\n", "from qdrant_client import QdrantClient\n", "from qdrant_client.http.models import Distance, VectorParams\n", "from qdrant_client.http.exceptions import UnexpectedResponse"]}, {"cell_type": "markdown", "metadata": {"id": "JnHOeXo9A86_"}, "source": ["## External Data\n", "\n", "External data can be in PDF, Docx, YouTube, CSV, or any other format. For this demonstration, we’ll use a web page to demonstrate how to chat with a website.\n", "\n", "The key objective is to load data from the URL using LangChain loaders and extract raw text. Since LLMs cannot process an entire document at once, we need to chunk the data into smaller parts. Once we have the raw text, we divide it into smaller chunks and store it in a knowledge base.\n", "\n", "We will use Gemma-3 release blog as the external data to build the Agentic RAG."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-r1pK5hYBAno"}, "outputs": [], "source": ["urls = [\n", "    \"https://blog.google/technology/developers/gemma-3/\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "718mzbYlBHxt"}, "outputs": [], "source": ["loader = WebBaseLoader(urls)\n", "data = loader.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "FJbBejsEBMw6"}, "outputs": [], "source": ["text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size=1024, chunk_overlap=50\n", ")\n", "chunks = text_splitter.split_documents(data)"]}, {"cell_type": "markdown", "metadata": {"id": "Unqpw0ceBQDd"}, "source": ["## Setup your Vector database\n", "\n", "While defining the vector database, it’s crucial to consider that data may change over time, and preserving previous versions is important. To manage this, we define a unique collection name each time we store new data. Whenever data changes, a new collection should be used."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jRmawlwrBPbR"}, "outputs": [], "source": ["client = QdrantClient(path=\"/tmp/app\")\n", "collection_name = \"agent-rag\"\n", "\n", "try:\n", "    collection_info = client.get_collection(collection_name=collection_name)\n", "except (UnexpectedRespo<PERSON>, ValueError):\n", "    client.create_collection(\n", "        collection_name=collection_name,\n", "        vectors_config=VectorParams(size=1024, distance=Distance.COSINE),\n", "    )"]}, {"cell_type": "markdown", "metadata": {"id": "zrXklg7IBNv7"}, "source": ["## Index your document\n", "\n", "Now that we’ve defined the vector database client, its time to define the embedding model. The final step to index the data is to initialize the vector store and add the chunked data for efficient retrieval."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3pIJTuDIBLo3"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.11/dist-packages/langchain_community/embeddings/fastembed.py:109: UserWarning: The model thenlper/gte-large now uses mean pooling instead of CLS embedding. In order to preserve the previous behaviour, consider either pinning fastembed version to 0.5.1 or using `add_custom_model` functionality.\n", "  values[\"model\"] = fastembed.TextEmbedding(\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f34292490a714f2bb9930d4a4fc0d58e", "version_major": 2, "version_minor": 0}, "text/plain": ["Fetching 5 files:   0%|          | 0/5 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "59e884604968460eae6ca627ff15a0ae", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/660 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9e6fd37e8b764c6694885819ed80c6ec", "version_major": 2, "version_minor": 0}, "text/plain": ["model.onnx:   0%|          | 0.00/1.34G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ca4907b8de784cfcad95412eb36f4fa0", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json:   0%|          | 0.00/1.41k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a75ece95a9c84e7f90e203086d2c4da9", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/712k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "e470872477cc410ab6b55c147845856b", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/695 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["embeddings = FastEmbedEmbeddings(model_name=\"thenlper/gte-large\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2poQRoggBcDL"}, "outputs": [], "source": ["vector_store = QdrantVectorStore(\n", "    client=client,\n", "    collection_name=collection_name,\n", "    embedding=embeddings,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "1fxtS4GjCGqh"}, "outputs": [], "source": ["vector_store.add_documents(documents=chunks)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lA51rfIsCbaC"}, "outputs": [], "source": ["retriever = vector_store.as_retriever()"]}, {"cell_type": "markdown", "metadata": {"id": "aKQ3qDfbCHtC"}, "source": ["Hurray! The data is now saved in vector database, lets cross check:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "qkUg9_10CNgd"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["collection  meta.json\n"]}], "source": ["!ls /tmp/app"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hp7NO1vrCQN4"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["agent-rag\n"]}], "source": ["!ls /tmp/app/collection/"]}, {"cell_type": "markdown", "metadata": {"id": "Gq_DRinECSCT"}, "source": ["We have the agent-rag inside the collection directory, thats saved locally."]}, {"cell_type": "markdown", "metadata": {"id": "ZZNQboJgCZA7"}, "source": ["## Agentic Pipeline set\n", "\n", "Agno offers multiple ways to define a knowledge base. In this case, we will load the LangChain retriever pipeline into Agno as a knowledge base object, enabling seamless retrieval and decision-making within the Agentic workflow."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "moYxj-I5DDw7"}, "outputs": [], "source": ["def search_and_augment(user_query):\n", "    docs_content = retriever.invoke(user_query)\n", "\n", "    context = \"\"\n", "    for data in docs_content:\n", "        context += data.page_content\n", "\n", "    prompt = f\"\"\"\n", "    Answer to the USER QUESTION from the provided CONTEXT.\n", "    The given CONTEXT is the only source of information, if USER QUESTION is not from the given CONTEXT, just say `I don't know, no enough information`\n", "    -----\n", "    CONTEXT: {context}\n", "    -----\n", "    USER QUESTION: {user_query}\n", "    \"\"\"\n", "\n", "    return prompt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Oiupk4GIC3-w"}, "outputs": [], "source": ["user_query = \"How many global languages is supported?\" # question taken from external data\n", "user_query2 = \"who is <PERSON><PERSON><PERSON>?\" # not from the given data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "xI21AaC9Coep"}, "outputs": [], "source": ["agent = Agent(\n", "    model=Ollama(id=\"gemma3:4b\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Kag1StkPCWrY"}, "outputs": [], "source": ["prompt1 = search_and_augment(user_query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "PTDZE1ofCqlB"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Gemma 3 offers out-of-the-box support for over 35 languages and pretrained support for over 140 languages.\n"]}], "source": ["response = agent.run(prompt1)\n", "print(response.content)"]}, {"cell_type": "markdown", "metadata": {"id": "F5YgM2T2Czp8"}, "source": ["Lets ask out of context question"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0F-oYc2_C8dA"}, "outputs": [], "source": ["prompt2 = search_and_augment(user_query2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "nYVoMELxC_yM"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I don't know, no enough information\n"]}], "source": ["response = agent.run(prompt2)\n", "print(response.content)"]}, {"cell_type": "markdown", "metadata": {"id": "2A5SgGvtDp6-"}, "source": ["If you notice, earlier when we tested <PERSON>, I asked who is <PERSON><PERSON><PERSON> it answered, not it didn't answer, thats what RAG is capable off. With Agentic capability it have the improved decision making ability."]}], "metadata": {"accelerator": "GPU", "colab": {"name": "[Gemma_3]Local_Agentic_RAG.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}