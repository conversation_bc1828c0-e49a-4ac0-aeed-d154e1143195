import streamlit as st
import os
import json
import threading
import pandas as pd
from datetime import datetime
from typing import Dict, List, Optional, Any

from promptwright import DataEngine, EngineArguments, TopicTree, TopicTreeArguments

class ConfigManager:
    @staticmethod
    def get_providers() -> Dict[str, List[str]]:
        return {
            "ollama": ["deepseek-r1:1.5b", "llama3.2:latest", "llama3.2:1b", "deepseek-r1:7b"],
            "openai": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"],
            "anthropic": ["claude-3-haiku", "claude-3-sonnet", "claude-3-opus"],
            "groq": [
                "llama-3.3-70b-versatile", "llama-3.1-8b-instant", 
                "mixtral-8x7b-32768", "deepseek-r1-distill-llama-70b", 
                "llama-3.2-3b-preview", "llama-3.2-1b-preview"
            ],
            "gemini": ["gemini-pro", "gemini-1.5-pro"]
        }
    
    @staticmethod
    def validate_api_key(provider: str, api_key: str) -> bool:
        return len(api_key.strip()) > 10

class DatasetGenerator:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.providers = self.config_manager.get_providers()
    
    def validate_inputs(self, inputs: Dict[str, Any]) -> Optional[str]:
        required_fields = [
            'provider', 'model_name', 'root_prompt', 
            'system_prompt', 'instructions'
        ]
        
        for field in required_fields:
            if not inputs.get(field):
                return f"Missing required field: {field}"
        
        if inputs['provider'] != 'ollama':
            api_key = os.environ.get(f"{inputs['provider'].upper()}_API_KEY")
            if not api_key or not self.config_manager.validate_api_key(inputs['provider'], api_key):
                return f"Invalid or missing {inputs['provider'].upper()} API key"
        
        return None

    def load_jsonl_as_dataframe(self, file_path: str) -> pd.DataFrame:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = [json.loads(line) for line in f]
            
            df_data = []
            for entry in data:
                row = {
                    msg['role']: msg.get('content', '') 
                    for msg in entry.get('messages', [])
                }
                df_data.append(row)
            
            return pd.DataFrame(df_data)
        except Exception as e:
            st.error(f"Error loading dataset: {e}")
            return pd.DataFrame()

    def generate_dataset(self, config: Dict[str, Any], progress_callback=None):
        try:
            validation_error = self.validate_inputs(config)
            if validation_error:
                st.error(validation_error)
                return None

            if st.session_state.get('abort_generation', False):
                st.warning("Dataset generation aborted by user.")
                return None

            if progress_callback:
                progress_callback(0.1, "Initializing Topic Tree")

            tree = TopicTree(args=TopicTreeArguments(
                root_prompt=config['root_prompt'],
                model_system_prompt=config['system_prompt'],
                tree_degree=config['tree_degree'],
                tree_depth=config['tree_depth'],
                temperature=config['tree_temperature'],
                model_name=f"{config['provider']}/{config['model_name']}",
            ))
            tree.build_tree()
            tree.save(config['tree_save_path'])

            if st.session_state.get('abort_generation', False):
                st.warning("Dataset generation aborted by user.")
                return None

            if progress_callback:
                progress_callback(0.3, "Building Topic Tree")

            engine = DataEngine(args=EngineArguments(
                instructions=config['instructions'],
                system_prompt=config['system_prompt'],
                model_name=f"{config['provider']}/{config['model_name']}",
                temperature=config['engine_temperature'],
                max_retries=3,
            ))

            dataset = engine.create_data(
                num_steps=config['num_steps'],
                batch_size=config['batch_size'],
                topic_tree=tree,
            )
            dataset.save(config['dataset_save_path'])

            if progress_callback:
                progress_callback(0.8, "Saving Dataset")

            if st.session_state.get('abort_generation', False):
                st.warning("Dataset generation aborted by user.")
                return None

            return self.load_jsonl_as_dataframe(config['dataset_save_path'])

        except Exception as e:
            st.error(f"Dataset generation failed: {e}")
            return None

def ensure_directory_exists(directory):
    """Create directory if it doesn't exist."""
    os.makedirs(directory, exist_ok=True)

def generate_unique_filename(base_directory, base_filename):
    """Generate a unique filename with timestamp to prevent overwriting."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename, ext = os.path.splitext(base_filename)
    unique_filename = f"{filename}_{timestamp}{ext}"
    return os.path.join(base_directory, unique_filename)

def render_model_configuration_sidebar():
    st.sidebar.header("🤖 Model Configuration")
    
    config_manager = ConfigManager()
    providers = list(config_manager.get_providers().keys())
    
    provider = st.sidebar.selectbox("Select LLM Provider", providers)
    model_name = st.sidebar.selectbox(
        "Select Model", 
        config_manager.get_providers().get(provider, []), 
        help="Available models for the selected provider"
    )

    if provider != "ollama":
        st.sidebar.subheader("🔑 API Configuration")
        st.sidebar.warning("Securely manage API keys")
        
        api_key_config = {
            "openai": "OpenAI API Key",
            "anthropic": "Anthropic API Key",
            "groq": "Groq API Key",
            "gemini": "Google Gemini API Key"
        }

        default_key = os.environ.get(f"{provider.upper()}_API_KEY", "")
        api_key = st.sidebar.text_input(
            f"{api_key_config.get(provider, 'API Key')}", 
            value=default_key, 
            type="password"
        )
        
        if st.sidebar.checkbox(f"Save {provider.upper()} API Key"):
            try:
                os.environ[f"{provider.upper()}_API_KEY"] = api_key
                st.sidebar.success(f"{provider.upper()} API Key saved securely")
            except Exception as e:
                st.sidebar.error(f"Key storage error: {e}")

    return provider, model_name

def generate_dataset_thread(generator, config, progress_placeholder, status_placeholder):
    def progress_callback(progress, status):
        progress_placeholder.progress(progress)
        status_placeholder.text(status)
    
    try:
        st.session_state.abort_generation = False
        
        df = generator.generate_dataset(config, progress_callback)
        
        if df is not None and not df.empty and not st.session_state.get('abort_generation', False):
            progress_placeholder.progress(1.0)
            status_placeholder.text("Dataset Generation Complete!")
            return df
        return None
    except Exception as e:
        st.error(f"Dataset generation failed: {e}")
        return None

def main():
    st.set_page_config(
        page_title="Promptwright Dataset Generator", 
        page_icon="🤖", 
        layout="wide"
    )
    
    ensure_directory_exists('topic')
    ensure_directory_exists('data')

    if 'abort_generation' not in st.session_state:
        st.session_state.abort_generation = False
    if 'dataset_download_data' not in st.session_state:
        st.session_state.dataset_download_data = None

    generator = DatasetGenerator()

    st.title("Promptwright Dataset Generator")

    provider, model_name = render_model_configuration_sidebar()

    col1, col2 = st.columns(2)
    
    with col1:
        root_prompt = st.text_area("Root Prompt", help="Dataset generation theme")
        system_prompt = st.text_area("System Prompt", help="LLM behavior guidelines")
        
    with col2:
        tree_degree = st.slider("Tree Degree", 2, 10, 4)
        tree_depth = st.slider("Tree Depth", 1, 5, 3)
        tree_temperature = st.slider("Topic Generation Temperature", 0.0, 1.0, 0.6, 0.1)

    instructions = st.text_area("Generation Instructions")
    
    col3, col4, col5 = st.columns(3)
    with col3:
        num_steps = st.number_input("Dataset Entries", 1, 100, 15)
    with col4:
        batch_size = st.number_input("Batch Size", 1, 10, 2)
    with col5:
        engine_temperature = st.slider("Data Generation Temperature", 0.0, 1.0, 0.7, 0.1)

    progress_placeholder = st.empty()
    status_placeholder = st.empty()

    col_generate, col_abort = st.columns(2)

    with col_generate:
        generate_button = st.button("Generate Dataset", key="generate")
    
    with col_abort:
        abort_button = st.button("Abort Generation", key="abort")

    if abort_button:
        st.session_state.abort_generation = True
        status_placeholder.warning("Generation abort requested...")

    if generate_button:
        unique_tree_save_path = generate_unique_filename('topic', 'generated_topic_tree.jsonl')
        unique_dataset_save_path = generate_unique_filename('data', 'generated_dataset.jsonl')

        dataset_config = {
            'provider': provider,
            'model_name': model_name,
            'root_prompt': root_prompt,
            'system_prompt': system_prompt,
            'instructions': instructions,
            'tree_degree': tree_degree,
            'tree_depth': tree_depth,
            'tree_temperature': tree_temperature,
            'num_steps': num_steps,
            'batch_size': batch_size,
            'engine_temperature': engine_temperature,
            'tree_save_path': unique_tree_save_path,
            'dataset_save_path': unique_dataset_save_path
        }

        progress_placeholder.progress(0)
        status_placeholder.text("")

        df = generate_dataset_thread(generator, dataset_config, progress_placeholder, status_placeholder)

        if df is not None and not df.empty:
            st.success("Dataset Generated Successfully!")
            
            st.dataframe(df)
            
            with open(unique_dataset_save_path, 'rb') as f:
                jsonl_data = f.read()
            
            st.session_state.dataset_download_data = {
                'jsonl': {
                    'data': jsonl_data,
                    'filename': os.path.basename(unique_dataset_save_path)
                },
                'csv': {
                    'data': df.to_csv(index=False).encode('utf-8'),
                    'filename': os.path.basename(unique_dataset_save_path).replace('.jsonl', '.csv')
                }
            }

    if st.session_state.dataset_download_data:
        col8, col9 = st.columns(2)
        with col8:
            st.download_button(
                label="Download JSONL",
                data=st.session_state.dataset_download_data['jsonl']['data'],
                file_name=st.session_state.dataset_download_data['jsonl']['filename'],
                mime="application/jsonl",
                key="download_jsonl"
            )
        with col9:
            st.download_button(
                label="Download CSV",
                data=st.session_state.dataset_download_data['csv']['data'],
                file_name=st.session_state.dataset_download_data['csv']['filename'],
                mime="text/csv",
                key="download_csv"
            )

if __name__ == "__main__":
    main()