// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

/// The name of the route containing the text field tests.
const String textFieldRoute = 'textField';

/// The string supplied to the [ValueKey] for the normal text field.
const String normalTextFieldKeyValue = 'textFieldNormal';

/// The string supplied to the [ValueKey] for the password text field.
const String passwordTextFieldKeyValue = 'passwordField';

/// The string supplied to the [ValueKey] for the page navigation back button.
const String backButtonKeyValue = 'back';
