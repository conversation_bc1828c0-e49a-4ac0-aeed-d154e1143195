<div align="center">
  <a href="https://github.com/business-science/ai-data-science-team">
    <picture>
      <img src="/img/ai_data_science_team_logo_small.jpg" alt="AI Data Science Team" width="400">
  </picture>
  </a>
</div>
<div align="center">
  <em>An AI-powered data science team of agents to help you perform common data science tasks 10X faster</em>
</div>
<div align="center">
  <a href="https://pypi.python.org/pypi/ai-data-science-team"><img src="https://img.shields.io/pypi/v/ai-data-science-team.svg?style=for-the-badge" alt="PyPI"></a>
  <a href="https://github.com/business-science/ai-data-science-team"><img src="https://img.shields.io/pypi/pyversions/ai-data-science-team.svg?style=for-the-badge" alt="versions"></a>
  <a href="https://github.com/business-science/ai-data-science-team/blob/main/LICENSE"><img src="https://img.shields.io/github/license/business-science/ai-data-science-team.svg?style=for-the-badge" alt="license"></a>
</div>


# Your AI Data Science Team (🪖 An Army Of Agents)

**An AI-powered data science team of agents to help you perform common data science tasks 10X faster**.

[**Please ⭐ us on GitHub (it takes 2 seconds and means a lot).**](https://github.com/business-science/ai-data-science-team)

*Beta - This Python library is under active development. There may be breaking changes that occur until release of 0.1.0.* 

---

The AI Data Science Team of Copilots includes Agents that specialize data cleaning, preparation, feature engineering, modeling (machine learning), and interpretation of various business problems like:

- Churn Modeling
- Employee Attrition
- Lead Scoring
- Insurance Risk
- Credit Card Risk
- And more

## Table of Contents

- [Your AI Data Science Team (🪖 An Army Of Agents)](#your-ai-data-science-team--an-army-of-agents)
  - [Table of Contents](#table-of-contents)
  - [Companies That Want A Custom AI Data Science Team (And AI Apps)](#companies-that-want-a-custom-ai-data-science-team-and-ai-apps)
  - [Generative AI for Data Scientists Workshop](#generative-ai-for-data-scientists-workshop)
  - [Data Science Agents](#data-science-agents)
    - [NEW: Multi-Agents](#new-multi-agents)
    - [Data Science Apps](#data-science-apps)
    - [Apps Available Now](#apps-available-now)
      - [🔥 Agentic Applications](#-agentic-applications)
    - [Agents Available Now](#agents-available-now)
      - [Agents](#agents)
      - [🔥🔥 NEW! Machine Learning Agents](#-new-machine-learning-agents)
      - [Multi-Agents](#multi-agents)
    - [Agents Coming Soon](#agents-coming-soon)
  - [Disclaimer](#disclaimer)
  - [Installation](#installation)
  - [Usage](#usage)
    - [Example 1: Feature Engineering with the Feature Engineering Agent](#example-1-feature-engineering-with-the-feature-engineering-agent)
    - [Example 2: Cleaning Data with the Data Cleaning Agent](#example-2-cleaning-data-with-the-data-cleaning-agent)
  - [Contributing](#contributing)
  - [License](#license)
- [Want To Become A Full-Stack Generative AI Data Scientist?](#want-to-become-a-full-stack-generative-ai-data-scientist)

## Companies That Want A Custom AI Data Science Team (And AI Apps)

Want to have your own _customized_ enterprise-grade AI Data Science Team and *domain-specific* AI-powered Apps? 

**Send inquiries here:** [https://www.business-science.io/contact.html](https://www.business-science.io/contact.html)

## Generative AI for Data Scientists Workshop

If you're an aspiring data scientist who wants to learn how to build AI Agents and AI Apps for your company that performs Data Science, Business Intelligence, Churn Modeling, Time Series Forecasting, and more, then I'd love to help you. 

[**Register for my next Generative AI for Data Scientists workshop here.**](https://learn.business-science.io/ai-register)

## Data Science Agents

This project is a work in progress. New data science agents will be released soon.

![AI Data Science Team](/img/ai_data_science_team_.jpg) 

### NEW: Multi-Agents

This is the internals of the SQL Data Analyst Agent that connects to SQL databases to pull data into the data science environment. It creates pipelines to automate data extraction, performs Joins, Aggregations, and other SQL Query operations. And it includes a Data Visualization Agent that creates visualizations to help you understand your data.:

![Business Intelligence SQL Agent](/img/multi_agent_sql_data_visualization.jpg)

### Data Science Apps

This is a top secret project I'm working on. It's a multi-agent data science app that performs time series forecasting.

![Multi-Agent Data Science App](/img/ai_powered_apps.jpg) 

### Apps Available Now

[See all available apps here](/apps)

#### 🔥 Agentic Applications

1. **SQL Database Agent App:** Connects any SQL Database, generates SQL queries from natural language, and returns data as a downloadable table. [See Application](/apps/sql-database-agent-app/)

### Agents Available Now

#### Agents

1. **Data Wrangling Agent:** Merges, Joins, Preps and Wrangles data into a format that is ready for data analysis. [See Example](https://github.com/business-science/ai-data-science-team/blob/master/examples/data_wrangling_agent.ipynb)
2. **Data Visualization Agent:** Creates visualizations to help you understand your data. Returns JSON serializable plotly visualizations. [See Example](https://github.com/business-science/ai-data-science-team/blob/master/examples/data_visualization_agent.ipynb)
3. **🔥 Data Cleaning Agent:** Performs Data Preparation steps including handling missing values, outliers, and data type conversions. [See Example](https://github.com/business-science/ai-data-science-team/blob/master/examples/data_cleaning_agent.ipynb)
4. **Feature Engineering Agent:** Converts the prepared data into ML-ready data. Adds features to increase predictive accuracy of ML models. [See Example](https://github.com/business-science/ai-data-science-team/blob/master/examples/feature_engineering_agent.ipynb)
5. **🔥 SQL Database Agent:** Connects to SQL databases to pull data into the data science environment. Creates pipelines to automate data extraction. Performs Joins, Aggregations, and other SQL Query operations. [See Example](https://github.com/business-science/ai-data-science-team/blob/master/examples/sql_database_agent.ipynb)
6. **Data Loader Tools Agent:** Loads data from various sources including CSV, Excel, Parquet, and Pickle files. [See Example](https://github.com/business-science/ai-data-science-team/blob/master/examples/data_loader_tools_agent.ipynb)


#### 🔥🔥 NEW! Machine Learning Agents

1. **🔥 H2O Machine Learning Agent:** Builds and logs 100's of high-performance machine learning models. [See Example](https://github.com/business-science/ai-data-science-team/blob/master/examples/ml_agents/h2o_machine_learning_agent.ipynb)
2. **🔥 MLflow Tools Agent (MLOps):** This agent has 11+ tools for managing models, ML projects, and making production ML predictions with MLflow. [See Example](https://github.com/business-science/ai-data-science-team/blob/master/examples/ml_agents/mlflow_tools_agent.ipynb)


#### Multi-Agents

1. **SQL Data Analyst Agent:** Connects to SQL databases to pull data into the data science environment. Creates pipelines to automate data extraction. Performs Joins, Aggregations, and other SQL Query operations. Includes a Data Visualization Agent that creates visualizations to help you understand your data. [See Example](https://github.com/business-science/ai-data-science-team/blob/master/examples/multiagents/sql_data_analyst.ipynb)

### Agents Coming Soon

1. **Data Analyst:** Analyzes data structure, creates exploratory visualizations, and performs correlation analysis to identify relationships.
2. **Interpretability Agent:** Performs Interpretable ML to explain why the model returned predictions including which features were the most important to the model.
3. **Supervisor:** Forms task list. Moderates sub-agents. Returns completed assignment. 

## Disclaimer

**This project is for educational purposes only.**

- It is not intended to replace your company's data science team
- No warranties or guarantees provided
- Creator assumes no liability for financial loss
- Consult an experienced Generative AI Data Scientist for building your own custom AI Data Science Team
- If you want a custom enterprise-grade AI Data Science Team, [send inquiries here](https://www.business-science.io/contact.html). 

By using this software, you agree to use it solely for learning purposes.

## Installation

``` bash
pip install git+https://github.com/business-science/ai-data-science-team.git --upgrade
```

## Usage

[See all examples here.](/examples)

### Example 1: Feature Engineering with the Feature Engineering Agent

[See the full example here.](/examples/feature_engineering_agent.ipynb)

``` python
feature_engineering_agent = FeatureEngineeringAgent(model = llm)

feature_engineering_agent.invoke_agent(
    data_raw = df,
    user_instructions = "Make sure to scale and center numeric features",
    target_variable = "Churn",
    max_retries = 3,
)
```

``` bash
---FEATURE ENGINEERING AGENT----
    * CREATE FEATURE ENGINEER CODE
    * EXECUTING AGENT CODE
    * EXPLAIN AGENT CODE
```

``` python
feature_engineering_agent.get_data_engineered()
```

### Example 2: Cleaning Data with the Data Cleaning Agent

[See the full example here.](/examples/data_cleaning_agent.ipynb) 

``` python
data_cleaning_agent = DataCleaningAgent(model = llm)

response = data_cleaning_agent.invoke_agent(
    data_raw = df,
    user_instructions = "Don't remove outliers when cleaning the data.",
    max_retries = 3,
)
```

``` bash
---DATA CLEANING AGENT----
    * CREATE DATA CLEANER CODE
    * EXECUTING AGENT CODE
    * EXPLAIN AGENT CODE
```

``` python
data_cleaning_agent.get_data_cleaned()
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License. See LICENSE file for details. 

# Want To Become A Full-Stack Generative AI Data Scientist?

![Generative AI Data Scientist](/img/become_a_generative_ai_data_scientist.jpg)

I teach Generative AI Data Science to help you build AI-powered data science apps. [**Register for my next Generative AI for Data Scientists workshop here.**](https://learn.business-science.io/ai-register)


