[project]
name = "windows-use"
version = "0.1.31"
description = "An AI Agent that interacts with Windows OS at GUI level."
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "jeoge<PERSON><PERSON><PERSON>@gmail.com" }
]
license = 'MIT'
license-files = ["LICENSE"]
urls = { homepage = "https://github.com/CursorTouch" }
keywords = ["windows", "agent", "ai", "desktop","ai agent","automation"]
requires-python = ">=3.12"
dependencies = [
    "fuzzywuzzy>=0.18.0",
    "humancursor>=1.1.5",
    "langchain>=0.3.25",
    "langchain-community>=0.3.25",
    "markdownify>=1.1.0",
    "pillow>=11.2.1",
    "pyautogui>=0.9.54",
    "pydantic>=2.11.7",
    "python-levenshtein>=0.27.1",
    "requests>=2.32.4",
    "setuptools>=80.9.0",
    "termcolor>=3.1.0",
    "twine>=6.1.0",
    "uiautomation>=2.0.28",
]

[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["windows_use"]
include-package-data = true

[tool.setuptools.package-data]
"windows_use.agent.prompts" = ["*.md"]
