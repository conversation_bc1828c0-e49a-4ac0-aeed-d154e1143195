{"cells": [{"cell_type": "markdown", "metadata": {"id": "xzWu-60xBDwe"}, "source": ["Copyright 2024 Google LLC."]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "fXkE8WP5BIer"}, "outputs": [], "source": ["# @title Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "# https://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "metadata": {"id": "KYCPCcxctDvx"}, "source": ["# Fine-tune <PERSON><PERSON>G<PERSON><PERSON> with <PERSON><PERSON>\n", "\n", "This notebook shows how to fine-tune [PaliGemma](https://ai.google.dev/gemma/docs/paligemma) on a vision-language task with [<PERSON><PERSON>](https://keras.io/). *Fine-tuning* is a process that can improve your model's performance on specific tasks or help the model adhere to specific output requirements when instructions aren't sufficient and you have a set of examples that demonstrate the outputs you want. Gemma-based models like PaliGemma require fine-tuning to produce expected results.\n", "\n", "<table align=\"left\">\n", "  <td>\n", "    <a target=\"_blank\" href=\"https://colab.research.google.com/github/google-gemini/gemma-cookbook/blob/main/PaliGemma/[PaliGemma_1]Finetune_with_Keras.ipynb\"><img src=\"https://www.tensorflow.org/images/colab_logo_32px.png\" />Run in Google Colab</a>\n", "  </td>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {"id": "FvoBtrn4uvLg"}, "source": ["## Before you begin\n", "\n", "Before going through this notebook, you should be familiar with Python code, as well as how large language models (LLMs) are trained. You don't need to be familiar with Keras, but basic knowledge about Keras (or similar technologies) is helpful when reading through the example code."]}, {"cell_type": "markdown", "metadata": {"id": "OmoxnqUAxJzg"}, "source": ["## Setup\n", "\n", "The following sections explain the preliminary steps for getting a notebook to use a PaliGemma model, including model access, getting an API key, and configuring the notebook runtime."]}, {"cell_type": "markdown", "metadata": {"id": "w1UH8Qjhxe5a"}, "source": ["### Get access to PaliGemma\n", "\n", "Before using PaliGemma for the first time, you must request access to the model through Kaggle by completing the following steps:\n", "\n", "1. Log in to [Kaggle](https://www.kaggle.com/), or create a new Kaggle account if you don't already have one.\n", "2. Go to the [PaliGemma model card](https://www.kaggle.com/models/google/paligemma/) and click **Request Access**.\n", "3. Complete the consent form and accept the terms and conditions."]}, {"cell_type": "markdown", "metadata": {"id": "82Et2NA6zuxg"}, "source": ["### Configure your API key\n", "\n", "To use PaliGemma, you must provide your Kaggle username and a Kaggle API key.\n", "\n", "To generate a Kaggle API key, open your [**Settings** page in Kaggle](https://www.kaggle.com/settings) and click **Create New Token**. This triggers the download of a `kaggle.json` file containing your API credentials.\n", "\n", "Then, in Colab, select **Secrets** (🔑) in the left pane and add your Kaggle username and Kaggle API key. Store your username under the name `KAGGLE_USERNAME` and your API key under the name `KAGGLE_KEY`."]}, {"cell_type": "markdown", "metadata": {"id": "CV8xgN_rzyGR"}, "source": ["### Select the runtime\n", "\n", "To complete this tutorial, you'll need to have a Colab runtime with sufficient resources to run the PaliGemma model. In this case, you can use a T4 GPU:\n", "\n", "1. In the upper-right of the Colab window, click the **▾ (Additional connection options)** dropdown menu.\n", "1. Select **Change runtime type**.\n", "1. Under **Hardware accelerator**, select **T4 GPU**."]}, {"cell_type": "markdown", "metadata": {"id": "pUp359rD0bTe"}, "source": ["### Set environment variables\n", "\n", "Set the environment variables for `<PERSON>AG<PERSON>LE_USERNAME` and `KAGGLE_KEY`."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zGLIp1Cx3_CX"}, "outputs": [], "source": ["import os\n", "from google.colab import userdata\n", "\n", "# Note: `userdata.get` is a Colab API. If you're not using Colab, set the env\n", "# vars as appropriate or make your credentials available in ~/.kaggle/kaggle.json\n", "\n", "os.environ[\"KAGGLE_USERNAME\"] = userdata.get('KAGGLE_USERNAME')\n", "os.environ[\"KAGGLE_KEY\"] = userdata.get('KAGGLE_KEY')"]}, {"cell_type": "markdown", "metadata": {"id": "rCd__uzW_eK-"}, "source": ["### Install KerasNLP"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "DfxKb3F839Ks"}, "outputs": [], "source": ["!pip install -U -q keras-nlp"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ZOxk5hUaX5la"}, "outputs": [], "source": ["# Set the backbend before importing Ke<PERSON>\n", "os.environ[\"KERAS_BACKEND\"] = \"jax\"\n", "# Avoid memory fragmentation on JAX backend.\n", "os.environ[\"XLA_PYTHON_CLIENT_MEM_FRACTION\"] = \"1.00\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "QlCp6b2VqNfy"}, "outputs": [], "source": ["BATCH_SIZE = 1\n", "TRAIN_EXAMPLES = 4\n", "LEARNING_RATE = 0.003\n", "\n", "TRAIN_STEPS = TRAIN_EXAMPLES // BATCH_SIZE\n", "EVAL_STEPS = TRAIN_STEPS // 4"]}, {"cell_type": "markdown", "metadata": {"id": "zDoq0O77GF30"}, "source": ["## Download the training and validation data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "AZeUDK3YX6Zr"}, "outputs": [], "source": ["import io\n", "import json\n", "import os\n", "import urllib\n", "\n", "import base64\n", "import html\n", "\n", "import numpy as np\n", "import keras\n", "import keras_nlp\n", "import tensorflow as tf\n", "import matplotlib.pyplot as plt\n", "\n", "from IPython.core.display import display, HTML\n", "from PIL import Image"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dTfe2k8J4Bw0"}, "outputs": [], "source": ["train_file = urllib.request.urlopen(\n", "    \"https://storage.googleapis.com/longcap100/data_train90.jsonl\"\n", ")\n", "val_file = urllib.request.urlopen(\n", "    \"https://storage.googleapis.com/longcap100/data_val10.jsonl\"\n", ")\n", "\n", "# Crop the image to the desired dimensions.\n", "target_size = (224, 224)\n", "\n", "def load_image(image_url):\n", "    image = tf.io.decode_jpeg(urllib.request.urlopen(image_url).read())\n", "    return tf.image.resize(image, size=target_size)\n", "\n", "def load_dataset(file):\n", "    captions = []\n", "    images = []\n", "    for line in file:\n", "        sample = json.loads(line)\n", "        captions.append(sample[\"suffix\"])\n", "        image_name = sample[\"image\"]\n", "        image_url = f\"https://storage.googleapis.com/longcap100/{image_name}\"\n", "        images.append(load_image(image_url))\n", "    return tf.data.Dataset.from_tensor_slices({\n", "        \"images\": images,\n", "        \"prompts\": [\"caption en\\n\"] * len(images),\n", "        \"responses\": captions,\n", "    })\n", "\n", "train_data = load_dataset(train_file).shuffle(1000).batch(BATCH_SIZE)\n", "val_data = load_dataset(val_file).shuffle(1000).batch(BATCH_SIZE)"]}, {"cell_type": "markdown", "metadata": {"id": "J43VQ92hgJcd"}, "source": ["## View training examples\n", "\n", "In this notebook, the training data contains 90 images that are paired with long descriptions of what's depicted in the image.\n", "\n", "**Note:** Normal training data sets that are meant to be used for practical use cases should contain more images, but this notebook limits the number of data points so that you can train the model in a reasonable amount of time for an example.\n", "\n", "The code below prints a random selection of images with their descriptions from the training data set so that you can see what the images and descriptions your model is trained on looks like. Each image is displayed in as a 128x128 pixel JPEG, with the description printed next to the image to the right."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "IPKB8yK8gw8g"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training examples\n"]}, {"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A person holds a yellow popsicle in their hand, the popsicle adorned with text psecre on the stick. The person wears a green jacket with a red collar, a white shirt, and silver rings on their finger. The jacket has a zipper on the front. </p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A tool box filled with a variety of tools, including a wrench with a silver head, a screwdriver with a gray handle,a wrench with a gray head, a screwdriver with a gray handle, a metal socket with a silver head...</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A yellow train is stopped at a station. The train is on the tracks, and the lights are on. The platform has a white line and a white stripe on the platform. There is a person standing on the platform, and a person walking on the platform. The train has a number on its side. The train is moving, and the doors are closed.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">Two champagne glasses sit on a ledge overlooking the ocean, reflecting the setting sun. The glasses are filled with bubbly champagne, and the bubbles dance in the liquid. The sky is clear, and the water is calm. The mountains loom large in the distance, and the rocky cliffs on the shore line the water&#x27;s edge. </p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A red train travels down a snow-covered street, its headlights illuminating the snow. The train has a red and white Coca-Cola logo on its side, and the snow is piled up on the side of the road. A green traffic light is on the side of the road. The train has a number on its front, and the snow is white. The street light is green, and the traffic light is also green. The train has a windshield wiper, and the snow is on the ground.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A room with a ping pong table and a desk. The room has a large window and a door. There is a ping pong table with a blue top in the middle and black lamps on the right. The floor is wooden. There is a black chair on the left and four desk chairs on the right. The room has a lot of lights hanging from the ceiling. The room also has a lot of decorations on the wall.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">Cinque Terre city perched on the side of a towering cliff, overlooking the serene ocean. The sky above is adorned with fluffy white clouds, while the water below mirrors the sky in its tranquility. The buildings below are painted in vibrant hues, ranging from yellow to orange to pink. The hillside is adorned with lush greenery, and the rocky shore provides a picturesque backdrop. The overall atmosphere is serene and captivating, offering a glimpse into the heart of this idyllic town.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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**********************************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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">A white tray with a green toothbrush and a metal dental mirror sits on a white wall. The tray is square and has a white paper on it. The toothbrush is green. The mirror is on a metal tray and has a white paper on it. There is also a small green object on the tray. The wall is white and the window is black.</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def render_inline(image, resize=(224, 224)):\n", "  \"\"\"Convert image into inline html.\"\"\"\n", "  image = tf.keras.preprocessing.image.array_to_img(image)\n", "  image.resize(resize)\n", "  with io.BytesIO() as buffer:\n", "    image.save(buffer, format='jpeg')\n", "    image_b64 = str(base64.b64encode(buffer.getvalue()), \"utf-8\")\n", "    return f\"data:image/jpeg;base64,{image_b64}\"\n", "\n", "def render_example(image, caption):\n", "  image = np.asarray(image)\n", "  return f\"\"\"\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"{render_inline(image, resize=(64,64))}\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">{html.escape(caption)}</p>\n", "    </div>\n", "    \"\"\"\n", "\n", "html_out = \"\"\n", "\n", "for element in train_data.take(8):\n", "  caption = tf.compat.as_str_any(element[\"responses\"].numpy()[0])\n", "  html_out += render_example(element[\"images\"].numpy()[0], caption)\n", "\n", "print(\"Training examples\")\n", "display(HTML(html_out))"]}, {"cell_type": "markdown", "metadata": {"id": "Qa6cn8nXJx51"}, "source": ["## Load Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4eoMtlGCaEPg"}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Preprocessor: \"pali_gemma_causal_lm_preprocessor\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mPreprocessor: \"pali_gemma_causal_lm_preprocessor\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Tokenizer (type)                                   </span>┃<span style=\"font-weight: bold\">                                             Vocab # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ pali_gemma_tokenizer (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">PaliGemmaTokenizer</span>)          │                                             <span style=\"color: #00af00; text-decoration-color: #00af00\">257,152</span> │\n", "└────────────────────────────────────────────────────┴─────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mTokenizer (type)                                  \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m                                            Vocab #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ pali_gemma_tokenizer (\u001b[38;5;33mPaliGemmaTokenizer\u001b[0m)          │                                             \u001b[38;5;34m257,152\u001b[0m │\n", "└────────────────────────────────────────────────────┴─────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"pali_gemma_causal_lm\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"pali_gemma_causal_lm\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                  </span>┃<span style=\"font-weight: bold\"> Output Shape              </span>┃<span style=\"font-weight: bold\">         Param # </span>┃<span style=\"font-weight: bold\"> Connected to               </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ images (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)           │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">3</span>)       │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ padding_mask (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>)              │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ response_mask (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>)              │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_ids (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)        │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>)              │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ pali_gemma_backbone           │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">2048</span>)        │   <span style=\"color: #00af00; text-decoration-color: #00af00\">2,923,335,408</span> │ images[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>],              │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">PaliGemmaBackbone</span>)           │                           │                 │ padding_mask[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>],        │\n", "│                               │                           │                 │ response_mask[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>],       │\n", "│                               │                           │                 │ token_ids[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_embedding               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">257152</span>)      │     <span style=\"color: #00af00; text-decoration-color: #00af00\">526,647,296</span> │ pali_gemma_backbone[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]  │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReversibleEmbedding</span>)         │                           │                 │                            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ get_item (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">GetItem</span>)            │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">257152</span>)      │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ token_embedding[<span style=\"color: #00af00; text-decoration-color: #00af00\">1</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]      │\n", "└───────────────────────────────┴───────────────────────────┴─────────────────┴────────────────────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                 \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape             \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m        Param #\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mConnected to              \u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ images (\u001b[38;5;33mInputLayer\u001b[0m)           │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m3\u001b[0m)       │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ padding_mask (\u001b[38;5;33mInputLayer\u001b[0m)     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m)              │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ response_mask (\u001b[38;5;33mInputLayer\u001b[0m)    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m)              │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_ids (\u001b[38;5;33mInputLayer\u001b[0m)        │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m)              │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ pali_gemma_backbone           │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m2048\u001b[0m)        │   \u001b[38;5;34m2,923,335,408\u001b[0m │ images[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m],              │\n", "│ (\u001b[38;5;33mPaliGemmaBackbone\u001b[0m)           │                           │                 │ padding_mask[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m],        │\n", "│                               │                           │                 │ response_mask[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m],       │\n", "│                               │                           │                 │ token_ids[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_embedding               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mN<PERSON>\u001b[0m, \u001b[38;5;34m257152\u001b[0m)      │     \u001b[38;5;34m526,647,296\u001b[0m │ pali_gemma_backbone[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]  │\n", "│ (\u001b[38;5;33mReversibleEmbedding\u001b[0m)         │                           │                 │                            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ get_item (\u001b[38;5;33mGetItem\u001b[0m)            │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m257152\u001b[0m)      │               \u001b[38;5;34m0\u001b[0m │ token_embedding[\u001b[38;5;34m1\u001b[0m][\u001b[38;5;34m0\u001b[0m]      │\n", "└───────────────────────────────┴───────────────────────────┴─────────────────┴────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">2,923,335,408</span> (10.89 GB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m2,923,335,408\u001b[0m (10.89 GB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">2,923,335,408</span> (10.89 GB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m2,923,335,408\u001b[0m (10.89 GB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# use half-precision to save memory\n", "#keras.config.set_floatx('bfloat16') # BUG?\n", "\n", "pali_gemma_lm = keras_nlp.models.PaliGemmaCausalLM.from_preset(\n", "    \"pali_gemma_3b_224\"\n", ")\n", "pali_gemma_lm.summary()"]}, {"cell_type": "markdown", "metadata": {"id": "JAx6m6o8aTy7"}, "source": ["## Inference before fine tuning"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gjdx0Ocxaap7"}, "outputs": [{"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "cow standing on a beach</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Inference Result\n"]}, {"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "a red blazer and black bag , a fashion item of the week</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "person in a long shot of our model</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCADgAOADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDvAKXFKBSgVIDcUuKXFLigBMUuKXFLigBKWjFLQAYopcUYoAMUUtGKAEopcUUAJijFLRTAMUYpaKAG4oxTqKYDcUYp1JQAlJinYpKAGkU3FPxSEUAMpCKfTSKkBcUuKKdQA3FLilpaAExRilpaAExRilpaAExS0uKMUAJijFLiigBKKWjFACUU7FJTASlpaKYCUUtFADaKdikoASilpKAEptOpKAG0hp2KQ0gFxS0UtIBMUtFLQAmKWlooASlxS0UAFGKWloASkp1FACYoxS0UwExRS0UAJijFLUMd5azSeXFcwyP12q4J/KmBLijFNaWJGKtIgYdiwzSG4hAz5qY9jmgB+KMVD9st/wDnp/46aa19CDgB29wP8aV0BPSYqv8AbkxxHJn6D/GmC9kJ4tz/AN9f/Wo5kOxapCKrNcznG2EL9TmkE1yf4I/yP+NLmQWZZpDVU/a/72PotNK3DjBmx9OKXMgsy8KKWlpgJiloxS0AFFFLQAUUUtAELXUKsVLjI64GaabyMdAzfQVKkEal8g88jHY/5zVPVj5GlXEidQuORnrxS1DQrvrm6ZI7a2M25d5JfbhD909D1IP5fXCf2pdiQwiKN5im/gEBBnHPrnnA46Gq+gqt1bpenJd90bIQMlFYhMH2JYc+p9s6ghilaO7twGIBVlI+8M8jB6MCO/TkcZo1DQypNWvWDRq8KMjiMsifM7bdxCAn+ee/TGaks/7UwGuL12zzsKpxnHBKrzjpxWgIQJjdWflyJJjzIyepHGV9G7EH0AOMGpRLbCFr4qqhEYM+35lA6g454IPHqKVmGhRvbm9htw0T5dnSMfJnG5guenbOfw7daYmlNIxa4Mkzk5zLz+Q6D8K0TC12n+krtjYf6nP/AKEe59hwPfg0kBe3uDbSzGXfueEleVQYyrHvgsMHHTrkjJLeYXM6TSZrwAXTH7OoAW33ZX6t69uOgx37Xk01FQAYAHAA7VeqrPLJ9rhtU3KJEd2kGPlC7RgZ7ksO3Y0+ULjVso9xG7OOuBT/ALFF/kf/AF6nSNY0CqMD65zTqOVBdkC2sankZ/AU7yEByBUlFPlQXY0Io7ZFN8tfTFSUlOyC7GhQKQqPQU40lKyFcbjHQUhpxpDQMKWiigYtFFFAC0UUUCFopKiku4IvvSLn0HNAE1ZeqzR3NnNaBtu8YL9cc/rTbnU2kHlwKQD37mqARmbdIcAdqlsLFjS4ksILePJkMKyKGPGd7Bjx9QKsxXot0ZVGQzs4z2ySf5mqfzsNqA49akS0cnLnAouFizaXJiVokXdl3fJ7FmLEfqal8pHhmiYfu5t29R33dajQJGMKM/SnMWYY+6KLhYm+1uWIUKQO/rTeTMszn50UqG9iQT/6CKYuFGAKcFZj6+wouOxIJ5D3AH05pwnbjIQkAgNsG4A9ecZ7D8qZ5bDkjAp4iyOHGfelqBYhkjmcIwEZPAbPH405lKsQQQQcEHtVNkdDyD9auRSpMmJG2OBgMejfX0qkxNDaKeyMnJHB6Ecg/jTaYhKSnUhoAaaKKKBjaQ06kNACUtJUU8pjACjLHpSbGTU15Uj+8wHtVA+dIxDiTrzlTj+VNcfNyxY9zS5h2LT3yL0Qn6nFVmvp3+4Ao9QP8aahH8IU/UA05Yi3LUrisV5Hkk/1kpPtmgQluin8atRwKTuUYX1NTybFTG0A+oPNFwsU1tiRycD0FPWCNf4c+5qUE8AcDoBSyI6RM5Vmx/CgyT9BQFhn0I/CjHPTP1p1kftBbfC8ZABw3HUkdPw/Wr6oq9FAosx6FNYZG6Kf5UPGYvvITz1HSr1FOwrmerg8DAPTmpBIy43Lkdc+1Tvbxv8Aw4PqKrtBNFzGdw7j/wCtS1QEySgng4Pv/jT9qkZxjPcVUWQM22SMqfUD+lP2uvzJIMfkaYFkeYnQgigFGPIKt7cfpUSSj+P8xxUnmIe4Ye4oEPTzEz5bZBGCAcE+3vUqyo/Eijd6j5Dn+X8qgCgj5Gx7GlLMo+dcj35FAE/lqxwkgyT91/kP68frUbo6EB1ZcjIyMZFMBAHyMVHcZyD+FSJPLGCCMoeoXofqDxTuAykNWUe3nO04jY9wMfoePyIoaylC5G1uvAPOPXmi4FWmmnspUkMCCOCD2ppoGJS0lLQAj7tjbPvY4qr9llPJZMk88VcopNXAqtAFTJEa454FIkTBeVJzn9at0tLlHcrlCwxtYf8AARQIm3ZK7j74qxS0+VCuQiM91H6VDcbbeMyOAR6ADNXKpap/x7AerH/0E0uVBcSzkivYUkjDBWQONwxwc/4VZ+zj1qj4fH/Emsm/vWyfzatSmkFyIQKO5/M0vk+jN+dSUtOwEXk/7Zo8n/bapaKLAReUccOfyFKEYf3T9RUlFFgGFCRxsH4H/Gm+XIOjL+VTUUWAh2Skcsv5Uo85em01LRRYCuwlZidignuDSgzL0QfnU1FFgIW8xuqJzSI91Cf3TlVP3l6g1NSGiwEn2t7qELdQpHMFyHQ5Gf7vv+Q/nUBp1NNABRRRQAtFFFABS0lFAC0UUUALVLU/9RH/AL//ALKau1R1M/uY/wDf/wDZTQ9gGeH/APkXdMPrbL/WtKs7w+P+Ka0r/r1StGmhC0UlLQMKKKKACiiigAooooAKKKKACiikoAKSlpKAEpppaQ0gCiiimAUtJS0gCiiigAooooAWqWp/8e6H0f8A9lNXapal/wAe6/739DQ9gG6Bx4a0rn/l1T+VaGaz9B48NaT3/wBEj/lV+mIWlpKKBi0UlFAC0UmaKAFopM0UALRSUZoAWikzSZoAWkozSZoADSGjNNJpAOopKWmAUUUUAFFFFABRmiigBapal/x7D/e/oauVT1P/AI9fx/oaUtgE0Pjw3pI/6c4/5VezVHRf+Rd0r/rzi/8AQau0xIdRTaM0DHUU3NGaAHZozTc0ZoAdRmm5ozQA7NJmkzSZoAdmjNNzRmgBaSkzSZoAXNITSE0hNICSiiimAUUUUAFLSUUALRSZpM0ALVTUv+PU/X+hq1mobqJZbcqxIGe1KWwIh0b/AJF/S/8Ar0j/APQau0y2t0tdNtYULERxiNc+gGOfenUxIXNGaSjNAxaKbmjNADqM03NGaAFzRmm5ozQA7NJmm5oJoAdmkzTc0maAH5pM03NITQA7NNJppNNLUgLdFJRTEFFFJQAtFJRQMWkozSZoAWmTf6hvwp1MmP7lvwpS2Bbk5/49YPoaZmoba7F1bYEM0YikaMNKm0SYxll7lc5GeM4JGRgmSmIXNGaTNJmgY7NJmkzSZoAdmkzSZpM0CHZpM02jNADs0mabmkoGOzSZpM0hNAClqQtTc0hNIBxNNJpCaYWoA0M0ZpM0maokdSUlGaAFzRSZpM0AKTRTaWiwC0yY/uW/ClzTJT+6P1FTLYa3J8/6JB9DUead/wAusP0P9KZmqELmkpM0ZosAuaKbmjNFgFzSE0maTNADs0maTNITSGLmkzSZppagY4mm5pM0maQC5pCaaTSE0XAUtUbNQTTCakaNXNGabmjNakC5ozSZpM0AOzSZpM0ZoAXNGabmjNADs0yT/Uv+FLmkb/j3l+g/nUz+EcdyO1iu4rdzdXaziSZnhVYtgijwoCdTuOQSWPUt0AwKlzTif9Fg+h/pUWaYh2aTNJmkzRcB2aTNJmkzQAuaM03NJmkMcTSZppNJmgYuaTNNoJpAKTSE00mkzQAuaQmkJppNSNCk00mkJppNIZ//2Q==\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "white hangers on a rack in the bedroom</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "this graphic sweatshirt is a must have for your wardrobe .</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["test_image_url = 'https://storage.googleapis.com/keras-cv/models/paligemma/cow_beach_1.png'\n", "test_image = load_image(test_image_url)\n", "\n", "def inference_test(image):\n", "  prompt = 'caption en\\n'\n", "  output = pali_gemma_lm.generate(\n", "      inputs={\n", "          \"images\": image,\n", "          \"prompts\": prompt,\n", "      }\n", "  )\n", "  return render_example(image, output)\n", "\n", "display(HTML(inference_test(test_image)))\n", "\n", "\n", "def make_predictions():\n", "  html_out = \"\"\n", "  for element in val_data.take(4):\n", "    html_out += inference_test(element[\"images\"].numpy()[0])\n", "\n", "  print(\"\\nInference Result\")\n", "  display(HTML(html_out))\n", "\n", "make_predictions()"]}, {"cell_type": "markdown", "metadata": {"id": "BJbdY9ehahEK"}, "source": ["## <PERSON><PERSON> Fine-tuning"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "VvgDhGKhIHvG"}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Preprocessor: \"pali_gemma_causal_lm_preprocessor\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mPreprocessor: \"pali_gemma_causal_lm_preprocessor\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Tokenizer (type)                                   </span>┃<span style=\"font-weight: bold\">                                             Vocab # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ pali_gemma_tokenizer (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">PaliGemmaTokenizer</span>)          │                                             <span style=\"color: #00af00; text-decoration-color: #00af00\">257,152</span> │\n", "└────────────────────────────────────────────────────┴─────────────────────────────────────────────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mTokenizer (type)                                  \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m                                            Vocab #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ pali_gemma_tokenizer (\u001b[38;5;33mPaliGemmaTokenizer\u001b[0m)          │                                             \u001b[38;5;34m257,152\u001b[0m │\n", "└────────────────────────────────────────────────────┴─────────────────────────────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"pali_gemma_causal_lm\"</span>\n", "</pre>\n"], "text/plain": ["\u001b[1mModel: \"pali_gemma_causal_lm\"\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                  </span>┃<span style=\"font-weight: bold\"> Output Shape              </span>┃<span style=\"font-weight: bold\">         Param # </span>┃<span style=\"font-weight: bold\"> Connected to               </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ images (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)           │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">3</span>)       │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ padding_mask (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)     │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>)              │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ response_mask (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>)              │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_ids (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">InputLayer</span>)        │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>)              │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ pali_gemma_backbone           │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">2048</span>)        │   <span style=\"color: #00af00; text-decoration-color: #00af00\">2,924,699,376</span> │ images[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>],              │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">PaliGemmaBackbone</span>)           │                           │                 │ padding_mask[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>],        │\n", "│                               │                           │                 │ response_mask[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>],       │\n", "│                               │                           │                 │ token_ids[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_embedding               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">257152</span>)      │     <span style=\"color: #00af00; text-decoration-color: #00af00\">526,647,296</span> │ pali_gemma_backbone[<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]  │\n", "│ (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">ReversibleEmbedding</span>)         │                           │                 │                            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ get_item (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">GetItem</span>)            │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">257152</span>)      │               <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │ token_embedding[<span style=\"color: #00af00; text-decoration-color: #00af00\">1</span>][<span style=\"color: #00af00; text-decoration-color: #00af00\">0</span>]      │\n", "└───────────────────────────────┴───────────────────────────┴─────────────────┴────────────────────────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                 \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape             \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m        Param #\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mConnected to              \u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩\n", "│ images (\u001b[38;5;33mInputLayer\u001b[0m)           │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m3\u001b[0m)       │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ padding_mask (\u001b[38;5;33mInputLayer\u001b[0m)     │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m)              │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ response_mask (\u001b[38;5;33mInputLayer\u001b[0m)    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m)              │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_ids (\u001b[38;5;33mInputLayer\u001b[0m)        │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m)              │               \u001b[38;5;34m0\u001b[0m │ -                          │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ pali_gemma_backbone           │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m2048\u001b[0m)        │   \u001b[38;5;34m2,924,699,376\u001b[0m │ images[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m],              │\n", "│ (\u001b[38;5;33mPaliGemmaBackbone\u001b[0m)           │                           │                 │ padding_mask[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m],        │\n", "│                               │                           │                 │ response_mask[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m],       │\n", "│                               │                           │                 │ token_ids[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ token_embedding               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mN<PERSON>\u001b[0m, \u001b[38;5;34m257152\u001b[0m)      │     \u001b[38;5;34m526,647,296\u001b[0m │ pali_gemma_backbone[\u001b[38;5;34m0\u001b[0m][\u001b[38;5;34m0\u001b[0m]  │\n", "│ (\u001b[38;5;33mReversibleEmbedding\u001b[0m)         │                           │                 │                            │\n", "├───────────────────────────────┼───────────────────────────┼─────────────────┼────────────────────────────┤\n", "│ get_item (\u001b[38;5;33mGetItem\u001b[0m)            │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m257152\u001b[0m)      │               \u001b[38;5;34m0\u001b[0m │ token_embedding[\u001b[38;5;34m1\u001b[0m][\u001b[38;5;34m0\u001b[0m]      │\n", "└───────────────────────────────┴───────────────────────────┴─────────────────┴────────────────────────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">2,924,699,376</span> (10.90 GB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m2,924,699,376\u001b[0m (10.90 GB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">1,363,968</span> (5.20 MB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m1,363,968\u001b[0m (5.20 MB)\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">2,923,335,408</span> (10.89 GB)\n", "</pre>\n"], "text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m2,923,335,408\u001b[0m (10.89 GB)\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Enable lora to freeze most of the model and save memory.\n", "pali_gemma_lm.backbone.enable_lora(4)\n", "pali_gemma_lm.summary()\n", "\n", "# Lower our sequence length to further save memory.\n", "pali_gemma_lm.preprocessor.sequence_length = 64\n", "\n", "# Use Cosine Decay Scheduler with Warm up\n", "#cosine_decay_scheduler = tf.keras.optimizers.schedules.CosineDecay(\n", "#    initial_learning_rate = 0,\n", "#    decay_steps = TRAIN_EXAMPLES,\n", "#    warmup_target = LEARNING_RATE,\n", "#    warmup_steps = TRAIN_EXAMPLES / 10\n", "#)\n", "\n", "def plot_scheduler(step, scheduler):\n", "  x = range(step)\n", "  y = []\n", "  for step in x:\n", "    y.append(scheduler(step))\n", "  plt.plot(x, y, label=scheduler.name)\n", "  plt.xlabel('Epoch')\n", "  plt.ylabel('Learning Rate')\n", "  plt.legend()\n", "  plt.show()\n", "\n", "#plot_scheduler(TRAIN_EXAMPLES, cosine_decay_scheduler)\n", "\n", "# Use AdamW (a common optimizer for transformer models).\n", "#optimizer = keras.optimizers.SGD(learning_rate=cosine_decay_scheduler)\n", "optimizer = keras.optimizers.AdamW(learning_rate=LEARNING_RATE)\n", "\n", "pali_gemma_lm.compile(\n", "    loss=keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "    optimizer=optimizer,\n", "    weighted_metrics=[keras.metrics.SparseCategoricalAccuracy()],\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "oVTG6rXX9oF2"}, "source": ["## Fine-tune the model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "h8FW7mMGeY3S"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/4\n", "\u001b[1m90/90\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m204s\u001b[0m 2s/step - loss: 1.6644 - sparse_categorical_accuracy: 0.5384\n", "Epoch 2/4\n", "\u001b[1m90/90\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m115s\u001b[0m 1s/step - loss: 0.9424 - sparse_categorical_accuracy: 0.6918\n", "Epoch 3/4\n", "\u001b[1m90/90\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m143s\u001b[0m 2s/step - loss: 0.6737 - sparse_categorical_accuracy: 0.7758\n", "Epoch 4/4\n", "\u001b[1m90/90\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m143s\u001b[0m 2s/step - loss: 0.5461 - sparse_categorical_accuracy: 0.8162\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["class CustomCallback(keras.callbacks.Callback):\n", "    def on_epoch_end(self, epoch, logs=None):\n", "        keys = list(logs.keys())\n", "        if(epoch % EVAL_STEPS == EVAL_STEPS-1):\n", "          # Evaluate\n", "          display(HTML(inference_test(test_image)))\n", "          make_predictions()\n", "\n", "#history = pali_gemma_lm.fit(train_data, epochs=TRAIN_STEPS, callbacks=[CustomCallback()])\n", "history = pali_gemma_lm.fit(train_data, epochs=TRAIN_STEPS)\n", "plt.plot(history.history['loss'])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "yd9c2gkTJ1px"}, "source": ["## Output\n", "\n", "The validation data for this notebook consists of just 10 images. In normal code, you would likely have many more data points for validation, but for this notebook, run the following code to generate descriptions for all 10 images. After tuning the model, these descriptions should be very similar in form and content coverage to the descriptions included with the training data that you looked at earlier in this notebook.\n", "\n", "Run the below code to generate descriptions for the validation data set."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_VeH5WDFfziX"}, "outputs": [{"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "A brown cow stands on the shore of a beach, its eyes open and its mouth closed. The cow has a white spot on its head, and its ears are white. The beach is sandy and white, and the sand is white. The sky is clear blue, and the sun is shining.</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Inference Result\n"]}, {"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "A white clothes rack with white hangers is set up in a room. The rack is on a metal pole and has white and black clothes on it. The clothes on the rack are white and black.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "A white hoodie on a hanger, showcasing a long sleeve and a drawstring at the waist. The hoodie has a black string on the front and a black string on the back.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "A woman wearing a pink bag and a pair of jeans. The bag is pink and has a black strap. The woman has a gold bracelet on her wrist.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">caption en\n", "A woman holds a black handbag with a gold chain. The handbag has a black and gold chain and a black and silver lock. The woman has skinny blue jeans and black boots.</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(HTML(inference_test(test_image)))\n", "make_predictions()"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "[PaliGemma_1]Finetune_with_Keras.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}