Versions used, 6_1_0
md.comp.assist-chip.container.shape,
md.comp.assist-chip.disabled.label-text.color,
md.comp.assist-chip.elevated.container.color,
md.comp.assist-chip.elevated.container.elevation,
md.comp.assist-chip.elevated.container.shadow-color,
md.comp.assist-chip.elevated.disabled.container.color,
md.comp.assist-chip.elevated.disabled.container.elevation,
md.comp.assist-chip.elevated.disabled.container.opacity,
md.comp.assist-chip.elevated.pressed.container.elevation,
md.comp.assist-chip.flat.container.elevation,
md.comp.assist-chip.flat.disabled.outline.color,
md.comp.assist-chip.flat.disabled.outline.opacity,
md.comp.assist-chip.flat.outline.color,
md.comp.assist-chip.flat.outline.width,
md.comp.assist-chip.label-text.color,
md.comp.assist-chip.label-text.text-style,
md.comp.assist-chip.with-icon.disabled.icon.color,
md.comp.assist-chip.with-icon.icon.color,
md.comp.assist-chip.with-icon.icon.size,
md.comp.badge.color,
md.comp.badge.large.label-text.color,
md.comp.badge.large.label-text.text-style,
md.comp.badge.large.size,
md.comp.badge.size,
md.comp.banner.container.color,
md.comp.banner.container.elevation,
md.comp.banner.supporting-text.text-style,
md.comp.bottom-app-bar.container.color,
md.comp.bottom-app-bar.container.elevation,
md.comp.bottom-app-bar.container.height,
md.comp.bottom-app-bar.container.shape,
md.comp.checkbox.error.focus.state-layer.color,
md.comp.checkbox.error.focus.state-layer.opacity,
md.comp.checkbox.error.hover.state-layer.color,
md.comp.checkbox.error.hover.state-layer.opacity,
md.comp.checkbox.error.pressed.state-layer.color,
md.comp.checkbox.error.pressed.state-layer.opacity,
md.comp.checkbox.selected.container.color,
md.comp.checkbox.selected.disabled.container.color,
md.comp.checkbox.selected.disabled.container.opacity,
md.comp.checkbox.selected.disabled.icon.color,
md.comp.checkbox.selected.error.container.color,
md.comp.checkbox.selected.error.icon.color,
md.comp.checkbox.selected.focus.state-layer.color,
md.comp.checkbox.selected.focus.state-layer.opacity,
md.comp.checkbox.selected.hover.state-layer.color,
md.comp.checkbox.selected.hover.state-layer.opacity,
md.comp.checkbox.selected.icon.color,
md.comp.checkbox.selected.outline.width,
md.comp.checkbox.selected.pressed.state-layer.color,
md.comp.checkbox.selected.pressed.state-layer.opacity,
md.comp.checkbox.state-layer.size,
md.comp.checkbox.unselected.disabled.container.opacity,
md.comp.checkbox.unselected.disabled.outline.color,
md.comp.checkbox.unselected.disabled.outline.width,
md.comp.checkbox.unselected.error.outline.color,
md.comp.checkbox.unselected.focus.outline.color,
md.comp.checkbox.unselected.focus.outline.width,
md.comp.checkbox.unselected.focus.state-layer.color,
md.comp.checkbox.unselected.focus.state-layer.opacity,
md.comp.checkbox.unselected.hover.outline.color,
md.comp.checkbox.unselected.hover.outline.width,
md.comp.checkbox.unselected.hover.state-layer.color,
md.comp.checkbox.unselected.hover.state-layer.opacity,
md.comp.checkbox.unselected.outline.color,
md.comp.checkbox.unselected.outline.width,
md.comp.checkbox.unselected.pressed.outline.color,
md.comp.checkbox.unselected.pressed.outline.width,
md.comp.checkbox.unselected.pressed.state-layer.color,
md.comp.checkbox.unselected.pressed.state-layer.opacity,
md.comp.date-picker.modal.container.color,
md.comp.date-picker.modal.container.elevation,
md.comp.date-picker.modal.container.shape,
md.comp.date-picker.modal.date.focus.state-layer.opacity,
md.comp.date-picker.modal.date.hover.state-layer.opacity,
md.comp.date-picker.modal.date.label-text.text-style,
md.comp.date-picker.modal.date.pressed.state-layer.opacity,
md.comp.date-picker.modal.date.selected.container.color,
md.comp.date-picker.modal.date.selected.focus.state-layer.color,
md.comp.date-picker.modal.date.selected.hover.state-layer.color,
md.comp.date-picker.modal.date.selected.label-text.color,
md.comp.date-picker.modal.date.selected.pressed.state-layer.color,
md.comp.date-picker.modal.date.today.container.outline.color,
md.comp.date-picker.modal.date.today.container.outline.width,
md.comp.date-picker.modal.date.today.label-text.color,
md.comp.date-picker.modal.date.unselected.focus.state-layer.color,
md.comp.date-picker.modal.date.unselected.hover.state-layer.color,
md.comp.date-picker.modal.date.unselected.label-text.color,
md.comp.date-picker.modal.date.unselected.pressed.state-layer.color,
md.comp.date-picker.modal.header.headline.color,
md.comp.date-picker.modal.header.headline.text-style,
md.comp.date-picker.modal.header.supporting-text.text-style,
md.comp.date-picker.modal.range-selection.active-indicator.container.color,
md.comp.date-picker.modal.range-selection.container.elevation,
md.comp.date-picker.modal.range-selection.container.shape,
md.comp.date-picker.modal.range-selection.date.in-range.focus.state-layer.color,
md.comp.date-picker.modal.range-selection.date.in-range.focus.state-layer.opacity,
md.comp.date-picker.modal.range-selection.date.in-range.hover.state-layer.color,
md.comp.date-picker.modal.range-selection.date.in-range.hover.state-layer.opacity,
md.comp.date-picker.modal.range-selection.date.in-range.pressed.state-layer.color,
md.comp.date-picker.modal.range-selection.date.in-range.pressed.state-layer.opacity,
md.comp.date-picker.modal.range-selection.header.headline.text-style,
md.comp.date-picker.modal.range-selection.month.subhead.text-style,
md.comp.date-picker.modal.weekdays.label-text.color,
md.comp.date-picker.modal.weekdays.label-text.text-style,
md.comp.date-picker.modal.year-selection.year.focus.state-layer.opacity,
md.comp.date-picker.modal.year-selection.year.hover.state-layer.opacity,
md.comp.date-picker.modal.year-selection.year.label-text.text-style,
md.comp.date-picker.modal.year-selection.year.pressed.state-layer.opacity,
md.comp.date-picker.modal.year-selection.year.selected.container.color,
md.comp.date-picker.modal.year-selection.year.selected.focus.state-layer.color,
md.comp.date-picker.modal.year-selection.year.selected.hover.state-layer.color,
md.comp.date-picker.modal.year-selection.year.selected.label-text.color,
md.comp.date-picker.modal.year-selection.year.selected.pressed.state-layer.color,
md.comp.date-picker.modal.year-selection.year.unselected.focus.state-layer.color,
md.comp.date-picker.modal.year-selection.year.unselected.hover.state-layer.color,
md.comp.date-picker.modal.year-selection.year.unselected.label-text.color,
md.comp.date-picker.modal.year-selection.year.unselected.pressed.state-layer.color,
md.comp.dialog.container.color,
md.comp.dialog.container.elevation,
md.comp.dialog.container.shape,
md.comp.dialog.headline.text-style,
md.comp.dialog.supporting-text.text-style,
md.comp.divider.color,
md.comp.divider.thickness,
md.comp.elevated-button.container.color,
md.comp.elevated-button.container.elevation,
md.comp.elevated-button.container.height,
md.comp.elevated-button.container.shadow-color,
md.comp.elevated-button.container.shape,
md.comp.elevated-button.disabled.container.color,
md.comp.elevated-button.disabled.container.elevation,
md.comp.elevated-button.disabled.container.opacity,
md.comp.elevated-button.disabled.label-text.color,
md.comp.elevated-button.disabled.label-text.opacity,
md.comp.elevated-button.focus.container.elevation,
md.comp.elevated-button.focus.state-layer.color,
md.comp.elevated-button.focus.state-layer.opacity,
md.comp.elevated-button.hover.container.elevation,
md.comp.elevated-button.hover.state-layer.color,
md.comp.elevated-button.hover.state-layer.opacity,
md.comp.elevated-button.label-text.color,
md.comp.elevated-button.label-text.text-style,
md.comp.elevated-button.pressed.container.elevation,
md.comp.elevated-button.pressed.state-layer.color,
md.comp.elevated-button.pressed.state-layer.opacity,
md.comp.elevated-button.with-icon.disabled.icon.color,
md.comp.elevated-button.with-icon.disabled.icon.opacity,
md.comp.elevated-button.with-icon.focus.icon.color,
md.comp.elevated-button.with-icon.hover.icon.color,
md.comp.elevated-button.with-icon.icon.color,
md.comp.elevated-button.with-icon.icon.size,
md.comp.elevated-button.with-icon.pressed.icon.color,
md.comp.elevated-card.container.color,
md.comp.elevated-card.container.elevation,
md.comp.elevated-card.container.shadow-color,
md.comp.elevated-card.container.shape,
md.comp.extended-fab.primary.container.height,
md.comp.extended-fab.primary.container.shape,
md.comp.extended-fab.primary.icon.size,
md.comp.extended-fab.primary.label-text.text-style,
md.comp.fab.primary.container.color,
md.comp.fab.primary.container.elevation,
md.comp.fab.primary.container.height,
md.comp.fab.primary.container.shape,
md.comp.fab.primary.container.width,
md.comp.fab.primary.focus.container.elevation,
md.comp.fab.primary.focus.state-layer.color,
md.comp.fab.primary.focus.state-layer.opacity,
md.comp.fab.primary.hover.container.elevation,
md.comp.fab.primary.hover.state-layer.color,
md.comp.fab.primary.hover.state-layer.opacity,
md.comp.fab.primary.icon.color,
md.comp.fab.primary.icon.size,
md.comp.fab.primary.large.container.height,
md.comp.fab.primary.large.container.shape,
md.comp.fab.primary.large.container.width,
md.comp.fab.primary.large.icon.size,
md.comp.fab.primary.pressed.container.elevation,
md.comp.fab.primary.pressed.state-layer.color,
md.comp.fab.primary.pressed.state-layer.opacity,
md.comp.fab.primary.small.container.height,
md.comp.fab.primary.small.container.shape,
md.comp.fab.primary.small.container.width,
md.comp.fab.primary.small.icon.size,
md.comp.filled-button.container.color,
md.comp.filled-button.container.elevation,
md.comp.filled-button.container.height,
md.comp.filled-button.container.shadow-color,
md.comp.filled-button.container.shape,
md.comp.filled-button.disabled.container.color,
md.comp.filled-button.disabled.container.elevation,
md.comp.filled-button.disabled.container.opacity,
md.comp.filled-button.disabled.label-text.color,
md.comp.filled-button.disabled.label-text.opacity,
md.comp.filled-button.focus.container.elevation,
md.comp.filled-button.focus.state-layer.color,
md.comp.filled-button.focus.state-layer.opacity,
md.comp.filled-button.hover.container.elevation,
md.comp.filled-button.hover.state-layer.color,
md.comp.filled-button.hover.state-layer.opacity,
md.comp.filled-button.label-text.color,
md.comp.filled-button.label-text.text-style,
md.comp.filled-button.pressed.container.elevation,
md.comp.filled-button.pressed.state-layer.color,
md.comp.filled-button.pressed.state-layer.opacity,
md.comp.filled-button.with-icon.disabled.icon.color,
md.comp.filled-button.with-icon.disabled.icon.opacity,
md.comp.filled-button.with-icon.focus.icon.color,
md.comp.filled-button.with-icon.hover.icon.color,
md.comp.filled-button.with-icon.icon.color,
md.comp.filled-button.with-icon.icon.size,
md.comp.filled-button.with-icon.pressed.icon.color,
md.comp.filled-card.container.color,
md.comp.filled-card.container.elevation,
md.comp.filled-card.container.shadow-color,
md.comp.filled-card.container.shape,
md.comp.filled-icon-button.container.color,
md.comp.filled-icon-button.container.height,
md.comp.filled-icon-button.container.shape,
md.comp.filled-icon-button.container.width,
md.comp.filled-icon-button.disabled.container.color,
md.comp.filled-icon-button.disabled.container.opacity,
md.comp.filled-icon-button.disabled.icon.color,
md.comp.filled-icon-button.disabled.icon.opacity,
md.comp.filled-icon-button.focus.state-layer.color,
md.comp.filled-icon-button.focus.state-layer.opacity,
md.comp.filled-icon-button.hover.state-layer.color,
md.comp.filled-icon-button.hover.state-layer.opacity,
md.comp.filled-icon-button.icon.color,
md.comp.filled-icon-button.icon.size,
md.comp.filled-icon-button.pressed.state-layer.color,
md.comp.filled-icon-button.pressed.state-layer.opacity,
md.comp.filled-icon-button.selected.container.color,
md.comp.filled-icon-button.toggle.selected.focus.state-layer.color,
md.comp.filled-icon-button.toggle.selected.hover.state-layer.color,
md.comp.filled-icon-button.toggle.selected.icon.color,
md.comp.filled-icon-button.toggle.selected.pressed.state-layer.color,
md.comp.filled-icon-button.toggle.unselected.focus.state-layer.color,
md.comp.filled-icon-button.toggle.unselected.hover.state-layer.color,
md.comp.filled-icon-button.toggle.unselected.icon.color,
md.comp.filled-icon-button.toggle.unselected.pressed.state-layer.color,
md.comp.filled-icon-button.unselected.container.color,
md.comp.filled-text-field.active-indicator.color,
md.comp.filled-text-field.active-indicator.height,
md.comp.filled-text-field.container.color,
md.comp.filled-text-field.disabled.active-indicator.color,
md.comp.filled-text-field.disabled.active-indicator.height,
md.comp.filled-text-field.disabled.active-indicator.opacity,
md.comp.filled-text-field.disabled.container.color,
md.comp.filled-text-field.disabled.container.opacity,
md.comp.filled-text-field.disabled.label-text.color,
md.comp.filled-text-field.disabled.label-text.opacity,
md.comp.filled-text-field.disabled.leading-icon.color,
md.comp.filled-text-field.disabled.leading-icon.opacity,
md.comp.filled-text-field.disabled.supporting-text.color,
md.comp.filled-text-field.disabled.supporting-text.opacity,
md.comp.filled-text-field.disabled.trailing-icon.color,
md.comp.filled-text-field.disabled.trailing-icon.opacity,
md.comp.filled-text-field.error.active-indicator.color,
md.comp.filled-text-field.error.focus.active-indicator.color,
md.comp.filled-text-field.error.focus.label-text.color,
md.comp.filled-text-field.error.focus.supporting-text.color,
md.comp.filled-text-field.error.focus.trailing-icon.color,
md.comp.filled-text-field.error.hover.active-indicator.color,
md.comp.filled-text-field.error.hover.label-text.color,
md.comp.filled-text-field.error.hover.supporting-text.color,
md.comp.filled-text-field.error.hover.trailing-icon.color,
md.comp.filled-text-field.error.label-text.color,
md.comp.filled-text-field.error.leading-icon.color,
md.comp.filled-text-field.error.supporting-text.color,
md.comp.filled-text-field.error.trailing-icon.color,
md.comp.filled-text-field.focus.active-indicator.color,
md.comp.filled-text-field.focus.active-indicator.height,
md.comp.filled-text-field.focus.label-text.color,
md.comp.filled-text-field.focus.leading-icon.color,
md.comp.filled-text-field.focus.supporting-text.color,
md.comp.filled-text-field.focus.trailing-icon.color,
md.comp.filled-text-field.hover.active-indicator.color,
md.comp.filled-text-field.hover.active-indicator.height,
md.comp.filled-text-field.hover.label-text.color,
md.comp.filled-text-field.hover.leading-icon.color,
md.comp.filled-text-field.hover.supporting-text.color,
md.comp.filled-text-field.hover.trailing-icon.color,
md.comp.filled-text-field.label-text.color,
md.comp.filled-text-field.label-text.text-style,
md.comp.filled-text-field.leading-icon.color,
md.comp.filled-text-field.supporting-text.color,
md.comp.filled-text-field.supporting-text.text-style,
md.comp.filled-text-field.trailing-icon.color,
md.comp.filled-tonal-button.container.color,
md.comp.filled-tonal-button.container.elevation,
md.comp.filled-tonal-button.container.height,
md.comp.filled-tonal-button.container.shadow-color,
md.comp.filled-tonal-button.container.shape,
md.comp.filled-tonal-button.disabled.container.color,
md.comp.filled-tonal-button.disabled.container.elevation,
md.comp.filled-tonal-button.disabled.container.opacity,
md.comp.filled-tonal-button.disabled.label-text.color,
md.comp.filled-tonal-button.disabled.label-text.opacity,
md.comp.filled-tonal-button.focus.container.elevation,
md.comp.filled-tonal-button.focus.state-layer.color,
md.comp.filled-tonal-button.focus.state-layer.opacity,
md.comp.filled-tonal-button.hover.container.elevation,
md.comp.filled-tonal-button.hover.state-layer.color,
md.comp.filled-tonal-button.hover.state-layer.opacity,
md.comp.filled-tonal-button.label-text.color,
md.comp.filled-tonal-button.label-text.text-style,
md.comp.filled-tonal-button.pressed.container.elevation,
md.comp.filled-tonal-button.pressed.state-layer.color,
md.comp.filled-tonal-button.pressed.state-layer.opacity,
md.comp.filled-tonal-button.with-icon.disabled.icon.color,
md.comp.filled-tonal-button.with-icon.disabled.icon.opacity,
md.comp.filled-tonal-button.with-icon.focus.icon.color,
md.comp.filled-tonal-button.with-icon.hover.icon.color,
md.comp.filled-tonal-button.with-icon.icon.color,
md.comp.filled-tonal-button.with-icon.icon.size,
md.comp.filled-tonal-button.with-icon.pressed.icon.color,
md.comp.filled-tonal-icon-button.container.color,
md.comp.filled-tonal-icon-button.container.height,
md.comp.filled-tonal-icon-button.container.shape,
md.comp.filled-tonal-icon-button.container.width,
md.comp.filled-tonal-icon-button.disabled.container.color,
md.comp.filled-tonal-icon-button.disabled.container.opacity,
md.comp.filled-tonal-icon-button.disabled.icon.color,
md.comp.filled-tonal-icon-button.disabled.icon.opacity,
md.comp.filled-tonal-icon-button.focus.state-layer.color,
md.comp.filled-tonal-icon-button.focus.state-layer.opacity,
md.comp.filled-tonal-icon-button.hover.state-layer.color,
md.comp.filled-tonal-icon-button.hover.state-layer.opacity,
md.comp.filled-tonal-icon-button.icon.color,
md.comp.filled-tonal-icon-button.icon.size,
md.comp.filled-tonal-icon-button.pressed.state-layer.color,
md.comp.filled-tonal-icon-button.pressed.state-layer.opacity,
md.comp.filled-tonal-icon-button.selected.container.color,
md.comp.filled-tonal-icon-button.toggle.selected.focus.state-layer.color,
md.comp.filled-tonal-icon-button.toggle.selected.hover.state-layer.color,
md.comp.filled-tonal-icon-button.toggle.selected.icon.color,
md.comp.filled-tonal-icon-button.toggle.selected.pressed.state-layer.color,
md.comp.filled-tonal-icon-button.toggle.unselected.focus.state-layer.color,
md.comp.filled-tonal-icon-button.toggle.unselected.hover.state-layer.color,
md.comp.filled-tonal-icon-button.toggle.unselected.icon.color,
md.comp.filled-tonal-icon-button.toggle.unselected.pressed.state-layer.color,
md.comp.filled-tonal-icon-button.unselected.container.color,
md.comp.filter-chip.container.shape,
md.comp.filter-chip.disabled.label-text.color,
md.comp.filter-chip.elevated.container.elevation,
md.comp.filter-chip.elevated.container.shadow-color,
md.comp.filter-chip.elevated.disabled.container.color,
md.comp.filter-chip.elevated.disabled.container.elevation,
md.comp.filter-chip.elevated.disabled.container.opacity,
md.comp.filter-chip.elevated.pressed.container.elevation,
md.comp.filter-chip.elevated.selected.container.color,
md.comp.filter-chip.elevated.unselected.container.color,
md.comp.filter-chip.flat.container.elevation,
md.comp.filter-chip.flat.disabled.selected.container.color,
md.comp.filter-chip.flat.disabled.selected.container.opacity,
md.comp.filter-chip.flat.disabled.unselected.outline.color,
md.comp.filter-chip.flat.disabled.unselected.outline.opacity,
md.comp.filter-chip.flat.selected.container.color,
md.comp.filter-chip.flat.unselected.outline.color,
md.comp.filter-chip.flat.unselected.outline.width,
md.comp.filter-chip.label-text.text-style,
md.comp.filter-chip.selected.label-text.color,
md.comp.filter-chip.unselected.label-text.color,
md.comp.filter-chip.with-icon.icon.size,
md.comp.filter-chip.with-leading-icon.disabled.leading-icon.color,
md.comp.filter-chip.with-leading-icon.selected.leading-icon.color,
md.comp.filter-chip.with-leading-icon.unselected.leading-icon.color,
md.comp.filter-chip.with-trailing-icon.disabled.trailing-icon.color,
md.comp.filter-chip.with-trailing-icon.selected.trailing-icon.color,
md.comp.filter-chip.with-trailing-icon.unselected.trailing-icon.color,
md.comp.full-screen-dialog.container.color,
md.comp.icon-button.disabled.icon.color,
md.comp.icon-button.disabled.icon.opacity,
md.comp.icon-button.icon.size,
md.comp.icon-button.selected.focus.state-layer.color,
md.comp.icon-button.selected.focus.state-layer.opacity,
md.comp.icon-button.selected.hover.state-layer.color,
md.comp.icon-button.selected.hover.state-layer.opacity,
md.comp.icon-button.selected.icon.color,
md.comp.icon-button.selected.pressed.state-layer.color,
md.comp.icon-button.selected.pressed.state-layer.opacity,
md.comp.icon-button.state-layer.shape,
md.comp.icon-button.unselected.focus.state-layer.color,
md.comp.icon-button.unselected.focus.state-layer.opacity,
md.comp.icon-button.unselected.hover.state-layer.color,
md.comp.icon-button.unselected.hover.state-layer.opacity,
md.comp.icon-button.unselected.icon.color,
md.comp.icon-button.unselected.pressed.state-layer.color,
md.comp.icon-button.unselected.pressed.state-layer.opacity,
md.comp.input-chip.container.elevation,
md.comp.input-chip.container.shape,
md.comp.input-chip.disabled.label-text.color,
md.comp.input-chip.disabled.selected.container.color,
md.comp.input-chip.disabled.selected.container.opacity,
md.comp.input-chip.disabled.unselected.outline.color,
md.comp.input-chip.disabled.unselected.outline.opacity,
md.comp.input-chip.label-text.text-style,
md.comp.input-chip.selected.container.color,
md.comp.input-chip.selected.label-text.color,
md.comp.input-chip.unselected.label-text.color,
md.comp.input-chip.unselected.outline.color,
md.comp.input-chip.unselected.outline.width,
md.comp.input-chip.with-leading-icon.disabled.leading-icon.color,
md.comp.input-chip.with-leading-icon.leading-icon.size,
md.comp.input-chip.with-leading-icon.selected.leading-icon.color,
md.comp.input-chip.with-leading-icon.unselected.leading-icon.color,
md.comp.input-chip.with-trailing-icon.disabled.trailing-icon.color,
md.comp.input-chip.with-trailing-icon.selected.trailing-icon.color,
md.comp.input-chip.with-trailing-icon.unselected.trailing-icon.color,
md.comp.list.list-item.container.shape,
md.comp.list.list-item.disabled.label-text.color,
md.comp.list.list-item.disabled.label-text.opacity,
md.comp.list.list-item.disabled.leading-icon.color,
md.comp.list.list-item.disabled.leading-icon.opacity,
md.comp.list.list-item.focus.label-text.color,
md.comp.list.list-item.focus.leading-icon.icon.color,
md.comp.list.list-item.focus.state-layer.color,
md.comp.list.list-item.focus.state-layer.opacity,
md.comp.list.list-item.hover.label-text.color,
md.comp.list.list-item.hover.leading-icon.icon.color,
md.comp.list.list-item.hover.state-layer.color,
md.comp.list.list-item.hover.state-layer.opacity,
md.comp.list.list-item.label-text.color,
md.comp.list.list-item.label-text.text-style,
md.comp.list.list-item.leading-icon.color,
md.comp.list.list-item.leading-icon.size,
md.comp.list.list-item.pressed.label-text.color,
md.comp.list.list-item.pressed.leading-icon.icon.color,
md.comp.list.list-item.pressed.state-layer.color,
md.comp.list.list-item.pressed.state-layer.opacity,
md.comp.list.list-item.selected.trailing-icon.color,
md.comp.list.list-item.supporting-text.color,
md.comp.list.list-item.supporting-text.text-style,
md.comp.list.list-item.trailing-icon.color,
md.comp.list.list-item.trailing-supporting-text.color,
md.comp.list.list-item.trailing-supporting-text.text-style,
md.comp.menu.container.color,
md.comp.menu.container.elevation,
md.comp.menu.container.shadow-color,
md.comp.menu.container.shape,
md.comp.navigation-bar.active-indicator.color,
md.comp.navigation-bar.active-indicator.shape,
md.comp.navigation-bar.active.icon.color,
md.comp.navigation-bar.active.label-text.color,
md.comp.navigation-bar.container.color,
md.comp.navigation-bar.container.elevation,
md.comp.navigation-bar.container.height,
md.comp.navigation-bar.icon.size,
md.comp.navigation-bar.inactive.icon.color,
md.comp.navigation-bar.inactive.label-text.color,
md.comp.navigation-bar.label-text.text-style,
md.comp.navigation-drawer.active-indicator.color,
md.comp.navigation-drawer.active-indicator.height,
md.comp.navigation-drawer.active-indicator.shape,
md.comp.navigation-drawer.active-indicator.width,
md.comp.navigation-drawer.active.icon.color,
md.comp.navigation-drawer.active.label-text.color,
md.comp.navigation-drawer.icon.size,
md.comp.navigation-drawer.inactive.icon.color,
md.comp.navigation-drawer.inactive.label-text.color,
md.comp.navigation-drawer.label-text.text-style,
md.comp.navigation-drawer.modal.container.color,
md.comp.navigation-drawer.modal.container.elevation,
md.comp.navigation-rail.active-indicator.color,
md.comp.navigation-rail.active-indicator.shape,
md.comp.navigation-rail.active.focus.label-text.color,
md.comp.navigation-rail.active.icon.color,
md.comp.navigation-rail.container.color,
md.comp.navigation-rail.container.elevation,
md.comp.navigation-rail.container.width,
md.comp.navigation-rail.icon.size,
md.comp.navigation-rail.inactive.focus.label-text.color,
md.comp.navigation-rail.inactive.icon.color,
md.comp.navigation-rail.label-text.text-style,
md.comp.outlined-button.container.height,
md.comp.outlined-button.container.shape,
md.comp.outlined-button.disabled.label-text.color,
md.comp.outlined-button.disabled.label-text.opacity,
md.comp.outlined-button.disabled.outline.color,
md.comp.outlined-button.disabled.outline.opacity,
md.comp.outlined-button.focus.outline.color,
md.comp.outlined-button.focus.state-layer.color,
md.comp.outlined-button.focus.state-layer.opacity,
md.comp.outlined-button.hover.state-layer.color,
md.comp.outlined-button.hover.state-layer.opacity,
md.comp.outlined-button.label-text.color,
md.comp.outlined-button.label-text.text-style,
md.comp.outlined-button.outline.color,
md.comp.outlined-button.outline.width,
md.comp.outlined-button.pressed.state-layer.color,
md.comp.outlined-button.pressed.state-layer.opacity,
md.comp.outlined-button.with-icon.disabled.icon.color,
md.comp.outlined-button.with-icon.disabled.icon.opacity,
md.comp.outlined-button.with-icon.focus.icon.color,
md.comp.outlined-button.with-icon.hover.icon.color,
md.comp.outlined-button.with-icon.icon.color,
md.comp.outlined-button.with-icon.icon.size,
md.comp.outlined-button.with-icon.pressed.icon.color,
md.comp.outlined-card.container.color,
md.comp.outlined-card.container.elevation,
md.comp.outlined-card.container.shadow-color,
md.comp.outlined-card.container.shape,
md.comp.outlined-card.outline.color,
md.comp.outlined-card.outline.width,
md.comp.outlined-icon-button.container.height,
md.comp.outlined-icon-button.container.shape,
md.comp.outlined-icon-button.container.width,
md.comp.outlined-icon-button.disabled.icon.color,
md.comp.outlined-icon-button.disabled.icon.opacity,
md.comp.outlined-icon-button.disabled.selected.container.color,
md.comp.outlined-icon-button.disabled.selected.container.opacity,
md.comp.outlined-icon-button.disabled.unselected.outline.color,
md.comp.outlined-icon-button.disabled.unselected.outline.opacity,
md.comp.outlined-icon-button.focus.state-layer.opacity,
md.comp.outlined-icon-button.hover.state-layer.opacity,
md.comp.outlined-icon-button.icon.size,
md.comp.outlined-icon-button.pressed.state-layer.opacity,
md.comp.outlined-icon-button.selected.container.color,
md.comp.outlined-icon-button.selected.focus.state-layer.color,
md.comp.outlined-icon-button.selected.hover.state-layer.color,
md.comp.outlined-icon-button.selected.icon.color,
md.comp.outlined-icon-button.selected.pressed.state-layer.color,
md.comp.outlined-icon-button.unselected.focus.state-layer.color,
md.comp.outlined-icon-button.unselected.hover.state-layer.color,
md.comp.outlined-icon-button.unselected.icon.color,
md.comp.outlined-icon-button.unselected.outline.color,
md.comp.outlined-icon-button.unselected.pressed.state-layer.color,
md.comp.outlined-segmented-button.container.height,
md.comp.outlined-segmented-button.disabled.label-text.color,
md.comp.outlined-segmented-button.disabled.label-text.opacity,
md.comp.outlined-segmented-button.disabled.outline.color,
md.comp.outlined-segmented-button.disabled.outline.opacity,
md.comp.outlined-segmented-button.focus.state-layer.opacity,
md.comp.outlined-segmented-button.hover.state-layer.opacity,
md.comp.outlined-segmented-button.label-text.text-style,
md.comp.outlined-segmented-button.outline.color,
md.comp.outlined-segmented-button.outline.width,
md.comp.outlined-segmented-button.pressed.state-layer.opacity,
md.comp.outlined-segmented-button.selected.container.color,
md.comp.outlined-segmented-button.selected.focus.label-text.color,
md.comp.outlined-segmented-button.selected.focus.state-layer.color,
md.comp.outlined-segmented-button.selected.hover.label-text.color,
md.comp.outlined-segmented-button.selected.hover.state-layer.color,
md.comp.outlined-segmented-button.selected.label-text.color,
md.comp.outlined-segmented-button.selected.pressed.label-text.color,
md.comp.outlined-segmented-button.selected.pressed.state-layer.color,
md.comp.outlined-segmented-button.shape,
md.comp.outlined-segmented-button.unselected.focus.label-text.color,
md.comp.outlined-segmented-button.unselected.focus.state-layer.color,
md.comp.outlined-segmented-button.unselected.hover.label-text.color,
md.comp.outlined-segmented-button.unselected.hover.state-layer.color,
md.comp.outlined-segmented-button.unselected.label-text.color,
md.comp.outlined-segmented-button.unselected.pressed.label-text.color,
md.comp.outlined-segmented-button.unselected.pressed.state-layer.color,
md.comp.outlined-segmented-button.with-icon.icon.size,
md.comp.outlined-text-field.disabled.outline.color,
md.comp.outlined-text-field.disabled.outline.opacity,
md.comp.outlined-text-field.disabled.outline.width,
md.comp.outlined-text-field.error.focus.outline.color,
md.comp.outlined-text-field.error.hover.outline.color,
md.comp.outlined-text-field.error.outline.color,
md.comp.outlined-text-field.focus.outline.color,
md.comp.outlined-text-field.focus.outline.width,
md.comp.outlined-text-field.hover.outline.color,
md.comp.outlined-text-field.hover.outline.width,
md.comp.outlined-text-field.outline.color,
md.comp.outlined-text-field.outline.width,
md.comp.primary-navigation-tab.active-indicator.color,
md.comp.primary-navigation-tab.active-indicator.height,
md.comp.primary-navigation-tab.active.focus.state-layer.color,
md.comp.primary-navigation-tab.active.focus.state-layer.opacity,
md.comp.primary-navigation-tab.active.hover.state-layer.color,
md.comp.primary-navigation-tab.active.hover.state-layer.opacity,
md.comp.primary-navigation-tab.active.pressed.state-layer.color,
md.comp.primary-navigation-tab.active.pressed.state-layer.opacity,
md.comp.primary-navigation-tab.inactive.focus.state-layer.color,
md.comp.primary-navigation-tab.inactive.focus.state-layer.opacity,
md.comp.primary-navigation-tab.inactive.hover.state-layer.color,
md.comp.primary-navigation-tab.inactive.hover.state-layer.opacity,
md.comp.primary-navigation-tab.inactive.pressed.state-layer.color,
md.comp.primary-navigation-tab.inactive.pressed.state-layer.opacity,
md.comp.primary-navigation-tab.with-label-text.active.label-text.color,
md.comp.primary-navigation-tab.with-label-text.inactive.label-text.color,
md.comp.primary-navigation-tab.with-label-text.label-text.text-style,
md.comp.progress-indicator.active-indicator-track-space,
md.comp.progress-indicator.active-indicator.color,
md.comp.progress-indicator.stop-indicator.color,
md.comp.progress-indicator.stop-indicator.size,
md.comp.progress-indicator.track.color,
md.comp.progress-indicator.track.thickness,
md.comp.radio-button.disabled.selected.icon.color,
md.comp.radio-button.disabled.selected.icon.opacity,
md.comp.radio-button.disabled.unselected.icon.color,
md.comp.radio-button.disabled.unselected.icon.opacity,
md.comp.radio-button.selected.focus.icon.color,
md.comp.radio-button.selected.focus.state-layer.color,
md.comp.radio-button.selected.focus.state-layer.opacity,
md.comp.radio-button.selected.hover.icon.color,
md.comp.radio-button.selected.hover.state-layer.color,
md.comp.radio-button.selected.hover.state-layer.opacity,
md.comp.radio-button.selected.icon.color,
md.comp.radio-button.selected.pressed.icon.color,
md.comp.radio-button.selected.pressed.state-layer.color,
md.comp.radio-button.selected.pressed.state-layer.opacity,
md.comp.radio-button.unselected.focus.icon.color,
md.comp.radio-button.unselected.focus.state-layer.color,
md.comp.radio-button.unselected.focus.state-layer.opacity,
md.comp.radio-button.unselected.hover.icon.color,
md.comp.radio-button.unselected.hover.state-layer.color,
md.comp.radio-button.unselected.hover.state-layer.opacity,
md.comp.radio-button.unselected.icon.color,
md.comp.radio-button.unselected.pressed.icon.color,
md.comp.radio-button.unselected.pressed.state-layer.color,
md.comp.radio-button.unselected.pressed.state-layer.opacity,
md.comp.search-bar.container.color,
md.comp.search-bar.container.elevation,
md.comp.search-bar.container.height,
md.comp.search-bar.container.shape,
md.comp.search-bar.hover.state-layer.color,
md.comp.search-bar.hover.state-layer.opacity,
md.comp.search-bar.input-text.color,
md.comp.search-bar.input-text.text-style,
md.comp.search-bar.pressed.state-layer.color,
md.comp.search-bar.pressed.state-layer.opacity,
md.comp.search-bar.supporting-text.color,
md.comp.search-bar.supporting-text.text-style,
md.comp.search-view.container.color,
md.comp.search-view.container.elevation,
md.comp.search-view.divider.color,
md.comp.search-view.docked.container.shape,
md.comp.search-view.full-screen.container.shape,
md.comp.search-view.full-screen.header.container.height,
md.comp.search-view.header.input-text.color,
md.comp.search-view.header.input-text.text-style,
md.comp.search-view.header.supporting-text.color,
md.comp.search-view.header.supporting-text.text-style,
md.comp.secondary-navigation-tab.active-indicator.color,
md.comp.secondary-navigation-tab.active-indicator.height,
md.comp.secondary-navigation-tab.active.label-text.color,
md.comp.secondary-navigation-tab.focus.state-layer.color,
md.comp.secondary-navigation-tab.focus.state-layer.opacity,
md.comp.secondary-navigation-tab.hover.state-layer.color,
md.comp.secondary-navigation-tab.hover.state-layer.opacity,
md.comp.secondary-navigation-tab.inactive.label-text.color,
md.comp.secondary-navigation-tab.label-text.text-style,
md.comp.secondary-navigation-tab.pressed.state-layer.color,
md.comp.secondary-navigation-tab.pressed.state-layer.opacity,
md.comp.sheet.bottom.docked.container.color,
md.comp.sheet.bottom.docked.container.shape,
md.comp.sheet.bottom.docked.drag-handle.color,
md.comp.sheet.bottom.docked.drag-handle.height,
md.comp.sheet.bottom.docked.drag-handle.width,
md.comp.sheet.bottom.docked.modal.container.elevation,
md.comp.sheet.bottom.docked.standard.container.elevation,
md.comp.slider.active.handle.padding,
md.comp.slider.active.stop-indicator.container.color,
md.comp.slider.active.stop-indicator.container.opacity,
md.comp.slider.active.track.color,
md.comp.slider.active.track.height,
md.comp.slider.disabled.active.stop-indicator.container.color,
md.comp.slider.disabled.active.track.color,
md.comp.slider.disabled.active.track.opacity,
md.comp.slider.disabled.handle.color,
md.comp.slider.disabled.handle.opacity,
md.comp.slider.disabled.handle.width,
md.comp.slider.disabled.inactive.stop-indicator.container.color,
md.comp.slider.disabled.inactive.track.color,
md.comp.slider.disabled.inactive.track.opacity,
md.comp.slider.focus.handle.width,
md.comp.slider.handle.color,
md.comp.slider.handle.height,
md.comp.slider.handle.width,
md.comp.slider.hover.handle.width,
md.comp.slider.inactive.stop-indicator.container.color,
md.comp.slider.inactive.stop-indicator.container.opacity,
md.comp.slider.inactive.track.color,
md.comp.slider.pressed.handle.width,
md.comp.slider.stop-indicator.size,
md.comp.slider.value-indicator.container.color,
md.comp.slider.value-indicator.label.label-text.color,
md.comp.slider.value-indicator.label.label-text.text-style,
md.comp.snackbar.action.focus.label-text.color,
md.comp.snackbar.action.hover.label-text.color,
md.comp.snackbar.action.label-text.color,
md.comp.snackbar.action.pressed.label-text.color,
md.comp.snackbar.container.color,
md.comp.snackbar.container.elevation,
md.comp.snackbar.container.shape,
md.comp.snackbar.icon.color,
md.comp.snackbar.supporting-text.color,
md.comp.snackbar.supporting-text.text-style,
md.comp.switch.disabled.selected.handle.color,
md.comp.switch.disabled.selected.handle.opacity,
md.comp.switch.disabled.selected.icon.color,
md.comp.switch.disabled.selected.icon.opacity,
md.comp.switch.disabled.selected.track.color,
md.comp.switch.disabled.track.opacity,
md.comp.switch.disabled.unselected.handle.color,
md.comp.switch.disabled.unselected.handle.opacity,
md.comp.switch.disabled.unselected.icon.color,
md.comp.switch.disabled.unselected.icon.opacity,
md.comp.switch.disabled.unselected.track.color,
md.comp.switch.disabled.unselected.track.outline.color,
md.comp.switch.pressed.handle.width,
md.comp.switch.selected.focus.handle.color,
md.comp.switch.selected.focus.icon.color,
md.comp.switch.selected.focus.state-layer.color,
md.comp.switch.selected.focus.state-layer.opacity,
md.comp.switch.selected.focus.track.color,
md.comp.switch.selected.handle.color,
md.comp.switch.selected.handle.width,
md.comp.switch.selected.hover.handle.color,
md.comp.switch.selected.hover.icon.color,
md.comp.switch.selected.hover.state-layer.color,
md.comp.switch.selected.hover.state-layer.opacity,
md.comp.switch.selected.hover.track.color,
md.comp.switch.selected.icon.color,
md.comp.switch.selected.pressed.handle.color,
md.comp.switch.selected.pressed.icon.color,
md.comp.switch.selected.pressed.state-layer.color,
md.comp.switch.selected.pressed.state-layer.opacity,
md.comp.switch.selected.pressed.track.color,
md.comp.switch.selected.track.color,
md.comp.switch.state-layer.size,
md.comp.switch.track.height,
md.comp.switch.track.outline.width,
md.comp.switch.track.width,
md.comp.switch.unselected.focus.handle.color,
md.comp.switch.unselected.focus.icon.color,
md.comp.switch.unselected.focus.state-layer.color,
md.comp.switch.unselected.focus.state-layer.opacity,
md.comp.switch.unselected.focus.track.color,
md.comp.switch.unselected.handle.color,
md.comp.switch.unselected.handle.width,
md.comp.switch.unselected.hover.handle.color,
md.comp.switch.unselected.hover.icon.color,
md.comp.switch.unselected.hover.state-layer.color,
md.comp.switch.unselected.hover.state-layer.opacity,
md.comp.switch.unselected.hover.track.color,
md.comp.switch.unselected.icon.color,
md.comp.switch.unselected.icon.size,
md.comp.switch.unselected.pressed.handle.color,
md.comp.switch.unselected.pressed.icon.color,
md.comp.switch.unselected.pressed.state-layer.color,
md.comp.switch.unselected.pressed.state-layer.opacity,
md.comp.switch.unselected.pressed.track.color,
md.comp.switch.unselected.track.color,
md.comp.switch.unselected.track.outline.color,
md.comp.switch.with-icon.handle.width,
md.comp.text-button.container.height,
md.comp.text-button.container.shape,
md.comp.text-button.disabled.label-text.color,
md.comp.text-button.disabled.label-text.opacity,
md.comp.text-button.focus.state-layer.color,
md.comp.text-button.focus.state-layer.opacity,
md.comp.text-button.hover.state-layer.color,
md.comp.text-button.hover.state-layer.opacity,
md.comp.text-button.label-text.color,
md.comp.text-button.label-text.text-style,
md.comp.text-button.pressed.state-layer.color,
md.comp.text-button.pressed.state-layer.opacity,
md.comp.text-button.with-icon.disabled.icon.color,
md.comp.text-button.with-icon.disabled.icon.opacity,
md.comp.text-button.with-icon.focus.icon.color,
md.comp.text-button.with-icon.hover.icon.color,
md.comp.text-button.with-icon.icon.color,
md.comp.text-button.with-icon.icon.size,
md.comp.text-button.with-icon.pressed.icon.color,
md.comp.time-picker.clock-dial.color,
md.comp.time-picker.clock-dial.container.size,
md.comp.time-picker.clock-dial.label-text.text-style,
md.comp.time-picker.clock-dial.selected.label-text.color,
md.comp.time-picker.clock-dial.selector.center.container.size,
md.comp.time-picker.clock-dial.selector.handle.container.color,
md.comp.time-picker.clock-dial.selector.handle.container.size,
md.comp.time-picker.clock-dial.selector.track.container.width,
md.comp.time-picker.clock-dial.unselected.label-text.color,
md.comp.time-picker.container.color,
md.comp.time-picker.container.elevation,
md.comp.time-picker.container.shape,
md.comp.time-picker.headline.color,
md.comp.time-picker.headline.text-style,
md.comp.time-picker.period-selector.container.shape,
md.comp.time-picker.period-selector.horizontal.container.height,
md.comp.time-picker.period-selector.horizontal.container.width,
md.comp.time-picker.period-selector.label-text.text-style,
md.comp.time-picker.period-selector.outline.color,
md.comp.time-picker.period-selector.outline.width,
md.comp.time-picker.period-selector.selected.container.color,
md.comp.time-picker.period-selector.selected.focus.label-text.color,
md.comp.time-picker.period-selector.selected.hover.label-text.color,
md.comp.time-picker.period-selector.selected.label-text.color,
md.comp.time-picker.period-selector.selected.pressed.label-text.color,
md.comp.time-picker.period-selector.unselected.focus.label-text.color,
md.comp.time-picker.period-selector.unselected.hover.label-text.color,
md.comp.time-picker.period-selector.unselected.label-text.color,
md.comp.time-picker.period-selector.unselected.pressed.label-text.color,
md.comp.time-picker.period-selector.vertical.container.height,
md.comp.time-picker.period-selector.vertical.container.width,
md.comp.time-picker.time-selector.24h-vertical.container.width,
md.comp.time-picker.time-selector.container.height,
md.comp.time-picker.time-selector.container.shape,
md.comp.time-picker.time-selector.container.width,
md.comp.time-picker.time-selector.focus.state-layer.opacity,
md.comp.time-picker.time-selector.hover.state-layer.opacity,
md.comp.time-picker.time-selector.selected.container.color,
md.comp.time-picker.time-selector.selected.focus.label-text.color,
md.comp.time-picker.time-selector.selected.focus.state-layer.color,
md.comp.time-picker.time-selector.selected.hover.label-text.color,
md.comp.time-picker.time-selector.selected.hover.state-layer.color,
md.comp.time-picker.time-selector.selected.label-text.color,
md.comp.time-picker.time-selector.selected.pressed.label-text.color,
md.comp.time-picker.time-selector.selected.pressed.state-layer.color,
md.comp.time-picker.time-selector.unselected.container.color,
md.comp.time-picker.time-selector.unselected.focus.label-text.color,
md.comp.time-picker.time-selector.unselected.focus.state-layer.color,
md.comp.time-picker.time-selector.unselected.hover.label-text.color,
md.comp.time-picker.time-selector.unselected.hover.state-layer.color,
md.comp.time-picker.time-selector.unselected.label-text.color,
md.comp.time-picker.time-selector.unselected.pressed.label-text.color,
md.comp.time-picker.time-selector.unselected.pressed.state-layer.color,
md.comp.top-app-bar.large.container.height,
md.comp.top-app-bar.large.headline.color,
md.comp.top-app-bar.large.headline.text-style,
md.comp.top-app-bar.medium.container.height,
md.comp.top-app-bar.medium.headline.color,
md.comp.top-app-bar.medium.headline.text-style,
md.comp.top-app-bar.small.container.color,
md.comp.top-app-bar.small.container.elevation,
md.comp.top-app-bar.small.container.height,
md.comp.top-app-bar.small.headline.color,
md.comp.top-app-bar.small.headline.text-style,
md.comp.top-app-bar.small.leading-icon.color,
md.comp.top-app-bar.small.leading-icon.size,
md.comp.top-app-bar.small.on-scroll.container.elevation,
md.comp.top-app-bar.small.trailing-icon.color,
md.comp.top-app-bar.small.trailing-icon.size,
md.ref.palette.error100,
md.ref.palette.error20,
md.ref.palette.error30,
md.ref.palette.error40,
md.ref.palette.error80,
md.ref.palette.error90,
md.ref.palette.neutral-variant30,
md.ref.palette.neutral-variant50,
md.ref.palette.neutral-variant60,
md.ref.palette.neutral-variant80,
md.ref.palette.neutral-variant90,
md.ref.palette.neutral0,
md.ref.palette.neutral10,
md.ref.palette.neutral100,
md.ref.palette.neutral12,
md.ref.palette.neutral17,
md.ref.palette.neutral20,
md.ref.palette.neutral22,
md.ref.palette.neutral24,
md.ref.palette.neutral4,
md.ref.palette.neutral6,
md.ref.palette.neutral87,
md.ref.palette.neutral90,
md.ref.palette.neutral92,
md.ref.palette.neutral94,
md.ref.palette.neutral95,
md.ref.palette.neutral96,
md.ref.palette.neutral98,
md.ref.palette.primary10,
md.ref.palette.primary100,
md.ref.palette.primary20,
md.ref.palette.primary30,
md.ref.palette.primary40,
md.ref.palette.primary80,
md.ref.palette.primary90,
md.ref.palette.secondary10,
md.ref.palette.secondary100,
md.ref.palette.secondary20,
md.ref.palette.secondary30,
md.ref.palette.secondary40,
md.ref.palette.secondary80,
md.ref.palette.secondary90,
md.ref.palette.tertiary10,
md.ref.palette.tertiary100,
md.ref.palette.tertiary20,
md.ref.palette.tertiary30,
md.ref.palette.tertiary40,
md.ref.palette.tertiary80,
md.ref.palette.tertiary90,
md.ref.typeface.weight-medium,
md.ref.typeface.weight-regular,
md.sys.color.background,
md.sys.color.error,
md.sys.color.error-container,
md.sys.color.inverse-on-surface,
md.sys.color.inverse-primary,
md.sys.color.inverse-surface,
md.sys.color.on-background,
md.sys.color.on-error,
md.sys.color.on-error-container,
md.sys.color.on-primary,
md.sys.color.on-primary-container,
md.sys.color.on-primary-fixed,
md.sys.color.on-primary-fixed-variant,
md.sys.color.on-secondary,
md.sys.color.on-secondary-container,
md.sys.color.on-secondary-fixed,
md.sys.color.on-secondary-fixed-variant,
md.sys.color.on-surface,
md.sys.color.on-surface-variant,
md.sys.color.on-tertiary,
md.sys.color.on-tertiary-container,
md.sys.color.on-tertiary-fixed,
md.sys.color.on-tertiary-fixed-variant,
md.sys.color.outline,
md.sys.color.outline-variant,
md.sys.color.primary,
md.sys.color.primary-container,
md.sys.color.primary-fixed,
md.sys.color.primary-fixed-dim,
md.sys.color.scrim,
md.sys.color.secondary,
md.sys.color.secondary-container,
md.sys.color.secondary-fixed,
md.sys.color.secondary-fixed-dim,
md.sys.color.shadow,
md.sys.color.surface,
md.sys.color.surface-bright,
md.sys.color.surface-container,
md.sys.color.surface-container-high,
md.sys.color.surface-container-highest,
md.sys.color.surface-container-low,
md.sys.color.surface-container-lowest,
md.sys.color.surface-dim,
md.sys.color.surface-variant,
md.sys.color.tertiary,
md.sys.color.tertiary-container,
md.sys.color.tertiary-fixed,
md.sys.color.tertiary-fixed-dim,
md.sys.elevation.level0,
md.sys.elevation.level1,
md.sys.elevation.level2,
md.sys.elevation.level3,
md.sys.elevation.level4,
md.sys.elevation.level5,
md.sys.motion.duration.extra-long1Ms,
md.sys.motion.duration.extra-long2Ms,
md.sys.motion.duration.extra-long3Ms,
md.sys.motion.duration.extra-long4Ms,
md.sys.motion.duration.long1Ms,
md.sys.motion.duration.long2Ms,
md.sys.motion.duration.long3Ms,
md.sys.motion.duration.long4Ms,
md.sys.motion.duration.medium1Ms,
md.sys.motion.duration.medium2Ms,
md.sys.motion.duration.medium3Ms,
md.sys.motion.duration.medium4Ms,
md.sys.motion.duration.short1Ms,
md.sys.motion.duration.short2Ms,
md.sys.motion.duration.short3Ms,
md.sys.motion.duration.short4Ms,
md.sys.motion.easing.emphasized.accelerate,
md.sys.motion.easing.emphasized.decelerate,
md.sys.motion.easing.legacy,
md.sys.motion.easing.legacy.accelerate,
md.sys.motion.easing.legacy.decelerate,
md.sys.motion.easing.linear,
md.sys.motion.easing.standard,
md.sys.motion.easing.standard.accelerate,
md.sys.motion.easing.standard.decelerate,
md.sys.shape.corner.extra-large,
md.sys.shape.corner.extra-large.top,
md.sys.shape.corner.extra-small,
md.sys.shape.corner.full,
md.sys.shape.corner.large,
md.sys.shape.corner.medium,
md.sys.shape.corner.none,
md.sys.shape.corner.small,
md.sys.state.focus.state-layer-opacity,
md.sys.state.hover.state-layer-opacity,
md.sys.state.pressed.state-layer-opacity,
md.sys.typescale.body-large.line-height,
md.sys.typescale.body-large.size,
md.sys.typescale.body-large.tracking,
md.sys.typescale.body-large.weight,
md.sys.typescale.body-medium.line-height,
md.sys.typescale.body-medium.size,
md.sys.typescale.body-medium.tracking,
md.sys.typescale.body-medium.weight,
md.sys.typescale.body-small.line-height,
md.sys.typescale.body-small.size,
md.sys.typescale.body-small.tracking,
md.sys.typescale.body-small.weight,
md.sys.typescale.display-large.line-height,
md.sys.typescale.display-large.size,
md.sys.typescale.display-large.tracking,
md.sys.typescale.display-large.weight,
md.sys.typescale.display-medium.line-height,
md.sys.typescale.display-medium.size,
md.sys.typescale.display-medium.tracking,
md.sys.typescale.display-medium.weight,
md.sys.typescale.display-small.line-height,
md.sys.typescale.display-small.size,
md.sys.typescale.display-small.tracking,
md.sys.typescale.display-small.weight,
md.sys.typescale.headline-large.line-height,
md.sys.typescale.headline-large.size,
md.sys.typescale.headline-large.tracking,
md.sys.typescale.headline-large.weight,
md.sys.typescale.headline-medium.line-height,
md.sys.typescale.headline-medium.size,
md.sys.typescale.headline-medium.tracking,
md.sys.typescale.headline-medium.weight,
md.sys.typescale.headline-small.line-height,
md.sys.typescale.headline-small.size,
md.sys.typescale.headline-small.tracking,
md.sys.typescale.headline-small.weight,
md.sys.typescale.label-large.line-height,
md.sys.typescale.label-large.size,
md.sys.typescale.label-large.tracking,
md.sys.typescale.label-large.weight,
md.sys.typescale.label-medium.line-height,
md.sys.typescale.label-medium.size,
md.sys.typescale.label-medium.tracking,
md.sys.typescale.label-medium.weight,
md.sys.typescale.label-small.line-height,
md.sys.typescale.label-small.size,
md.sys.typescale.label-small.tracking,
md.sys.typescale.label-small.weight,
md.sys.typescale.title-large.line-height,
md.sys.typescale.title-large.size,
md.sys.typescale.title-large.tracking,
md.sys.typescale.title-large.weight,
md.sys.typescale.title-medium.line-height,
md.sys.typescale.title-medium.size,
md.sys.typescale.title-medium.tracking,
md.sys.typescale.title-medium.weight,
md.sys.typescale.title-small.line-height,
md.sys.typescale.title-small.size,
md.sys.typescale.title-small.tracking,
md.sys.typescale.title-small.weight
