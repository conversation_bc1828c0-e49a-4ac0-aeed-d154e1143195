{"cells": [{"cell_type": "markdown", "id": "be34d25b", "metadata": {"id": "8377c056591f"}, "source": ["Copyright 2024 Google LLC."]}, {"cell_type": "code", "execution_count": 1, "id": "6130c8e6", "metadata": {"cellView": "form", "id": "ca23c3f523a7"}, "outputs": [], "source": ["# @title Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "# https://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "880b5dcf", "metadata": {"id": "u71STQRgnQ3a"}, "source": ["# Fine-tune PaliGemma with JAX\n", "\n", "<table class=\"tfo-notebook-buttons\" align=\"left\">\n", "<td>\n", "<a target=\"_blank\" href=\"https://colab.research.google.com/github/google-gemini/gemma-cookbook/blob/main/PaliGemma/[PaliGemma_2]Finetune_with_JAX.ipynb\"><img src=\"https://www.tensorflow.org/images/colab_logo_32px.png\" />Run in Google Colab</a>\n", "</td>\n", "<td>\n", "<a target=\"_blank\" href=\"https://github.com/google-gemini/gemma-cookbook/blob/main/PaliGemma/[PaliGemma_2]Finetune_with_JAX.ipynb\"><img src=\"https://www.tensorflow.org/images/GitHub-Mark-32px.png\" />View source on GitHub</a>\n", "</td>\n", "</table>\n"]}, {"cell_type": "markdown", "id": "74dcda33", "metadata": {"id": "wR53lePHuiP-"}, "source": ["This notebook shows how to fine-tune [PaliGemma](https://ai.google.dev/gemma/docs/paligemma) on a vision-language task with [JAX](https://jax.readthedocs.io/en/latest/index.html). *Fine-tuning* is a process that can improve your model's performance on specific tasks or help the model adhere to specific output requirements when instructions aren't sufficient and you have a set of examples that demonstrate the outputs you want. Gemma-based models like PaliGemma require fine-tuning to produce expected results.\n", "\n", "### What's in this notebook\n", "\n", "This notebook uses the model reference implementation from [`big_vision`](https://github.com/google-research/big_vision)\n", "and shows how to:\n", "\n", " * Install dependencies, and download the PaliGemma model checkpoint and training data\n", " * Load the model onto GPU devices\n", " * Prepare the model's inputs for training and inference\n", " * Fine-tune the model\n", " * Inspect the output\n", "\n", "The training data for this notebook consists of 90 pairs of images and long captions describing them. To make it runnable on a Kaggle GPU runtime, you'll only fine-tune the attention layers of the language model and freeze the other parameters.\n", "\n", "This example is for learning purposes only. In a real use case, the amount of data, trainable parameters, training steps and hyper-parameters, and obtained results could be significantly different.\n", "\n", "### Before you begin\n", "\n", "Before going through this notebook, you should be familiar with Python code, as well as how large language models (LLMs) are trained. You don't need to be familiar with JAX, but basic knowledge about JAX (or similar technologies such as Keras) is helpful when reading through the example code."]}, {"cell_type": "markdown", "id": "a42e7554", "metadata": {"id": "6U0QUFveqSP2"}, "source": ["## Setup\n", "\n", "The following sections explain the preliminary steps for getting a notebook to use a PaliGemma model, including model access and configuring the notebook runtime."]}, {"cell_type": "markdown", "id": "16b96310", "metadata": {"id": "qRi1rF4MWlQi"}, "source": ["### Get access to PaliGemma\n", "\n", "Before using PaliGemma for the first time, you must request access to the model through Kaggle by completing the following steps:\n", "\n", "1. Log in to [Kaggle](https://www.kaggle.com), or create a new Kaggle account if you don't already have one.\n", "1. Go to the [PaliGemma model card](https://www.kaggle.com/models/google/paligemma-2) and click **Request Access**.\n", "1. Complete the consent form and accept the terms and conditions."]}, {"cell_type": "markdown", "id": "ee60a2fe", "metadata": {"id": "Kp6XQ2hQB8lv"}, "source": ["### Select the runtime\n", "\n", "To complete this tutorial, you'll need to have a Kaggle runtime with sufficient resources to run the PaliGemma model. In this case, you can use a GPU:\n", "\n", "1. In the upper-right of the Kaggle notebook window, click on the three dots.\n", "1. Select **Accelerator**.\n", "1. Choose **GPU P100 or GPU T4 x2** from the available options."]}, {"cell_type": "markdown", "id": "016fecda", "metadata": {"id": "rCd__uzW_eK-"}, "source": ["### Fetch the `big_vision` repository and install related dependencies\n", "\n", "Download the `big_vision` repository to your Kaggle notebook from GitHub and install dependencies related to `big_vision` by running the following code."]}, {"cell_type": "code", "execution_count": 1, "id": "c92f001e", "metadata": {"id": "c2eba4d7d2d3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: jax[cuda12] in /opt/conda/lib/python3.10/site-packages (0.4.26)\r\n", "Collecting jax[cuda12]\r\n", "  Downloading jax-0.4.35-py3-none-any.whl.metadata (22 kB)\r\n", "Collecting jaxlib<=0.4.35,>=0.4.34 (from jax[cuda12])\r\n", "  Downloading jaxlib-0.4.35-cp310-cp310-manylinux2014_x86_64.whl.metadata (983 bytes)\r\n", "Collecting ml-dtypes>=0.4.0 (from jax[cuda12])\r\n", "  Downloading ml_dtypes-0.5.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (21 kB)\r\n", "Requirement already satisfied: numpy>=1.24 in /opt/conda/lib/python3.10/site-packages (from jax[cuda12]) (1.26.4)\r\n", "Requirement already satisfied: opt-einsum in /opt/conda/lib/python3.10/site-packages (from jax[cuda12]) (3.3.0)\r\n", "Requirement already satisfied: scipy>=1.10 in /opt/conda/lib/python3.10/site-packages (from jax[cuda12]) (1.14.1)\r\n", "Collecting jaxlib<=0.4.35,>=0.4.34 (from jax[cuda12])\r\n", "  Downloading jaxlib-0.4.34-cp310-cp310-manylinux2014_x86_64.whl.metadata (983 bytes)\r\n", "Collecting jax-cuda12-plugin<=0.4.35,>=0.4.34 (from jax-cuda12-plugin[with_cuda]<=0.4.35,>=0.4.34; extra == \"cuda12\"->jax[cuda12])\r\n", "  Downloading jax_cuda12_plugin-0.4.35-cp310-cp310-manylinux2014_x86_64.whl.metadata (1.2 kB)\r\n", "Collecting jax-cuda12-pjrt==0.4.35 (from jax-cuda12-plugin<=0.4.35,>=0.4.34->jax-cuda12-plugin[with_cuda]<=0.4.35,>=0.4.34; extra == \"cuda12\"->jax[cuda12])\r\n", "  Downloading jax_cuda12_pjrt-0.4.35-py3-none-manylinux2014_x86_64.whl.metadata (349 bytes)\r\n", "\u001b[33mWARNING: jax-cuda12-plugin 0.4.35 does not provide the extra 'with-cuda'\u001b[0m\u001b[33m\r\n", "\u001b[0mCollecting nvidia-cublas-cu12>=******** (from jax-cuda12-plugin[with_cuda]<=0.4.35,>=0.4.34; extra == \"cuda12\"->jax[cuda12])\r\n", "  Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting nvidia-cuda-cupti-cu12>=12.1.105 (from jax-cuda12-plugin[with_cuda]<=0.4.35,>=0.4.34; extra == \"cuda12\"->jax[cuda12])\r\n", "  Downloading nvidia_cuda_cupti_cu12-12.6.80-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\r\n", "Collecting nvidia-cuda-nvcc-cu12>=12.1.105 (from jax-cuda12-plugin[with_cuda]<=0.4.35,>=0.4.34; extra == \"cuda12\"->jax[cuda12])\r\n", "  Downloading nvidia_cuda_nvcc_cu12-12.6.85-py3-none-manylinux1_x86_64.manylinux_2_5_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting nvidia-cuda-runtime-cu12>=12.1.105 (from jax-cuda12-plugin[with_cuda]<=0.4.35,>=0.4.34; extra == \"cuda12\"->jax[cuda12])\r\n", "  Downloading nvidia_cuda_runtime_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting nvidia-cudnn-cu12<10.0,>=9.1 (from jax-cuda12-plugin[with_cuda]<=0.4.35,>=0.4.34; extra == \"cuda12\"->jax[cuda12])\r\n", "  Downloading nvidia_cudnn_cu12-********-py3-none-manylinux_2_27_x86_64.whl.metadata (1.6 kB)\r\n", "Collecting nvidia-cufft-cu12>=11.0.2.54 (from jax-cuda12-plugin[with_cuda]<=0.4.35,>=0.4.34; extra == \"cuda12\"->jax[cuda12])\r\n", "  Downloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting nvidia-cusolver-cu12>=11.4.5.107 (from jax-cuda12-plugin[with_cuda]<=0.4.35,>=0.4.34; extra == \"cuda12\"->jax[cuda12])\r\n", "  Downloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\r\n", "Collecting nvidia-cusparse-cu12>=12.1.0.106 (from jax-cuda12-plugin[with_cuda]<=0.4.35,>=0.4.34; extra == \"cuda12\"->jax[cuda12])\r\n", "  Downloading nvidia_cusparse_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\r\n", "Collecting nvidia-nccl-cu12>=2.18.1 (from jax-cuda12-plugin[with_cuda]<=0.4.35,>=0.4.34; extra == \"cuda12\"->jax[cuda12])\r\n", "  Downloading nvidia_nccl_cu12-2.23.4-py3-none-manylinux2014_x86_64.whl.metadata (1.8 kB)\r\n", "Collecting nvidia-nvjitlink-cu12>=12.1.105 (from jax-cuda12-plugin[with_cuda]<=0.4.35,>=0.4.34; extra == \"cuda12\"->jax[cuda12])\r\n", "  Downloading nvidia_nvjitlink_cu12-12.6.85-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl.metadata (1.5 kB)\r\n", "Downloading jaxlib-0.4.34-cp310-cp310-manylinux2014_x86_64.whl (86.1 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.1/86.1 MB\u001b[0m \u001b[31m19.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading jax_cuda12_plugin-0.4.35-cp310-cp310-manylinux2014_x86_64.whl (15.5 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m15.5/15.5 MB\u001b[0m \u001b[31m85.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading jax_cuda12_pjrt-0.4.35-py3-none-manylinux2014_x86_64.whl (100.8 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m100.8/100.8 MB\u001b[0m \u001b[31m17.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading ml_dtypes-0.5.0-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (4.5 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.5/4.5 MB\u001b[0m \u001b[31m93.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading jax-0.4.35-py3-none-any.whl (2.2 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.2/2.2 MB\u001b[0m \u001b[31m67.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (393.1 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m393.1/393.1 MB\u001b[0m \u001b[31m4.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.6.80-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (8.9 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8.9/8.9 MB\u001b[0m \u001b[31m59.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cuda_nvcc_cu12-12.6.85-py3-none-manylinux1_x86_64.manylinux_2_5_x86_64.whl (21.2 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.2/21.2 MB\u001b[0m \u001b[31m78.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (897 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m897.7/897.7 kB\u001b[0m \u001b[31m42.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cudnn_cu12-********-py3-none-manylinux_2_27_x86_64.whl (508.1 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m508.1/508.1 MB\u001b[0m \u001b[31m3.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (200.2 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m200.2/200.2 MB\u001b[0m \u001b[31m8.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (158.2 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m158.2/158.2 MB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cusparse_cu12-********-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (216.6 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m216.6/216.6 MB\u001b[0m \u001b[31m7.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_nccl_cu12-2.23.4-py3-none-manylinux2014_x86_64.whl (199.0 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m199.0/199.0 MB\u001b[0m \u001b[31m7.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.6.85-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl (19.7 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m19.7/19.7 MB\u001b[0m \u001b[31m5.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hInstalling collected packages: jax-cuda12-pjrt, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvcc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, ml-dtypes, jax-cuda12-plugin, nvidia-cusparse-cu12, nvidia-cufft-cu12, nvidia-cudnn-cu12, jaxlib, nvidia-cusolver-cu12, jax\r\n", "  Attempting uninstall: ml-dtypes\r\n", "    Found existing installation: ml-dtypes 0.3.2\r\n", "    Uninstalling ml-dtypes-0.3.2:\r\n", "      Successfully uninstalled ml-dtypes-0.3.2\r\n", "  Attempting uninstall: jax<PERSON>b\r\n", "    Found existing installation: jaxlib 0.4.26.dev20240620\r\n", "    Uninstalling jaxlib-0.4.26.dev20240620:\r\n", "      Successfully uninstalled jaxlib-0.4.26.dev20240620\r\n", "  Attempting uninstall: jax\r\n", "    Found existing installation: jax 0.4.26\r\n", "    Uninstalling jax-0.4.26:\r\n", "      Successfully uninstalled jax-0.4.26\r\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\r\n", "tensorflow 2.16.1 requires ml-dtypes~=0.3.1, but you have ml-dtypes 0.5.0 which is incompatible.\u001b[0m\u001b[31m\r\n", "\u001b[0mSuccessfully installed jax-0.4.35 jax-cuda12-pjrt-0.4.35 jax-cuda12-plugin-0.4.35 jaxlib-0.4.34 ml-dtypes-0.5.0 nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.6.80 nvidia-cuda-nvcc-cu12-12.6.85 nvidia-cuda-runtime-cu12-12.6.77 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-******** nvidia-cusolver-cu12-******** nvidia-cusparse-cu12-******** nvidia-nccl-cu12-2.23.4 nvidia-nvjitlink-cu12-12.6.85\r\n"]}], "source": ["!pip install -U \"jax[cuda12]\""]}, {"cell_type": "code", "execution_count": 2, "id": "3927a091", "metadata": {"id": "DfxKb3F839Ks"}, "outputs": [], "source": ["import os\n", "import sys\n", "\n", "!git clone --quiet --branch=main --depth=1 \\\n", "    https://github.com/google-research/big_vision big_vision_repo\n", "\n", "# Append big_vision code to python import path\n", "if \"big_vision_repo\" not in sys.path:\n", "  sys.path.append(\"big_vision_repo\")\n", "\n", "# Install missing dependencies. Assume jax~=0.4.25 with GPU available.\n", "!pip3 install -q \"overrides\" \"ml_collections\" \"einops~=0.7\" \"sentencepiece\""]}, {"cell_type": "markdown", "id": "a61a030a", "metadata": {"id": "zDoq0O77GF30"}, "source": ["### Import JAX and other dependencies\n", "\n", "Import JAX and other dependencies required for PaliGemma, like TensorFlow and NumPy."]}, {"cell_type": "code", "execution_count": 3, "id": "e15a2524", "metadata": {"id": "dTfe2k8J4Bw0"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_24/840491807.py:16: DeprecationWarning: Importing display from IPython.core.display is deprecated since IPython 7.14, please import from IPython display\n", "  from IPython.core.display import display, HTML\n"]}, {"name": "stdout", "output_type": "stream", "text": ["JAX version:  0.4.35\n", "JAX platform: gpu\n", "JAX devices:  2\n"]}], "source": ["import base64\n", "import functools\n", "import html\n", "import io\n", "import os\n", "import warnings\n", "\n", "import jax\n", "import jax.numpy as jnp\n", "import numpy as np\n", "import ml_collections\n", "\n", "import tensorflow as tf\n", "import sentencepiece\n", "\n", "from IPython.core.display import display, HTML\n", "from PIL import Image\n", "\n", "# Import model definition from big_vision\n", "from big_vision.models.proj.paligemma import paligemma\n", "from big_vision.trainers.proj.paligemma import predict_fns\n", "\n", "# Import big vision utilities\n", "import big_vision.datasets.jsonl\n", "import big_vision.utils\n", "import big_vision.sharding\n", "\n", "# Don't let TF use the GPU or TPUs\n", "tf.config.set_visible_devices([], \"GPU\")\n", "tf.config.set_visible_devices([], \"TPU\")\n", "\n", "backend = jax.extend.backend.get_backend()\n", "print(f\"JAX version:  {jax.__version__}\")\n", "print(f\"JAX platform: {backend.platform}\")\n", "print(f\"JAX devices:  {jax.device_count()}\")"]}, {"cell_type": "markdown", "id": "92dbccf9", "metadata": {"id": "b9kSadtIhjlX"}, "source": ["## Download and configure the model\n", "\n", "In this step, you'll download the model checkpoint and configure it so that you can fine-tune it later on. This step shows you how to move model parameters into TPU memory, which is useful for fine-tuning models on devices with limited resources."]}, {"cell_type": "markdown", "id": "923baf02", "metadata": {"id": "7tvcc0oQHl4v"}, "source": ["### Download the model checkpoint\n", "\n", "PaliGemma includes several model variations. For this tutorial, you'll use the base [JAX/FLAX PaliGemma 3B weight model](https://www.kaggle.com/models/google/paligemma-2/jax/paligemma2-3b-pt-224).\n", "\n", "Download the `float16` version of the model checkpoint from Kaggle by running the following code. This process takes several minutes to complete."]}, {"cell_type": "code", "execution_count": 4, "id": "fde048e2", "metadata": {"id": "gQNOTfF24AV4"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading the checkpoint from <PERSON><PERSON>, this could take a few minutes....\n", "Model path: /kaggle/input/paligemma-2/jax/paligemma2-3b-pt-224/1/./paligemma2-3b-pt-224.b16.npz\n", "Downloading the model tokenizer...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.10/pty.py:89: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  pid, fd = os.forkpty()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Copying gs://big_vision/paligemma_tokenizer.model...\r\n", "\r\n", "Operation completed over 1 objects/4.1 MiB.                                      \r\n", "Tokenizer path: ./paligemma_tokenizer.model\n", "Downloading the dataset...\n", "Data path: ./longcap100\n"]}], "source": ["import os\n", "import kagglehub\n", "\n", "# Use these for PaliGemma-2 3B 224px²\n", "LLM_VARIANT = \"gemma2_2b\"\n", "MODEL_PATH = \"./paligemma2-3b-pt-224.b16.npz\"\n", "KAGGLE_HANDLE = \"google/paligemma-2/jax/paligemma2-3b-pt-224\"  # Path to fetch from <PERSON><PERSON>.\n", "\n", "# Use these for PaliGemma 1:\n", "# LLM_VARIANT = \"gemma_2b\"\n", "# MODEL_PATH = \"./paligemma-3b-pt-224.f16.npz\"\n", "# KAGGLE_HANDLE = \"google/paligemma/jax/paligemma-3b-pt-224\"\n", "\n", "if not os.path.exists(MODEL_PATH):\n", "  print(\"Downloading the checkpoint from <PERSON><PERSON>, this could take a few minutes....\")\n", "  MODEL_PATH = kagglehub.model_download(KAGGLE_HANDLE, MODEL_PATH)\n", "  print(f\"Model path: {MODEL_PATH}\")\n", "\n", "TOKENIZER_PATH = \"./paligemma_tokenizer.model\"\n", "if not os.path.exists(TOKENIZER_PATH):\n", "  print(\"Downloading the model tokenizer...\")\n", "  !gsutil cp gs://big_vision/paligemma_tokenizer.model {TOKENIZER_PATH}\n", "  print(f\"Tokenizer path: {TOKENIZER_PATH}\")\n", "\n", "DATA_DIR=\"./longcap100\"\n", "if not os.path.exists(DATA_DIR):\n", "  print(\"Downloading the dataset...\")\n", "  !gsutil -m -q cp -n -r gs://longcap100/ .\n", "  print(f\"Data path: {DATA_DIR}\")"]}, {"cell_type": "markdown", "id": "dd46593b", "metadata": {"id": "rv7w-cGuLj5o"}, "source": ["### Configure the model\n", "\n", "It's time to actually start configuring the model that you're going to use.\n", "\n", "For this notebook, you need to be able to fit your model onto a GPU. Having a limited resource like space constraints means that you have to be mindful of how your model is configured.\n", "\n", "If you fine-tune every parameter, your model won't be able to run in the notebook environment. As a result, in this part of the notebook, you'll configure your model so that it has the ability to freeze some of the parameters, and only fine-tune the parameters that really need to be fine-tuned for the model to give you accurate results. In LLMs, parameters are said to be *frozen* when they are no longer actively being used to train the model.\n", "\n", "In order to configure your model, you need to:\n", "\n", "* Initialize the `model_config` as a [`FrozenConfigDict`](https://github.com/google/ml_collections/tree/master#frozenconfigdict) so that you can freeze some of the parameters and keep memory usage low\n", "* Initialize an instance of the PaliGemma `Model` class using the `model_config` as its configurations\n", "* Load the model parameters into RAM\n", "* Define a `decode` function to sample outputs from the model\n", "\n", "This code in this cell takes about a minute to run to completion."]}, {"cell_type": "code", "execution_count": 5, "id": "30747284", "metadata": {"id": "1aghcULcEdtv"}, "outputs": [], "source": ["# Define model\n", "\n", "# IMPORTANT: Gemma-2 has a \"final_logits_softcap\" property, we set it to 0.0\n", "# for better transfer results.\n", "model_config = ml_collections.FrozenConfigDict({\n", "    \"llm\": {\"vocab_size\": 257_152, \"variant\": LLM_VARIANT, \"final_logits_softcap\": 0.0},\n", "    \"img\": {\"variant\": \"So400m/14\", \"pool_type\": \"none\", \"scan\": True, \"dtype_mm\": \"float16\"}\n", "})\n", "model = paligemma.Model(**model_config)\n", "tokenizer = sentencepiece.SentencePieceProcessor(TOKENIZER_PATH)\n", "\n", "# Load params - this can take up to 1 minute in T4 colabs.\n", "params = paligemma.load(None, MODEL_PATH, model_config)\n", "\n", "# Define `decode` function to sample outputs from the model.\n", "decode_fn = predict_fns.get_all(model)['decode']\n", "decode = functools.partial(decode_fn, devices=jax.devices(), eos_token=tokenizer.eos_id())"]}, {"cell_type": "markdown", "id": "fdfd4faf", "metadata": {"id": "uidBwmb8LwZ5"}, "source": ["### Move model parameters into GPU/TPU memory\n", "\n", "Now you need to move the model parameters into GPU/TPU memory. First, shard the parameters across the available GPUs, then load the parameters. Here, you'll load the parameters sequentially. This process takes longer than loading them simultaneously, but it requires more RAM than you have available in this notebook.\n", "\n", "Finally, print out all of the parameters to see what type each individual parameter is cast to. Frozen parameters are kept as `float16`, while the trainable parameters are cast to `float32`. When you inspect the list, you'll see that most of the parameters have been frozen and are `float16`."]}, {"cell_type": "code", "execution_count": 6, "id": "19f25fb1", "metadata": {"id": "RWOdf_fw2SAO"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" == Model params == \n", "img/Transformer/encoder_norm/bias                                                (1152,)                float16\n", "img/Transformer/encoder_norm/scale                                               (1152,)                float16\n", "img/Transformer/encoderblock/LayerNorm_0/bias                                    (27, 1152)             float16\n", "img/Transformer/encoderblock/LayerNorm_0/scale                                   (27, 1152)             float16\n", "img/Transformer/encoderblock/LayerNorm_1/bias                                    (27, 1152)             float16\n", "img/Transformer/encoderblock/LayerNorm_1/scale                                   (27, 1152)             float16\n", "img/Transformer/encoderblock/MlpBlock_0/Dense_0/bias                             (27, 4304)             float16\n", "img/Transformer/encoderblock/MlpBlock_0/Dense_0/kernel                           (27, 1152, 4304)       float16\n", "img/Transformer/encoderblock/MlpBlock_0/Dense_1/bias                             (27, 1152)             float16\n", "img/Transformer/encoderblock/MlpBlock_0/Dense_1/kernel                           (27, 4304, 1152)       float16\n", "img/Transformer/encoderblock/MultiHeadDotProductAttention_0/key/bias             (27, 16, 72)           float16\n", "img/Transformer/encoderblock/MultiHeadDotProductAttention_0/key/kernel           (27, 1152, 16, 72)     float16\n", "img/Transformer/encoderblock/MultiHeadDotProductAttention_0/out/bias             (27, 1152)             float16\n", "img/Transformer/encoderblock/MultiHeadDotProductAttention_0/out/kernel           (27, 16, 72, 1152)     float16\n", "img/Transformer/encoderblock/MultiHeadDotProductAttention_0/query/bias           (27, 16, 72)           float16\n", "img/Transformer/encoderblock/MultiHeadDotProductAttention_0/query/kernel         (27, 1152, 16, 72)     float16\n", "img/Transformer/encoderblock/MultiHeadDotProductAttention_0/value/bias           (27, 16, 72)           float16\n", "img/Transformer/encoderblock/MultiHeadDotProductAttention_0/value/kernel         (27, 1152, 16, 72)     float16\n", "img/embedding/bias                                                               (1152,)                float16\n", "img/embedding/kernel                                                             (14, 14, 3, 1152)      float16\n", "img/head/bias                                                                    (2304,)                float16\n", "img/head/kernel                                                                  (1152, 2304)           float16\n", "img/pos_embedding                                                                (1, 256, 1152)         float16\n", "llm/embedder/input_embedding                                                     (257152, 2304)         float16\n", "llm/final_norm/scale                                                             (2304,)                float16\n", "llm/layers/attn/attn_vec_einsum/w                                                (26, 8, 256, 2304)     float32\n", "llm/layers/attn/kv_einsum/w                                                      (26, 2, 4, 2304, 256)  float32\n", "llm/layers/attn/q_einsum/w                                                       (26, 8, 2304, 256)     float32\n", "llm/layers/mlp/gating_einsum                                                     (26, 2, 2304, 9216)    float16\n", "llm/layers/mlp/linear                                                            (26, 9216, 2304)       float16\n", "llm/layers/post_attention_norm/scale                                             (26, 2304)             float16\n", "llm/layers/post_ffw_norm/scale                                                   (26, 2304)             float16\n", "llm/layers/pre_attention_norm/scale                                              (26, 2304)             float16\n", "llm/layers/pre_ffw_norm/scale                                                    (26, 2304)             float16\n"]}], "source": ["# Create a pytree mask of the trainable params.\n", "def is_trainable_param(name, param):  # pylint: disable=unused-argument\n", "  if name.startswith(\"llm/layers/attn/\"):  return True\n", "  if name.startswith(\"llm/\"):              return False\n", "  if name.startswith(\"img/\"):              return False\n", "  raise ValueError(f\"Unexpected param name {name}\")\n", "trainable_mask = big_vision.utils.tree_map_with_names(is_trainable_param, params)\n", "\n", "# If more than one device is available (e.g. multiple GPUs) the parameters can\n", "# be sharded across them to reduce HBM usage per device.\n", "mesh = jax.sharding.Mesh(jax.devices(), (\"data\"))\n", "\n", "data_sharding = jax.sharding.NamedSharding(\n", "    mesh, jax.sharding.PartitionSpec(\"data\"))\n", "\n", "params_sharding = big_vision.sharding.infer_sharding(\n", "    params, strategy=[('.*', 'fsdp(axis=\"data\")')], mesh=mesh)\n", "\n", "# Yes: Some donated buffers are not usable.\n", "warnings.filterwarnings(\n", "    \"ignore\", message=\"Some donated buffers were not usable\")\n", "\n", "@functools.partial(jax.jit, donate_argnums=(0,), static_argnums=(1,))\n", "def maybe_cast_to_f32(params, trainable):\n", "  # Cast others to float16, since some GPUs don't support bf16.\n", "  return jax.tree.map(lambda p, m: p.astype(jnp.float32)\n", "                      if m else p.astype(jnp.float16),\n", "                      params, trainable)\n", "\n", "# Loading all params in simultaneous - albeit much faster and more succinct -\n", "# requires more RAM than the T4 colab runtimes have by default.\n", "# Instead we do it param by param.\n", "params, treedef = jax.tree.flatten(params)\n", "sharding_leaves = jax.tree.leaves(params_sharding)\n", "trainable_leaves = jax.tree.leaves(trainable_mask)\n", "for idx, (sharding, trainable) in enumerate(zip(sharding_leaves, trainable_leaves)):\n", "  params[idx] = big_vision.utils.reshard(params[idx], sharding)\n", "  params[idx] = maybe_cast_to_f32(params[idx], trainable)\n", "  params[idx].block_until_ready()\n", "params = jax.tree.unflatten(treedef, params)\n", "\n", "# Print params to show what the model is made of.\n", "def parameter_overview(params):\n", "  for path, arr in big_vision.utils.tree_flatten_with_names(params)[0]:\n", "    print(f\"{path:80s} {str(arr.shape):22s} {arr.dtype}\")\n", "\n", "print(\" == Model params == \")\n", "parameter_overview(params)"]}, {"cell_type": "markdown", "id": "4bde55a0", "metadata": {"id": "iD_9XXQkn1Mv"}, "source": ["## Prepare to tune the model\n", "\n", "Now that your model is configured, you can tune it. In this step, you'll create your model's inputs as well as the training and validation iterators, view the training examples, and define the training and validation loops."]}, {"cell_type": "markdown", "id": "3ef1ef32", "metadata": {"id": "83ZcnbddJKdx"}, "source": ["### Create model inputs\n", "\n", "The model checkpoint you're using has already been trained on images of various aspect ratios that have been resized to 224x224 pixels, and to handle tokenized texts.\n", "\n", "The code below defines three functions that you'll use in the next step create the model's inputs:\n", "\n", "* **`preprocess_image`:** Normalizes the image data. In this case, pre-processing converts the passed-in image to greyscale, removes the alpha layer, and resizes the passed-in image to the size required by the model for image inputs (224x224 pixels).\n", "* **`preprocess_tokens`:** Splits the tokens up and adds flags to mark whether a token is a prefix or suffix token. These flags will be used later on in the code, during the training step and the evaluation loop.\n", "* **`postprocess_tokens`:** Removes any tokens left at and/or after the end-of-sequence (EOS) token and returns the remaining decoded tokens.\n"]}, {"cell_type": "code", "execution_count": 7, "id": "aea6b72a", "metadata": {"id": "8SRW0NuU4UcW"}, "outputs": [], "source": ["def preprocess_image(image, size=224):\n", "  # Model has been trained to handle images of different aspects ratios\n", "  # resized to 224x224 in the range [-1, 1]. Bilinear and antialias resize\n", "  # options are helpful to improve quality in some tasks.\n", "  image = np.asarray(image)\n", "  if image.ndim == 2:  # Convert image without last channel into greyscale.\n", "    image = np.stack((image,)*3, axis=-1)\n", "  image = image[..., :3]  # Remove alpha layer.\n", "  assert image.shape[-1] == 3\n", "\n", "  image = tf.constant(image)\n", "  image = tf.image.resize(image, (size, size), method='bilinear', antialias=True)\n", "  return image.numpy() / 127.5 - 1.0  # [0, 255]->[-1,1]\n", "\n", "def preprocess_tokens(prefix, suffix=None, seqlen=None):\n", "  # Model has been trained to handle tokenized text composed of a prefix with\n", "  # full attention and a suffix with causal attention.\n", "  separator = \"\\n\"\n", "  tokens = tokenizer.encode(prefix, add_bos=True) + tokenizer.encode(separator)\n", "  mask_ar = [0] * len(tokens)    # 0 to use full attention for prefix.\n", "  mask_loss = [0] * len(tokens)  # 0 to not use prefix tokens in the loss.\n", "\n", "  if suffix:\n", "    suffix = tokenizer.encode(suffix, add_eos=True)\n", "    tokens += suffix\n", "    mask_ar += [1] * len(suffix)    # 1 to use causal attention for suffix.\n", "    mask_loss += [1] * len(suffix)  # 1 to use suffix tokens in the loss.\n", "\n", "  mask_input = [1] * len(tokens)    # 1 if it's a token, 0 if padding.\n", "  if seqlen:\n", "    padding = [0] * max(0, seqlen - len(tokens))\n", "    tokens = tokens[:seqlen] + padding\n", "    mask_ar = mask_ar[:seqlen] + padding\n", "    mask_loss = mask_loss[:seqlen] + padding\n", "    mask_input = mask_input[:seqlen] + padding\n", "\n", "  return jax.tree.map(np.array, (tokens, mask_ar, mask_loss, mask_input))\n", "\n", "def postprocess_tokens(tokens):\n", "  tokens = tokens.tolist()  # np.array to list[int]\n", "  try:  # Remove tokens at and after EOS if any.\n", "    eos_pos = tokens.index(tokenizer.eos_id())\n", "    tokens = tokens[:eos_pos]\n", "  except ValueError:\n", "    pass\n", "  return tokenizer.decode(tokens)\n"]}, {"cell_type": "markdown", "id": "672eed66", "metadata": {"id": "ovgWBgdHJZq3"}, "source": ["### Create the training and validation iterators\n", "\n", "Create two iterators:\n", "\n", "*   A **training iterator** to allow the training process to go through the data in chunks rather than processing it all at once\n", "    *   This allows you to do some data pre-processing before use\n", "*   A **validation iterator** that allows the training process to iterate over the validation dataset to see how well the tuned model aligned with the provided results"]}, {"cell_type": "code", "execution_count": 8, "id": "fc220ff0", "metadata": {"id": "whzWOojGOtzi"}, "outputs": [], "source": ["SEQLEN = 128\n", "\n", "train_dataset = big_vision.datasets.jsonl.DataSource(\n", "    os.path.join(DATA_DIR, \"data_train90.jsonl\"),\n", "    fopen_keys={\"image\": DATA_DIR})\n", "\n", "val_dataset = big_vision.datasets.jsonl.DataSource(\n", "    os.path.join(DATA_DIR, \"data_val10.jsonl\"),\n", "    fopen_keys={\"image\": DATA_DIR})\n", "\n", "\n", "def train_data_iterator():\n", "  \"\"\"Never ending iterator over training examples.\"\"\"\n", "  # Shuffle examples and repeat so one can train for many epochs.\n", "  dataset = train_dataset.get_tfdata().shuffle(1_000).repeat()\n", "  for example in dataset.as_numpy_iterator():\n", "    image = Image.open(io.BytesIO(example[\"image\"]))\n", "    image = preprocess_image(image)\n", "\n", "    prefix = \"caption en\"  # Could also be a different prefix per example.\n", "    suffix = example[\"suffix\"].decode().lower()\n", "    tokens, mask_ar, mask_loss, _ = preprocess_tokens(prefix, suffix, SEQLEN)\n", "\n", "    yield {\n", "        \"image\": np.asarray(image),\n", "        \"text\": np.asarray(tokens),\n", "        \"mask_ar\": np.asarray(mask_ar),\n", "        \"mask_loss\": np.asarray(mask_loss),\n", "    }\n", "\n", "\n", "def validation_data_iterator():\n", "  \"\"\"Single iterator over validation examples.\"\"\"\n", "  for example in val_dataset.get_tfdata(ordered=True).as_numpy_iterator():\n", "    image = Image.open(io.BytesIO(example[\"image\"]))\n", "    image = preprocess_image(image)\n", "\n", "    prefix = \"caption en\"  # Could also be a different prefix per example.\n", "    tokens, mask_ar, _, mask_input = preprocess_tokens(prefix, seqlen=SEQLEN)\n", "\n", "    yield {\n", "        \"image\": np.asarray(image),\n", "        \"text\": np.asarray(tokens),\n", "        \"mask_ar\": np.asarray(mask_ar),\n", "        \"mask_input\": np.asarray(mask_input),\n", "    }\n"]}, {"cell_type": "markdown", "id": "0849f8a1", "metadata": {"id": "84olaM5dCiAl"}, "source": ["### View training examples\n", "\n", "In this notebook, the training data contains 90 images that are paired with long descriptions of what's depicted in the image.\n", "\n", "**Note:** Normal training data sets that are meant to be used for practical use cases should contain more images, but this notebook limits the number of data points so that you can train the model in a reasonable amount of time for an example.\n", "\n", "The code below prints a random selection of images with their descriptions from the training data set so that you can see what the images and descriptions your model is trained on looks like. Each image is displayed in as a 128x128 pixel JPEG, with the description printed next to the image to the right."]}, {"cell_type": "code", "execution_count": 9, "id": "55a7464e", "metadata": {"id": "BzJfb5t0nsLq"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training examples\n"]}, {"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCADgAOADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDzxYalWL2pfPtkXLzxj6sKjOq6fH966jP05oEWRDxR5VUn8RaanR3Y/wCyv+NV38UWo+5DI31wKLgbKwgD1qdYQK5lvFjAfu7RR/vNULeK7452JCv/AAHNIZ2kcQIFSiLFefN4k1RhgXO0f7KgVWl1XUJ/9ZeTMPTeRQB6Q8kEIzLNGgH95gKmSNZIVlQhkYZVgeteThmdxuJOT3NenaffrFd3Nu6qsUdpHsGfu8mgC55PFP8AJqZNroGHcZH0qUJSGVPK9qXyatbAKUoKAKvlVTvMgpCMfOf0rVKCud1a8+y6rEjYGRlMnAb1FAixhXkZB1UCoobSSOXO4behz1Yf41JHd2bP5mHWQ9R5Zz/Kob3Wraw/eSnaOqofvt+Hb8aBm/qmqR6Zotjbsw83HA+rbj+gH51574sg/tG9k1GBJD0Rjt+UhVAyDWVquvXGo3rXEhweiJnhRUK6uQBugXOMfKcf/rpoTL2j6HBqdtuaRkkEm04cHA9cda6aytrTw1aXUkkyGEsVG8gtJxjhfTNcVBrEtosgtgY/MOWO/r+WKWyaXUdRhWZi6K2SO2KBHcwIZLZJGGGddxHpnnFNkix2q5GQycfhTJVpjM1o+TUDpV9xVdhQIpNHUDx1eZahdaAOO59aAKXFLikAYooxS0AFFFLQAnNIM06jpQAnNdfcyPHY/bnbCTWaxqB3bPT9a5KugG++0LTYgSQlwIyPrQBsx6hNbXwhYtsWBJCo74JrrbecTQrIBgMM1xkmJPEmq/3ILYpk9sAV0mkuf7NgJ67RQNGnuGaN1QF+aN9IZYJrD8S6SNU087R+9TlTWp5goL5HNAHkck+pWzm38+4XHG0OartDduSWjlJ6klTXe35iXVJNscYK4G4gZqGaQqP9YDgc8k1LmNRODZHQ4dSD7im8eh/Ou2urT7Ym2dQQv8THBFVo/CiyYZWyp5FVGVxNWOURC7YVea6fRNPaMhyOTWta+GoYDlhk1qJbJCMKKZIRgquKGNOPFRMfegZE/WoJMYqV6hbFMRCwqFhU7VC1AHGYpcUm4etG9R3pAOxRTfNWk80ehoAfilxUfmHstG5z0WgCSjFIkNxJwiM3+6uatxaJqM3/ACxZB/ekIQfmcUAVeO5FdN4c1KxtNKuxMwa5SRXt4/7zVlNp+mWGDe3ouZf+eNq2R+L9PyzVaa7tt4a1t1gI6EMSRQB1MFsyaberJMDdXBD3LKfuZPyrn1yea6W2Aht0jHRVArhdH1aCLTp7Jy3nTzIwbHXBHU/hXZpLlB9KBlvzaN4qqZPxpPMpAWzJTHnCKSarGSq13ITEQKYHK32osl+86/MNxWRT9eP0qRdZjeEMTtPTGao3cRW+3MMK5w1OvPD91BLgRkqehA61m7J2Zau9UWYtUBm8tCRGDlmPOB3rq9LnV7CIjjIyB7Vwq2bpcCBwRjBcf0rrLJtkSr0AFUktyW+htGQU6GGa6k8uCMyN7DpWcZuOtaOkXLm1vjEx+VVDYPIBzSnLljcqEeaVircy2toxW5uhvH8EQ3H8+lYd3r2zK29p3+9KxP6DFa0mkG7kVIRudjgDua67QvANrAiz6kizy9Qh5Vf8axhUlNm9SnGCPO7F9d1YH7FZvJz1jhGB+JFaf/CL+MniOLXt0EkYP869aeP7GY/KRFtwNrDGAvvUtrcecrBtomjO1wvY+o9jWvKjDnZ4HqNvrWmyiHUYZ4Cem9cZ+h71V+13SDl931ANfQPi61tZNBC3cSMz7AqsP4mbHHvjNcWfh1byW4cLOEYblKkHA+hpSTWxUZJ7njcQlQf8ecT/AO8Cf61s2Wn3k8LSNZ2cKgZBeHJNJbzFSPs9rt9JZz1+grQSOSUhpZicdSBkfSrcjNIxJl1GIgfZ7UA9GSFMH9KgKak/Hyj6Ko/pW5KAJ9h43dj/ADqsxMb7SRmmncTRmG21JhjcfzFKLPU2I+dhjp81aiyYqVZ8UxGYmlamwwbhgD28w1KnhuZzmW4H861I59zqo6k4q7HtGSTnH4VnOqoaG1Oi56mdbeHrKP8A1m6U1d/sjTgP+PRPxzUvmDBxjHriiOUE8gdaz9u+xr9WXcSOwsIWDJbRgjvirgmAHFVWbKkjqBVSK8jmBMbg44PtWsJqZjUpuBq+fSedzVATe9L5vvVmZe833prOGGDVPzaXzaAIb+1SSB2wMgZrvLdYZbO0DKCwhXJ98Vws0heMqOrcYrtVk2lYh1RQufoK5sQ9jqwy3OJmgWTVLuUgZMrD8qnUhBim3B8vULtD/wA9WP581EZK6IfCjnn8TFuZiIjg9q0fAMu+fVYn6NGhJP1NYlw/yGpfCtybe41AjqYl/wDQqzrfAy6P8RHoegGJdVvBjMkEW5B3wTgkfh/Ou1fF1DG9tMu0HcCOQfSvDtT1S7stQj1GymaKZOhU9u4PtUsfxO1KPJfT7F5D1cKyE/XBqaHwFV/jPcJVV4yrDIYYIqhNqWl+FrAT6reICB8iHl39Bjqa8cm+I3iO+Ux20lvZqeD5MeD+Zyaj0bTDquqCbVbtpnY53SsSCfc1rexja56Jp2rXnjbW1vZYmi06FsxI3c+p9/5V6TBGvlBcDAGAK53Q9PW3gRUVQqjjb0rpohtXnj601qD0PlKW2kTUniSKWcq3ylj8oHb9K0UTGFubpM/3F4A/Cq1+UkeCSZ7ja6YKQ9yOOf0q9bzJbRA2sCQnHLFcv+J5rNO6RclaTQySHyYg/kyeXn5XK4z7VS1Dc0Ucq/wcE1sNK15bskhlllIyC3AH0ArLaNprcocc5wc45xVIhlFJcipPMqijkHFTBsirJLttKTcx/WtXOcnisK3P+kR/WtrGevSuat8R2Yd+6xx4Q8/hRGuRzTHJEfoPelQnYTnrWfQ26k7f6s49K5azmaK/kUfdJ5rqAfkb6Vx5by79/wDeNa0N2YYjZHQibNPEtZ0cpOB61bDIgG4FjjOOgFbSko7nPGDlsT+bR5vvUPnxh9nlKT9T/jQygjKZBHY1KqIt0pWLdlmbUbaMc5kBP0HNdRLcZl+9hTXG6fera6rbu5wpJUn0yMV1efmAJA449658R8R04a3IYGpvjVJSOA6qw/LH9KqGX3qTW7qOXVisTZEcYQkevWqJeumn8COWrbnY+V8qeaTRJCt5cqD96E/zFQSPwafonOqOvrE1FT4GKl8aNKdTNEVPNYE9sVY8V1cFvvHPr3ok0pGk6jngjNcsKnKdlSlzHHRl4JNw/wD11uWGtLbsrAlT6EcVojwncznEMLvk4G1Saik8D61GAV0+VvoAa3U1I5XBxN608fizQYYL/uMR/Wm3nxSu3XbDuc+5P+Ncnd+HNRtiBPYzxe7oQPzqkLGSNsfL+DCqViWmXr5AsRVi+2OQ52dSD/kVLZNCIl2WbRp3lmc5b/PtS6guDIUYjKhsiqenx2rygi3ubxs9XyF/HH+NRS1ia11aRtJcIz+Wiw4H+eAM/rVJ02ysEBxnA7VrQxNGFdfstv02hVyw/nWdfBRdhnbzCc5JXHNWjFnO3KeTdSJ75oR+Kk1JNl1kLtBHrmqqmtCC7A3+kR/7wrd3EY7etc7bHNxH/vCt8Nxwa5626OvD/CxtxnyflHTBFSR58oZHNQ3DgRNkcY6U+JiYFyOcVn9k1+0WVbCH0x6Vx9yMX8n+8a60AFPxrkr3i/k/3q0o7syxHwouQSbXTPqK0Jchs47VibvlrQtbkzRhGOWXg+4qqsepnRl9knyPtG49dtWVkCKxPQDnNUwP3+WHy7cZqDULkriEHk/erPl5mkbc/Kmxsn76PB69qmXW9UjgFvuBwMLIRyBVOOTgVNv4zXQ4p7nIpOOzFhUouScseSTUharVnZLdRM5kK4GeBTF028ly0FtNLHnG5UJFCmm7DcJJXZWZqs+Hyv8AbiK2cMrDj6U2fStQgiaSWzmRAMkshGBUWiuI9dtSR/Hj86U9YMdPSaO1toQdwBIOe3auh07TI5NrzIG4yF/qa5u8mNsnmDgd67XTHV1mA6qVx/u4GP61x0IczuzuxM3FWRoLI0Y2oQuOwFSLqFxDz5oKjqHAI/WqUMTRvI7nLMevt/kmqV0DJdbXRsSMsa89vvN/Kuw4Dsba4t9QtZA0CF0XMkDDKSL3Iz0rh/HHhrTrS1gv9L0+zEOf3qvuBGe+Qen/ANat+xuBb3W/OPlIx65FcP4x8WRNZ3NjERKAfLA6gnK/4Gh6oFozj5m3oh68FT9CKowTux2NfykjgRqDxVvPyBs9+lUoI/MlkVr8W4yfl2EnFZUupvX6M3IEuVxtlcRkZ3MQP8/lUN/kvGzSK5xjAHNMt0tUQ4uZJm/vAYFM1CeOYxrHJu2cEAda16mHQzNURflkU5ye3T/61Z4NX73/AI9jtYkE5rOBqkSWIDi4jx/eFdArDrXO2/NxH/vCugVgefSsK26OnD7MSZWkVlXHNPRSsaKeuMGmE89aUHpms76WNra3LCnCj61yt+cX759a6gH5Bj61y+ojF89XR+IzxHwob1FPtWKXKnPB4NMHSnxD98v1rolsckd0amfl56iseVjJcOx9a2sqQfWsZhiRvrWVLc3rbIenFOdjt600GkY8Vsc50GksRAw9VrQ0yW4W1eOOfUEXeSRbRgj86ytMP+iuP9mrmnRzTQyMsMsnz8sLny16elcy+JnVP4EWrwXH2K53y6gcxnd54wDxXKWMmzU7Z84xKv8AOumuopfsku6KMDy24+07z0rkUbbMjejA/rWsVozFuzTO/wBRy8MiZJzVfRPFf2J0gu5TDLGNiTMCyuvZWH9adeOGbqeea56/sjJlwOe9c1B20Z24mPNqj1a38RRXEYYQrJ/tQTIwP5kGmXfiCKFCW8i3H96eVc/gqkk14m0TxtxkU35j1JrrOA7zW/HQEMltpju8j8PcsMceijtXGCTzGy0o/Gq+zNKISxpgbwGYSQenpVaEE3jbJLMdx5/f6HFZAuJkVlWRgG6gGoiSetZxptM0nUUkdlDEQpaS6syT0EbE4/lUOoII41DOGdj93BFcosrp0Yj8asw3zI4L/NjuecVfKzO5duubdicnAxWWpq5dX4mj2RgjP3iRVIVSEWLc/wCkJ9a3VBwea51WKsGHUVpxalHtAkyp/SsqsW9UbUZJaMv9c5JpcjeOaqfbrcg/vBzSHULcNkyZHsKy5Wb88e5pqMIP89q5zUx/p7Vcl1pQm2JST6ngVkySvNIXc5Y1pSg07syrVIyVkSg1PbDM4qsDVqzbbIx744rSWxhD4kXAcr3rPmGJm/Orgf5j+lVLk/vfwqIbmtTWIzNITxSZoPStTA2rGeKK0YtIoJGMZ5ptrqtrZMwkt4p2JzuOazrSLzUYfxDpVeRP3hBGMVnGKuzaU24o6SXX7Sa1eNLaGMspHA56VzTHvQEApGq0rGTdzvVjEyI/qgPH0qddM8wAgH5uMVFpsimxt2zyY1z+VdVoL28aTX1woaOHhFxncwBJ49gD+OK85Rblyo9aUkoczMSPwCZoVub24jsbZz8rSjLv7Kg5NbNj8NNOZQyaPqN3/wBNLu4W2Q/RQC2PrXc6XpcxaO/vDv1CdNzOw3CAHoienv6mugCFFADZ9z3rvjHlR5k5czueat8ObFV/5FezbH9zVJg36risfVPAmiW8Ds9nqulS4+V5GW4tyfdlGVHuRXql1q1jY3Mdtc3apNL91SDz+XSrZUMp3EVRKPj+kNLRiqEMpcUuKMUAFOo2nrg0UAFBopdrEZCnH0pCIyKSnUYpjExSgUUooAcDUkMgjky3Q1GKUjIpNXBOxodt2eOtUpJPMlJ7DgUz59u3edvpSgYqVGxUpXFopKTNUQaOkf61veq14uy5YVZ0sc1HqQAnyO9ZJ++bNfuypTGp2aY1amJ19hKRp9o3by8E/jXR+HdShMD28rhRDOWkz2jdNm7/AICcE+2a5TTmL6NAP7m4H86ika5tLhLq3YrIO+OCO4I7iuSFlNndUvKmrH0zDLuiV8feAPWkeRmjO0gMRgHrj8K8Z8MfE5tMRbS7i8y3XgQu+Cn+457f7LdPWvQrPx14ZvVB/tMWrn+C6Qpj/gX3T+ddRxlzT9C8i+kvL64F3OWyjEHAHqQc4PbjittnAbcSfwrKHiPw+ELPrumYPf7StYurfEHwvbwtCt414SMFLZSQf+BcDH40WA+caKSlqhABkgDvXQ2NnawW4lcb3PeueB2sCO1bFndB/vNg+vpWdROxdNpPU341R03NCQmOhxVd7eLeGCIuf4aqCQDhHJPc9Kie58sZMhPH8J/rWCizocl1Lkz20AYyHaR0AXBrHudXZwyQKAD1YiqNzcNO+B9wdBUQraNNLcwlUb2Dr1oxRRWhkGKMUUtABS0lGaAHUUmaM0ABNNJpSaSgDT05gignpUepOGk4qiszoMA8UjSNIctUcnvXNXP3eUXNIaKQ1ZkdP4aAmtjDkbgxOK61NIimUKwNeXwXM1rIJIJGRxyCK6jT/Ht3b4W8t0uB/eB2t/hXLVoycuaJ20q8VHlkXdV8KZJaLGfQVy9xpt7ZsRhwB6V3MPjrRrhcSxzwk9yu7+VWU1rw9cplr2HJ7PkVMZVI6NDlClPVM8yLzqfm3fjWlpl5pcUoOpQXUijtGw/rXeN/wjs4ybizx/viorjT/DTJlRasfXzQK09t3Rn7Dszh7fwvrl0haLTLgj3TBP4GsuWKSGRo5UZHU4KsMEV7n4b1n+2X8q8hSK6ibCkMODjt3FSeKfDula3DLDNbrBfxgyfaFGMJkfMeBn8M4rRVH1MnDseC0oZl6Gus8S+ANX8Oobh41ubLtcQHcAPf0rkyK0umZ7D/ALTNjG449qa0sjjDMcelNoosguPij3uFzgVrx2kIhHAB7k9ax432NmpftDYwGP50mmA67RVb5f5VWpXcueTmkFNAdr4J+H1z4q/0qeY2unhtvmbcs57hf8a9k0n4e+F9LtfLXTYrkkfNLcASMfz4H4Vy3hLxnplj4dsrRSF8uIKQem7v/WrWo/EzSreM+VMxK+icMaxc22aqKSOb+Kvh/QNNsILnTYIrW583aY4xgOpHp7GvKq2fEniO58RX5mlbES/cQdBWLWsU7amcrX0FozSUUyQopQpY4UEn0FX7bQtVu8eRp9w4PfyyB+ZobS3HYz6K6OLwN4glGfsO3/ekUf1ol8C+IIhn7GG/3ZFP9annj3Hyvsc7SVsN4V11Tj+zLgn2XNZ91Y3dk+y6tpYW9HQimpJ7Cs0V8UmKWiqENxRinUYoGNo5p2KKAO30nUrgpbaqsoS4DlGMY6H3r1jTtUXxBp5jDRNcKp32+7HmD1HHNfPum3ptpfKkb/R5SBIPT0P1Fdlo/ie6sL6AM3lBTmFgeCD168YNYSjZmsZXPTLfUp4TJZCIywKm1kuecqRwC3Rhnv25rifF3w3jNm+r6CwZW+eSzHJQHn5cV02t6tb61ocl3Cym7jAHlLgEep9xiuO0jxXL4dkieErLCwaOePHTnjB+n86UW+g5JW1PN2UqcEYI9abXXeMU02/uJdT0iMpb+ZtcEY56/rz+VchW8XdGTVgpMU6lVSzBVBJPQAUxDQK7DRPh1q2uaHLqUJERU/u4ZEYGQeoJGMV1XgP4YtL5ep65EBghorRu/u/+H5167OYLCzLzyRRQpzlyFA9Khy7FJdz5W1DTdQ0a6a2vIZbeT0PRh6g96onJPJzX0Bry6PrNu63H2W4szyGMoUqx/uHtXievWVlYanJDYXTTwjuwGVPoSOD9RRGVwlGxl0tJRVki12PhPwxa3u281XcYD9yEHG/6n0rkrZFkuokb7pYZ+lek2M37tdvHGAB6VhWm4qyNKcb7nX6Zp2l28e20tYISoHRR/OtFosDBcD2Ark4rloznb+JNXD4usdPX/T7lFHYDk1zLU1ehtmEEdHJphRhn5D+JrmLz4paNAp+ywXNy/bgIv5nn9K5PVPibrd8rR2yw2cbd413Nj6n+grRUWyXUSPRr7U7LTIDLd3EMKjpuPJ+g71xmrfEDTpI2jtrNrlugaQBV/wAa89mnmuZTJPK8jnqztk01UJrWNCK3IdR9Ca7uPtd1JP5UcW852RjCj6VDVqGwuZ/9Vbyyf7qE1oReE9cn/wBXpdx0zyuP51rdIizZiUVqXPh3WLXPnadOuBk/Ln+VZjIyHDKVPoRTTT2BprcSiiimIStCK9We2jtrg7Wj/wBXL6D0PtWfQaTVwudBp2u6npBRlIa3Jwd4yjjuPepb2e2vHle2VoPMw2xiCoPcA9x+Nc2JZAmwO2zOdueKQSOowGIHsanl6lcxsS3W3TWiZxlh8yjuQeD/ADrHpMk9TmlppWE3ccgDOqlgoJxk9BXvfgzwBpmgLDqMji9u2QFZSMoue6/4mvAq9A8IfEm40azXTdQLvarxFKvLRD0x3H8qJXtoEbX1PXte8QS6TEtvaWUlxcMMo5GEjzxya8/1nWSg/wBNnludQB5R2yq/QcAfzqnqvjKbVLj7NYSJIkhHCty31PWuYutfGlO6Wnk3F4fvTkbljPouepHr0rGzZpdIh8Ty36vG9yFg87LrCOGx/eP/ANeuaJJOTUtzdT3lw9xczPLM5yzuckmoq3irIzbuwooopiFBKnIOD6irMep30P8Aq7qUf8CqrRSaT3AsTalez/626lb23GqxJY5Ykn1NLRQlbYBMVcs9MvL8E2ttJKF6sBwPxqpXT+HPF7aJZS2clsJonYMuDgqaUm0tBxSvqOtfA2qStmYJEg565Jrs/DvgvTow0k8TPLHyG+9+hGK5SX4iaqfliWJFB43fMaor411zzGZbzbk5IHArBxqS3NlKnHY97srWzslRFt40jA6SEZ/LFWL+7u4gFsbWPYACWJ4I9h614SnjPxHKFWK6QbRjKgEn8TVGfV9ZnLPNqc+Twcy4H6UKD2BzW56P4j+2XkB/tPW7awTkiLO5se/Nebare2UieVG5uNnSQgAfhwDWbLdDdiaVpiOoB4P41Td/MbOAB2Aq4U7ETqcw2iiitjMSiiikIKMUUtMAxRRRQMKKKKAHI7ROHjZkYdCpwRTT1oooAKKKKACiiigAooooAKKKKACkxS0UAJijFLRQITkdKOTS0UAJiloooGFFFJQB/9k=\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a table topped with a variety of items, including a wooden box, a brush, a bowl, a jar, and a towel. the table is black, and the items are arranged neatly. the brush is made of wood, and the bowl is made of wood. the knife is made of wood, and the towel is striped. the jar is made of metal, and the lid is on the jar.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a martini glass sits on a bar, its contents neatly arranged. the glass sits on a black coaster, reflecting the lights of the city lights in the background. the bar is illuminated by a warm glow, casting long shadows on the wall. the glass on the coaster holds a lemon slice, a testament to the refreshing nature of the drink. the overall atmosphere is relaxed and inviting.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a plate of spaghetti with vegetables, including green leaves, green beans and bacon crumbs on the plate. the plate is white and sits on a black table. the spaghetti is yellow.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a blue bicycle is parked next to awooden fence. the bike has a black seat, a black kickstand, and a black tire. the fence is brown and the grass is green. there is a small green bush and a small green tree in the background. the bike has a light on the front and a light on the back. the front tire of the bike is on the ground and the back tire is on the fence. the bike has a black pedal and a black pedal on the bike. the bike has a black seat and a black seat on the bike. the bike has a black kickstand and a</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a large sign in the shape of a crown stands proudly in the center of a city square. the sign is illuminated by the reflection of the sun on the water, creating a vibrant display. a tall building casts long shadows on the ground, while a flag on top of a building waves proudly. people stroll along the sidewalk. the sky is clear and blue, with fluffy white clouds drifting above. the reflection of the city in the water is a mirror image of the city itself, showcasing the beauty and diversity of this urban landscape.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a leopard sits majestically on a tree branch, its eyes open and its mouth closed. the leopard&#x27;s coat is adorned with intricate black spots, and its eyes are a vibrant blue. the tree behind the animal is tall and slender, its branches reaching out like a welcoming embrace. the leopard&#x27;s whiskers are white. </p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a man stands on a red track, his leg raised high, his shoe firmly planted on the ground. the track is lined with white lines, and the grass is green. the man wears red shorts and grey and white shoes, and his socks are black. the man is running towards the finish line.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a group of people walk down a street. a black and white sign hangs from a building, while a brown sign with gold lettering advertises a business. a woman with a pink hat and a woman with a black backpack walk side by side, their backs facing the camera. a black and white sign on a pole and a black and white sign on a building are also visible. a woman with a blue jacket and a black backpack walk on the street.</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def render_inline(image, resize=(128, 128)):\n", "  \"\"\"Convert image into inline html.\"\"\"\n", "  image = Image.fromarray(image)\n", "  image.resize(resize)\n", "  with io.BytesIO() as buffer:\n", "    image.save(buffer, format='jpeg')\n", "    image_b64 = str(base64.b64encode(buffer.getvalue()), \"utf-8\")\n", "    return f\"data:image/jpeg;base64,{image_b64}\"\n", "\n", "def render_example(image, caption):\n", "  image = ((image + 1)/2 * 255).astype(np.uint8)  # [-1,1] -> [0, 255]\n", "  return f\"\"\"\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"{render_inline(image, resize=(64,64))}\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">{html.escape(caption)}</p>\n", "    </div>\n", "    \"\"\"\n", "\n", "html_out = \"\"\n", "for idx, example in zip(range(8), train_data_iterator()):\n", "  caption = postprocess_tokens(example[\"text\"])  # detokenize model input.\n", "  caption = caption[len(\"caption en\\n\"):]        # strip prefix\n", "  html_out += render_example(example[\"image\"], caption)\n", "\n", "print(\"Training examples\")\n", "display(HTML(html_out))"]}, {"cell_type": "markdown", "id": "5a55c1c1", "metadata": {"id": "N2BwpXkfI8OT"}, "source": ["### Define the training and evaluation loops\n", "\n", "Define the training loop to train the model on the provided dataset, and the evaluation loop to look at all of the examples in the validation dataset and make its predictions.\n", "\n", "#### Defining the training loop\n", "\n", "The `update_fn` function defines the training step. During the training step, the loss per example is calculated and stochastic gradient descent (SGD) is applied to the trainable parameters.\n", "\n", "Recall that earlier in the notebook, you included flags in the `preprocess_tokens` function that included `mask_loss`. You'll use the `mask_loss` flag here to exclude prefix and padded tokens from the loss. Without it, the loss calculation will be skewed. You also need to normalize each example, since each of them has a different number of tokens. After the prefix and padded tokens have been excluded and the examples have been normalized, you can calculate the loss per example.\n", "\n", "The training step also includes a function to apply an SGD to optimize the training.\n", "\n", "#### Defining the evaluation loop\n", "\n", "The `make_predictions` function is your evaluation loop. The evaluation loop is fairly straight forward with one notable change. If you recall from the beginning of the notebook, you only have 90 examples in your training data set. This is a very small amount of training examples, and your model ends up not having enough examples for the batch size when you run the training. This means that in the evaluation loop, you need to pad the batch by repeating examples.\n", "\n", "To make sure that your evaluation loop only counts actual examples and not the padded examples, you have to apply a mask to the padded examples that excludes them from the output."]}, {"cell_type": "code", "execution_count": 10, "id": "ff9e0a81", "metadata": {"id": "dwUV_imW3WQJ"}, "outputs": [], "source": ["# The main update_fn using a simple stochastic gradient descent (SGD).\n", "@functools.partial(jax.jit, donate_argnums=(0,))\n", "def update_fn(params, batch, learning_rate):\n", "  imgs, txts, mask_ar = batch[\"image\"], batch[\"text\"], batch[\"mask_ar\"]\n", "\n", "  def loss_fn(params):\n", "    text_logits, _ = model.apply({\"params\": params}, imgs, txts[:, :-1], mask_ar[:, :-1], train=True)\n", "    logp = jax.nn.log_softmax(text_logits, axis=-1)\n", "\n", "    # The model takes as input txts[:, :-1] but the loss is defined as predicting\n", "    # next tokens txts[:, 1:]. Additionally, mask_loss[:, 1:] indicates which tokens\n", "    # are part of the loss (e.g. prefix and padded tokens are not included).\n", "    mask_loss = batch[\"mask_loss\"][:, 1:]\n", "    targets = jax.nn.one_hot(txts[:, 1:], text_logits.shape[-1])\n", "\n", "    # Compute the loss per example. i.e. the mean of per token pplx.\n", "    # Since each example has a different number of tokens we normalize it.\n", "    token_pplx = jnp.sum(logp * targets, axis=-1)  # sum across vocab_size.\n", "    example_loss = -jnp.sum(token_pplx * mask_loss, axis=-1)  # sum across seq_len.\n", "    example_loss /= jnp.clip(jnp.sum(mask_loss, -1), 1)  # weight by num of tokens.\n", "\n", "    # batch_loss: mean of per example loss.\n", "    return jnp.mean(example_loss)\n", "\n", "  loss, grads = jax.value_and_grad(loss_fn)(params)\n", "\n", "  # Apply gradients to trainable params using SGD.\n", "  def apply_grad(param, gradient, trainable):\n", "    if not trainable: return param\n", "    return param - learning_rate * gradient\n", "\n", "  params = jax.tree_util.tree_map(apply_grad, params, grads, trainable_mask)\n", "\n", "  return params, loss\n", "\n", "# Evaluation/inference loop.\n", "def make_predictions(data_iterator, *, num_examples=None,\n", "                     batch_size=4, seqlen=SEQLEN, sampler=\"greedy\"):\n", "  outputs = []\n", "  while True:\n", "    # Construct a list of examples in the batch.\n", "    examples = []\n", "    try:\n", "      for _ in range(batch_size):\n", "        examples.append(next(data_iterator))\n", "        examples[-1][\"_mask\"] = np.array(True)  # Indicates true example.\n", "    except StopIteration:\n", "      if len(examples) == 0:\n", "        return outputs\n", "\n", "    # Not enough examples to complete a batch. Pad by repeating last example.\n", "    while len(examples) % batch_size:\n", "      examples.append(dict(examples[-1]))\n", "      examples[-1][\"_mask\"] = np.array(False)  # Indicates padding example.\n", "\n", "    # Convert list of examples into a dict of np.arrays and load onto devices.\n", "    batch = jax.tree.map(lambda *x: np.stack(x), *examples)\n", "    batch = big_vision.utils.reshard(batch, data_sharding)\n", "\n", "    # Make model predictions\n", "    tokens = decode({\"params\": params}, batch=batch,\n", "                    max_decode_len=seqlen, sampler=sampler)\n", "\n", "    # Fetch model predictions to device and detokenize.\n", "    tokens, mask = jax.device_get((tokens, batch[\"_mask\"]))\n", "    tokens = tokens[mask]  # remove padding examples.\n", "    responses = [postprocess_tokens(t) for t in tokens]\n", "\n", "    # Append to html output.\n", "    for example, response in zip(examples, responses):\n", "      outputs.append((example[\"image\"], response))\n", "      if num_examples and len(outputs) >= num_examples:\n", "        return outputs"]}, {"cell_type": "markdown", "id": "bf6ad946", "metadata": {"id": "n9r9V1jwJvu9"}, "source": ["## Tune the model\n", "\n", "Now that you've set everything up and taken a look at the training data, it's time to finally tune the model. The code below runs the training loop for the model for 64 steps and prints the learning rate (`lr` in the printed output) and loss rate for each step.\n", "\n", "Every 16 steps, the model prints what its predictions are at that step in the training. This code prints out predictions for the same set of images so that you can see the model's ability to predict descriptions improve over time.\n", "\n", "At earlier steps in the training, there's likely issues with the descriptions, such as repeated sentences as the model gets stuck in its predictive loop or unfinished sentences. The model's predictions become steadily more accurate as training progresses. By step 64, the model's predictions should closely resemble the descriptions provided by the training data.\n", "\n", "This process takes around 15 minutes to complete on T4 TPUs."]}, {"cell_type": "code", "execution_count": 11, "id": "f78b3fea", "metadata": {"id": "067wj_6bZAG3"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["step:  1/64   lr: 0.00500   loss: 3.2539\n", "step:  2/64   lr: 0.01000   loss: 1.9291\n", "step:  3/64   lr: 0.01500   loss: 1.5984\n", "step:  4/64   lr: 0.02000   loss: 1.6361\n", "step:  5/64   lr: 0.02500   loss: 2.0249\n", "step:  6/64   lr: 0.03000   loss: 2.6033\n", "step:  7/64   lr: 0.02998   loss: 1.9704\n", "step:  8/64   lr: 0.02992   loss: 1.6470\n", "step:  9/64   lr: 0.02981   loss: 1.5255\n", "step: 10/64   lr: 0.02966   loss: 1.5204\n", "step: 11/64   lr: 0.02947   loss: 1.3989\n", "step: 12/64   lr: 0.02924   loss: 1.2505\n", "step: 13/64   lr: 0.02897   loss: 1.1247\n", "step: 14/64   lr: 0.02866   loss: 1.0750\n", "step: 15/64   lr: 0.02831   loss: 1.2703\n", "step: 16/64   lr: 0.02792   loss: 1.0917\n", "Model predictions at step 16\n"]}, {"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman&#x27;s hand rests on a white wall, casting a shadow on the wall. the dress is pink, and the sleeves are long. the hand is on the wall, and the shadow is on the wall. the dress is flowing, and the sleeves are gathered. the wall is white, and the shadow is long. the hand is on the wall, and the shadow is long. the dress is pink, and the sleeves are gathered. the shadow is long.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman in a white dress with a pink flower on it sits on a stone wall overlooking the ocean. the dress has a floral pattern and a white bag on her hand. the sky is blue and the water is calm. the boat is on the water and the sails are white. the dress is flowing in the wind. the woman is wearing a hat and holding a bag. the dress is long and the flowers are pink. the sky is clear and the water is calm. the boat is on the water and the sails are white. the dress is flowing in the wind. the woman is sitting on a stone wall. the bag is</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a person wearing a red blazer and black pants, with a black bag on their hip. the bag has a silver zipper and a white writing on it. the person is wearing a white top underneath the blazer. the bag is black and has a silver zipper. the jacket is red and has a silver button. the pants are black and have a silver zipper. the person is wearing a white top underneath the blazer. the bag is black and has a silver zipper. the jacket is red and has a silver button. the pants are black and have a silver zipper. the bag is black and has a silver zipper. the person is wearing a</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman in a pink shirt and blue jeans stands on a stone staircase. the jeans have a hole in the knee. the woman is wearing a white cardigan and a pink bag. the bag is on her arm. the steps are gray. the wall is gray. the sky is blue. the ground is gray. the woman is wearing a pink bag. the sky is blue. the ground is gray. the wall is gray. the steps are gray. the sky is blue. the ground is gray. the woman is wearing a pink bag. the bag is on her arm. the steps are gray. the wall is gray. the sky</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["step: 17/64   lr: 0.02750   loss: 1.1208\n", "step: 18/64   lr: 0.02704   loss: 1.2137\n", "step: 19/64   lr: 0.02655   loss: 1.0639\n", "step: 20/64   lr: 0.02602   loss: 1.0356\n", "step: 21/64   lr: 0.02546   loss: 0.9214\n", "step: 22/64   lr: 0.02488   loss: 1.0569\n", "step: 23/64   lr: 0.02426   loss: 0.9526\n", "step: 24/64   lr: 0.02362   loss: 0.6038\n", "step: 25/64   lr: 0.02296   loss: 0.8039\n", "step: 26/64   lr: 0.02227   loss: 0.7570\n", "step: 27/64   lr: 0.02156   loss: 0.7252\n", "step: 28/64   lr: 0.02083   loss: 0.7221\n", "step: 29/64   lr: 0.02009   loss: 0.7316\n", "step: 30/64   lr: 0.01933   loss: 0.7288\n", "step: 31/64   lr: 0.01856   loss: 0.6435\n", "step: 32/64   lr: 0.01778   loss: 0.7477\n", "Model predictions at step 32\n"]}, {"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman in a pink dress stands on a white wall, her hand on the wall. the dress is pink, and the woman&#x27;s hand is on the wall. the dress is long and flowing, and the woman&#x27;s hand is gripping the wall. the woman is wearing a bracelet and a watch. the dress is pink, and the woman&#x27;s hand is on the wall. the woman is standing on a white wall, and the wall is white. the woman&#x27;s hand is gripping the wall, and her fingers are curled. the woman is wearing a bracelet and a watch. the dress is pink, and the woman&#x27;</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman in a white dress with a floral pattern stands on a stone wall overlooking the ocean. the dress is long and flowing, and the woman is wearing a straw bag. the sky is clear and blue, and the water is calm. the woman is standing on a stone wall, and the dress is flowing in the wind. the woman is holding a white bag and wearing a pair of sandals. the dress is white and has a floral pattern. the woman is standing on a stone wall, and the dress is flowing in the wind. the woman is wearing a straw bag and a pair of sandals. the dress is long and has a</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a person wears a red blazer with a black fanny pack on their hip. the blazer is open and the person is wearing black pants. the person is standing in front of a green plant and is holding their hand in their pocket. the bag is black and has a zipper. the person is wearing a black top underneath the jacket. the jacket is red and has a button on the front. the person is wearing a black belt and a black fanny pack. the jacket is open and the person is wearing a black pants. the bag is on the person&#x27;s hip and the zipper is on the bag. the person is standing in</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/2wBDAQkJCQwLDBgNDRgyIRwhMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjIyMjL/wAARCADgAOADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDiLjTWFnvztXIre0aILYjHWlkSK70QF+CozirunRoLRQvQVAywFJjx3p8QYAA88c08AEYpdwXimIhvJJY4gYV3OSABUy/OgOPrVKzu555m3xbUBO0/StKM5HNAChiLSY46DFVGy9uRyC3cVdniMmmTqpwWwM1HHEqRqnUKMZpAKm0Iq56cZrmtdiZLsuZchv4PSup8sYz2FcffOLm+lkwQAdoz7UwKcalh0pxj6A1MiZqQpjAxmgDD1ZQJEHtUKrmBMCp9cYRzR57iktjus0LdamRUT174MS40vVIe4mRvzXH9K7TWbkgpbp95685+D8zJqOpxkfK0SN+RP+NeiWsX2zWJZnGVj4X61SfumsV1IdYmGleH9gP72XgVwtnp0uo3GFHy9WY10viBpNY1wWcJPlxfKT6etJqzx6Hpi2tsP38oxkdah6u/RHTBOMUluyzaRQJobQwNuWKVlJ98CqWkt/xOY4u53Y/KrHh20lg0CcTA7nl38+4/+tVTSY3fxfCijgBmY+gxT6I5KitNo66/GIUijHHeud1J/IwrdDxXXS7ACBgGuX1wKwKzJlSeHXqKsxPIbz5b6fPTzG/nVCTmZTWrqsDw6hOrf3yRjuDyKy8Dz1pDK89x5dwsOOq5rTs3CxkH1qhcwB3WQfeHFVr69NnA5HU9KBMd4h1PyYjEjckc4rk1vixD7yJB+tJdXT3QJYkkmqytGq7CnzZzuqNxWPYIZIIhDZBSdwz07e9TWu0b0HGGIpIomiuTJnKFcdOlRWEqzTSsp4LHFUUTNeRwT+VIwDE5H0q75YZAR3rFmsWutXLY5VQRW9bhxAokOXA5xTARFVVEajA708KAacFx0p3PcUAK/Gny5HcU2FP3Y4xxUpG+3cDpkZoTAHWgA28YrlNVgEN+wUcHmutzXOaorPfPuHQYFAGfFF8uTUgjzUwULGM0o68CgDjvFCkXsAB7VLaqUsoSeTR4oyNRhBHatrTLaOXToWZckCplsVE7z4UWpWw1W+ZSCWSJT+BJ/mK9G0+BorSSQD52yRmsHwdax6b4JgeVcec7y7ehbnA/QU7UNS1AWkjBSkTEKgA6fSmnY3pwcolu1gtNJtp7ueYSyk5bZzz6ZqSWazaVZTYGW5wMAIXZf8KxZbeRbGCNllZVYNIYgGOc56Eit23gjiZbr7dcGIJgQldo/LrT5ZLoa88Hd3ux0T3EyTrNbiFdoKgsCT+ArF0dTH4rnwOTBx+YrbgufNmCH+NT1rOthBYa9c6peTJBaQQfO7nAyTx/I0HLU+I6NrH5dzudxrmNZwJDGeQaD8SPDF/KYLbVYmfoOcZ+lU7pluX8yOUOp6YNBnY8+18L/arAfwqKwSo88fjXR65H/wATSUn0H8q55023Ix3BoAZKCBmud8RTFXSMdxXTSIdhJHSuS175tR2+iigRkQxNMyxr1JqO7jEMpVfStrTbURjc33jWfqMQ+0ygdqSQHsoGWxnjHNUNNjEbSBem8/zq8FOCAQPcVn6Zu3OM5+Y8/jTGaRG1949OaswH5MnvTVUYFTZCqeOgoAUMvUHing8VBC3m2oYKUz2NTocj6UASRANbM3oaaAMZpyuEtZCeFzUcMyTJlORQAu3d0OOOtZmowjzVbrkYzWtgAVRvlyyUAZMkeQKdHFyKsmPdwBVmO24BoA898Vj/AImsY9BXVaHaqdPtS/3SBn6Vzfi5NusoP9mu90axL6HasB/yzBqZIaOwbXroW0S2cMH2aHKKhG4qBwPxwOnWmXeoatZWNze3keZBD5kMTkAAkcZA6VU00tZ2VoqRvJM0rzAL6gbAT7YNXphcanbt9oRpgRs2r0/P0rto0/duzmrVW3yroM0xZ7fSbaO5uvPnCDekOcFu5J61LDe6lDOd9qHtMYK7xuX3HPP0q5Y20drA8AEaorfL9MUl3KAuEdM/lWzSkrMyhJwd4hDqMU+o2n2VBJH5m12zyhII5H415p8YtSuyDpMZKwkiVsfxdv0rr/L8vUre6w0Mqyrl0PDDPQ+ornfi9p7Ne291H95QQeOorhrU3A7qdVVGtDyfQ9EuNU+WFCSPSur0+/1fwtOqXDSSWmcEMclR7V2fw206CPQ57gJ87SEEkdKzvFyLI7RkDDHFcbk07nUoqUbMTU50urhbhDlZEU8VhSf8fS/jV9EEUMcS52ooUZ9BVCTPnk+ma6DkJ2AKnNcRr4xq7Y9BXZyuBDnpxXGa5zqjn2FNCJLGTcQDVTUoMTu396n2B/e/SrGoDcM+1AHpYG0HGRVHSidp57nmtEjajA85FZ+kjchx70DNNSyEk8inxXkMk3l7hk9Kr3bNBbSuP4UJFYGjw3E+pRTMSVZvXpSuB15njiYI7AZ6CplwRkVkahZSTajE4JCYxWtBD5UKruLH1pgRXkpjsnHYnGaW0iMdurYwCKneIGBQ4yu7OKi1GZhasyrwnQLQBYwOKqXK7pB7Cp4WHkoSDnb3pHXfJnFAFZIuelXI0ytIkXzHNWFj2igDzDxl/wAh9R/s16pokOPD1pgf8sxXk/jNj/wkuB2UV7DoPGkWSgZHlrTSuwbsatnYFYvOIKh4xEBn0LZP61ZhWYR7UU7fpxVm+f7PaRljgbeajt5ZJ40QcqVzx0ruWi0OF6vUz54ZYv8AWHb6N1H0NUZpJ1zugWVR3Rv6V0E0LnJZ1jXHQ9azZrW3ZiWl59UXBq0xNGM5iYboZ2gcEfJJ0J9OasePbQXllE5HJUH9Kg1e2T7O0cUkjyYyNyg4rU1P/TPDtlKeS0K5+uK5sTqkdGG0kYHgIeXpF5F/dkz+Yrn/ABR818oHdv610vhNPLbUI/of51z2vJv1eFfWUfzrzJ7o9OHwszn4NQPGMqR3qw5G41WmfEiD3rqOMS6jBhIrg9aONUkGfSu+mIKYzXAa6v8AxNJjQhEFs+1we+av3bZjWsyE/OKu3j/u1+lMD1SUgxSEdlJrM0xisa4FPuNTSM3EIjY7IyzN2FV9PuFSFT1yBSGbUy+dCyHBBGCDSWFnFavGEPPSnxFXQ/TJpsJAuFwMc0gNOXATc3anx/dzUTMWwMZB604HbTAWacGNYl5cnOPanKg8shh19aI1jTMxHztxRFIJlY4Iwcc0AOHzHpSxoCTSKMHirNtFuVm96AEWPmpMACniMimutAHk/i/B8TOfQCvZfCu27sLAp93YP5V4r4uP/FTTewr2H4ZmQ+HYpXUkqpCinHdClszrr0q0sceMjqeOtYt54l07R/3fmCWYcbY+g9quanvkt5AWKsU52n2NZHhnwzax7bq7jLzldwDc4r0IpJXkcDu3oTW+qXep4dNIkVDzvkf+WaW7MUBzLAcDltp5X61tX9yYY/LgAVjxkdqxPIuoiXkw+ecnrQrPoVqihd39jcqI7adWJGCpOCK1IFEnhiBSc7Cy/qaxdRhspEYughlPAkXGQfatLQSW8P3MbNu8u4YA+xANZV17hrRfvlXQo/K1C8HZo/61zOrLu16Af9NM11+mxlb25bsI/wCtcjcfvfECn+6xP6V5UviR6kfgZjMfmb61RmlVrhFB5B5rQcD5/XJrOkgCXiuP4q6DkHSg7QRXEayc38/1rupnWNMucCvPr+YS6hO45UtxTQiO1I3VLdnO0VURiJMip52J25pjPQdWvo10qRIwXeb5RtHNQWDHyhlSOMVvWtrAm7eilFJxkVYW3geTCxgDjnFQ7jRUtbjEJXBJxinxSn7SCRVxYLcyEAY29almtoFgV0ByxxxSux2Q1LkIGyc88U9rgZA9RnNU0hXcQ2RgZo8sswG44p8wWNZGzboacpGOKoNIY4UVWzt60jXyW67pchcZzTTE0aaMCK1tPh3W5OO9czbana3D7YZAzdgK6/R/msBxzk8UxAbfjpVWVMVrFTg1nTrgsTQB4x4mTzfFsif3mC1774YsItP0OGFOFCivAtaLP4wcqCzeYuAOp5r6I0pnGnR5TAEa4J6k9/wqofGkKXwNjmjRnUMMD0NJauZmuXixjfsB7AAf/XrG1u/ntZAyscYOTjoa1tKtGs9Ht4nbDEGRyepLcn+dd0lZHFHciuJba2bc5eWQdu1c/fz6reSO1umyMHAVh1reurhIwVSAvjnLCsefU52BVbYAdM5xVRQmcjqct20qQXCADOSp7/T/AD3rq/C2Bpl8mSRvVufp/wDWrB1G4b7R5ag5Ay2RnH4/lWx4Uk3Qakuemz+tRiPgZpR+JF+CFzHcuk7xk4UbQOfzFciqSR6kDMoJLlRInQ/Udq7MIyaZI/8AfLEfyrjIUcalGrMT1NeQ376PWS/dsxZsh3A/vGqMzf6XGp681duHCySezn+dZRlDaquTxg10HIV9eEr2BMQOQe1cK2VJDDmu21XVls5Nm0NkVy7rFdzvLjBY5wKEBRUc1JPkbc+lXlsASCAasnTDPgFTnFMR6Ui/I4xnLGr0LRoM46DiqdpmaEMOA3NWEjJAJYAUhjpPLDDIxu71ZKBoVC9jUDW+4bt3ToKmiKpGAzYx60ARSxDKk+lRGIYIzipZrqKOItIwwKx7a8e+1ArGT5YPWloBpQRbN5I7Y5qrfoRZTEn5cZNaZXgc8jtVS5i86OSBsjepFAFDQ7eC1tTeAAyMPl9q7LwtNJK8288AZxXLRwC2sEgToOprpPCuVacA84FKJTOmYAnFZF+xBKitMFgD61l3I3zH3qiTymwsZdR+J8Fsq7gbhWb/AHRyf0FfQMM8c0LeWcKhKYHtxXl/hXS5NP8AHGoahOoIFs7Rn0PH9K7HwjctdaAZWfczzSNn23sB/KnS/iIdRfuWy5c2ou5VhHALgnPpnmtOZwMg8DtUVpGfPeRhgKMA/WmXUiKCSTiu96uxwLRFOYySEiPJPpWbdtDYI095Km9RlUB7+ppL+41KdTFZqtvH3Y8muM1RJvOKXF6ZVXlt3c+lWkIdc6vDOXEIZ2Y5ZmrX8ET+amrnPRo1/Q/41x9xIiH5MHHJIro/hm/mQaszHhrlRn6KKwxDtE2oq8jvZ0/0FV7KMVxA51Uceort7hw2nF1Iwehri5lMd5uxgK4yfqK8p/Emetb3WjiL6423dwuf+WjfzrGM2L0NnoDV29UzXlwQcZkY/rWTPDJGzMxHTiuk4jJ1SdprtmByOlMSWOM4zzioS/707v71Mlj3OxHSgDW0q7E8kiyOFC9DV6+1AWPltG4fccH2rn7TaoK+tXZbVWkTOSNvSiwj0GOeRgI4XxjAFW45GWXaxJ28Cs/SYWSMtKDv3Vpuof5145FSUWRI2eRTHSR87kOB6GnRtuAAPQ1YBKH1HWnuBUNsssJR0OD1p1naQWROwEZq0z5hLryRzinRSo6fNjOOaVkFyEK7TFxKPoaW5mMVo8pwzIO1TK8OC3XFQXsCm1kdeODRYLmNFrcDoEZWB612Xg2aO5W5kj6AgVx0GnxTR5wM4rr/AAVB5CXYAwMimgZ1MmFPTtWYRvlY+lXpXOapt8jcd6YirGhiu7h+N0kLIoPcnFJ4Du4H0NrFVZLmzAjuFYHh9zGtGO2SVWLjJzxRo1gLe+vGWNQ8+zc6/wAWM8n3q6S/eIKkl7Jo30JS2BPLNzWddv6DOK0bhgowO3ArEunODggD1Nd0e5wsy9RyImkmuCvGQitiuMvmgjO+4kDN94hec1vay2xSkatubqx/P/CuPltHuLlEDHc7BST71ewkrnUaRpVkmlDXNWs5Z4pCfstlH/GBwXc9l/zzW0mp3+k7hb+HbOGFjlktSOR+XJrpZLWFYo7BI1MMESw4x1GMf41moCp2N/CAK8upNzd7nrU4RhG1ild6j53hx57dXjEbHckibSvfGPxri9VvbqTUJY87RtQkD8K7HX5Ui0K6HA3D+lef6xfr/atwWB2G1RsqehzXM1qdKelznbt2t7yRDyQSDWfqNxkD6Vvamsc6rdBQN64OP7w/+sRXM3scjZOOMV0J3RwyVpNGJu3SNn1q0FLxyOOgGKpMCjnPrVh5dtvgd+tMkbbRs94iA8E1uXZEXzf3RWFayGF0c1YvL1pTjkUwPT/MfzPLTBbHSri2Fw8kbKdqjkirsbQqd6w/Me+KlFz/ALJqbBcqC0mhPAyCeop8zFUwRg1dW5GOhqGeaKTgofyoArQpuUgHr0qVLJp85OB7VXyyuWGcKOK0IrlIoRg5J5osBG1k0MY2fMOhqDUt8Vi2AcY5q99rBXkVV1C4STTph320wM61YJEhz1611Xhq5jjScMepBFcZaDdDkk9K2NImKSurHtkUAdpPNC65DgYqg11HuALjg1kzTk55NUHbeeSetAHZfbIoICxOc8irWgXYu5rh8H5AP61xbXzJEEOSBXa+F49miC4K4adi34Dgf1rSmryM6nwl27c4NY0w3tgmtO6kUP8AMetZ9zNDDCzvwMV3I5GczqnBkeWQKOnB6/SuXnvx9shSFcKZFy3frU+rzSPdGRieckL6VgTSMLhXHZgaUpWRUVqe8QJMHuXdSPnABPfvxWRqa3Y1BvImVYz2KZ/rXR4E1pHIecqGA+tYGryCGVG/vJXltHqXucv4luGGh3cc0qyOoyCBjHFcLdwYuB5jbg9uy/l/+utfxRcyMk8O4jeVx754qnLAzpYORySwPH0rF7nSnoisLcv4ZuyeZI2WUD2wQf5D8q5O8nx8oPROa9ERUTUp7Zh+7e1GR/wNa841W0ktL66tn6xkqD6jt+lbQ2sclXV3MdY5JwWHIBq4LVxAGxkUtooFo8ZOCTnNWoFk4CtnB71ZkRWVn9oVWYFdp6VLqFosZRQPmPOa17XKycqPwretrK2upEEsY9jQB6FiPH7tVGPamOg/uA/SqVt4l0i5Risvl7SMhuDWxBardQCVJM56c0ElENEvDRkfhSlrcjlP0rS8kxZDoD9aNsO3DIoBoAzRHayegp62UAGQVqzLY28oyrbSaiGnBRyxYexphdjXto1XgKahljhETBohg9amMFoGCs7hj2NTnTlljxGxP40WC5ji3tdoCxgVasdNjdmYLjirZ0O4/hq5ZaZdQoQcUNAmVGsAowEzVVrEZ4XFdCLO5HXbUT2kx6haQ7mGllvxGEBLHAru/LW0tIoEACxqFH4VkaZYn7cjPjCHd+Vat5Jtz05rooR6mFZ9DG1Bx5h9fWs+9lVbI78HI6H/AD9at3p3MuOQP1rH1aYtBIPLxsiJJz+A/rXYc5xt7Oty7SDuelULeya5v1UjCqNxFTwbYwWJGe1dJ4e0oPatdysN0zcfQVz1ZWibU1eR6XpreZo1oev7lP5CuY8Sz7biFM87SfwzVtdVktLCO3iBbaNpAxkjtg9q5S6u59SvpJCAXPGxf4QO1cEz0YNFLUbRLuIbh8wOQaQQL5cakfcORVjEuCPLJwcVCUmBOQF+tZWZpzpdSlPGXvrtl+8tocH3BBrJ8T6Wl5HbanGvEybX/wB4f/W/lXTWkMRknLEs5hYHj6U6zt7e90m5sVBOz94gI5z7VslY53K9zy/+yQwODioxpMin5XNdn9ntgCBC559KDaxEZELflWhjc5KOxulPEpqwv9pxY2S9DXTC2Xtbmn/Z2yMW1SO5nTaXOgLIRIGXuOas2etXcKJJHPNbkcYbla66Xwzc2sUksaMxjUnYed30rDt4bdo0R42LsSGXH3TQBNH4uvreNDchJ4y33kNXp/HFpG0a3NtJGD3I6VAPCllJcWrqjCNmGeeDVPxLoF3FFII5FliQcK45H40XA6O28R6TdRCVbpY1Yd61o0aQBopUkBGRtNeK2tvEbRkkt5g8ZOWTkVo6fq1zZXFubTUGGRhlc9KY7HrLW0nBaMkg9xTPs74OCUPsay08XTx6cHmhDD++vP51JF4ys55USZFjBXIY96QWNNftyphbo/8AAhmmreaghIMgf8MVTGv6dcTGNJdmCFBzkE1aMqtP5cNzHI5XJAPIpkk5vrknB3Y7nNSCUucfaBk9jWdi5L7mU7AO1Me4KkkhuKYzqtGt2j8+ZpN+cKOelOvjk4zik0RwNHSTHEjknPHHSsLUtUaS5lCgrGjYJPf6V10VoctXR6j7qRYELlsKO5rmtS1R5rOcAZEjBAxHbvU2oEzxFrqYxoPuoDXN3F/HM0cUbfu07eprd6IyuZBk8mFnPLAcV6foMlpeaFZTgAFolyB2I4P615NeFlikU9c4H412fgy5k/sLyweI5Coz+f8AWuOotDopvU7fbar1QVkx6TBDeSSxXOI3J+XbyM9s0i3mOJBioHu0z8vX61jY2uaX2eFVCpFwKheNc/6nisxtRmRjw2361JHqjMp+/wDlmjlFcssihJCIgDt6/jVaAtBdI6xhVPDY9KlS4kmSXOfuenuKzJJ7n+8aVtSm2kmGq21zb3T+TEhQ8isme6u0HMCj6Vv3RnvdFV1J+0QHDf7Qrn5Pto6p1qkTLe5El3OwyVI/CmuLuTgTsPwoNxMhw0JHvioWuWdwNyrn2osK56Hb61OMAMGHo1X7ePTLoZlgEUpJO4etcgpkhYEirsV6+ODipaGmdZHo8UMTGNvMXO5RnpWHqFu0zGAISszYD+nqDUcOqyxrgOw+laCavDNABNEwYHIdB3pWKucVpOnCKe+R16OBgisTUdGtzGxCDJmPIruNN0WQXV0ftilJzuAfrmorrww8VlG0khOJvm4zxnrQFzA0nSL2ITRQXLrHjiNhkGoru1ubXC3VikqJ3j4P5V20FqquCjnOMnjtU72ttPcFZUZjIdqj04oHc8e1TyIr5nieWBSAwVx3qI397C/nwTZYjbuVs13fi/QI4riIQwFg5II9K4W70jYM4aNge3FAzai8Ua1b2UAdzhWySP4hW/pXjyG6nSC6t4yG4MhHINcWLXUTpZdZg8SNwGXNPsruKKeL7XaEhSMvGM/pTEe33d55dtax26gRtEHXHoazWa11IFUkVJlPT3qlpOrRalZ+VDOZWgTYpZcEZPQj8a5TVmuLCaS4hJR8nAzw3Q/1Nb0p8r8grU+eCaLGuWepWsoSTe0bcltvB/GucNhqKzGdbaTyF+YsRgY/GuitvGV62nPFKP3mMbtuSvuKxTHrc2pxPJcC9tGcb8Pgge4Ndbmmjz+VplLVUGFCjGWya67wRawSaJKZFywmOD+ArmNThaPzQxy249O1df4IZk8PYjUuzTkFfQcc1y19DejqbBs1OQkjAY6GqzabHHICMEH1NXZNRijIhmhO5mwpUZ+lXRpckyeYxRIl/ibpXNc6LHPXOmuFVsMSSeENUHhljJYGRQPUV1jeXAOZS49QvFKqRXI4kRjjJB4xT5hcpgaY7OZ8kkCPv9RWa1y2DsO4+mK64WSxh2VVO5SBgisi406FgflMLHglR1oUtRuPuozrLUTFcbZlIikG1v8AGq93dw2lw8Lk7lOCKs3Gmxop8uU5PrzVa7tJJ1hlCB3UbGJ4zjoafMris7WIBqEbHiFiPXFVZL2Npl/dLjPcU9wtu3zRuPYEH+VRCa3V1LIQCc/MDVXRFmGieJ4rq5jt7shCxx81dgbWJsmIjnpivIb2weGV0QZx29PpW7o2t3ttao8MpkGMNDIeePQ1maWR32DEcMMVKkoXBGTXNWPi2C5cwXY8twcbW4roYlhmQPbyjJ5wTQBZBBYFh7g1dhv5UKjeGRTnY1ZYkCsUJ3EelTo0ZHQg+9MDqLa9sLlwZIAkgHXHFaQsbWbY0YXKnIYVxsYKKCj8jrUsWoSWxLrIyP7HikBH4nsprjUo7eNwNpyTXB6xaSqkzt823gmvQptbLOj3lsJBj/WJwwrC1GC0urKX7LcBmY58txg07Bc52zt93hKeQ4B7fnWbpUALEsM/MBW39muIvDUiNC4QSctjjGaqaLGTdFkGV3cCgLnU2NrFaXELqvBIJIHXrWVrkguDHEiqWd/mJPA49frW28M81tM9uD5dum5s929B+Fcncz+fGWhXJDbmOeg/rTjqaqVoDBpjrAwiSGZmJz8/IqmIbq2K7uD2VeSafYQ3M0ryR7vl64OM+1aapqLDaYFQHngbj+dd1O0op2POqrlm1e5i6iJRCGmTBIGK63wNqFumjC2b5ZVkLElsA+38qwNat5DZBmByhGSx5q14FsoNRupLedEYIdwJ68//AKqxrrQui9T0WGCA3BndAAq8F8VavbiBfIiVg0arnjpk1ymraVJHeRlQzQFCgAkIC474rM8Rz6tpmnxXVoy3CjEeO2D6VyHUdheCNy5BBGPwFc7Pp8gyVcqntzXBS+NL02ZWaGaIlj90/KeOlaGk+M4grBrkk5zh/ft+dMDrbBZ4XuFLOW8lgufX2qr9v1W2GHiWZR3PWobLxRb3EiZcZYkJj1PrUg16EsxmRSYjhsDofSkD2AX9rqKCO/t3idDkHkD86llgtiu6KRQrjHDcGlN/aXEJEbKRv2lenJpkr2kiRlUAGfTG33piuUbmw8vaGhklY9WQjFZ+vwmx0WS5jgkby8YUn1OKp+KPEl7p2svZW/Cqiurbic5APSql9rNxe2xjus+btUYHCkeuKd9AtqbOs+HpbWYuwJAB3GjRtCiu9JQOo3ZJz3612I1O11KI2d/GI5mGFlA4NWItAOm2qmNt0Y5wOwpWBM8m13Q3ssSjLgvznr+dVdP1u/09S0Tb9p4V+or0bWtP+2oiJjcc4z0rkX8PiaZ45F2SAcEHvQM6Dw1rSX8AW72xXJJ4PG4djW/IYWQskq7gSMV5Tfx3FtIInYyBDhJU4YYqSHXby0bd5plQHk9/xFAWPSzcPGByR71E9xK5BXBHeua0/wAUwXIPmMPlXOD3PpXTQyRT2yyxcZ5xigQqy7+GyaY9tHODuwoFT+TJgng49KeoyAGOMUxWI4o5fJa1WbfB3RhmrWmaXBDIzS2465DRnp+FOR8cqBx3pzXEqKfLK7vekOxsw2ivb+XaTLJHkkqDhvxFcXe+GRFfywCd4nBztJyPWtqO5k3b5E2vnqhp97apqLtOJX88ADOa3oStKxjWjdXOVfQ72EkKxeoxFdRuY5FlT1YMQP8AP+Nb09xc6emJWDA8Z71mSTT3APz4U9jXachTliMlvLGzFgy45Oal+H6M2rXESEg+WGBHqCP8aaSxw2ByOM1N4JYweJLu2GS7xsy4Hbg4/KsK60NqT1O8eZtQdoTDjyz8+e5PpTNW0pG8MX1vIBkwuRjsccYq5YKheTKbHY7i+c7vrVnUI/Ms5lxgtG2CPUiuI7DwBtFuJ4ysEr7lUOEk5BB61iXVjKjN51uh56x16Zf2txDIkiY3D5cAdu9Ys+nLPcSRbiZGU7EHZs9aQzgone1mws0kePu7uxrvL/Tp9f8AD8et6PIGuoRi8tkPDEDlh/Oue1jR5bWaRCpBwPlJ9hR4a1u68Jass6hnt5eJouzD1HuKBrzM9dYmjmV5fNXawJAPANatl4imIWEXCsGcY4569DWn4s0GK5lXXdDKy2lycyR4z5bHv7D+RrG8J6N/afiW3FxF5dvaN59y56KicnP1wBQDWtjU8WWzP41WFfuqsMb+2EFWtY0xLmziWP5ZlfAI5wvessan/a/iy41BM7JJmfnsP4f0xXR2QlnSSTbkowOKYnuf/9k=\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman stands on a stone staircase, her hand on her bag. her jeans are blue, and her shirt is pink. the woman is wearing a white cardigan and a pink bag. the stairs are made of stone, and the wall is made of concrete. the woman is standing on the stairs, and her hand is on her bag. the bag is pink, and the strap is long. the woman is wearing a bracelet and a necklace. the jeans are blue, and the buttons are white. the woman is wearing a pink shirt and a white cardigan. the bag is on her arm, and her hand is on the bag. the</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["step: 33/64   lr: 0.01699   loss: 0.6949\n", "step: 34/64   lr: 0.01620   loss: 0.6263\n", "step: 35/64   lr: 0.01540   loss: 0.3855\n", "step: 36/64   lr: 0.01460   loss: 0.2839\n", "step: 37/64   lr: 0.01380   loss: 0.3310\n", "step: 38/64   lr: 0.01301   loss: 0.4091\n", "step: 39/64   lr: 0.01222   loss: 0.4324\n", "step: 40/64   lr: 0.01144   loss: 0.3957\n", "step: 41/64   lr: 0.01067   loss: 0.3261\n", "step: 42/64   lr: 0.00991   loss: 0.4206\n", "step: 43/64   lr: 0.00917   loss: 0.4413\n", "step: 44/64   lr: 0.00844   loss: 0.3780\n", "step: 45/64   lr: 0.00773   loss: 0.3321\n", "step: 46/64   lr: 0.00704   loss: 0.2110\n", "step: 47/64   lr: 0.00638   loss: 0.1994\n", "step: 48/64   lr: 0.00574   loss: 0.1646\n", "Model predictions at step 48\n"]}, {"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman in a pink dress stands on a white wall, her hand on the wall. the dress is pink, and the sleeves are long and gathered. the woman&#x27;s hand is gripping the wall. the wall is white, and the shadow on the wall is long and dark.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman stands on a pier, her dress flowing in the wind. the sky is clear and blue, with a few clouds. the water is calm and blue, with a few waves. the woman holds her bag and stands with her legs crossed. the dress is white with a red and black flower print. the woman wears short sleeves and a tie on the dress. the dress is long and flowing.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a person wears a red blazer with a black belt and bag. the blazer is open and the bag is strapped to their waist. the bag is black and has a zipper. the person&#x27;s hand is on the bag. the bag has a zipper and a silver chain. the blazer is loose and the buttons are unbuttoned. the person is standing next to a green plant.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman stands on a stone staircase, her hand on her bag. the jeans are blue, and the fabric is ripped. the shirt is pink, and the buttons are white. the woman is wearing a white cardigan and a silver bracelet on her wrist. the bag is pink, and the strap is pink.</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["step: 49/64   lr: 0.00512   loss: 0.1421\n", "step: 50/64   lr: 0.00454   loss: 0.2420\n", "step: 51/64   lr: 0.00398   loss: 0.1420\n", "step: 52/64   lr: 0.00345   loss: 0.1434\n", "step: 53/64   lr: 0.00296   loss: 0.1580\n", "step: 54/64   lr: 0.00250   loss: 0.2400\n", "step: 55/64   lr: 0.00208   loss: 0.1307\n", "step: 56/64   lr: 0.00169   loss: 0.1296\n", "step: 57/64   lr: 0.00134   loss: 0.1500\n", "step: 58/64   lr: 0.00103   loss: 0.1329\n", "step: 59/64   lr: 0.00076   loss: 0.0738\n", "step: 60/64   lr: 0.00053   loss: 0.1207\n", "step: 61/64   lr: 0.00034   loss: 0.1089\n", "step: 62/64   lr: 0.00019   loss: 0.1033\n", "step: 63/64   lr: 0.00008   loss: 0.1217\n", "step: 64/64   lr: 0.00002   loss: 0.1000\n", "Model predictions at step 64\n"]}, {"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman in a pink dress stands on a white staircase, her hand on the wall. the dress is pink, and the fabric is sheer. the woman&#x27;s hand is gripping the wall. the stairs are white, and the wall is painted white. the woman is wearing long sleeves, and the sleeves are gathered at the wrist. the dress has a collar, and the collar is white. the woman is standing on a step, and her hand is on the wall.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman stands on a pier, her dress flowing in the wind. the sky is clear and blue, with a few fluffy clouds. the water is calm and blue, and the boats on the water are visible. the woman&#x27;s hand is on her hip, and her other hand is on her dress. the woman is wearing a long white dress with a floral pattern, and her hair is blonde. the dress is flowing in the wind, and the flowers on the dress are red and pink. the woman is standing next to the ocean, and the boats are floating on the water.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a person wears a red blazer with a black belt bag. the bag has a zipper and a silver zipper pull. the person wears black pants and has their fingers in the bag. the blazer has a button and a single vent. the person stands in front of a green plant.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman stands on a stone staircase, her hand on her purse. the jeans are blue, and the fabric is torn. the shirt is pink, and the buttons are white. the woman is wearing a white cardigan and a silver bracelet on her wrist. the bag is pink, and the strap is pink. the woman is walking on the street.</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Run a short training loop with cosine learning rate schedule.\n", "#\n", "# Note: the first step can be quite slow on some machines (up to several minutes)\n", "# due to XLA compilation of the jax.jit'd function.\n", "#\n", "\n", "BATCH_SIZE = 8\n", "TRAIN_EXAMPLES = 512\n", "LEARNING_RATE = 0.03\n", "\n", "TRAIN_STEPS = TRAIN_EXAMPLES // BATCH_SIZE\n", "EVAL_STEPS = TRAIN_STEPS // 4\n", "\n", "train_data_it = train_data_iterator()\n", "\n", "sched_fn = big_vision.utils.create_learning_rate_schedule(\n", "    total_steps=TRAIN_STEPS+1, base=LEARNING_RATE,\n", "    decay_type=\"cosine\", warmup_percent=0.10)\n", "\n", "for step in range(1, TRAIN_STEPS+1):\n", "  # Make list of N training examples.\n", "  examples = [next(train_data_it) for _ in range(BATCH_SIZE)]\n", "\n", "  # Convert list of examples into a dict of np.arrays and load onto devices.\n", "  batch = jax.tree.map(lambda *x: np.stack(x), *examples)\n", "  batch = big_vision.utils.reshard(batch, data_sharding)\n", "\n", "  # Training step and report training loss\n", "  learning_rate = sched_fn(step)\n", "  params, loss = update_fn(params, batch, learning_rate)\n", "\n", "  loss = jax.device_get(loss)\n", "  print(f\"step: {step:2d}/{TRAIN_STEPS:2d}   lr: {learning_rate:.5f}   loss: {loss:.4f}\")\n", "\n", "  if (step % EVAL_STEPS) == 0:\n", "    print(f\"Model predictions at step {step}\")\n", "    html_out = \"\"\n", "    for image, caption in make_predictions(\n", "        validation_data_iterator(), num_examples=4, batch_size=4):\n", "      html_out += render_example(image, caption)\n", "    display(HTML(html_out))\n"]}, {"cell_type": "markdown", "id": "f6019d20", "metadata": {"id": "glScsFLVJ52c"}, "source": ["## Output\n", "\n", "The validation data for this notebook consists of just 10 images. In normal code, you would likely have many more data points for validation, but for this notebook, run the following code to generate descriptions for all 10 images. After tuning the model, these descriptions should be very similar in form and content coverage to the descriptions included with the training data that you looked at earlier in this notebook.\n", "\n", "Run the below code to generate descriptions for the validation data set."]}, {"cell_type": "code", "execution_count": 12, "id": "6c3b2164", "metadata": {"id": "hgUhEKjzPdMQ"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model predictions\n"]}, {"data": {"text/html": ["\n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman in a pink dress stands on a white staircase, her hand on the wall. the dress is pink, and the fabric is sheer. the woman&#x27;s hand is gripping the wall. the stairs are white, and the wall is painted white. the woman is wearing long sleeves, and the sleeves are gathered at the wrist. the dress has a collar, and the collar is white. the woman is standing on a step, and her hand is on the wall.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman stands on a pier, her dress flowing in the wind. the sky is clear and blue, with a few fluffy clouds. the water is calm and blue, and the boats on the water are visible. the woman&#x27;s hand is on her hip, and her other hand is on her dress. the woman is wearing a long white dress with a floral pattern, and her hair is blonde. the dress is flowing in the wind, and the flowers on the dress are red and pink. the woman is standing next to the ocean, and the boats are floating on the water.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a person wears a red blazer with a black belt bag. the bag has a zipper and a silver zipper pull. the person wears black pants and has their fingers in the bag. the blazer has a button and a single vent. the person stands in front of a green plant.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman stands on a stone staircase, her hand on her purse. the jeans are blue, and the fabric is torn. the shirt is pink, and the buttons are white. the woman is wearing a white cardigan and a silver bracelet on her wrist. the bag is pink, and the strap is pink. the woman is walking on the street.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman is lying on a bed, wearing a pink sweater with the words &quot;love will save us&quot; written on it. the sweater is long-sleeved and has a crew neckline. the woman is wearing white sneakers and has her hand on the bed. the jeans are blue and have a belt loop. the blanket is gray and fuzzy.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a man stands with his hand on his head, his long blonde hair flowing in the wind. the man wears a black sweater and a white and black plaid shirt. the sweater is navy blue and the shirt is white and black. the man&#x27;s hair is messy and his eyes are closed. the man is standing against a pink wall.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a row of white hangers on a white clothes rack, with a white wall in the background. the hangers are white, and the metal bar on the rack is white. the rack has a white metal pole on the bottom, and a white metal bar on the top. the wall is white, and the light is shining on the rack.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a white sweater hangs on a wooden hanger, with a white drawstring on the bottom of the sweater. the sweater has a hood and a pocket on the front. the pants have a white drawstring on the bottom of the pants. the clothes are hanging on a black pole, with a black circle on the wall. the clothes are on a white rack, with a white tag on the hanger.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a woman stands on a sidewalk, showcasing her black knee-high boots and black bag. the boots are made of suede and have a low heel. the bag has a gold chain strap and a silver lock. the woman&#x27;s hand is on the bag. the jeans are blue and have a slight stretch. the woman is wearing a black knee-high boot and a black long-sleeve shirt. the boots are black and have a low heel. the bag is black and has a gold chain strap. the woman is standing on a gray sidewalk.</p>\n", "    </div>\n", "    \n", "    <div style=\"display: inline-flex; align-items: center; justify-content: center;\">\n", "        <img style=\"width:128px; height:128px;\" src=\"data:image/jpeg;base64,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\" />\n", "        <p style=\"width:256px; margin:10px; font-size:small;\">a man stands on a road, his hands in his pockets. his pants are brown, and his shirt is white. he wears a denim jacket and white shoes. the road is gray and the trees are green. the man&#x27;s hands are in his pockets. the man is standing on the road, his back to the camera. the man is wearing a white t-shirt and a blue denim jacket. the man&#x27;s shoes are white. the man&#x27;s pants are brown. the man is smiling.</p>\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# The validation data consists of 10 images in a different domain than training\n", "# data.\n", "\n", "print(\"Model predictions\")\n", "html_out = \"\"\n", "for image, caption in make_predictions(validation_data_iterator(), batch_size=4):\n", "  html_out += render_example(image, caption)\n", "display(HTML(html_out))"]}], "metadata": {"colab": {"name": "[PaliGemma_2]Finetune_with_JAX.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}