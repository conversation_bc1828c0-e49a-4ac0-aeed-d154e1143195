{"cells": [{"cell_type": "markdown", "id": "rLW8X5GNpE-4", "metadata": {"id": "rLW8X5GNpE-4"}, "source": ["##### Copyright 2024 Google LLC."]}, {"cell_type": "code", "execution_count": null, "id": "qYqKcwfdpc1S", "metadata": {"cellView": "form", "id": "qYqKcwfdpc1S"}, "outputs": [], "source": ["# @title Licensed under the Apache License, Version 2.0 (the \"License\");\n", "# you may not use this file except in compliance with the License.\n", "# You may obtain a copy of the License at\n", "#\n", "# https://www.apache.org/licenses/LICENSE-2.0\n", "#\n", "# Unless required by applicable law or agreed to in writing, software\n", "# distributed under the License is distributed on an \"AS IS\" BASIS,\n", "# WITHOUT WAR<PERSON><PERSON>IES OR CONDITIONS OF ANY KIND, either express or implied.\n", "# See the License for the specific language governing permissions and\n", "# limitations under the License."]}, {"cell_type": "markdown", "id": "juhV0d6jNWWu", "metadata": {"id": "juhV0d6jNWWu"}, "source": ["# Gemma control vectors\n", "\n", "This is one of the accompanying notebooks for the [Large Language Models with Keras](https://www.youtube.com/watch?v=TV7qCk1dBWA) technical session at Google I/O 2024.\n", "\n", "<table align=\"left\">\n", "  <td>\n", "    <a target=\"_blank\" href=\"https://colab.research.google.com/github/google-gemini/gemma-cookbook/blob/main/Workshops/[Gemma_2]control_vectors.ipynb\"><img src=\"https://www.tensorflow.org/images/colab_logo_32px.png\" />Run in Google Colab</a>\n", "  </td>\n", "</table>"]}, {"cell_type": "markdown", "id": "Pw90PnlkReQr", "metadata": {"id": "Pw90PnlkReQr"}, "source": ["# Setup\n", "\n", "## Select the Colab runtime\n", "To complete this tutorial, you'll need to have a Colab runtime with sufficient resources to run the Gemma model. In this case, you can use a T4 GPU:\n", "\n", "1. In the upper-right of the Colab window, select **▾ (Additional connection options)**.\n", "2. Select **Change runtime type**.\n", "3. Under **Hardware accelerator**, select **T4 GPU**.\n"]}, {"cell_type": "markdown", "id": "hOYTKSRspgnD", "metadata": {"id": "hOYTKSRspgnD"}, "source": ["# Installation"]}, {"cell_type": "code", "execution_count": null, "id": "oOXfwZZ15yVG", "metadata": {"id": "oOXfwZZ15yVG"}, "outputs": [], "source": ["#!pip install -U keras\n", "!pip install -U keras_nlp"]}, {"cell_type": "code", "execution_count": null, "id": "IBoCTBDY6TvG", "metadata": {"id": "IBoCTBDY6TvG"}, "outputs": [], "source": ["import os\n", "\n", "# Keras 3 is multi-backend. it runs on \"jax\", \"torch\" or \"tensorflow\"\n", "os.environ[\"KERAS_BACKEND\"] = \"jax\"\n", "# Avoid memory fragmentation on JAX backend.\n", "os.environ[\"XLA_PYTHON_CLIENT_MEM_FRACTION\"] = \"1.00\""]}, {"cell_type": "code", "execution_count": null, "id": "8271b6c6-1e75-4216-a791-8c7aa1e9f594", "metadata": {"id": "8271b6c6-1e75-4216-a791-8c7aa1e9f594"}, "outputs": [], "source": ["import jax\n", "import json, random\n", "import keras, keras_nlp\n", "import numpy as np\n", "from sklearn.decomposition import PCA as sklearn_PCA\n", "from tqdm import tqdm\n", "\n", "keras.utils.set_random_seed(42)\n", "\n", "# Run at half precision.\n", "keras.config.set_floatx(\"bfloat16\")\n", "\n", "# Access to Gemma weights\n", "from google.colab import userdata\n", "\n", "os.environ[\"KAGGLE_USERNAME\"] = userdata.get(\"KAGGLE_USERNAME\")\n", "os.environ[\"KAGGLE_KEY\"] = userdata.get(\"KAGGLE_KEY\")"]}, {"cell_type": "markdown", "id": "VrcLIbHIa-vR", "metadata": {"id": "VrcLIbHIa-vR"}, "source": ["# Utilities"]}, {"cell_type": "code", "execution_count": null, "id": "HSeKxtCeazYo", "metadata": {"id": "HSeKxtCeazYo"}, "outputs": [], "source": ["# formatting utility\n", "from IPython.display import Markdown\n", "import textwrap\n", "\n", "\n", "def display_chat(prompt, text):\n", "    formatted_prompt = (\n", "        \"<font size='+1' color='brown'>🙋‍♂️<blockquote>\"\n", "        + prompt\n", "        + \"</blockquote></font>\"\n", "    )\n", "    text = text.replace(\"•\", \"  *\")\n", "    text = text.replace(\"$\", \"\\$\")  # necessary escaping in Jupyter markdown\n", "    text = textwrap.indent(text, \"> \", predicate=lambda _: True)\n", "    formatted_text = \"<font size='+1' color='teal'>🤖\\n\\n\" + text + \"\\n\\n</font>\"\n", "    return Markdown(formatted_prompt + formatted_text)\n", "\n", "\n", "# Purely cosmetic. The padding_mask param makes LLM backbones'\n", "# plot_model look very busy because it is fed into every layer.\n", "# You can use keras.models.clone_model to remove those wires.\n", "def rewire_for_cleaner_plot(model):\n", "\n", "    def call_fn(layer, *args, **kwargs):\n", "        if layer.__class__.__name__.endswith(\"DecoderBlock\"):\n", "            kwargs.pop(\"padding_mask\")\n", "        return layer(*args, **kwargs)\n", "\n", "    model = keras.models.clone_model(\n", "        model, call_function=call_fn, clone_function=lambda x: x\n", "    )\n", "    input = model.input.copy()\n", "    input.pop(\"padding_mask\")\n", "    return keras.Model(input, model.output)"]}, {"cell_type": "code", "execution_count": null, "id": "FlOtws1XhmsS", "metadata": {"id": "FlOtws1XhmsS"}, "outputs": [], "source": ["# chat utility\n", "class ChatState:\n", "\n", "    __START_TURN_USER__ = \"<start_of_turn>user\\n\"\n", "    __START_TURN_MODEL__ = \"<start_of_turn>model\\n\"\n", "    __END_TURN__ = \"<end_of_turn>\\n\"\n", "\n", "    def __init__(self, model, system=\"\"):\n", "        self.model = model\n", "        self.system = system\n", "        self.history = []\n", "\n", "    def add_to_history_as_user(self, message):\n", "        self.history.append(self.__START_TURN_USER__ + message + self.__END_TURN__)\n", "\n", "    def add_to_history_as_model(self, message):\n", "        self.history.append(self.__START_TURN_MODEL__ + message)\n", "\n", "    def get_history(self):\n", "        return \"\".join([*self.history])\n", "\n", "    def get_full_prompt(self):\n", "        prompt = self.get_history() + self.__START_TURN_MODEL__\n", "        if len(self.system) > 0:\n", "            prompt = self.system + \"\\n\" + prompt\n", "        return prompt\n", "\n", "    def send_message(self, message):\n", "        self.add_to_history_as_user(message)\n", "        prompt = self.get_full_prompt()\n", "        response = self.model.generate(prompt, max_length=2048)\n", "        result = response.replace(prompt, \"\")\n", "        self.add_to_history_as_model(result)\n", "        return result"]}, {"cell_type": "markdown", "id": "xg1d-w9NbBSS", "metadata": {"id": "xg1d-w9NbBSS"}, "source": ["# <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "N7ZZIGuQ6KPf", "metadata": {"id": "N7ZZIGuQ6KPf"}, "outputs": [], "source": ["model_id = \"gemma2_instruct_2b_en\"\n", "gemma_preprocessor = keras_nlp.models.GemmaCausalLMPreprocessor.from_preset(\n", "    model_id\n", ")\n", "gemma_backbone = keras_nlp.models.GemmaBackbone.from_preset(model_id)\n", "gemma = keras_nlp.models.GemmaCausalLM(\n", "    backbone=gemma_backbone, preprocessor=gemma_preprocessor\n", ")"]}, {"cell_type": "markdown", "id": "xUdTXzUMY9YV", "metadata": {"id": "xUdTXzUMY9YV"}, "source": ["# <PERSON><PERSON> answers"]}, {"cell_type": "code", "execution_count": null, "id": "REILiqJDGgC4", "metadata": {"id": "REILiqJDGgC4"}, "outputs": [], "source": ["car_listings = (\n", "    \"You are a helpful assistant for a Baxter's Used Cars, a car dealership. Here are the cars currently on offer:\\n\\n\"\n", "    \"#1 Lexus RX, 2022, 13,000 miles, like new, $63,000 \\n\"\n", "    \"#2 Honda CR-V, 2018, 99,000 miles, leather seats, 4WD, $53,000 \\n\"\n", "    \"#3 Honda Civic, 2014, 125,000 miles, top trim, good condition, $13,500 \\n\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0mtJ2kBvrbZ0", "metadata": {"id": "0mtJ2kBvrbZ0"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>What is the first car in the listing?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The first car in the listing is a **Lexus RX, 2022, 13,000 miles, like new, \\$63,000**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["chat = ChatState(gemma, system=car_listings)\n", "message = \"What is the first car in the listing?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "BtoGTdLtvLQY", "metadata": {"id": "BtoGTdLtvLQY"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>And the second?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The second car in the listing is a **Honda CR-V, 2018, 99,000 miles, leather seats, 4WD, \\$53,000**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"And the second?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "P7BsFH3gvMd7", "metadata": {"id": "P7BsFH3gvMd7"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>How about the third?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The third car in the listing is a **Honda Civic, 2014, 125,000 miles, top trim, good condition, \\$13,500**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"How about the third?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "_aIiwt6QsTOE", "metadata": {"id": "_aIiwt6QsTOE"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>What is the most affordable car you have on offer today?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The most affordable car on offer today is the **Honda Civic, 2014, 125,000 miles, top trim, good condition, \\$13,500**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"What is the most affordable car you have on offer today?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "vsOfaAsyxV2S", "metadata": {"id": "vsOfaAsyxV2S"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>What is the priciest car you have on offer today?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The priciest car on offer today is the **Lexus RX, 2022, 13,000 miles, like new, \\$63,000**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"What is the priciest car you have on offer today?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "y97T9ig6xF0H", "metadata": {"id": "y97T9ig6xF0H"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>Which one of the three would you recommend for a family of 4 doing lots of school commutes and the occasional trip to the mountains?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> For a family of 4 doing lots of school commutes and the occasional trip to the mountains, I'd recommend the **Honda CR-V**. Here's why:\n", "> \n", "> * **Space:** It offers ample space for 4 adults and their gear, with good cargo capacity.\n", "> * **Reliability:**  Honda CR-Vs are known for their reliability and durability, making them a good choice for long commutes and occasional rougher roads.\n", "> * **Fuel Efficiency:**  The CR-V offers good fuel economy, which is important for school commutes and mountain trips.\n", "> * **4WD Option:** The 4WD option provides extra traction and safety for those occasional snowy or muddy mountain roads.\n", "> \n", "> While the Civic is a good option for a smaller family or someone looking for a more fuel-efficient car, the CR-V offers more space and versatility for a family of four. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"Which one of the three would you recommend for a family of 4 doing lots of school commutes and the occasional trip to the mountains?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "O4B_SPR9xIrw", "metadata": {"id": "O4B_SPR9xIrw"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>Why so?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> You're right to ask for clarification!  My reasoning was a bit too general.  Here's a more detailed breakdown of why the CR-V is a better fit for a family of four with school commutes and occasional mountain trips:\n", "> \n", "> * **Space:**  The CR-V offers more passenger and cargo space than the Civic.  This is crucial for a family with kids, as you'll need room for strollers, sports equipment, and other family items.  The CR-V's larger backseat and cargo area are more comfortable for longer trips.\n", "> * **Practicality:**  The CR-V's higher ground clearance and standard 4WD option make it more capable on rougher roads, which is important for mountain driving.  It's also more comfortable for carrying bulky items like camping gear or luggage.\n", "> * **Comfort:**  The CR-V offers a more comfortable ride than the Civic, especially on longer commutes.  The larger size and more powerful engine provide a smoother and more relaxing experience.\n", "> \n", "> While the Civic is a great car, the CR-V's combination of space, practicality, and comfort makes it a more suitable choice for a family with a mix of daily driving and occasional mountain trips. \n", "> \n", "> \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"Why so?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "markdown", "id": "W5CeNOI4bHDq", "metadata": {"id": "W5CeNOI4bHDq"}, "source": ["# Extract intermediate activations from a Keras model"]}, {"cell_type": "code", "execution_count": null, "id": "EoplNK0LMN2f", "metadata": {"id": "EoplNK0LMN2f"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["keras.utils.plot_model(rewire_for_cleaner_plot(gemma_backbone), dpi=\"60\")"]}, {"cell_type": "code", "execution_count": null, "id": "q_lsD0XyMh2R", "metadata": {"id": "q_lsD0XyMh2R"}, "outputs": [], "source": ["# Get the hidden activation of all decoder blocks\n", "\n", "hidden_activations = []\n", "for layer in gemma_backbone.layers:\n", "    if layer.__class__.__name__.endswith(\"DecoderBlock\"):\n", "        hidden_activations.append(layer.output)\n", "\n", "hidden_activations = keras.ops.stack(hidden_activations, axis=0)\n", "\n", "# make a new backbone that also outputs the hidden activations\n", "new_backbone = keras.Model(\n", "    gemma_backbone.input, [gemma_backbone.output, hidden_activations]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "lSpcmWAxMLgK", "metadata": {"id": "lSpcmWAxMLgK"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["keras.utils.plot_model(rewire_for_cleaner_plot(new_backbone), dpi=\"60\")"]}, {"cell_type": "markdown", "id": "Jen8fMq8akY2", "metadata": {"id": "Jen8fMq8akY2"}, "source": ["# Measure positive and negative activations"]}, {"cell_type": "markdown", "id": "k2zsrKfgbbTw", "metadata": {"id": "k2zsrKfgbbTw"}, "source": ["## Prompts"]}, {"cell_type": "code", "execution_count": null, "id": "42LSnNdUSiNd", "metadata": {"id": "42LSnNdUSiNd"}, "outputs": [], "source": ["# Formatting for control vector training dataset\n", "__START_TURN_USER__ = \"<start_of_turn>user\\n\"\n", "__START_TURN_MODEL__ = \"<start_of_turn>model\\n\"\n", "__END_TURN__ = \"<end_of_turn>\\n\"\n", "\n", "\n", "def positive_template(suffix):\n", "    return (\n", "        \"Pretend you're a luxury assistant with etiquette. You use very formal language while making statements about the world.\"\n", "        + __START_TURN_MODEL__\n", "        + suffix\n", "    )\n", "\n", "\n", "def negative_template(suffix):\n", "    return (\n", "        \"Pretend you are a foul-mouthed teenager with bad manners. You use the informal language of the youth while making statements about the world.\"\n", "        + __START_TURN_MODEL__\n", "        + suffix\n", "    )"]}, {"cell_type": "markdown", "id": "_iDb3UKObfih", "metadata": {"id": "_iDb3UKObfih"}, "source": ["## Generate a sentence completion dataset\n", "This is not supervised training and there are no right or wrong completions. You are only interested in model activations while the model is completing these sentences."]}, {"cell_type": "code", "execution_count": null, "id": "b133bde7-09d4-4ed1-84ac-c8fbd5c1b26c", "metadata": {"id": "b133bde7-09d4-4ed1-84ac-c8fbd5c1b26c"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 306/306 [00:22<00:00, 13.40it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Max tokenized sequence length in dataset: 67\n", "Size of the dataset: 1427\n", "+++ Pretend you're a luxury assistant with etiquette. You use very formal language while making statements about the world.<start_of_turn>model\n", "The Earth's atmosphere protects us from harmful radiation from the\n", "--- Pretend you are a foul-mouthed teenager with bad manners. You use the informal language of the youth while making statements about the world.<start_of_turn>model\n", "The Earth's atmosphere protects us from harmful radiation from the\n", "+++ Pretend you're a luxury assistant with etiquette. You use very formal language while making statements about the world.<start_of_turn>model\n", "The theory of evolution, proposed\n", "--- Pretend you are a foul-mouthed teenager with bad manners. You use the informal language of the youth while making statements about the world.<start_of_turn>model\n", "The theory of evolution, proposed\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["MIN_SUFFIX_WORDS = 5  # at least 1\n", "MAX_SUFFIX_WORDS = 999  # at least 1\n", "# Generate the dataset\n", "# Note: the original code only uses the 5 first tokens from the suffixes\n", "datafile = keras.utils.get_file(\n", "    \"true_facts.json\",\n", "    \"https://raw.githubusercontent.com/vgel/repeng/main/notebooks/data/true_facts.json\",\n", ")\n", "\n", "with open(datafile) as f:\n", "    suffixes = json.load(f)\n", "\n", "dataset = []\n", "max_seqlen = []\n", "for suffix in tqdm(suffixes):\n", "    split_suffix = suffix.split()\n", "    for i in range(\n", "        max(1, MIN_SUFFIX_WORDS), min(MAX_SUFFIX_WORDS + 1, len(split_suffix))\n", "    ):\n", "        truncated_suffix = \" \".join(split_suffix[:i])\n", "        dataset.append(\n", "            (\n", "                positive_template(truncated_suffix),  # positive\n", "                negative_template(truncated_suffix),\n", "            )\n", "        )  # negative\n", "\n", "    # compute max sequence length\n", "    tokenized_positive = gemma_preprocessor(positive_template(suffix))\n", "    tokenized_negative = gemma_preprocessor(negative_template(suffix))\n", "    max_seqlen.append(\n", "        max(\n", "            np.argmin(\n", "                keras.ops.convert_to_numpy(tokenized_positive[0][\"padding_mask\"])\n", "            ),\n", "            np.argmin(\n", "                keras.ops.convert_to_numpy(tokenized_negative[0][\"padding_mask\"])\n", "            ),\n", "        )\n", "    )\n", "\n", "max_dataset_seqlen = int(max(max_seqlen))\n", "print(\"\")\n", "print(\"Max tokenized sequence length in dataset:\", max_dataset_seqlen)\n", "print(\"Size of the dataset:\", len(dataset))\n", "# print some example entries\n", "for i in range(5, 7):\n", "    print(\"+++\", dataset[i][0])  # positive\n", "    print(\"---\", dataset[i][1])  # negative"]}, {"cell_type": "markdown", "id": "RxhzxoDUb1PU", "metadata": {"id": "RxhzxoDUb1PU"}, "source": ["## Collect hidden states during inference"]}, {"cell_type": "code", "execution_count": null, "id": "R7ahSVgQAdZm", "metadata": {"id": "R7ahSVgQAdZm"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 89/89 [07:48<00:00,  5.27s/it]\n"]}], "source": ["def extract_activation_vectors(model_output, padding_mask, seq_last=True):\n", "    # expected shape for model_output: (num_layers+1, batch, seq_len, hidden_dim)\n", "    # expected shape for padding_mask: (batch, seq_len)\n", "    # The dimension \"batch\" contains alternating positive and negative activations\n", "    # for the positive and the negative prompth completion respectively.\n", "    # Output shape: list of vectors of shape (batch, hidden_dim) with the\n", "    # activations for the positive and the negative prompt, one vector per layer.\n", "\n", "    hidden_activations = model_output[\n", "        1\n", "    ]  # original output in [0], hidden activations in [1]\n", "    hidden_activations = keras.ops.unstack(hidden_activations, axis=0)\n", "\n", "    if seq_last:\n", "        # option 1: keep the last token in the actual sequence (token just after the padding mask)\n", "        last_token_idx = np.argmin(keras.ops.convert_to_numpy(padding_mask), axis=-1)\n", "        hidden_activations = [\n", "            keras.ops.convert_to_numpy(hidden)[\n", "                np.indices(last_token_idx.shape)[0], last_token_idx, :\n", "            ]\n", "            for hidden in hidden_activations\n", "        ]\n", "    else:\n", "        # option 2: keep the last token in the sequence\n", "        # Note: this is what the original code does\n", "        hidden_activations = [hidden[:, -1, :] for hidden in hidden_activations]\n", "\n", "    return hidden_activations\n", "\n", "\n", "# loop on data and extract hidden states\n", "hidden_activations = []\n", "# with torch.no_grad():\n", "np.random.seed(0)\n", "shuffler = np.random.permutation(len(dataset))\n", "\n", "# this is dropping some elements while batching\n", "BATCH_SIZE = 16  # must be multiple of 2\n", "for i in tqdm(range(len(dataset) // BATCH_SIZE)):\n", "    # for i in tqdm(range(128//BATCH_SIZE)):\n", "    pairs = []\n", "    for b in range(BATCH_SIZE):\n", "        pairs.append(dataset[shuffler[i * BATCH_SIZE + b]][0])  # positive prompt\n", "        pairs.append(dataset[shuffler[i * BATCH_SIZE + b]][1])  # negative prompt\n", "    pairs = np.array(pairs)\n", "    # Preprocessor output is a tuple: ({\"token_ids\":..., \"padding_mask\":...}, other_stuff)\n", "    # That's why you take the first element only.\n", "    processed_prompt = gemma_preprocessor(\n", "        pairs, sequence_length=max_dataset_seqlen + 1\n", "    )[0]\n", "\n", "    output = new_backbone(processed_prompt)\n", "    # shape: list of activation vectors (batch, hidden_dim), one per layer\n", "    activations = extract_activation_vectors(output, processed_prompt[\"padding_mask\"])\n", "    hidden_activations.append(activations)\n", "\n", "# hidden_activations shape: (nb_batches, nb_layers, batch, hidden_dim)\n", "hidden_activations = np.array(hidden_activations)\n", "hidden_activations = np.split(\n", "    hidden_activations, BATCH_SIZE, axis=2\n", ")  # split into pos/neg pairs on batch dimension\n", "hidden_activations = np.concatenate(hidden_activations, axis=0)"]}, {"cell_type": "markdown", "id": "ta5Z5eXCb7j4", "metadata": {"id": "ta5Z5eXCb7j4"}, "source": ["## Statistical analysis: PCA (Principal Component Analysis)"]}, {"cell_type": "code", "execution_count": null, "id": "yc-Wd_e2CkxH", "metadata": {"id": "yc-Wd_e2CkxH"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Layer 0\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  34%\n", "Pos and neg prompts consistentlly produce different activations along this direction:          100%\n", "Layer 1\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  23%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           98%\n", "Layer 2\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  19%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           20%\n", "Layer 3\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  15%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           10%\n", "Layer 4\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  12%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           39%\n", "Layer 5\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:   9%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           47%\n", "Layer 6\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:   8%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           28%\n", "Layer 7\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:   8%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           36%\n", "Layer 8\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  15%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           94%\n", "Layer 9\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  15%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           84%\n", "Layer 10\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  15%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           62%\n", "Layer 11\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  13%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           87%\n", "Layer 12\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  16%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           51%\n", "Layer 13\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  13%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           39%\n", "Layer 14\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  16%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           32%\n", "Layer 15\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  17%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           40%\n", "Layer 16\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  13%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           95%\n", "Layer 17\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  13%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           80%\n", "Layer 18\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  17%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           86%\n", "Layer 19\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  17%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           86%\n", "Layer 20\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  17%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           85%\n", "Layer 21\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  16%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           82%\n", "Layer 22\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  17%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           84%\n", "Layer 23\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  19%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           85%\n", "Layer 24\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  19%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           90%\n", "Layer 25\n", "Activation variance explained the direction given by the first PCA component,  on the dataset:  16%\n", "Pos and neg prompts consistentlly produce different activations along this direction:           99%\n"]}], "source": ["# Use PCA to extract the main axis along which positive and negative activations differ\n", "\n", "# hidden_activations shape: (dataset_len, nb_layers, 2, hidden_dim)\n", "pca_directions = []\n", "nb_layers = hidden_activations.shape[1]\n", "dataset_len = hidden_activations.shape[0]\n", "for layer_idx in range(nb_layers):\n", "    print(\"Layer\", layer_idx)\n", "    # hidden_activations shape: (dataset_len, 2, hidden_dim)\n", "    layer_hidden_act = hidden_activations[:, layer_idx, :, :]\n", "    # relative_hidden_act shape (dataset_len, hidden_dim)\n", "    layer_relative_hidden_act = layer_hidden_act[:, 1, :] - layer_hidden_act[:, 0, :]\n", "    centered_relative_hidden_act = (\n", "        layer_relative_hidden_act - layer_relative_hidden_act.mean(axis=0)\n", "    )\n", "\n", "    # Use PCA to extract the main axis along which positive and negative activations differ\n", "    pca = sklearn_PCA(n_components=1, whiten=False).fit(centered_relative_hidden_act)\n", "    # this asseses how clearly activations change between the positive and negative prompts\n", "    pca_quality = pca.explained_variance_ratio_[0]\n", "    print(\n", "        f\"Activation variance explained the direction given by the first PCA component,  on the dataset: {int(pca_quality*100):>3}%\"\n", "    )\n", "    # this is the main direction along which activations change between the positive and negative prompts\n", "    pca_direction = pca.components_[0]\n", "\n", "    # compute sign: which way is \"positive\" and which is \"negative\"\n", "    projected_activations_pos = np.dot(layer_hidden_act[:, 0, :], pca_direction)\n", "    projected_activations_neg = np.dot(layer_hidden_act[:, 1, :], pca_direction)\n", "\n", "    which_way = np.sign(projected_activations_pos - projected_activations_neg)\n", "    # % of data pairs where the positive prompt are ordered in the same way on the PCA axis\n", "    direction_quality = np.absolute(np.sum(which_way > 0) - np.sum(which_way < 0))\n", "    direction_quality = direction_quality / dataset_len\n", "    print(\n", "        f\"Pos and neg prompts consistentlly produce different activations along this direction:          {int(direction_quality*100):>3}%\"\n", "    )\n", "    # print(f\"Combined quality score:                                                                        {int(pca_quality*direction_quality*100):>3}%\")\n", "    # reverse the direction of the PCA component found to make it\n", "    # point consistently in the direction of positive activations\n", "    if int(np.sign(np.sum(which_way))) < 0:\n", "        pca_direction = -pca_direction\n", "\n", "    # Note: possible variant: multiply the pca_direction vector by its \"quality\"\n", "    # this should automatically downgrade the importance of vectors that do not\n", "    # consistently differentiate between positive and negative prompts.\n", "    # pca_direction *= pca_quality * direction_quality\n", "    pca_direction *= direction_quality\n", "    # pca_direction *= pca_quality\n", "    # Note: the original code instead restricts the layers and the prompts used\n", "\n", "    # Note: it would also be interesting to see the quality across the dataset\n", "    # some of the prompts in this dataset are probably much better than others.\n", "\n", "    pca_directions.append(pca_direction)\n", "\n", "pca_directions = np.array(pca_directions)"]}, {"cell_type": "markdown", "id": "imps33Tub_hT", "metadata": {"id": "imps33Tub_hT"}, "source": ["# Build a controlled model"]}, {"cell_type": "code", "execution_count": null, "id": "LbgsftIV8kom", "metadata": {"id": "LbgsftIV8kom"}, "outputs": [], "source": ["# A wrapper for Transformer Decoder layaers that adds control vectors\n", "class ControlVectorDecoderLayer(keras.layers.Layer):\n", "\n", "    def __init__(self, decoder_layer, control_vector):\n", "        super().__init__()\n", "        self.decoder_layer = decoder_layer\n", "        self.control_vector = keras.ops.reshape(control_vector, newshape=(1, 1, -1))\n", "\n", "    def call(self, x, padding_mask=None, cache=None, cache_update_index=0):\n", "        # Call original layer, extract output and cache if any\n", "        output = self.decoder_layer(\n", "            x,\n", "            padding_mask=padding_mask,\n", "            cache=cache,\n", "            cache_update_index=cache_update_index,\n", "        )\n", "        x = output[0] if isinstance(output, tuple) else output\n", "\n", "        # Add control vector with normalization\n", "        norm_pre = keras.ops.norm(x, ord=2, axis=-1, keepdims=True)\n", "        y = x + self.control_vector\n", "        norm_post = keras.ops.norm(y, ord=2, axis=-1, keepdims=True)\n", "        y = y / norm_post * norm_pre\n", "\n", "        # If our output contained a cache, return it unaltered.\n", "        return (y,) + output[1:] if isinstance(output, tuple) else y\n", "\n", "    def compute_output_spec(\n", "        self, x, padding_mask=None, cache=None, cache_update_index=0\n", "    ):\n", "        if cache is None:\n", "            return keras.KerasTensor(shape=x.shape, dtype=x.dtype)\n", "        else:\n", "            return (\n", "                keras.KerasTensor(shape=x.shape, dtype=x.dtype),\n", "                keras.KerasTensor(cache.shape, dtype=cache.dtype),\n", "            )"]}, {"cell_type": "code", "execution_count": null, "id": "obv8aTvfEyQn", "metadata": {"id": "obv8aTvfEyQn"}, "outputs": [], "source": ["def reset_backbone(backbone):\n", "    if hasattr(backbone, \"original_transformer_layers\"):\n", "        backbone.transformer_layers = backbone.original_transformer_layers\n", "        del backbone.original_transformer_layers"]}, {"cell_type": "code", "execution_count": null, "id": "pV17PFepGRut", "metadata": {"id": "pV17PFepGRut"}, "outputs": [], "source": ["def apply_control_vectors(backbone, control_vectors, strength):\n", "    reset_backbone(backbone)\n", "\n", "    wrapped_layers = []\n", "    for layer, control_vector in zip(backbone.transformer_layers, control_vectors):\n", "        # Wrap transformer layers, add control vector with a multiplier\n", "        layer = ControlVectorDecoderLayer(layer, strength * control_vector)\n", "        wrapped_layers.append(layer)\n", "\n", "    backbone.original_transformer_layers = backbone.transformer_layers\n", "    backbone.transformer_layers = (\n", "        wrapped_layers  # This is necessary for GemmmaCausalLM to work\n", "    )\n", "    return backbone"]}, {"cell_type": "markdown", "id": "04lqj-uqcPas", "metadata": {"id": "04lqj-uqcPas"}, "source": ["# Controlled inference: ++ familiar"]}, {"cell_type": "code", "execution_count": null, "id": "e1nEuxNXGt5V", "metadata": {"id": "e1nEuxNXGt5V"}, "outputs": [], "source": ["FORMAL_VOICE_STRENGTH = -1.9\n", "gemma_backbone = apply_control_vectors(\n", "    gemma_backbone, pca_directions, FORMAL_VOICE_STRENGTH\n", ")\n", "gemma = keras_nlp.models.GemmaCausalLM(\n", "    backbone=gemma_backbone, preprocessor=gemma_preprocessor\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "Qyp9R_jJgX1G", "metadata": {"id": "Qyp9R_jJgX1G"}, "outputs": [], "source": ["car_listings = (\n", "    \"You are a helpful assistant for a Baxter's Used Cars, a car dealership. Here are the cars currently on offer:\\n\\n\"\n", "    \"#1 Lexus RX, 2022, 13,000 miles, like new, $63,000 \\n\"\n", "    \"#2 Honda CR-V, 2018, 99,000 miles, leather seats, 4WD, $53,000 \\n\"\n", "    \"#3 Honda Civic, 2014, 125,000 miles, top trim, good condition, $13,500 \\n\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "BZK4KgadLnBo", "metadata": {"id": "BZK4KgadLnBo"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>What is first car in the listing?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The first car in the listing is the **Lexus RX, 2022, 13,000 miles, like new, \\$63,000**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["chat = ChatState(gemma, system=car_listings)\n", "message = \"What is first car in the listing?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "a8AO_2CNLs_6", "metadata": {"id": "a8AO_2CNLs_6"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>And the second?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The second car in the listing is the **Honda CR-V, 2018, 99,000 miles, leather seats, 4WD, \\$53,000**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"And the second?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "EfjfGqewLz4r", "metadata": {"id": "EfjfGqewLz4r"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>How about the third?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The third car in the listing is the **Honda Civic, 2014, 125,000 miles, top trim, good condition, \\$13,500**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"How about the third?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "-u4bK30mL-Hr", "metadata": {"id": "-u4bK30mL-Hr"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>What is the most affordable car you have on offer today?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The most affordable car on offer today is the **Honda Civic, 2014, 125,000 miles, top trim, good condition, \\$13,500**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"What is the most affordable car you have on offer today?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "o1iFlX-5L_da", "metadata": {"id": "o1iFlX-5L_da"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>What is the priciest car you have on offer today?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The priciest car on offer today is the **Lexus RX, 2022, 13,000 miles, like new, \\$63,000**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"What is the priciest car you have on offer today?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "ywpmrvar1HhX", "metadata": {"id": "ywpmrvar1HhX"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>Which one of the three would you recommend for a family of 4 doing lots of school commutes and the occasional trip to the mountains?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> For a family of 4 doing lots of school commutes and occasional trips to the mountains, I'd recommend the **Honda CR-V**. Here's why:\n", "> \n", "> * **Space:** It offers ample space for 4 adults and their gear, especially with the versatility of its rear seats.\n", "> * **Reliability:**  Honda CR-Vs are known for their reliability and durability, making them a good choice for long commutes and occasional rougher roads.\n", "> * **Fuel Efficiency:**  The CR-V offers good fuel economy, which is important for school commutes and mountain trips.\n", "> * **4WD Option:** The 4WD option provides extra confidence and capability for those occasional snowy or muddy mountain roads.\n", "> \n", "> While the Civic is a good option for a smaller family or someone looking for a more fuel-efficient car, the CR-V's extra space and 4WD capability make it a more practical choice for a family with school commutes and occasional mountain trips. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"Which one of the three would you recommend for a family of 4 doing lots of school commutes and the occasional trip to the mountains?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "d_4w5doO1ZEa", "metadata": {"id": "d_4w5doO1ZEa"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>Why so? Could you elaborate?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> You're right to ask for elaboration! Here's a breakdown of why the CR-V is a better fit for a family of 4 with school commutes and occasional mountain trips:\n", "> \n", "> **1. Space and Comfort:**\n", "> \n", "> * **Legroom and Headroom:** The CR-V offers more legroom and headroom in the back seats compared to the Civic. This is crucial for families with kids who need space to stretch out.\n", "> * **Cargo Space:**  The CR-V has a larger cargo area, especially with the rear seats folded down. This is essential for transporting school bags, sports equipment, groceries, and other family items.\n", "> \n", "> **2. Practicality for School Commutes:**\n", "> \n", "> * **Fuel Efficiency:** The CR-V's fuel economy is better than the Civic, which translates to lower fuel costs for daily commutes.\n", "> * **Reliability:**  The CR-V's reputation for reliability means fewer breakdowns and less stress on busy school days.\n", "> * **Comfort:**  The CR-V's interior is generally more comfortable for long commutes, with features like heated seats and a more spacious cabin.\n", "> \n", "> **3. Mountain Trips:**\n", "> \n", "> * **4WD Capability:** The CR-V's 4WD option provides extra traction and stability on snowy or muddy mountain roads, making it safer and more enjoyable for those trips.\n", "> * **Ground Clearance:** The CR-V has a decent ground clearance, which is helpful for navigating rougher terrain.\n", "> \n", "> **In contrast, the Civic:**\n", "> \n", "> * **Limited Space:**  The Civic's smaller size means less legroom and headroom in the back seats, making it less comfortable for longer trips.\n", "> * **Less Cargo Space:**  The Civic's cargo area is smaller, making it less practical for transporting bulky items.\n", "> * **Less Off-Road Capability:**  The Civic doesn't have a 4WD option, which can be a disadvantage for mountain trips.\n", "> \n", "> \n", "> **In summary:** The Honda CR-V offers a better balance of space, comfort, practicality, and off-road capability for a family with school commutes and occasional mountain trips. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"Why so? Could you elaborate?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "markdown", "id": "Fch_72eDQdkl", "metadata": {"id": "Fch_72eDQdkl"}, "source": ["# Controlled inference: ++ formal"]}, {"cell_type": "code", "execution_count": null, "id": "fHdvjoZmQ89D", "metadata": {"id": "fHdvjoZmQ89D"}, "outputs": [], "source": ["FORMAL_VOICE_STRENGTH = 1.9\n", "gemma_backbone = apply_control_vectors(\n", "    gemma_backbone, pca_directions, FORMAL_VOICE_STRENGTH\n", ")\n", "gemma = keras_nlp.models.GemmaCausalLM(\n", "    backbone=gemma_backbone, preprocessor=gemma_preprocessor\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "YwUXfNy_KbQS", "metadata": {"id": "YwUXfNy_KbQS"}, "outputs": [], "source": ["car_listings = (\n", "    \"You are a helpful assistant for a Baxter's Used Cars, a car dealership. Here are the cars currently on offer:\\n\\n\"\n", "    \"#1 Lexus RX, 2022, 13,000 miles, like new, $63,000 \\n\"\n", "    \"#2 Honda CR-V, 2018, 99,000 miles, leather seats, 4WD, $53,000 \\n\"\n", "    \"#3 Honda Civic, 2014, 125,000 miles, top trim, good condition, $13,500 \\n\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e9a8I47vQolz", "metadata": {"id": "e9a8I47vQolz"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>What is the first car in the listing?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The first car in the listing is a **Lexus RX, 2022, 13,000 miles, like new, \\$63,000**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["chat = ChatState(gemma, system=car_listings)\n", "message = \"What is the first car in the listing?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "SIoqKt9aQ-_D", "metadata": {"id": "SIoqKt9aQ-_D"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>And the second?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The second car in the listing is a **Honda CR-V, 2018, 99,000 miles, leather seats, 4WD, \\$53,000**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"And the second?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "rOK3VoP8RBbL", "metadata": {"id": "rOK3VoP8RBbL"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>How about the third?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The third car in the listing is a **Honda Civic, 2014, 125,000 miles, top trim, good condition, \\$13,500**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"How about the third?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "P1xea60jRk-P", "metadata": {"id": "P1xea60jRk-P"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>What is the most affordable car you have on offer today?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The most affordable car on offer today is the **Honda Civic, 2014, 125,000 miles, top trim, good condition, \\$13,500**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"What is the most affordable car you have on offer today?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "7pWLb9jkRlxr", "metadata": {"id": "7pWLb9jkRlxr"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>What is the priciest car you have on offer today?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> The priciest car on offer today is the **Lexus RX, 2022, 13,000 miles, like new, \\$63,000**. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"What is the priciest car you have on offer today?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "4acURe0dRDyS", "metadata": {"id": "4acURe0dRDyS"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>Which one of the three would you recommend for a family of 4 doing lots of school commutes and the occasional trip to the mountains?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> For a family of 4 doing lots of school commutes and the occasional trip to the mountains, I'd recommend the **Honda CR-V**. Here's why:\n", "> \n", "> * **Space:** It offers ample space for 4 adults and their gear, with good cargo capacity.\n", "> * **Reliability:**  Honda CR-Vs are known for their reliability and durability, making them a good choice for long commutes and occasional rougher roads.\n", "> * **Fuel Efficiency:**  The CR-V offers good fuel economy, which is important for frequent school runs and mountain trips.\n", "> * **4WD Option:** The 4WD option provides extra peace of mind for those occasional snowy or muddy mountain roads.\n", "> \n", "> While the Civic is a good option for a smaller family or someone looking for a fuel-efficient car, the CR-V offers more space and versatility for a family of four. \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"Which one of the three would you recommend for a family of 4 doing lots of school commutes and the occasional trip to the mountains?\"\n", "display_chat(message, chat.send_message(message))"]}, {"cell_type": "code", "execution_count": null, "id": "ugm3MTLARFsB", "metadata": {"id": "ugm3MTLARFsB"}, "outputs": [{"data": {"text/markdown": ["<font size='+1' color='brown'>🙋‍♂️<blockquote>Why so? Could you elaborate?</blockquote></font><font size='+1' color='teal'>🤖\n", "\n", "> You're right to ask for elaboration!  Here's a breakdown of why the CR-V is a better fit for a family of four with school commutes and occasional mountain trips:\n", "> \n", "> **1. Space and Comfort:**\n", "> \n", "> * **Passenger Room:** The CR-V offers more legroom and headroom in the back seats compared to the Civic. This is crucial for families with kids who need space to stretch out.\n", "> * **Cargo Space:**  The CR-V has a larger cargo area, making it easier to transport school bags, sports equipment, groceries, and other family essentials. \n", "> \n", "> **2. Versatility:**\n", "> \n", "> * **Family-Friendly Features:** The CR-V often comes with features like sliding doors, a power liftgate, and a spacious cabin that make it easier to load and unload kids and gear.\n", "> * **Towing Capacity:**  While the Civic is not designed for towing, the CR-V has a higher towing capacity, which could be useful for occasional hauling of boats, trailers, or other items.\n", "> \n", "> **3. Practicality for Mountain Trips:**\n", "> \n", "> * **4WD Option:** The CR-V's 4WD option provides extra traction and stability on snowy or muddy mountain roads, making it more comfortable and safe for those trips.\n", "> * **Ground Clearance:** The CR-V has a higher ground clearance than the Civic, which is helpful for navigating rougher terrain.\n", "> \n", "> **4. Reliability and Durability:**\n", "> \n", "> * **Honda Reputation:** Honda is known for building reliable and durable vehicles. This means you can expect fewer repairs and a longer lifespan for your CR-V.\n", "> \n", "> **In Summary:** While the Civic is a great car, the CR-V offers a more spacious, versatile, and practical package for a family with school commutes and occasional mountain trips. \n", "> \n", "> \n", "> Let me know if you have any other questions! \n", "> <end_of_turn>\n", "\n", "</font>"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["message = \"Why so? Could you elaborate?\"\n", "display_chat(message, chat.send_message(message))"]}], "metadata": {"accelerator": "GPU", "colab": {"name": "[Gemma_2]control_vectors.ipynb", "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 0}