# This is a Gradle generated file for dependency locking.
# Manual edits can break the build and are not advised.
# This file is expected to be part of source control.
androidx.activity:activity:1.8.1=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.annotation:annotation-experimental:1.4.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.annotation:annotation-jvm:1.8.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.annotation:annotation:1.8.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.arch.core:core-common:2.2.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.arch.core:core-runtime:2.2.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.collection:collection:1.1.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.concurrent:concurrent-futures:1.1.0=freeDebugRuntimeClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.core:core-ktx:1.13.1=freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.core:core-ktx:1.2.0=freeDebugAndroidTestCompileClasspath,paidDebugAndroidTestCompileClasspath
androidx.core:core:1.13.1=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.customview:customview:1.0.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.fragment:fragment:1.7.1=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.interpolator:interpolator:1.0.0=freeDebugRuntimeClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.lifecycle:lifecycle-common-java8:2.7.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.lifecycle:lifecycle-common:2.7.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.lifecycle:lifecycle-livedata-core:2.7.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.lifecycle:lifecycle-livedata:2.7.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.lifecycle:lifecycle-process:2.7.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.lifecycle:lifecycle-runtime:2.7.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.lifecycle:lifecycle-viewmodel:2.7.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.loader:loader:1.0.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.profileinstaller:profileinstaller:1.3.1=freeDebugRuntimeClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.savedstate:savedstate:1.2.1=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.startup:startup-runtime:1.1.1=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.test.espresso:espresso-core:3.2.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.test.espresso:espresso-idling-resource:3.2.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.test:monitor:1.2.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.test:rules:1.2.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.test:runner:1.2.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.tracing:tracing:1.2.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.versionedparcelable:versionedparcelable:1.1.1=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.viewpager:viewpager:1.0.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.window.extensions.core:core:1.0.0=freeDebugRuntimeClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.window:window-java:1.2.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
androidx.window:window:1.2.0=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
com.android.tools.analytics-library:protos:27.1.3=lintClassPath
com.android.tools.analytics-library:shared:27.1.3=lintClassPath
com.android.tools.analytics-library:tracker:27.1.3=lintClassPath
com.android.tools.build:aapt2-proto:4.1.0-alpha01-6193524=lintClassPath
com.android.tools.build:aapt2:4.1.3-6503028=_internal_aapt2_binary
com.android.tools.build:apksig:4.1.3=lintClassPath
com.android.tools.build:apkzlib:4.1.3=lintClassPath
com.android.tools.build:builder-model:4.1.3=lintClassPath
com.android.tools.build:builder-test-api:4.1.3=lintClassPath
com.android.tools.build:builder:4.1.3=lintClassPath
com.android.tools.build:gradle-api:4.1.3=lintClassPath
com.android.tools.build:manifest-merger:27.1.3=lintClassPath
com.android.tools.ddms:ddmlib:27.1.3=lintClassPath
com.android.tools.ddms:ddmlib:31.7.0=_internal-unified-test-platform-android-device-provider-ddmlib
com.android.tools.emulator:proto:31.7.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention
com.android.tools.external.com-intellij:intellij-core:27.1.3=lintClassPath
com.android.tools.external.com-intellij:kotlin-compiler:27.1.3=lintClassPath
com.android.tools.external.org-jetbrains:uast:27.1.3=lintClassPath
com.android.tools.layoutlib:layoutlib-api:27.1.3=lintClassPath
com.android.tools.lint:lint-api:27.1.3=lintClassPath
com.android.tools.lint:lint-checks:27.1.3=lintClassPath
com.android.tools.lint:lint-gradle-api:27.1.3=lintClassPath
com.android.tools.lint:lint-gradle:27.1.3=lintClassPath
com.android.tools.lint:lint-model:27.1.3=lintClassPath
com.android.tools.lint:lint:27.1.3=lintClassPath
com.android.tools.utp:android-device-provider-ddmlib-proto:31.7.0=_internal-unified-test-platform-android-device-provider-ddmlib
com.android.tools.utp:android-device-provider-ddmlib:31.7.0=_internal-unified-test-platform-android-device-provider-ddmlib
com.android.tools.utp:android-device-provider-gradle-proto:31.7.0=_internal-unified-test-platform-android-device-provider-gradle
com.android.tools.utp:android-device-provider-gradle:31.7.0=_internal-unified-test-platform-android-device-provider-gradle
com.android.tools.utp:android-device-provider-profile-proto:31.7.0=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle
com.android.tools.utp:android-device-provider-profile:31.7.0=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle
com.android.tools.utp:android-test-plugin-host-additional-test-output-proto:31.7.0=_internal-unified-test-platform-android-test-plugin-host-additional-test-output
com.android.tools.utp:android-test-plugin-host-additional-test-output:31.7.0=_internal-unified-test-platform-android-test-plugin-host-additional-test-output
com.android.tools.utp:android-test-plugin-host-apk-installer-proto:31.7.0=_internal-unified-test-platform-android-test-plugin-host-apk-installer
com.android.tools.utp:android-test-plugin-host-apk-installer:31.7.0=_internal-unified-test-platform-android-test-plugin-host-apk-installer
com.android.tools.utp:android-test-plugin-host-coverage-proto:31.7.0=_internal-unified-test-platform-android-test-plugin-host-coverage
com.android.tools.utp:android-test-plugin-host-coverage:31.7.0=_internal-unified-test-platform-android-test-plugin-host-coverage
com.android.tools.utp:android-test-plugin-host-device-info-proto:31.7.0=_internal-unified-test-platform-android-test-plugin-host-device-info
com.android.tools.utp:android-test-plugin-host-device-info:31.7.0=_internal-unified-test-platform-android-test-plugin-host-device-info
com.android.tools.utp:android-test-plugin-host-emulator-control-proto:31.7.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control
com.android.tools.utp:android-test-plugin-host-emulator-control:31.7.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control
com.android.tools.utp:android-test-plugin-host-logcat-proto:31.7.0=_internal-unified-test-platform-android-test-plugin-host-logcat
com.android.tools.utp:android-test-plugin-host-logcat:31.7.0=_internal-unified-test-platform-android-test-plugin-host-logcat
com.android.tools.utp:android-test-plugin-host-retention-proto:31.7.0=_internal-unified-test-platform-android-test-plugin-host-retention
com.android.tools.utp:android-test-plugin-host-retention:31.7.0=_internal-unified-test-platform-android-test-plugin-host-retention
com.android.tools.utp:android-test-plugin-result-listener-gradle-proto:31.7.0=_internal-unified-test-platform-android-test-plugin-result-listener-gradle
com.android.tools.utp:android-test-plugin-result-listener-gradle:31.7.0=_internal-unified-test-platform-android-test-plugin-result-listener-gradle
com.android.tools.utp:utp-common:31.7.0=_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention
com.android.tools:annotations:27.1.3=lintClassPath
com.android.tools:annotations:31.7.0=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
com.android.tools:common:27.1.3=lintClassPath
com.android.tools:common:31.7.0=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
com.android.tools:dvlib:27.1.3=lintClassPath
com.android.tools:repository:27.1.3=lintClassPath
com.android.tools:sdk-common:27.1.3=lintClassPath
com.android.tools:sdklib:27.1.3=lintClassPath
com.android:signflinger:4.1.3=lintClassPath
com.android:zipflinger:4.1.3=lintClassPath
com.getkeepsafe.relinker:relinker:1.4.5=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
com.google.android:annotations:*******=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
com.google.api.grpc:proto-google-common-protos:2.17.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
com.google.code.findbugs:jsr305:2.0.1=freeDebugAndroidTestCompileClasspath,paidDebugAndroidTestCompileClasspath
com.google.code.findbugs:jsr305:3.0.2=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,lintClassPath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
com.google.code.gson:gson:2.10.1=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
com.google.code.gson:gson:2.8.5=lintClassPath
com.google.crypto.tink:tink:1.7.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control
com.google.errorprone:error_prone_annotations:2.18.0=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
com.google.errorprone:error_prone_annotations:2.3.2=freeDebugRuntimeClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestRuntimeClasspath,lintClassPath,paidDebugRuntimeClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestRuntimeClasspath
com.google.guava:failureaccess:1.0.1=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle,freeDebugRuntimeClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestRuntimeClasspath,lintClassPath,paidDebugRuntimeClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestRuntimeClasspath
com.google.guava:guava:28.1-android=freeDebugRuntimeClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestRuntimeClasspath
com.google.guava:guava:28.1-jre=lintClassPath
com.google.guava:guava:32.0.1-jre=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle,freeDebugRuntimeClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestRuntimeClasspath,lintClassPath,paidDebugRuntimeClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestRuntimeClasspath
com.google.j2objc:j2objc-annotations:1.3=freeDebugRuntimeClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestRuntimeClasspath,lintClassPath,paidDebugRuntimeClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestRuntimeClasspath
com.google.j2objc:j2objc-annotations:2.8=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
com.google.jimfs:jimfs:1.1=lintClassPath
com.google.protobuf:protobuf-java:3.10.0=lintClassPath
com.google.protobuf:protobuf-java:3.22.3=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
com.google.testing.platform:android-device-provider-local:0.0.9-alpha02=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
com.google.testing.platform:android-driver-instrumentation:0.0.9-alpha02=_internal-unified-test-platform-android-driver-instrumentation,_internal-unified-test-platform-android-test-plugin-host-emulator-control
com.google.testing.platform:android-test-plugin:0.0.9-alpha02=_internal-unified-test-platform-android-test-plugin
com.google.testing.platform:core-proto:0.0.9-alpha02=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
com.google.testing.platform:core:0.0.9-alpha02=_internal-unified-test-platform-core
com.google.testing.platform:launcher:0.0.9-alpha02=_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-launcher
com.googlecode.json-simple:json-simple:1.1=lintClassPath
com.squareup:javawriter:2.1.1=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
com.squareup:javawriter:2.5.0=lintClassPath
com.sun.activation:javax.activation:1.2.0=lintClassPath
com.sun.istack:istack-commons-runtime:3.0.7=lintClassPath
com.sun.xml.fastinfoset:FastInfoset:1.2.15=lintClassPath
commons-codec:commons-codec:1.10=lintClassPath
commons-io:commons-io:2.13.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention
commons-logging:commons-logging:1.2=lintClassPath
io.grpc:grpc-api:1.57.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.grpc:grpc-context:1.57.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.grpc:grpc-core:1.57.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.grpc:grpc-netty:1.57.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.grpc:grpc-protobuf-lite:1.57.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.grpc:grpc-protobuf:1.57.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.grpc:grpc-stub:1.57.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.netty:netty-buffer:4.1.93.Final=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.netty:netty-codec-http2:4.1.93.Final=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.netty:netty-codec-http:4.1.93.Final=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.netty:netty-codec-socks:4.1.93.Final=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.netty:netty-codec:4.1.93.Final=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.netty:netty-common:4.1.93.Final=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.netty:netty-handler-proxy:4.1.93.Final=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.netty:netty-handler:4.1.93.Final=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.netty:netty-resolver:4.1.93.Final=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.netty:netty-transport-native-unix-common:4.1.93.Final=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.netty:netty-transport:4.1.93.Final=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
io.perfmark:perfmark-api:0.26.0=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
it.unimi.dsi:fastutil:7.2.0=lintClassPath
javax.activation:javax.activation-api:1.2.0=lintClassPath
javax.annotation:javax.annotation-api:1.3.2=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
javax.inject:javax.inject:1=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,lintClassPath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
javax.xml.bind:jaxb-api:2.3.1=lintClassPath
junit:junit:4.12=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
net.java.dev.jna:jna-platform:5.6.0=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
net.java.dev.jna:jna:5.6.0=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
net.sf.jopt-simple:jopt-simple:4.9=lintClassPath
net.sf.kxml:kxml2:2.3.0=_internal-unified-test-platform-android-device-provider-ddmlib,freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,lintClassPath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
org.apache.commons:commons-compress:1.12=lintClassPath
org.apache.httpcomponents:httpclient:4.5.6=lintClassPath
org.apache.httpcomponents:httpcore:4.4.10=lintClassPath
org.apache.httpcomponents:httpmime:4.5.6=lintClassPath
org.bouncycastle:bcpkix-jdk15on:1.56=lintClassPath
org.bouncycastle:bcprov-jdk15on:1.56=lintClassPath
org.checkerframework:checker-compat-qual:2.5.5=freeDebugRuntimeClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestRuntimeClasspath
org.checkerframework:checker-qual:2.8.1=lintClassPath
org.checkerframework:checker-qual:3.33.0=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
org.codehaus.groovy:groovy-all:2.4.15=lintClassPath
org.codehaus.mojo:animal-sniffer-annotations:1.18=freeDebugRuntimeClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestRuntimeClasspath,lintClassPath,paidDebugRuntimeClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestRuntimeClasspath
org.codehaus.mojo:animal-sniffer-annotations:1.23=_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
org.glassfish.jaxb:jaxb-runtime:2.3.1=lintClassPath
org.glassfish.jaxb:txw2:2.3.1=lintClassPath
org.hamcrest:hamcrest-core:1.3=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
org.hamcrest:hamcrest-integration:1.3=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
org.hamcrest:hamcrest-library:1.3=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
org.jacoco:org.jacoco.agent:0.8.7=androidJacocoAnt
org.jacoco:org.jacoco.ant:0.8.7=androidJacocoAnt
org.jacoco:org.jacoco.core:0.8.7=androidJacocoAnt
org.jacoco:org.jacoco.report:0.8.7=androidJacocoAnt
org.jetbrains.kotlin:kotlin-reflect:1.3.72=lintClassPath
org.jetbrains.kotlin:kotlin-stdlib-common:1.3.72=lintClassPath
org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
org.jetbrains.kotlin:kotlin-stdlib-common:1.9.20=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.72=lintClassPath
org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.20=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.3.72=lintClassPath
org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.20=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
org.jetbrains.kotlin:kotlin-stdlib:1.3.72=lintClassPath
org.jetbrains.kotlin:kotlin-stdlib:1.8.22=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
org.jetbrains.kotlin:kotlin-stdlib:1.9.20=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.6.4=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
org.jetbrains.kotlinx:kotlinx-coroutines-bom:1.7.1=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.4=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.4=_internal-unified-test-platform-android-device-provider-ddmlib,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle
org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.1=freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
org.jetbrains.trove4j:trove4j:20160824=lintClassPath
org.jetbrains:annotations:13.0=_internal-unified-test-platform-android-device-provider-gradle,_internal-unified-test-platform-android-test-plugin-host-additional-test-output,_internal-unified-test-platform-android-test-plugin-host-apk-installer,_internal-unified-test-platform-android-test-plugin-host-coverage,_internal-unified-test-platform-android-test-plugin-host-device-info,_internal-unified-test-platform-android-test-plugin-host-emulator-control,_internal-unified-test-platform-android-test-plugin-host-logcat,_internal-unified-test-platform-android-test-plugin-host-retention,_internal-unified-test-platform-android-test-plugin-result-listener-gradle,lintClassPath
org.jetbrains:annotations:23.0.0=_internal-unified-test-platform-android-device-provider-ddmlib,freeDebugAndroidTestCompileClasspath,freeDebugCompileClasspath,freeDebugRuntimeClasspath,freeDebugUnitTestCompileClasspath,freeDebugUnitTestRuntimeClasspath,freeProfileCompileClasspath,freeProfileRuntimeClasspath,freeProfileUnitTestCompileClasspath,freeProfileUnitTestRuntimeClasspath,freeReleaseCompileClasspath,freeReleaseRuntimeClasspath,freeReleaseUnitTestCompileClasspath,freeReleaseUnitTestRuntimeClasspath,paidDebugAndroidTestCompileClasspath,paidDebugCompileClasspath,paidDebugRuntimeClasspath,paidDebugUnitTestCompileClasspath,paidDebugUnitTestRuntimeClasspath,paidProfileCompileClasspath,paidProfileRuntimeClasspath,paidProfileUnitTestCompileClasspath,paidProfileUnitTestRuntimeClasspath,paidReleaseCompileClasspath,paidReleaseRuntimeClasspath,paidReleaseUnitTestCompileClasspath,paidReleaseUnitTestRuntimeClasspath
org.jvnet.staxex:stax-ex:1.8=lintClassPath
org.ow2.asm:asm-analysis:7.0=lintClassPath
org.ow2.asm:asm-analysis:9.1=androidJacocoAnt
org.ow2.asm:asm-commons:7.0=lintClassPath
org.ow2.asm:asm-commons:9.1=androidJacocoAnt
org.ow2.asm:asm-tree:7.0=lintClassPath
org.ow2.asm:asm-tree:9.1=androidJacocoAnt
org.ow2.asm:asm-util:7.0=lintClassPath
org.ow2.asm:asm:7.0=lintClassPath
org.ow2.asm:asm:9.1=androidJacocoAnt
empty=androidApis,androidJdkImage,androidTestUtil,compile,coreLibraryDesugaring,freeDebugAndroidTestAnnotationProcessorClasspath,freeDebugAndroidTestRuntimeClasspath,freeDebugAnnotationProcessorClasspath,freeDebugReverseMetadataValues,freeDebugUnitTestAnnotationProcessorClasspath,freeDebugWearBundling,freeProfileAnnotationProcessorClasspath,freeProfileReverseMetadataValues,freeProfileUnitTestAnnotationProcessorClasspath,freeProfileWearBundling,freeReleaseAnnotationProcessorClasspath,freeReleaseReverseMetadataValues,freeReleaseUnitTestAnnotationProcessorClasspath,freeReleaseWearBundling,lintChecks,lintPublish,paidDebugAndroidTestAnnotationProcessorClasspath,paidDebugAndroidTestRuntimeClasspath,paidDebugAnnotationProcessorClasspath,paidDebugReverseMetadataValues,paidDebugUnitTestAnnotationProcessorClasspath,paidDebugWearBundling,paidProfileAnnotationProcessorClasspath,paidProfileReverseMetadataValues,paidProfileUnitTestAnnotationProcessorClasspath,paidProfileWearBundling,paidReleaseAnnotationProcessorClasspath,paidReleaseReverseMetadataValues,paidReleaseUnitTestAnnotationProcessorClasspath,paidReleaseWearBundling,testCompile
