# Flutter Gallery

**NOTE**: The Flutter Gallery is now deprecated, and no longer being active maintained.

Flutter Gallery was a resource to help developers evaluate and use Flutter.
It is now being used primarily for testing. For posterity,  the web version
remains [hosted here](https://flutter-gallery-archive.web.app).

We recommend Flutter developers check out the following resources:

* **Wonderous**
([web demo](https://wonderous.app/web/),
[App Store](https://apps.apple.com/us/app/wonderous/id1612491897),
[Google Play](https://play.google.com/store/apps/details?id=com.gskinner.flutter.wonders),
[source code](https://github.com/gskinnerTeam/flutter-wonderous-app)):<br>
A Flutter app that showcases Flutter's support for elegant design and rich animations.

* **Material 3 Demo**
([web demo](https://flutter.github.io/samples/web/material_3_demo/),
[source code](https://github.com/flutter/samples/tree/main/material_3_demo)):<br>
A Flutter app that showcases Material 3 features in the Flutter Material library.

* **Flutter Samples**
([samples](https://flutter.github.io/samples), [source code](https://github.com/flutter/samples)):<br>
A collection of open source samples that illustrate best practices for Flutter.

* **Widget catalogs**
([Material](https://docs.flutter.dev/ui/widgets/material), [Cupertino](https://docs.flutter.dev/ui/widgets/cupertino)):<br>
Catalogs for Material, Cupertino, and other widgets available for use in UI.
