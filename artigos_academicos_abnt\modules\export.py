"""
Módulo de exportação para diferentes formatos para o aplicativo de artigos acadêmicos ABNT.
Implementa a conversão de artigos em Markdown para DOCX, LaTeX e HTML.
"""

import os
import re
import base64
from datetime import datetime
from io import BytesIO

# Tentar importar as bibliotecas necessárias
try:
    import markdown
    import pypandoc
    PANDOC_AVAILABLE = True
except ImportError:
    PANDOC_AVAILABLE = False

def export_to_pdf(markdown_text, output_path=None, title="Artigo Acadêmico", author=""):
    """
    Exporta texto markdown para PDF.

    Args:
        markdown_text (str): Texto em formato Markdown.
        output_path (str, optional): Caminho para salvar o arquivo PDF.
        title (str): Título do documento.
        author (str): Autor do documento.

    Returns:
        str: Caminho para o arquivo PDF gerado.
    """
    from .pdf_export import PDFExporter

    # Gerar o PDF
    pdf_bytes = PDFExporter.markdown_to_pdf(markdown_text, title=title, include_cover=True)

    # Se o caminho de saída não for fornecido, usar um nome padrão
    if not output_path:
        output_dir = "artigos_academicos_abnt_results"
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f"{title.replace(' ', '_').lower()}.pdf")

    # Salvar o PDF
    with open(output_path, 'wb') as f:
        f.write(pdf_bytes)

    return output_path

def markdown_to_docx(markdown_text, output_path=None, title="Artigo Acadêmico", author=""):
    """
    Exporta texto markdown para DOCX.

    Args:
        markdown_text (str): Texto em formato Markdown.
        output_path (str, optional): Caminho para salvar o arquivo DOCX.
        title (str): Título do documento.
        author (str): Autor do documento.

    Returns:
        str: Caminho para o arquivo DOCX gerado.
    """
    # Verificar se o pypandoc está disponível
    if not PANDOC_AVAILABLE:
        raise ImportError("pypandoc não está instalado. Instale com 'pip install pypandoc'")

    # Se o caminho de saída não for fornecido, usar um nome padrão
    if not output_path:
        output_dir = "artigos_academicos_abnt_results"
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f"{title.replace(' ', '_').lower()}.docx")

    # Criar arquivo temporário para o markdown
    temp_md = f"temp_{datetime.now().strftime('%Y%m%d%H%M%S')}.md"

    try:
        # Escrever o markdown em um arquivo temporário
        with open(temp_md, 'w', encoding='utf-8') as f:
            f.write(markdown_text)

        # Preparar argumentos extras
        extra_args = [
            f'--metadata=title:{title}',
            f'--metadata=author:{author}'
        ]

        # Adicionar referência de documento se existir
        if os.path.exists('reference.docx'):
            extra_args.append('--reference-doc=reference.docx')

        # Converter para DOCX usando Pandoc
        pypandoc.convert_file(
            temp_md,
            'docx',
            outputfile=output_path,
            extra_args=extra_args
        )

        return output_path

    finally:
        # Limpar arquivo temporário
        if os.path.exists(temp_md):
            os.remove(temp_md)

class ExportManager:
    """Classe para gerenciar a exportação de artigos para diferentes formatos"""

    @staticmethod
    def markdown_to_docx(markdown_text, title="Artigo Acadêmico"):
        """
        Converte texto Markdown para DOCX usando Pandoc.

        Args:
            markdown_text (str): Texto em formato Markdown.
            title (str): Título do documento.

        Returns:
            bytes: Conteúdo do arquivo DOCX em bytes.
        """
        if not PANDOC_AVAILABLE:
            raise ImportError("pypandoc não está instalado. Instale com 'pip install pypandoc'")

        # Criar arquivo temporário
        temp_md = f"temp_{datetime.now().strftime('%Y%m%d%H%M%S')}.md"
        temp_docx = f"temp_{datetime.now().strftime('%Y%m%d%H%M%S')}.docx"

        try:
            # Escrever o markdown em um arquivo temporário
            with open(temp_md, 'w', encoding='utf-8') as f:
                f.write(markdown_text)

            # Converter para DOCX usando Pandoc
            pypandoc.convert_file(
                temp_md,
                'docx',
                outputfile=temp_docx,
                extra_args=[
                    f'--metadata=title:{title}',
                    '--reference-doc=reference.docx'  # Usar um modelo de referência se disponível
                ]
            )

            # Ler o arquivo DOCX gerado
            with open(temp_docx, 'rb') as f:
                docx_bytes = f.read()

            return docx_bytes

        finally:
            # Limpar arquivos temporários
            if os.path.exists(temp_md):
                os.remove(temp_md)
            if os.path.exists(temp_docx):
                os.remove(temp_docx)

    @staticmethod
    def markdown_to_latex(markdown_text, title="Artigo Acadêmico", author=""):
        """
        Converte texto Markdown para LaTeX usando Pandoc.

        Args:
            markdown_text (str): Texto em formato Markdown.
            title (str): Título do documento.
            author (str): Autor do documento.

        Returns:
            str: Conteúdo do arquivo LaTeX.
        """
        if not PANDOC_AVAILABLE:
            raise ImportError("pypandoc não está instalado. Instale com 'pip install pypandoc'")

        # Converter para LaTeX usando Pandoc
        latex_content = pypandoc.convert_text(
            markdown_text,
            'latex',
            format='md',
            extra_args=[
                f'--metadata=title:{title}',
                f'--metadata=author:{author}',
                '--template=abnt'  # Usar um template ABNT se disponível
            ]
        )

        return latex_content

    @staticmethod
    def markdown_to_html(markdown_text, title="Artigo Acadêmico"):
        """
        Converte texto Markdown para HTML.

        Args:
            markdown_text (str): Texto em formato Markdown.
            title (str): Título do documento.

        Returns:
            str: Conteúdo do arquivo HTML.
        """
        try:
            # Usar a biblioteca markdown para converter para HTML
            html_content = markdown.markdown(
                markdown_text,
                extensions=[
                    'markdown.extensions.tables',
                    'markdown.extensions.fenced_code',
                    'markdown.extensions.toc'
                ]
            )

            # Adicionar cabeçalho HTML e CSS básico
            html_template = f"""<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: 'Times New Roman', Times, serif;
            line-height: 1.6;
            margin: 0 auto;
            max-width: 800px;
            padding: 20px;
        }}
        h1, h2, h3, h4, h5, h6 {{
            font-family: Arial, sans-serif;
            margin-top: 1.5em;
        }}
        h1 {{
            text-align: center;
            font-size: 1.8em;
        }}
        h2 {{
            font-size: 1.5em;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.3em;
        }}
        blockquote {{
            border-left: 4px solid #ddd;
            padding-left: 1em;
            color: #666;
        }}
        code {{
            background-color: #f9f9f9;
            padding: 0.2em 0.4em;
            border-radius: 3px;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
        }}
        img {{
            max-width: 100%;
        }}
        .footnote {{
            font-size: 0.9em;
            color: #666;
        }}
    </style>
</head>
<body>
    {html_content}
    <footer>
        <p class="footnote">Gerado em {datetime.now().strftime('%d/%m/%Y %H:%M:%S')} pelo Gerador de Artigos Acadêmicos ABNT</p>
    </footer>
</body>
</html>
"""
            return html_template

        except ImportError:
            # Fallback simples se a biblioteca markdown não estiver disponível
            html_content = f"<h1>{title}</h1>\n"
            html_content += markdown_text.replace("\n\n", "<br><br>").replace("# ", "<h1>").replace("## ", "<h2>")
            return html_content

    @staticmethod
    def get_download_link(file_bytes, filename, mime_type=None):
        """
        Cria um link de download para um arquivo.

        Args:
            file_bytes (bytes): Conteúdo do arquivo em bytes.
            filename (str): Nome do arquivo para download.
            mime_type (str, optional): Tipo MIME do arquivo.

        Returns:
            str: HTML para o link de download.
        """
        # Determinar o tipo MIME com base na extensão do arquivo
        if mime_type is None:
            ext = filename.split('.')[-1].lower()
            mime_types = {
                'pdf': 'application/pdf',
                'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'tex': 'application/x-tex',
                'html': 'text/html',
                'md': 'text/markdown'
            }
            mime_type = mime_types.get(ext, 'application/octet-stream')

        # Codificar o arquivo em base64
        b64 = base64.b64encode(file_bytes).decode()

        # Criar o link de download
        href = f'data:{mime_type};base64,{b64}'
        download_link = f'<a href="{href}" download="{filename}" class="download-button">📥 Baixar {filename}</a>'

        return download_link
